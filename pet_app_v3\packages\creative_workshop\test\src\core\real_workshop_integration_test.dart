import 'dart:io';
import 'package:test/test.dart';
import 'package:path/path.dart' as path;

void main() {
  group('真实插件文件验证测试', () {
    late String testPluginPath;

    setUpAll(() async {
      // 设置测试插件路径
      final currentDir = Directory.current.path;
      testPluginPath = path.join(currentDir, '..', '..', 'plugins', 'testing');
    });

    group('真实插件文件验证测试', () {
      test('应该能够识别生成的测试插件目录', () {
        final pluginDir = Directory(testPluginPath);
        expect(pluginDir.existsSync(), isTrue, reason: '测试插件目录应该存在');
      });

      test('应该能够读取插件清单文件', () {
        final manifestFile = File(path.join(testPluginPath, 'plugin.yaml'));
        expect(manifestFile.existsSync(), isTrue, reason: '插件清单文件应该存在');

        final content = manifestFile.readAsStringSync();
        expect(content.isNotEmpty, isTrue, reason: '清单文件不应为空');

        // 验证包含基本插件信息
        expect(content.contains('id:'), isTrue);
        expect(content.contains('name:'), isTrue);
        expect(content.contains('version:'), isTrue);
      });

      test('应该能够读取插件主文件', () {
        final mainFile =
            File(path.join(testPluginPath, 'lib', 'test_plugin.dart'));
        expect(mainFile.existsSync(), isTrue, reason: '插件主文件应该存在');

        final content = mainFile.readAsStringSync();
        expect(content.isNotEmpty, isTrue, reason: '主文件不应为空');
        expect(content.contains('library test_plugin'), isTrue);
      });

      test('应该能够读取插件核心实现', () {
        final coreFile =
            File(path.join(testPluginPath, 'lib', 'src', 'plugin_core.dart'));
        expect(coreFile.existsSync(), isTrue, reason: '插件核心文件应该存在');

        final content = coreFile.readAsStringSync();
        expect(content.isNotEmpty, isTrue, reason: '核心文件不应为空');
        expect(
            content.contains(
                'import \'package:plugin_system/plugin_system.dart\''),
            isTrue);
      });

      test('应该能够验证插件使用Plugin System基类', () {
        final coreFile =
            File(path.join(testPluginPath, 'lib', 'src', 'plugin_core.dart'));
        expect(coreFile.existsSync(), isTrue);

        final content = coreFile.readAsStringSync();

        // 验证正确导入Plugin System
        expect(
            content.contains(
                'import \'package:plugin_system/plugin_system.dart\' as plugin_sys;'),
            isTrue);

        // 验证继承Plugin System基类
        expect(content.contains('extends plugin_sys.Plugin'), isTrue);

        // 验证使用Plugin System类型
        expect(content.contains('plugin_sys.PluginState'), isTrue);
        expect(content.contains('plugin_sys.PluginType'), isTrue);
      });

      test('应该能够验证插件清单包含Pet App V3兼容性配置', () {
        final manifestFile = File(path.join(testPluginPath, 'plugin.yaml'));
        expect(manifestFile.existsSync(), isTrue);

        final content = manifestFile.readAsStringSync();

        // 验证Pet App V3兼容性
        expect(content.contains('min_pet_app_version: "3.0.0"'), isTrue);
        expect(content.contains('plugin_system_version: "^1.0.0"'), isTrue);

        // 验证多平台支持
        expect(content.contains('platforms:'), isTrue);
        expect(content.contains('- android'), isTrue);
        expect(content.contains('- ios'), isTrue);
        expect(content.contains('- web'), isTrue);

        // 验证生命周期钩子
        expect(content.contains('entry_points:'), isTrue);
        expect(content.contains('lifecycle:'), isTrue);
      });
    });
  });
}
