# home_dashboard - Git忽略文件
# 生成时间: 2025-07-18
# 更多信息: https://git-scm.com/docs/gitignore

# === Dart/Flutter 核心文件 ===

# Dart工具生成的文件
.dart_tool/
.packages
build/
pubspec.lock

# 代码生成文件
*.g.dart
*.freezed.dart
*.gr.dart
*.config.dart
*.mocks.dart
lib/generated/

# 测试覆盖率
coverage/
test/coverage/
lcov.info

# Flutter特定文件
.flutter-plugins
.flutter-plugins-dependencies
.metadata

# === IDE 和编辑器文件 ===

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# IntelliJ IDEA / Android Studio
.idea/
*.iml
*.ipr
*.iws
.idea_modules/

# Sublime Text
*.sublime-workspace
*.sublime-project

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp

# === 操作系统文件 ===

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# === 开发工具文件 ===

# Melos
.melos_tool/
melos_bootstrap.log

# Shorebird
.shorebird/
shorebird.log

# FVM (Flutter Version Management)
.fvm/

# Mason
.mason/

# Very Good CLI
.vgv/

# === 部署和CI/CD文件 ===

# Docker
.dockerignore
Dockerfile.dev
docker-compose.override.yml

# Kubernetes
k8s/secrets/
*.kubeconfig

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl
terraform.tfvars
terraform.tfvars.json

# === 安全和敏感文件 ===

# 环境变量
.env
.env.local
.env.development
.env.test
.env.production
.env.staging

# 密钥和证书
*.pem
*.key
*.p12
*.keystore
*.jks
google-services.json
GoogleService-Info.plist

# API密钥
api_keys.dart
secrets.dart
config/secrets/

# 数据库
*.db
*.sqlite
*.sqlite3
database.json

# === 临时和缓存文件 ===

# 日志文件
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 缓存目录
node_modules/
.npm
.yarn/
.pnp
.pnp.js

# 临时文件
*.tmp
*.temp
*.bak
*.backup
*~

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# === 项目特定文件 ===

# 文档生成
doc/api/
dartdoc_options.yaml

# 性能分析
*.trace
*.timeline
*.profile

# 自定义忽略
# 在此添加项目特定的忽略规则

