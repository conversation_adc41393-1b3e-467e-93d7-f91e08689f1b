/*
---------------------------------------------------------------
File name:          colors.dart
Author:             Ming Status CLI
Date created:       2025-07-14
Last modified:      2025-07-14
Dart Version:       3.2+
Description:        core_services_v1应用程序颜色资源
---------------------------------------------------------------
Change History:
    2025-07-14: Initial creation - core_services_v1应用程序颜色资源;
---------------------------------------------------------------
*/


import 'package:flutter/material.dart';

/// 颜色资源常量
///
/// 定义应用程序中使用的所有颜色
class AppColors {
  /// 私有构造函数，防止实例化
  AppColors._();

  /// === 主要颜色 ===
  
  /// 主色调
  static const Color primary = Color(0xFF2196F3);
  
  /// 主色调变体
  static const Color primaryVariant = Color(0xFF1976D2);
  
  /// 次要色调
  static const Color secondary = Color(0xFF03DAC6);
  
  /// 次要色调变体
  static const Color secondaryVariant = Color(0xFF018786);

  /// === 表面颜色 ===
  
  /// 背景色
  static const Color background = Color(0xFFFAFAFA);
  
  /// 表面色
  static const Color surface = Color(0xFFFFFFFF);
  
  /// 错误色
  static const Color error = Color(0xFFB00020);

  /// === 文本颜色 ===
  
  /// 主要文本色（在主色调上）
  static const Color onPrimary = Color(0xFFFFFFFF);
  
  /// 次要文本色（在次要色调上）
  static const Color onSecondary = Color(0xFF000000);
  
  /// 背景文本色（在背景上）
  static const Color onBackground = Color(0xFF000000);
  
  /// 表面文本色（在表面上）
  static const Color onSurface = Color(0xFF000000);
  
  /// 错误文本色（在错误色上）
  static const Color onError = Color(0xFFFFFFFF);

  /// === 语义颜色 ===
  
  /// 成功色
  static const Color success = Color(0xFF4CAF50);
  
  /// 警告色
  static const Color warning = Color(0xFFFF9800);
  
  /// 信息色
  static const Color info = Color(0xFF2196F3);

  /// === 灰度颜色 ===
  
  /// 灰色调色板
  static const Map<int, Color> grey = {
    50: Color(0xFFFAFAFA),
    100: Color(0xFFF5F5F5),
    200: Color(0xFFEEEEEE),
    300: Color(0xFFE0E0E0),
    400: Color(0xFFBDBDBD),
    500: Color(0xFF9E9E9E),
    600: Color(0xFF757575),
    700: Color(0xFF616161),
    800: Color(0xFF424242),
    900: Color(0xFF212121),
  };
}
