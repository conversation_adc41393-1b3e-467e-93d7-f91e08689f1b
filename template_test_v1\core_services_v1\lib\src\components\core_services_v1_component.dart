/*
---------------------------------------------------------------
File name:          core_services_v1_component.dart
Author:             Ming Status CLI
Date created:       2025-07-14
Last modified:      2025-07-14
Dart Version:       3.2+
Description:        core_services_v1 组件组件
---------------------------------------------------------------
Change History:
    2025-07-14: Initial creation - core_services_v1 组件组件;
---------------------------------------------------------------
*/

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:provider/provider.dart';

import '../providers/core_services_v1_provider.dart';
import '../constants/core_services_v1_constants.dart';
import '../utils/core_services_v1_utils.dart';

/// core_services_v1可复用组件
///
class CoreServicesV1Component extends StatelessWidget {
  /// 数据
  final Map<String, dynamic>? data;

  /// 点击回调
  final VoidCallback? onTap;

  /// 创建CoreServicesV1Component实例
  const CoreServicesV1Component({
    super.key,
    this.data,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: 16.0,
        vertical: 8.0,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8.0),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: _buildContent(context),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    if (data == null) {
      return const Text('No data available');
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (data!['title'] != null)
          Text(
            data!['title'] as String,
            style: Theme.of(context).textTheme.titleMedium,
          ),
        if (data!['subtitle'] != null) ...[
          const SizedBox(height: 4),
          Text(
            data!['subtitle'] as String,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
        if (data!['description'] != null) ...[
          const SizedBox(height: 8),
          Text(
            data!['description'] as String,
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ],
    );
  }
}
