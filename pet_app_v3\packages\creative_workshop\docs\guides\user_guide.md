# Creative Workshop 用户指南

## 概述

Creative Workshop 是一个功能强大的 Flutter 应用商店与开发者平台模块，为用户提供插件发现、安装、管理等完整的应用生态功能。本指南将帮助您快速上手并充分利用这些功能。

**🔄 Phase 5.0.6 重大更新**: 从绘画工具转型为应用商店+开发者平台+插件管理三位一体系统

## 快速开始

### 1. 安装和配置

在您的 Flutter 项目中添加 Creative Workshop 依赖：

```yaml
dependencies:
  creative_workshop:
    path: ../packages/creative_workshop
```

### 2. 基本使用

```dart
import 'package:creative_workshop/creative_workshop.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化插件管理器
  await PluginManager.instance.initialize();

  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Creative Workshop',
      home: Scaffold(
        body: CreativeWorkspace(
          initialLayout: WorkspaceLayout.store,
        ),
      ),
    );
  }
}
```

## 主要功能

### 1. 三种工作模式

Creative Workshop 提供三种主要工作模式：

#### 1.1 应用商店模式 (App Store)
- **功能**: 浏览、搜索、安装插件
- **界面**: 现代化的商店体验
- **特性**:
  - 插件卡片展示：图标、名称、描述、评分
  - 实时搜索：按名称、描述、标签搜索
  - 分类过滤：按类型筛选插件
  - 排序功能：按评分、下载量、更新时间排序

#### 1.2 开发者平台模式 (Developer Platform)
- **功能**: 项目管理、插件开发、发布管理
- **界面**: 专业的开发者工具集
- **特性**:
  - 项目管理：创建、跟踪、管理开发项目
  - 插件开发：集成开发环境和调试工具
  - 发布管理：插件发布、审核、版本控制
  - Ming CLI 集成：命令行工具无缝集成

#### 1.3 插件管理模式 (Plugin Management)
- **功能**: 插件生命周期管理
- **界面**: 专业的管理控制台
- **特性**:
  - 已安装插件管理：查看、启用、禁用、卸载
  - 权限管理：细粒度权限控制
  - 更新管理：自动和手动更新
  - 依赖管理：依赖关系分析和可视化

### 2. 应用商店使用指南

#### 2.1 浏览插件

1. **进入应用商店**：
   - 启动应用后默认进入应用商店模式
   - 或点击左侧导航栏的"商店"图标

2. **浏览插件**：
   - 查看推荐插件：首页展示热门和推荐插件
   - 浏览分类：点击分类标签查看特定类型插件
   - 查看详情：点击插件卡片查看详细信息

#### 2.2 搜索插件

1. **使用搜索栏**：
   - 在顶部搜索栏输入关键词
   - 支持按名称、描述、标签搜索
   - 实时显示搜索结果

2. **过滤和排序**：
   - 分类过滤：选择特定插件类别
   - 排序选项：按评分、下载量、更新时间排序
   - 高级筛选：按权限、大小等条件筛选

#### 2.3 安装插件

1. **查看插件详情**：
   - 点击插件卡片进入详情页
   - 查看插件描述、截图、评价
   - 检查权限要求和依赖关系

2. **安装插件**：
   - 点击"安装"按钮
   - 确认权限授权
   - 等待下载和安装完成
   - 安装成功后可立即使用

### 3. 开发者平台使用指南

#### 3.1 项目管理

1. **创建项目**：
   - 切换到开发者平台模式
   - 点击"项目管理"标签
   - 点击"新建项目"按钮
   - 选择项目类型：工具、游戏、实用程序、主题、其他
   - 填写项目信息并创建

2. **管理项目**：
   - 查看项目列表和状态
   - 搜索和过滤项目
   - 编辑项目信息
   - 删除不需要的项目

#### 3.2 插件开发

1. **开发环境**：
   - 使用集成的代码编辑器
   - 访问调试工具和测试环境
   - 查看开发文档和API参考
   - 使用模板快速开始

2. **开发工具**：
   - 代码编辑器：语法高亮和自动完成
   - 调试器：断点调试和变量查看
   - 测试工具：单元测试和集成测试
   - 构建工具：自动构建和打包
   - 预览工具：实时预览插件效果
   - 性能分析：性能监控和优化建议

#### 3.3 发布管理

1. **准备发布**：
   - 完成插件开发和测试
   - 准备插件元数据和截图
   - 编写详细的插件描述
   - 设置版本信息和更新日志

2. **提交审核**：
   - 点击"发布管理"标签
   - 选择要发布的项目
   - 填写发布信息
   - 提交审核申请

3. **发布状态跟踪**：
   - 查看审核进度：草稿、审核中、已通过、已拒绝
   - 查看发布统计：下载量、评分、用户反馈
   - 管理版本：发布更新、回滚版本

#### 3.4 Ming CLI 集成

1. **CLI 状态检查**：
   - 查看 Ming CLI 安装状态
   - 检查版本信息和更新
   - 配置 CLI 环境

2. **快速命令**：
   - 创建新项目：`ming create`
   - 构建项目：`ming build`
   - 测试项目：`ming test`
   - 发布项目：`ming publish`
   - 清理项目：`ming clean`
   - 帮助信息：`ming help`

### 4. 插件管理使用指南

#### 4.1 已安装插件管理

1. **查看已安装插件**：
   - 切换到插件管理模式
   - 点击"已安装插件"标签
   - 查看所有已安装插件列表

2. **插件操作**：
   - 启用/禁用插件：切换插件状态
   - 卸载插件：移除不需要的插件
   - 查看详情：查看插件信息和使用情况
   - 搜索过滤：快速找到特定插件

3. **批量操作**：
   - 批量启用/禁用：选择多个插件进行操作
   - 批量卸载：一次性移除多个插件
   - 导出列表：导出插件列表用于备份

#### 4.2 权限管理

1. **查看权限**：
   - 点击"权限管理"标签
   - 查看所有插件的权限使用情况
   - 了解8种权限类型：文件系统、网络、通知、剪贴板、相机、麦克风、位置、设备信息

2. **权限控制**：
   - 授权/撤销权限：精确控制插件权限
   - 权限分组：按权限类型查看插件
   - 安全警告：识别高风险权限使用

#### 4.3 更新管理

1. **检查更新**：
   - 点击"更新管理"标签
   - 查看可更新插件列表
   - 手动检查更新

2. **更新操作**：
   - 单个更新：更新特定插件
   - 批量更新：一次性更新所有插件
   - 自动更新：配置自动更新策略
   - 更新历史：查看更新记录和回滚

#### 4.4 依赖管理

1. **查看依赖**：
   - 点击"依赖管理"标签
   - 查看插件依赖关系图
   - 识别依赖冲突

2. **依赖操作**：
   - 解决冲突：处理依赖版本冲突
   - 安装依赖：自动安装缺失依赖
   - 清理依赖：移除无用依赖

## 快捷键

### 通用快捷键
- `Ctrl+1`：切换到应用商店模式
- `Ctrl+2`：切换到开发者平台模式
- `Ctrl+3`：切换到插件管理模式
- `Ctrl+F`：搜索插件
- `Ctrl+R`：刷新当前页面
- `F5`：刷新插件列表
- `Esc`：关闭当前对话框

### 插件管理快捷键
- `Ctrl+I`：安装选中插件
- `Ctrl+U`：卸载选中插件
- `Ctrl+E`：启用/禁用选中插件
- `Ctrl+Shift+U`：检查更新
- `Delete`：删除选中项目

### 开发者快捷键
- `Ctrl+N`：新建项目
- `Ctrl+B`：构建项目
- `Ctrl+T`：运行测试
- `Ctrl+P`：发布项目
- `F9`：调试模式

## 设置和配置

### 1. 插件管理设置

**自动更新**：
- 启用/禁用自动更新
- 设置更新检查频率
- 选择更新时间窗口

**下载设置**：
- 最大并发下载数
- 下载超时时间
- 下载缓存大小

**安全设置**：
- 权限验证级别
- 插件签名验证
- 沙箱隔离设置

### 2. 界面设置

**主题选择**：
- 浅色主题：适合白天使用
- 深色主题：适合夜间使用
- 自动主题：根据系统设置自动切换

**布局设置**：
- 应用商店布局：网格视图、列表视图
- 开发者平台布局：标签页排列
- 插件管理布局：详细视图、紧凑视图

### 3. 性能设置

**缓存管理**：
- 插件缓存大小
- 图片缓存设置
- 自动清理策略

**网络设置**：
- API 服务器地址
- 连接超时时间
- 重试次数设置

## 故障排除

### 常见问题

**Q: 插件安装失败**
A: 检查网络连接，确保有足够的存储空间，验证插件权限

**Q: 插件无法启动**
A: 检查插件依赖是否满足，尝试重新安装插件

**Q: 权限被拒绝**
A: 在权限管理中检查并授予必要的权限

**Q: 更新检查失败**
A: 检查网络连接，确认服务器状态正常

**Q: 开发者平台无法访问**
A: 确认开发者账户状态，检查登录凭据

### 性能优化建议

1. **定期清理缓存**：清理不需要的插件缓存和临时文件
2. **合理配置权限**：只授予插件必要的权限
3. **控制并发下载**：避免同时下载过多插件
4. **定期更新插件**：保持插件为最新版本以获得最佳性能

### 安全建议

1. **谨慎授权权限**：仔细审查插件请求的权限
2. **定期检查权限**：定期审查已授权的权限
3. **使用可信来源**：只从官方商店安装插件
4. **及时更新**：保持插件和系统为最新版本

## 高级功能

### 1. 批量操作

**批量安装**：
- 选择多个插件进行批量安装
- 导入插件列表进行批量安装
- 设置安装优先级

**批量管理**：
- 批量启用/禁用插件
- 批量更新插件
- 批量卸载插件

### 2. 插件开发

**开发环境配置**：
- 设置开发工具路径
- 配置调试环境
- 设置测试参数

**项目模板**：
- 使用预设模板快速创建项目
- 自定义项目模板
- 分享模板给其他开发者

### 3. 数据导入导出

**插件列表导出**：
- 导出已安装插件列表
- 导出插件配置信息
- 创建插件安装包

**数据备份**：
- 备份插件数据和配置
- 恢复插件数据
- 同步到云端存储

## 技术支持

如果您在使用过程中遇到问题，可以：

1. **查看文档**：
   - [用户指南](user_guide.md) - 详细使用说明
   - [API 文档](../api/api.md) - 技术接口文档
   - [架构文档](../architecture/architecture.md) - 系统设计文档
   - [开发指南](../development/development.md) - 开发者指南

2. **获取帮助**：
   - 查看常见问题解答
   - 提交问题报告
   - 联系技术支持团队
   - 参与社区讨论

## 更新日志

### 版本 5.0.6 - Phase 5.0.6 (2025-07-22)
- ✅ **应用商店功能**：完整的插件浏览、搜索、安装体验
- ✅ **开发者平台**：项目管理、插件开发、发布管理
- ✅ **插件管理系统**：生命周期管理、权限控制、依赖管理
- ✅ **企业级架构**：双核心架构(PluginManager + PluginRegistry)
- ✅ **安全性增强**：8种权限类型、沙箱隔离、签名验证

### 版本 1.4.0 (2025-07-19) - 已废弃
- 绘画工具系统（已转型为插件管理系统）
- 游戏功能（已重构为插件生态）
- 项目管理（已升级为开发者平台）

### 未来计划
- **云端生态**：全球插件分发网络、多设备同步
- **AI 智能化**：智能推荐、自动测试、代码生成
- **企业级功能**：企业插件管理、合规性检查、批量部署
- **开放生态**：第三方集成、API 开放、插件市场

---

**文档版本**: 5.0.6
**最后更新**: 2025-07-22
**适用版本**: Creative Workshop 5.0.6+
