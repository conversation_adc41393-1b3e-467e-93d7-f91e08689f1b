import 'package:flutter_test/flutter_test.dart';

import 'package:creative_workshop/src/core/workshop_manager.dart';

void main() {
  group('WorkshopManager', () {
    late WorkshopManager workshopManager;

    setUp(() {
      workshopManager = WorkshopManager.instance;
    });

    group('基础功能', () {
      test('应该能够获取实例', () {
        expect(workshopManager, isNotNull);
        expect(workshopManager.state, equals(WorkshopState.uninitialized));
      });

      test('应该成功启动', () async {
        // Arrange
        await workshopManager.initialize();

        // Act
        final result = await workshopManager.start();

        // Assert
        expect(result, isTrue);
        expect(workshopManager.state, equals(WorkshopState.running));
      });

      test('应该成功停止', () async {
        // Arrange
        await workshopManager.initialize();
        await workshopManager.start();

        // Act
        final result = await workshopManager.stop();

        // Assert
        expect(result, isTrue);
        expect(workshopManager.state, equals(WorkshopState.ready));
      });

      test('应该支持暂停和恢复', () async {
        // Arrange
        await workshopManager.initialize();
        await workshopManager.start();

        // Act & Assert
        final pauseResult = await workshopManager.pause();
        expect(pauseResult, isTrue);
        expect(workshopManager.state, equals(WorkshopState.paused));

        final resumeResult = await workshopManager.resume();
        expect(resumeResult, isTrue);
        expect(workshopManager.state, equals(WorkshopState.running));
      });
    });

    group('状态管理', () {
      test('应该正确报告初始状态', () {
        expect(workshopManager.state, equals(WorkshopState.uninitialized));
      });

      test('应该提供项目管理器访问', () {
        expect(workshopManager.projectManager, isNotNull);
      });

      test('应该提供状态变更流', () {
        expect(workshopManager.stateChanges, isNotNull);
      });
    });

    group('错误处理', () {
      test('应该处理重复初始化', () async {
        // Arrange
        await workshopManager.initialize();

        // Act
        final result = await workshopManager.initialize();

        // Assert
        expect(result, isTrue); // 应该返回true，因为已经初始化
        expect(workshopManager.state, equals(WorkshopState.ready));
      });

      test('应该处理无效状态转换', () async {
        // Act - 尝试在未初始化时启动
        final result = await workshopManager.start();

        // Assert
        expect(result, isFalse);
        expect(workshopManager.state, equals(WorkshopState.uninitialized));
      });
    });
  });
}
