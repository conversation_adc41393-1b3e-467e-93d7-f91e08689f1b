# =============================================================================
# Ming Status CLI 插件模板配置文件 (brick.yaml)
# 版本: 2.0
# 符合Ming Status CLI规范，专为Pet App V3插件开发设计
# =============================================================================

# 基本信息 (必需字段)
name: plugin
description: Pet App V3兼容的企业级插件模板，提供完整的插件开发框架
version: 2.0.0
repository: https://github.com/lgnorant-lu/ming_status_cli.git

# 模板变量定义
vars:
  # 核心变量 - 插件基础信息
  plugin_name:
    type: string
    description: 插件名称
    default: my_awesome_plugin

  plugin_display_name:
    type: string
    description: 插件显示名称
    default: My Awesome Plugin

  description:
    type: string
    description: 插件描述信息
    default: "An awesome test plugin for Pet App V3"

  author:
    type: string
    description: 作者名称
    default: Test Developer

  author_email:
    type: string
    description: 作者邮箱地址
    default: <EMAIL>

  plugin_type:
    type: enum
    description: 插件类型
    values: ["tool", "game", "utility", "social", "productivity", "entertainment", "education", "business"]
    default: tool

  version:
    type: string
    description: 初始版本号
    default: "1.0.0"

  dart_version:
    type: string
    description: Dart SDK版本要求
    default: "^3.2.0"

  license:
    type: enum
    description: 开源许可证类型
    values: ["MIT", "Apache-2.0", "GPL-3.0", "BSD-3-Clause", "ISC"]
    default: MIT

  include_ui_components:
    type: boolean
    description: 是否包含UI组件支持
    default: true

  include_services:
    type: boolean
    description: 是否包含服务层架构
    default: true

  include_models:
    type: boolean
    description: 是否包含数据模型管理
    default: true

  include_utils:
    type: boolean
    description: 是否包含工具类支持
    default: true

  include_l10n:
    type: boolean
    description: 是否包含国际化支持
    default: false

  repository_url:
    type: string
    description: 仓库URL (可选)
    default: ""
