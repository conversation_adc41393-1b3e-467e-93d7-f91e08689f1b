/*
---------------------------------------------------------------
File name:          plugin_installation_service.dart
Author:             Pet App Team
Date created:       2025-08-02
Last modified:      2025-08-02
Dart Version:       3.2+
Description:        插件安装服务 - 真实集成Plugin System
---------------------------------------------------------------
Change History:
    2025-08-02: Initial creation - 插件安装服务真实集成;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:io';
import 'package:plugin_system/plugin_system.dart';
import '../models/installation_result.dart';

/// 插件安装错误类型
enum PluginInstallationError {
  /// 插件已安装
  alreadyInstalled,

  /// 插件不存在
  pluginNotFound,

  /// 依赖冲突
  dependencyConflict,

  /// 权限不足
  insufficientPermissions,

  /// 网络错误
  networkError,

  /// 存储空间不足
  insufficientStorage,

  /// 插件包损坏
  corruptedPackage,

  /// 版本不兼容
  incompatibleVersion,

  /// 安装超时
  installationTimeout,

  /// 未知错误
  unknown,
}

/// 插件安装结果
class PluginInstallationResult {
  const PluginInstallationResult({
    required this.success,
    required this.pluginId,
    this.message,
    this.error,
    this.installedVersion,
    this.installationPath,
    this.installationTime,
  });

  /// 安装是否成功
  final bool success;

  /// 插件ID
  final String pluginId;

  /// 结果消息
  final String? message;

  /// 错误类型
  final PluginInstallationError? error;

  /// 已安装版本
  final String? installedVersion;

  /// 安装路径
  final String? installationPath;

  /// 安装耗时
  final Duration? installationTime;

  /// 创建成功结果
  factory PluginInstallationResult.success({
    required String pluginId,
    String? message,
    String? installedVersion,
    String? installationPath,
    Duration? installationTime,
  }) =>
      PluginInstallationResult(
        success: true,
        pluginId: pluginId,
        message: message,
        installedVersion: installedVersion,
        installationPath: installationPath,
        installationTime: installationTime,
      );

  /// 创建失败结果
  factory PluginInstallationResult.failure({
    required String pluginId,
    String? message,
    PluginInstallationError? error,
  }) =>
      PluginInstallationResult(
        success: false,
        pluginId: pluginId,
        message: message,
        error: error,
      );
}

/// 插件安装进度信息
class PluginInstallationProgress {
  const PluginInstallationProgress({
    required this.pluginId,
    required this.progress,
    required this.stage,
    this.message,
  });

  /// 插件ID
  final String pluginId;

  /// 进度百分比 (0.0 - 1.0)
  final double progress;

  /// 当前阶段
  final String stage;

  /// 阶段消息
  final String? message;
}

/// 插件安装服务
///
/// 负责插件的真实安装、卸载和管理，集成Plugin System
class PluginInstallationService {
  PluginInstallationService._();

  /// 单例实例
  static final PluginInstallationService _instance =
      PluginInstallationService._();
  static PluginInstallationService get instance => _instance;

  /// Plugin System组件
  final PluginRegistry _registry = PluginRegistry.instance;
  final PluginLoader _loader = PluginLoader.instance;
  late final PluginInstallationManager _installationManager;

  /// 安装进度控制器
  final Map<String, StreamController<PluginInstallationProgress>>
      _progressControllers =
      <String, StreamController<PluginInstallationProgress>>{};

  /// 是否已初始化
  bool _isInitialized = false;

  /// 初始化服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // 初始化Plugin System安装管理器
      _installationManager = PluginInstallationManager(registry: _registry);

      _isInitialized = true;
      _log('info', '插件安装服务初始化完成');
    } catch (e) {
      _log('error', '插件安装服务初始化失败: $e');
      rethrow;
    }
  }

  /// 安装插件
  Future<PluginInstallationResult> installPlugin({
    required String pluginId,
    String? version,
    String? source,
    Map<String, dynamic>? config,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    final stopwatch = Stopwatch()..start();

    try {
      _log('info', '开始安装插件: $pluginId');

      // 检查插件是否已安装
      if (_registry.contains(pluginId)) {
        return PluginInstallationResult.failure(
          pluginId: pluginId,
          message: '插件已安装',
          error: PluginInstallationError.alreadyInstalled,
        );
      }

      // 创建进度控制器
      final progressController =
          StreamController<PluginInstallationProgress>.broadcast();
      _progressControllers[pluginId] = progressController;

      try {
        // 执行真实安装过程
        final result = await _performRealInstallation(
          pluginId,
          version: version,
          source: source,
          config: config,
          progressController: progressController,
        );

        stopwatch.stop();

        if (result.isSuccess) {
          _log('info', '插件安装成功: $pluginId, 耗时: ${stopwatch.elapsed}');
          return PluginInstallationResult.success(
            pluginId: pluginId,
            message: '插件安装成功',
            installedVersion: result.data?['version'] as String?,
            installationPath: result.data?['location'] as String?,
            installationTime: stopwatch.elapsed,
          );
        } else {
          _log('error', '插件安装失败: $pluginId, 错误: ${result.message}');
          return PluginInstallationResult.failure(
            pluginId: pluginId,
            message: result.message,
            error: _mapErrorType(result.message ?? ''),
          );
        }
      } finally {
        // 清理进度控制器
        await progressController.close();
        _progressControllers.remove(pluginId);
      }
    } catch (e) {
      stopwatch.stop();
      _log('error', '插件安装异常: $pluginId, 错误: $e');
      return PluginInstallationResult.failure(
        pluginId: pluginId,
        message: '安装过程中发生异常: $e',
        error: PluginInstallationError.unknown,
      );
    }
  }

  /// 获取安装进度流
  Stream<PluginInstallationProgress>? getInstallationProgress(String pluginId) {
    return _progressControllers[pluginId]?.stream;
  }

  /// 日志记录
  void _log(String level, String message) {
    print('[$level] [PluginInstallationService] $message');
  }

  /// 执行真实安装过程
  Future<ApiResponse<Map<String, dynamic>>> _performRealInstallation(
    String pluginId, {
    String? version,
    String? source,
    Map<String, dynamic>? config,
    required StreamController<PluginInstallationProgress> progressController,
  }) async {
    try {
      // 阶段1: 验证和准备 (0-20%)
      _reportProgress(
          progressController, pluginId, 0.0, '验证插件信息', '正在验证插件ID和版本...');

      if (pluginId.isEmpty) {
        return ApiResponse.error(message: '插件ID不能为空', statusCode: 400);
      }

      _reportProgress(
          progressController, pluginId, 0.1, '验证插件信息', '检查插件是否已安装...');

      if (_registry.contains(pluginId)) {
        return ApiResponse.error(message: '插件已安装', statusCode: 409);
      }

      _reportProgress(progressController, pluginId, 0.2, '验证插件信息', '验证完成');

      // 阶段2: 使用Plugin System安装管理器 (20-100%)
      _reportProgress(
          progressController, pluginId, 0.2, '开始安装', '调用Plugin System安装管理器...');

      final result = await _installationManager.installPlugin(
        pluginId,
        version: version,
        source: source,
        config: config,
      );

      if (result.isSuccess) {
        _reportProgress(
            progressController, pluginId, 0.8, '注册插件', '插件安装完成，正在注册...');

        // 集成PluginLoader.instance.loadPlugin()
        await _integrateWithPluginLoader(pluginId, result.data);

        _reportProgress(progressController, pluginId, 1.0, '安装完成', '插件安装和注册成功');
      }

      return result;
    } catch (e) {
      _reportProgress(
          progressController, pluginId, 0.0, '安装失败', '安装过程中发生错误: $e');
      return ApiResponse.error(message: '安装失败: $e', statusCode: 500);
    }
  }

  /// 集成PluginLoader加载插件
  Future<void> _integrateWithPluginLoader(
    String pluginId,
    Map<String, dynamic>? installationData,
  ) async {
    try {
      _log('info', '开始集成PluginLoader: $pluginId');

      // 获取已注册的插件实例
      final plugin = _registry.get(pluginId);
      if (plugin == null) {
        throw Exception('插件注册失败，无法找到插件实例: $pluginId');
      }

      // 使用PluginLoader加载插件
      await _loader.loadPlugin(plugin);

      _log('info', 'PluginLoader集成完成: $pluginId');
    } catch (e) {
      _log('error', 'PluginLoader集成失败: $pluginId, 错误: $e');
      rethrow;
    }
  }

  /// 报告安装进度
  void _reportProgress(
    StreamController<PluginInstallationProgress> controller,
    String pluginId,
    double progress,
    String stage,
    String? message,
  ) {
    if (!controller.isClosed) {
      controller.add(PluginInstallationProgress(
        pluginId: pluginId,
        progress: progress,
        stage: stage,
        message: message,
      ));
    }
  }

  /// 卸载插件
  Future<PluginInstallationResult> uninstallPlugin(String pluginId) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      _log('info', '开始卸载插件: $pluginId');

      // 检查插件是否存在
      if (!_registry.contains(pluginId)) {
        return PluginInstallationResult.failure(
          pluginId: pluginId,
          message: '插件未安装',
          error: PluginInstallationError.pluginNotFound,
        );
      }

      // 使用PluginLoader卸载插件
      await _loader.unloadPlugin(pluginId);

      // 使用安装管理器卸载
      final result = await _installationManager.uninstallPlugin(pluginId);

      if (result.isSuccess) {
        _log('info', '插件卸载成功: $pluginId');
        return PluginInstallationResult.success(
          pluginId: pluginId,
          message: '插件卸载成功',
        );
      } else {
        _log('error', '插件卸载失败: $pluginId, 错误: ${result.message}');
        return PluginInstallationResult.failure(
          pluginId: pluginId,
          message: result.message,
          error: _mapErrorType(result.message ?? ''),
        );
      }
    } catch (e) {
      _log('error', '插件卸载异常: $pluginId, 错误: $e');
      return PluginInstallationResult.failure(
        pluginId: pluginId,
        message: '卸载过程中发生异常: $e',
        error: PluginInstallationError.unknown,
      );
    }
  }

  /// 映射错误类型
  PluginInstallationError _mapErrorType(String errorMessage) {
    if (errorMessage.contains('已安装')) {
      return PluginInstallationError.alreadyInstalled;
    } else if (errorMessage.contains('不存在') ||
        errorMessage.contains('not found')) {
      return PluginInstallationError.pluginNotFound;
    } else if (errorMessage.contains('依赖') ||
        errorMessage.contains('dependency')) {
      return PluginInstallationError.dependencyConflict;
    } else if (errorMessage.contains('权限') ||
        errorMessage.contains('permission')) {
      return PluginInstallationError.insufficientPermissions;
    } else if (errorMessage.contains('网络') ||
        errorMessage.contains('network')) {
      return PluginInstallationError.networkError;
    } else if (errorMessage.contains('存储') ||
        errorMessage.contains('storage')) {
      return PluginInstallationError.insufficientStorage;
    } else if (errorMessage.contains('损坏') ||
        errorMessage.contains('corrupt')) {
      return PluginInstallationError.corruptedPackage;
    } else if (errorMessage.contains('版本') ||
        errorMessage.contains('version')) {
      return PluginInstallationError.incompatibleVersion;
    } else if (errorMessage.contains('超时') ||
        errorMessage.contains('timeout')) {
      return PluginInstallationError.installationTimeout;
    } else {
      return PluginInstallationError.unknown;
    }
  }
}
