import 'dart:io';
import 'package:test/test.dart';
import 'package:path/path.dart' as path;
import 'package:creative_workshop/creative_workshop.dart';
import 'package:plugin_system/plugin_system.dart' as plugin_sys;

void main() {
  group('真实Plugin System集成测试', () {
    late String testPluginPath;
    late WorkshopManager workshopManager;

    setUpAll(() async {
      // 设置测试插件路径
      final currentDir = Directory.current.path;
      testPluginPath = path.join(currentDir, '..', '..', 'plugins', 'testing');

      // 获取WorkshopManager实例
      workshopManager = WorkshopManager.instance;
    });

    group('插件注册到Plugin System验证', () {
      test('应该能够将插件注册到Plugin System', () async {
        // 验证Plugin System注册表可访问
        final pluginSystemRegistry = plugin_sys.PluginRegistry.instance;
        expect(pluginSystemRegistry, isNotNull,
            reason: 'Plugin System注册表应该可访问');

        // 验证注册表基本功能可用
        expect(() => pluginSystemRegistry.contains('test'), returnsNormally,
            reason: '注册表应该可以查询');
      });

      test('应该能够检测插件是否已注册', () async {
        final pluginSystemRegistry = plugin_sys.PluginRegistry.instance;

        // 测试一个不存在的插件ID
        const testPluginId = 'non_existent_plugin_test_12345';
        expect(pluginSystemRegistry.contains(testPluginId), isFalse,
            reason: '不存在的插件应该返回false');
      });
    });

    group('安全检查功能验证', () {
      test('应该能够执行文件完整性检查', () async {
        // 验证测试插件目录存在
        final pluginDir = Directory(testPluginPath);
        expect(pluginDir.existsSync(), isTrue, reason: '测试插件目录应该存在');

        // 验证必需文件存在
        final requiredFiles = [
          'pubspec.yaml',
          'plugin.yaml',
          'lib/test_plugin_fixed.dart',
        ];

        for (final filePath in requiredFiles) {
          final file = File(path.join(testPluginPath, filePath));
          expect(file.existsSync(), isTrue, reason: '必需文件应该存在: $filePath');
        }
      });

      test('应该能够验证插件清单格式', () async {
        final manifestFile = File(path.join(testPluginPath, 'plugin.yaml'));
        expect(manifestFile.existsSync(), isTrue, reason: '插件清单应该存在');

        final content = manifestFile.readAsStringSync();

        // 验证基本格式
        expect(content.contains('plugin:'), isTrue, reason: '应该包含plugin节点');
        expect(content.contains('id:'), isTrue, reason: '应该包含id字段');
        expect(content.contains('name:'), isTrue, reason: '应该包含name字段');
        expect(content.contains('version:'), isTrue, reason: '应该包含version字段');
      });

      test('应该能够扫描Dart文件', () async {
        final libDir = Directory(path.join(testPluginPath, 'lib'));
        expect(libDir.existsSync(), isTrue, reason: 'lib目录应该存在');

        // 查找Dart文件
        final dartFiles = await libDir
            .list(recursive: true)
            .where((entity) => entity is File && entity.path.endsWith('.dart'))
            .cast<File>()
            .toList();

        expect(dartFiles.isNotEmpty, isTrue, reason: '应该包含Dart文件');

        // 验证可以读取文件内容
        for (final file in dartFiles) {
          final content = await file.readAsString();
          expect(content.isNotEmpty, isTrue,
              reason: 'Dart文件应该有内容: ${path.basename(file.path)}');
        }
      });
    });

    group('依赖检查功能验证', () {
      test('应该能够访问Plugin System注册表', () async {
        final pluginSystemRegistry = plugin_sys.PluginRegistry.instance;

        // 验证注册表方法可用
        expect(() => pluginSystemRegistry.contains('test'), returnsNormally);
        expect(() => pluginSystemRegistry.getMetadata('test'), returnsNormally);
        expect(() => pluginSystemRegistry.getState('test'), returnsNormally);
      });

      test('应该能够解析版本号', () {
        // 测试版本号解析逻辑
        final testVersions = [
          '1.0.0',
          '2.1.3',
          '0.9.15',
          '10.20.30',
        ];

        for (final version in testVersions) {
          final parts = version.split('.').map(int.parse).toList();
          expect(parts.length, equals(3), reason: '版本号应该有3个部分: $version');
          expect(parts.every((part) => part >= 0), isTrue,
              reason: '版本号部分应该非负: $version');
        }
      });
    });

    group('插件实例化验证', () {
      test('应该能够读取插件代码', () async {
        final mainFile =
            File(path.join(testPluginPath, 'lib', 'test_plugin_fixed.dart'));
        expect(mainFile.existsSync(), isTrue, reason: '插件主文件应该存在');

        final content = await mainFile.readAsString();
        expect(content.isNotEmpty, isTrue, reason: '插件代码不应为空');

        // 验证Plugin System集成
        expect(content.contains('package:plugin_system/plugin_system.dart'),
            isTrue,
            reason: '应该导入Plugin System');
        expect(content.contains('plugin_sys.Plugin'), isTrue,
            reason: '应该使用Plugin System基类');
      });

      test('应该能够解析插件清单', () async {
        final manifestFile = File(path.join(testPluginPath, 'plugin.yaml'));
        final content = manifestFile.readAsStringSync();

        // 验证清单包含必需信息
        expect(content.contains('test_plugin_fixed'), isTrue,
            reason: '应该包含插件ID');
        expect(content.contains('Test Plugin Fixed'), isTrue,
            reason: '应该包含插件名称');
        expect(content.contains('1.0.0'), isTrue, reason: '应该包含版本号');
      });
    });

    group('WorkshopManager集成验证', () {
      test('应该能够初始化WorkshopManager', () async {
        // 验证WorkshopManager可以初始化
        expect(() => WorkshopManager.instance, returnsNormally);
        expect(workshopManager, isNotNull);
      });

      test('应该能够扫描插件目录', () async {
        // 验证插件目录扫描功能
        final pluginsDir =
            Directory(path.join(Directory.current.path, '..', '..', 'plugins'));

        if (pluginsDir.existsSync()) {
          // 如果插件目录存在，验证可以列出内容
          final entities = await pluginsDir.list().toList();
          expect(entities, isA<List>(), reason: '应该能够列出插件目录内容');

          // 查找测试插件
          Directory? testPluginDir;
          for (final entity in entities.whereType<Directory>()) {
            if (path.basename(entity.path) == 'testing') {
              testPluginDir = entity;
              break;
            }
          }

          if (testPluginDir != null) {
            expect(testPluginDir.existsSync(), isTrue, reason: '测试插件目录应该存在');
          }
        }
      });
    });

    group('端到端集成验证', () {
      test('验证完整的插件发现流程', () async {
        // 1. 验证插件目录存在
        final pluginDir = Directory(testPluginPath);
        expect(pluginDir.existsSync(), isTrue, reason: '插件目录应该存在');

        // 2. 验证必需文件存在
        final manifestFile = File(path.join(testPluginPath, 'plugin.yaml'));
        final pubspecFile = File(path.join(testPluginPath, 'pubspec.yaml'));
        final mainFile =
            File(path.join(testPluginPath, 'lib', 'test_plugin_fixed.dart'));

        expect(manifestFile.existsSync(), isTrue, reason: '插件清单应该存在');
        expect(pubspecFile.existsSync(), isTrue, reason: 'pubspec.yaml应该存在');
        expect(mainFile.existsSync(), isTrue, reason: '插件主文件应该存在');

        // 3. 验证Plugin System依赖
        final pubspecContent = await pubspecFile.readAsString();
        expect(pubspecContent.contains('plugin_system:'), isTrue,
            reason: '应该包含Plugin System依赖');

        // 4. 验证插件代码结构
        final mainContent = await mainFile.readAsString();
        expect(
            mainContent.contains(
                'import \'package:plugin_system/plugin_system.dart\' as plugin_sys;'),
            isTrue,
            reason: '应该正确导入Plugin System');
        expect(mainContent.contains('extends plugin_sys.Plugin'), isTrue,
            reason: '应该继承Plugin System基类');

        // 5. 验证清单格式
        final manifestContent = await manifestFile.readAsString();
        expect(manifestContent.contains('compatibility:'), isTrue,
            reason: '应该包含兼容性配置');
        expect(manifestContent.contains('min_pet_app_version: "3.0.0"'), isTrue,
            reason: '应该包含Pet App V3兼容性');
      });

      test('验证Plugin System可用性', () async {
        // 验证Plugin System的核心组件可用
        expect(() => plugin_sys.PluginRegistry.instance, returnsNormally);
        expect(() => plugin_sys.PluginLoader.instance, returnsNormally);

        // 验证Plugin System枚举可用
        expect(plugin_sys.PluginState.values.isNotEmpty, isTrue);
        expect(plugin_sys.PluginType.values.isNotEmpty, isTrue);

        // 验证Plugin System异常类可用
        expect(
            () => plugin_sys.PluginNotFoundException('test'), returnsNormally);
        expect(() => plugin_sys.PluginAlreadyExistsException('test'),
            returnsNormally);
      });

      test('验证Creative Workshop与Plugin System的接口兼容性', () async {
        // 验证Creative Workshop可以与Plugin System交互
        final pluginSystemRegistry = plugin_sys.PluginRegistry.instance;

        // 验证基本操作不会抛出异常
        expect(() => pluginSystemRegistry.contains('test_plugin'),
            returnsNormally);
        expect(() => pluginSystemRegistry.getMetadata('test_plugin'),
            returnsNormally);
        expect(() => pluginSystemRegistry.getState('test_plugin'),
            returnsNormally);

        // 验证WorkshopManager可以访问Plugin System
        expect(workshopManager, isNotNull);
        expect(() => WorkshopManager.instance, returnsNormally);
      });
    });
  });
}
