/*
---------------------------------------------------------------
File name:          core_services_v1_repository.dart
Author:             Ming Status CLI
Date created:       2025-07-14
Last modified:      2025-07-14
Dart Version:       3.2+
Description:        core_services_v1数据仓库Repository
---------------------------------------------------------------
Change History:
    2025-07-14: Initial creation - core_services_v1数据仓库Repository;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

import '../models/core_services_v1_model.dart';
import '../utils/database_helper.dart';
import '../utils/logger.dart';

/// core_services_v1数据访问层Repository
///
/// ## 使用示例
///
/// ```dart
/// final repository = Core_services_v1Repository();
/// ```
///
/// ```dart
/// await repository.initialize();
/// ```
///
/// ```dart
/// final data = await repository.findAll();
/// ```
///
/// ```dart
/// final item = await repository.findById(1);
/// ```
///
/// ## 相关类
///
/// * [Service]
/// * [Model]
///
class Core_services_v1Repository {
  /// 是否已初始化
  bool _isInitialized = false;

  /// 内存缓存
  final Map<int, Map<String, dynamic>> _cache = {};

  /// 缓存过期时间（毫秒）
  static const int _cacheExpirationMs = 300000; // 5分钟

  /// 数据库实例
  Database? _database;

  /// 表名
  static const String _tableName = 'core_services_v1s';

  /// 获取初始化状态
  bool get isInitialized => _isInitialized;

  /// 创建Core_services_v1Repository实例
  Core_services_v1Repository();

  /// 初始化Repository
  Future<void> initialize() async {
    if (_isInitialized) return;

    // 初始化数据库
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, 'core_services_v1.db');

    _database = await openDatabase(
      path,
      version: 1,
      onCreate: _createTables,
      onUpgrade: _upgradeTables,
    );

    _isInitialized = true;
  }

  /// 创建数据库表
  Future<void> _createTables(Database db, int version) async {
    await db.execute('''
      CREATE TABLE $_tableName (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        status TEXT DEFAULT 'active',
        metadata TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');
  }

  /// 升级数据库表
  Future<void> _upgradeTables(Database db, int oldVersion, int newVersion) async {
    // TODO: 实现数据库升级逻辑
  }

  /// 创建新记录
  Future<Map<String, dynamic>> create(Map<String, dynamic> data) async {
    _ensureInitialized();

    final now = DateTime.now().toIso8601String();
    final insertData = {
      ...data,
      'created_at': now,
      'updated_at': now,
    };

    final id = await _database!.insert(_tableName, insertData);
    final result = {'id': id, ...insertData};

    // 更新缓存
    _cache[id] = result;

    return result;
  }

  /// 根据ID查找记录
  Future<Map<String, dynamic>?> findById(int id) async {
    _ensureInitialized();

    // 检查缓存
    if (_cache.containsKey(id)) {
      return _cache[id];
    }

    final results = await _database!.query(
      _tableName,
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );

    if (results.isNotEmpty) {
      final result = results.first;
      _cache[id] = result;
      return result;
    }

    return null;
  }

  /// 更新记录
  Future<Map<String, dynamic>?> update(int id, Map<String, dynamic> data) async {
    _ensureInitialized();

    final updateData = {
      ...data,
      'updated_at': DateTime.now().toIso8601String(),
    };

    final count = await _database!.update(
      _tableName,
      updateData,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (count > 0) {
      final updated = await findById(id);
      if (updated != null) {
        _cache[id] = updated;
      }
      return updated;
    }

    return null;
  }

  /// 删除记录
  Future<bool> delete(int id) async {
    _ensureInitialized();

    final count = await _database!.delete(
      _tableName,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (count > 0) {
      _cache.remove(id);
      return true;
    }

    return false;
  }

  /// 查找所有记录
  Future<List<Map<String, dynamic>>> findAll({
    int? limit,
    int? offset,
    String? orderBy,
  }) async {
    _ensureInitialized();

    final results = await _database!.query(
      _tableName,
      orderBy: orderBy ?? 'created_at DESC',
      limit: limit,
      offset: offset,
    );

    // 更新缓存
    for (final result in results) {
      final id = result['id'] as int;
      _cache[id] = result;
    }

    return results;
  }

  /// 获取记录总数
  Future<int> count() async {
    _ensureInitialized();

    final result = await _database!.rawQuery(
      'SELECT COUNT(*) as count FROM $_tableName',
    );
    return result.first['count'] as int;
  }

  /// 确保Repository已初始化
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('Repository not initialized. Call initialize() first.');
    }
  }

  /// 清除缓存
  void clearCache() {
    _cache.clear();
  }

  /// 清理资源
  Future<void> dispose() async {
    await _database?.close();
    _database = null;
    _cache.clear();
    _isInitialized = false;
  }

}

/// core_services_v1仓库异常
class Core_services_v1RepositoryException implements Exception {
  /// 错误消息
  final String message;

  /// 创建异常实例
  const Core_services_v1RepositoryException(this.message);

  @override
  String toString() => 'Core_services_v1RepositoryException: $message';
}
