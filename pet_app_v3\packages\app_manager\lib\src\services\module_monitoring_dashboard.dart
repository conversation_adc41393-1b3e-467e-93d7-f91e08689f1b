/*
---------------------------------------------------------------
File name:          module_monitoring_dashboard.dart
Author:             lgnorant-lu
Date created:       2025-07-24
Last modified:      2025-07-24
Dart Version:       3.2+
Description:        模块监控仪表板
---------------------------------------------------------------
Change History:
    2025-07-24: Phase ******* - 实现模块监控仪表板;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:developer' as developer;
import '../models/monitoring_dashboard.dart';
import '../models/module_state.dart';
import '../models/module_resource.dart';

import 'module_state_controller.dart';
import 'module_resource_manager.dart';
import 'module_permission_manager.dart';

/// 模块监控仪表板
///
/// 提供模块运行状态、资源使用、权限管理的统一监控视图
class ModuleMonitoringDashboard {
  static ModuleMonitoringDashboard? _instance;
  static ModuleMonitoringDashboard get instance =>
      _instance ??= ModuleMonitoringDashboard._();

  ModuleMonitoringDashboard._();

  final StreamController<DashboardEvent> _eventController =
      StreamController.broadcast();
  Timer? _updateTimer;
  bool _isMonitoring = false;

  /// 仪表板事件流
  Stream<DashboardEvent> get dashboardEvents => _eventController.stream;

  /// 开始监控
  Future<void> startMonitoring(
      {Duration interval = const Duration(seconds: 5)}) async {
    if (_isMonitoring) return;

    _log('info', '启动模块监控仪表板');

    _isMonitoring = true;
    _updateTimer = Timer.periodic(interval, (_) => _updateDashboard());

    // 立即更新一次
    await _updateDashboard();

    _eventController.add(DashboardEvent.monitoringStarted());
    _log('info', '模块监控仪表板已启动');
  }

  /// 停止监控
  void stopMonitoring() {
    if (!_isMonitoring) return;

    _log('info', '停止模块监控仪表板');

    _isMonitoring = false;
    _updateTimer?.cancel();
    _updateTimer = null;

    _eventController.add(DashboardEvent.monitoringStopped());
    _log('info', '模块监控仪表板已停止');
  }

  /// 获取仪表板概览
  Future<DashboardOverview> getDashboardOverview() async {
    try {
      // 获取模块状态信息
      final stateController = ModuleStateController.instance;
      final moduleStates = stateController.moduleStates;

      final runningModules = moduleStates.values
          .where((state) => state.status == ModuleStatus.running)
          .length;
      final stoppedModules = moduleStates.values
          .where((state) => state.status == ModuleStatus.stopped)
          .length;
      final errorModules = moduleStates.values
          .where((state) => state.status == ModuleStatus.error)
          .length;

      // 获取资源使用信息
      final resourceManager = ModuleResourceManager.instance;
      final systemOverview = resourceManager.getSystemResourceOverview();

      // 获取权限统计信息
      final permissionManager = ModulePermissionManager.instance;
      final permissionStats = permissionManager.getPermissionStatistics();

      // 计算健康度分数
      final healthScore = _calculateHealthScore(
        moduleStates.values.toList(),
        systemOverview,
      );

      return DashboardOverview(
        totalModules: moduleStates.length,
        runningModules: runningModules,
        stoppedModules: stoppedModules,
        errorModules: errorModules,
        systemResourceOverview: systemOverview,
        permissionStatistics: permissionStats,
        healthScore: healthScore,
        lastUpdated: DateTime.now(),
      );
    } catch (e, stackTrace) {
      _log('severe', '获取仪表板概览失败', e, stackTrace);
      return DashboardOverview.empty();
    }
  }

  /// 获取模块详细信息
  Future<ModuleDetailInfo?> getModuleDetailInfo(String moduleId) async {
    try {
      final stateController = ModuleStateController.instance;
      final resourceManager = ModuleResourceManager.instance;
      final permissionManager = ModulePermissionManager.instance;

      // 获取模块状态
      final moduleState = stateController.getModuleState(moduleId);
      if (moduleState == null) return null;

      // 获取资源使用情况
      final resourceUsage = resourceManager.getResourceUsage(moduleId);

      // 获取模块权限
      final permissions = permissionManager.getModulePermissions(moduleId);

      // 获取最近的权限审计日志
      final auditLogs = permissionManager.getAuditLogs(
        moduleId: moduleId,
        startTime: DateTime.now().subtract(const Duration(days: 7)),
      );

      return ModuleDetailInfo(
        moduleId: moduleId,
        state: moduleState,
        resourceUsage: resourceUsage,
        permissions: permissions,
        recentAuditLogs: auditLogs,
        lastUpdated: DateTime.now(),
      );
    } catch (e, stackTrace) {
      _log('severe', '获取模块详细信息失败: $moduleId', e, stackTrace);
      return null;
    }
  }

  /// 获取系统警报
  List<SystemAlert> getSystemAlerts() {
    final alerts = <SystemAlert>[];

    try {
      final stateController = ModuleStateController.instance;
      final resourceManager = ModuleResourceManager.instance;

      // 检查错误模块
      final errorModules = stateController.moduleStates.values
          .where((state) => state.status == ModuleStatus.error)
          .toList();

      for (final state in errorModules) {
        alerts.add(SystemAlert(
          id: 'module_error_${state.moduleId}',
          type: AlertType.error,
          title: '模块运行错误',
          message: '模块 ${state.moduleId} 处于错误状态',
          moduleId: state.moduleId,
          timestamp: state.lastUpdated,
          severity: AlertSeverity.high,
        ));
      }

      // 检查资源使用警报
      final allResourceUsage = resourceManager.getAllResourceUsage();
      for (final entry in allResourceUsage.entries) {
        final moduleId = entry.key;
        final usage = entry.value;

        // CPU 使用率过高
        if (usage.cpuUsage > 0.8) {
          alerts.add(SystemAlert(
            id: 'high_cpu_${moduleId}',
            type: AlertType.warning,
            title: 'CPU 使用率过高',
            message: '模块 $moduleId CPU 使用率达到 ${usage.formattedCpuUsage}',
            moduleId: moduleId,
            timestamp: usage.lastUpdated,
            severity: AlertSeverity.medium,
          ));
        }

        // 内存使用过高
        if (usage.memoryUsage > 500 * 1024 * 1024) {
          // 500MB
          alerts.add(SystemAlert(
            id: 'high_memory_${moduleId}',
            type: AlertType.warning,
            title: '内存使用过高',
            message: '模块 $moduleId 内存使用量达到 ${usage.formattedMemoryUsage}',
            moduleId: moduleId,
            timestamp: usage.lastUpdated,
            severity: AlertSeverity.medium,
          ));
        }
      }

      // 按严重程度和时间排序
      alerts.sort((a, b) {
        final severityCompare = b.severity.index.compareTo(a.severity.index);
        if (severityCompare != 0) return severityCompare;
        return b.timestamp.compareTo(a.timestamp);
      });
    } catch (e, stackTrace) {
      _log('severe', '获取系统警报失败', e, stackTrace);
    }

    return alerts;
  }

  /// 获取性能趋势数据
  Future<PerformanceTrend> getPerformanceTrend(
    String moduleId, {
    Duration period = const Duration(hours: 1),
  }) async {
    try {
      // 模拟性能趋势数据
      final now = DateTime.now();
      final dataPoints = <PerformanceDataPoint>[];

      // 生成过去一小时的数据点（每分钟一个）
      for (int i = 60; i >= 0; i--) {
        final timestamp = now.subtract(Duration(minutes: i));

        // 模拟数据
        final cpuUsage = 0.1 + (i % 10) * 0.05; // 10-60%
        final memoryUsage =
            50 * 1024 * 1024 + (i % 20) * 5 * 1024 * 1024; // 50-150MB

        dataPoints.add(PerformanceDataPoint(
          timestamp: timestamp,
          cpuUsage: cpuUsage,
          memoryUsage: memoryUsage,
          networkSent: i * 1024,
          networkReceived: i * 2 * 1024,
        ));
      }

      return PerformanceTrend(
        moduleId: moduleId,
        period: period,
        dataPoints: dataPoints,
        generatedAt: now,
      );
    } catch (e, stackTrace) {
      _log('severe', '获取性能趋势失败: $moduleId', e, stackTrace);
      return PerformanceTrend.empty(moduleId, period);
    }
  }

  /// 执行系统健康检查
  Future<HealthCheckResult> performHealthCheck() async {
    try {
      _log('info', '执行系统健康检查');

      final checks = <HealthCheckItem>[];

      // 检查模块状态
      final stateController = ModuleStateController.instance;
      final moduleStates = stateController.moduleStates;
      final errorCount = moduleStates.values
          .where((state) => state.status == ModuleStatus.error)
          .length;

      checks.add(HealthCheckItem(
        name: '模块状态检查',
        status: errorCount == 0 ? HealthStatus.healthy : HealthStatus.warning,
        message: errorCount == 0 ? '所有模块运行正常' : '$errorCount 个模块处于错误状态',
        details: {
          'errorCount': errorCount,
          'totalModules': moduleStates.length
        },
      ));

      // 检查系统资源
      final resourceManager = ModuleResourceManager.instance;
      final systemOverview = resourceManager.getSystemResourceOverview();

      checks.add(HealthCheckItem(
        name: '系统资源检查',
        status: systemOverview.totalCpuUsage < 0.8
            ? HealthStatus.healthy
            : HealthStatus.warning,
        message:
            'CPU 使用率: ${(systemOverview.totalCpuUsage * 100).toStringAsFixed(1)}%',
        details: {
          'cpuUsage': systemOverview.totalCpuUsage,
          'memoryUsage': systemOverview.totalMemoryUsage,
        },
      ));

      // 检查权限系统
      final permissionManager = ModulePermissionManager.instance;
      final permissionStats = permissionManager.getPermissionStatistics();

      checks.add(HealthCheckItem(
        name: '权限系统检查',
        status: HealthStatus.healthy,
        message: '权限系统运行正常',
        details: {
          'totalPermissions': permissionStats.totalPermissions,
          'recentAuditLogs': permissionStats.recentAuditLogs,
        },
      ));

      // 计算总体健康状态
      final overallStatus =
          checks.any((check) => check.status == HealthStatus.critical)
              ? HealthStatus.critical
              : checks.any((check) => check.status == HealthStatus.warning)
                  ? HealthStatus.warning
                  : HealthStatus.healthy;

      final result = HealthCheckResult(
        overallStatus: overallStatus,
        checks: checks,
        timestamp: DateTime.now(),
      );

      _eventController.add(DashboardEvent.healthCheckCompleted(result));
      _log('info', '系统健康检查完成: $overallStatus');

      return result;
    } catch (e, stackTrace) {
      _log('severe', '系统健康检查失败', e, stackTrace);
      return HealthCheckResult.failed();
    }
  }

  /// 更新仪表板
  Future<void> _updateDashboard() async {
    try {
      final overview = await getDashboardOverview();
      _eventController.add(DashboardEvent.overviewUpdated(overview));

      // 检查是否需要发送警报
      final alerts = getSystemAlerts();
      if (alerts.isNotEmpty) {
        _eventController.add(DashboardEvent.alertsGenerated(alerts));
      }
    } catch (e, stackTrace) {
      _log('severe', '更新仪表板失败', e, stackTrace);
    }
  }

  /// 计算健康度分数
  double _calculateHealthScore(
    List<ModuleState> moduleStates,
    SystemResourceOverview systemOverview,
  ) {
    double score = 100.0;

    // 根据错误模块数量扣分
    final errorCount = moduleStates
        .where((state) => state.status == ModuleStatus.error)
        .length;
    score -= errorCount * 20.0;

    // 根据 CPU 使用率扣分
    if (systemOverview.totalCpuUsage > 0.8) {
      score -= (systemOverview.totalCpuUsage - 0.8) * 100;
    }

    return score.clamp(0.0, 100.0);
  }

  /// 日志记录
  void _log(String level, String message,
      [Object? error, StackTrace? stackTrace]) {
    developer.log(
      message,
      name: 'ModuleMonitoringDashboard',
      level: _getLogLevel(level),
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// 获取日志级别
  int _getLogLevel(String level) {
    switch (level) {
      case 'info':
        return 800;
      case 'warning':
        return 900;
      case 'severe':
        return 1000;
      default:
        return 700;
    }
  }

  /// 清理资源
  void dispose() {
    stopMonitoring();
    _eventController.close();
  }
}
