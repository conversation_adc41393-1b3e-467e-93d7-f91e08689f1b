# UserModel API 文档

## 概述

UserModel是桌宠应用的用户数据模型核心，提供完整的用户身份、权限、偏好设置和认证令牌管理功能。它是整个应用用户系统的数据基础，支持认证、授权、个性化配置等核心功能。

- **主要功能**: 用户身份管理、JWT令牌管理、权限控制、偏好设置
- **设计模式**: Model模式 + 值对象模式
- **核心特性**: 认证管理、角色权限、用户偏好、安全存储
- **位置**: `lib/core/models/user_model.dart`

## 核心枚举定义

### UserStatus 枚举
```dart
enum UserStatus {
  inactive,    // 未激活状态
  active,      // 活跃状态
  locked,      // 已锁定
  disabled,    // 已禁用
  deleted,     // 已删除
}
```

### UserRole 枚举
```dart
enum UserRole {
  guest,       // 游客
  user,        // 普通用户
  premium,     // 高级用户
  admin,       // 管理员
  superAdmin,  // 超级管理员
}
```

## 核心类定义

### TokenInfo 类
```dart
class TokenInfo {
  final String accessToken;           // 访问令牌
  final String refreshToken;          // 刷新令牌
  final DateTime expiresAt;          // 访问令牌过期时间
  final DateTime refreshExpiresAt;   // 刷新令牌过期时间
  final String tokenType;            // 令牌类型（默认'Bearer'）
  final List<String> scopes;         // 权限范围
  
  // 令牌状态检查
  bool get isAccessTokenExpired;     // 访问令牌是否过期
  bool get isRefreshTokenExpired;    // 刷新令牌是否过期
  bool get isValid;                  // 令牌是否有效
  
  // 剩余时间
  int get accessTokenRemainingSeconds;  // 访问令牌剩余秒数
  int get refreshTokenRemainingSeconds; // 刷新令牌剩余秒数
  
  // 权限范围检查
  bool hasScope(String scope);
  bool hasAnyScope(List<String> requiredScopes);
  bool hasAllScopes(List<String> requiredScopes);
}
```

### UserPreferences 类
```dart
class UserPreferences {
  final String theme;                        // 主题设置（默认'system'）
  final String language;                     // 语言设置（默认'zh-CN'）
  final bool notificationsEnabled;          // 是否启用通知
  final bool soundEnabled;                   // 是否启用声音
  final Map<String, dynamic> moduleSettings; // 模块设置
  final Map<String, dynamic> customSettings; // 自定义设置
}
```

### User 主用户类
```dart
class User {
  final String id;                    // 用户ID
  final String username;              // 用户名
  final String email;                 // 邮箱地址
  final String? displayName;          // 显示名称
  final String? avatar;               // 头像URL
  final String? phoneNumber;          // 电话号码
  final UserStatus status;            // 用户状态
  final UserRole role;                // 用户角色
  final DateTime createdAt;           // 创建时间
  final DateTime updatedAt;           // 更新时间
  final DateTime? lastLoginAt;        // 最后登录时间
  final TokenInfo? tokenInfo;         // 令牌信息
  final UserPreferences preferences;  // 用户偏好
  final Map<String, dynamic> metadata; // 元数据
}
```

## 认证相关属性

### 认证状态
```dart
// 检查用户是否已认证
bool get isAuthenticated => tokenInfo != null && tokenInfo!.isValid;

// 检查用户是否活跃
bool get isActive => status == UserStatus.active;

// 检查用户是否可用
bool get isAvailable => isActive && isAuthenticated;

// 获取访问令牌
String? get accessToken => tokenInfo?.accessToken;

// 获取刷新令牌
String? get refreshToken => tokenInfo?.refreshToken;

// 获取授权头部
String? get authorizationHeader {
  if (tokenInfo == null) return null;
  return '${tokenInfo!.tokenType} ${tokenInfo!.accessToken}';
}
```

## 角色权限检查

### 权限验证方法
```dart
// 检查是否为管理员
bool get isAdmin => role == UserRole.admin || role == UserRole.superAdmin;

// 检查是否为超级管理员
bool get isSuperAdmin => role == UserRole.superAdmin;

// 检查是否为高级用户
bool get isPremium => role == UserRole.premium || isAdmin;

// 检查是否为游客
bool get isGuest => role == UserRole.guest;

// 检查是否具有指定角色
bool hasRole(UserRole requiredRole) => role == requiredRole || isAdmin;

// 检查是否具有最小角色等级
bool hasMinimumRole(UserRole minimumRole) {
  const roleHierarchy = [
    UserRole.guest,
    UserRole.user,
    UserRole.premium,
    UserRole.admin,
    UserRole.superAdmin,
  ];
  
  final currentIndex = roleHierarchy.indexOf(role);
  final requiredIndex = roleHierarchy.indexOf(minimumRole);
  
  return currentIndex >= requiredIndex;
}

// 检查是否具有指定权限范围
bool hasScope(String scope) => tokenInfo?.hasScope(scope) ?? false;

// 检查是否具有任意指定权限范围
bool hasAnyScope(List<String> scopes) => tokenInfo?.hasAnyScope(scopes) ?? false;

// 检查是否具有所有指定权限范围
bool hasAllScopes(List<String> scopes) => tokenInfo?.hasAllScopes(scopes) ?? false;
```

## 用户信息属性

### 显示信息
```dart
// 获取显示名称（优先使用displayName，否则使用username）
String get displayNameOrUsername => displayName ?? username;

// 获取用户初始字母
String get initials {
  final name = displayNameOrUsername;
  if (name.isEmpty) return '';
  
  final words = name.split(' ').where((word) => word.isNotEmpty);
  if (words.isEmpty) return '';
  
  if (words.length == 1) {
    return words.first.substring(0, 1).toUpperCase();
  } else {
    return words.take(2).map((word) => word.substring(0, 1).toUpperCase()).join();
  }
}

// 获取用户唯一标识哈希
String get userHash {
  final content = '$id:$username:$email';
  final bytes = utf8.encode(content);
  final digest = sha256.convert(bytes);
  return digest.toString().substring(0, 8);
}
```

### 时间相关属性
```dart
// 获取账户年龄（天数）
int get accountAgeInDays => DateTime.now().difference(createdAt).inDays;

// 获取最后登录时间描述
String get lastLoginDescription {
  if (lastLoginAt == null) return '从未登录';
  
  final difference = DateTime.now().difference(lastLoginAt!);
  if (difference.inDays > 0) {
    return '${difference.inDays}天前';
  } else if (difference.inHours > 0) {
    return '${difference.inHours}小时前';
  } else if (difference.inMinutes > 0) {
    return '${difference.inMinutes}分钟前';
  } else {
    return '刚刚';
  }
}

// 检查是否为新用户（注册不超过7天）
bool get isNewUser => accountAgeInDays <= 7;
```

## 操作方法

### 数据更新方法
```dart
// 创建副本
User copyWith({
  String? id,
  String? username,
  String? email,
  String? displayName,
  String? avatar,
  String? phoneNumber,
  UserStatus? status,
  UserRole? role,
  DateTime? createdAt,
  DateTime? updatedAt,
  DateTime? lastLoginAt,
  TokenInfo? tokenInfo,
  UserPreferences? preferences,
  Map<String, dynamic>? metadata,
});

// 更新令牌信息
User updateToken(TokenInfo newTokenInfo) {
  return copyWith(
    tokenInfo: newTokenInfo,
    lastLoginAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );
}

// 清除令牌信息（登出）
User clearToken() {
  return copyWith(
    tokenInfo: null,
    updatedAt: DateTime.now(),
  );
}

// 更新偏好设置
User updatePreferences(UserPreferences newPreferences) {
  return copyWith(
    preferences: newPreferences,
    updatedAt: DateTime.now(),
  );
}

// 更新用户状态
User updateStatus(UserStatus newStatus) {
  return copyWith(
    status: newStatus,
    updatedAt: DateTime.now(),
  );
}

// 更新基本信息
User updateBasicInfo({
  String? displayName,
  String? avatar,
  String? phoneNumber,
}) {
  return copyWith(
    displayName: displayName ?? this.displayName,
    avatar: avatar ?? this.avatar,
    phoneNumber: phoneNumber ?? this.phoneNumber,
    updatedAt: DateTime.now(),
  );
}
```

## 数据序列化

### toJson 方法
```dart
// 序列化为Map（安全版本，不包含敏感信息）
Map<String, dynamic> toJson({bool includeSensitive = false}) {
  final json = <String, dynamic>{
    'id': id,
    'username': username,
    'email': email,
    'displayName': displayName,
    'avatar': avatar,
    'phoneNumber': phoneNumber,
    'status': status.toString(),
    'role': role.toString(),
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
    'lastLoginAt': lastLoginAt?.toIso8601String(),
    'preferences': preferences.toJson(),
    'metadata': metadata,
  };

  if (includeSensitive && tokenInfo != null) {
    json['tokenInfo'] = tokenInfo!.toJson();
  }

  return json;
}

// 从Map反序列化
static User fromJson(Map<String, dynamic> json) {
  return User(
    id: json['id'] as String,
    username: json['username'] as String,
    email: json['email'] as String,
    displayName: json['displayName'] as String?,
    avatar: json['avatar'] as String?,
    phoneNumber: json['phoneNumber'] as String?,
    status: UserStatus.values.byName(json['status'].toString().split('.').last),
    role: UserRole.values.byName(json['role'].toString().split('.').last),
    createdAt: DateTime.parse(json['createdAt'] as String),
    updatedAt: DateTime.parse(json['updatedAt'] as String),
    lastLoginAt: json['lastLoginAt'] != null 
        ? DateTime.parse(json['lastLoginAt'] as String) 
        : null,
    tokenInfo: json['tokenInfo'] != null 
        ? TokenInfo.fromJson(json['tokenInfo'] as Map<String, dynamic>) 
        : null,
    preferences: UserPreferences.fromJson(json['preferences'] as Map<String, dynamic>? ?? {}),
    metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
  );
}
```

## 使用示例

### 用户认证
```dart
// 检查用户认证状态
if (user.isAuthenticated) {
  print('用户已登录: ${user.displayNameOrUsername}');
  print('令牌有效期: ${user.tokenInfo!.accessTokenRemainingSeconds}秒');
} else {
  print('用户未登录或令牌已过期');
}

// 权限检查
if (user.hasMinimumRole(UserRole.admin)) {
  // 执行管理员操作
} else {
  // 显示权限不足提示
}

// 范围权限检查
if (user.hasScope('read:users')) {
  // 允许读取用户信息
}
```

### 令牌管理
```dart
// 更新令牌
final newTokenInfo = TokenInfo(
  accessToken: 'new_access_token',
  refreshToken: 'new_refresh_token',
  expiresAt: DateTime.now().add(Duration(hours: 1)),
  refreshExpiresAt: DateTime.now().add(Duration(days: 7)),
);

final updatedUser = user.updateToken(newTokenInfo);

// 登出
final loggedOutUser = user.clearToken();
```

### 偏好设置
```dart
// 更新用户偏好
final newPreferences = user.preferences.copyWith(
  theme: 'dark',
  notificationsEnabled: false,
);

final updatedUser = user.updatePreferences(newPreferences);
```

## 版本历史

- **v1.0.0** (2025-06-25): Phase 1初始实现
  - 基础用户模型定义
  - JWT令牌管理
  - 角色权限系统
  - 用户偏好设置
  - 数据序列化支持
  