/*
---------------------------------------------------------------
File name:          monitoring_dashboard.dart
Author:             lgnorant-lu
Date created:       2025-07-24
Last modified:      2025-07-24
Dart Version:       3.2+
Description:        监控仪表板模型
---------------------------------------------------------------
Change History:
    2025-07-24: Phase ******* - 实现监控仪表板模型;
---------------------------------------------------------------
*/

import 'package:equatable/equatable.dart';
import 'module_state.dart';
import 'module_resource.dart';
import 'module_permission.dart';

/// 仪表板概览
class DashboardOverview extends Equatable {
  /// 模块总数
  final int totalModules;

  /// 运行中的模块数
  final int runningModules;

  /// 已停止的模块数
  final int stoppedModules;

  /// 错误状态的模块数
  final int errorModules;

  /// 系统资源概览
  final SystemResourceOverview systemResourceOverview;

  /// 权限统计信息
  final PermissionStatistics permissionStatistics;

  /// 健康度分数 (0-100)
  final double healthScore;

  /// 最后更新时间
  final DateTime lastUpdated;

  const DashboardOverview({
    required this.totalModules,
    required this.runningModules,
    required this.stoppedModules,
    required this.errorModules,
    required this.systemResourceOverview,
    required this.permissionStatistics,
    required this.healthScore,
    required this.lastUpdated,
  });

  /// 创建空概览
  factory DashboardOverview.empty() {
    return DashboardOverview(
      totalModules: 0,
      runningModules: 0,
      stoppedModules: 0,
      errorModules: 0,
      systemResourceOverview: SystemResourceOverview.empty(),
      permissionStatistics: const PermissionStatistics(
        totalModules: 0,
        totalPermissions: 0,
        permissionCounts: {},
        recentAuditLogs: 0,
      ),
      healthScore: 100.0,
      lastUpdated: DateTime.now(),
    );
  }

  /// 获取健康状态
  HealthStatus get healthStatus {
    if (healthScore >= 80) return HealthStatus.healthy;
    if (healthScore >= 60) return HealthStatus.warning;
    return HealthStatus.critical;
  }

  @override
  List<Object?> get props => [
        totalModules,
        runningModules,
        stoppedModules,
        errorModules,
        systemResourceOverview,
        permissionStatistics,
        healthScore,
        lastUpdated,
      ];

  @override
  String toString() {
    return 'DashboardOverview(totalModules: $totalModules, healthScore: $healthScore)';
  }
}

/// 模块详细信息
class ModuleDetailInfo extends Equatable {
  /// 模块ID
  final String moduleId;

  /// 模块状态
  final ModuleState state;

  /// 资源使用情况
  final ModuleResourceUsage? resourceUsage;

  /// 模块权限
  final Set<ModulePermission> permissions;

  /// 最近的审计日志
  final List<PermissionAuditLog> recentAuditLogs;

  /// 最后更新时间
  final DateTime lastUpdated;

  const ModuleDetailInfo({
    required this.moduleId,
    required this.state,
    this.resourceUsage,
    required this.permissions,
    required this.recentAuditLogs,
    required this.lastUpdated,
  });

  @override
  List<Object?> get props => [
        moduleId,
        state,
        resourceUsage,
        permissions,
        recentAuditLogs,
        lastUpdated,
      ];

  @override
  String toString() {
    return 'ModuleDetailInfo(moduleId: $moduleId, status: ${state.status})';
  }
}

/// 系统警报
class SystemAlert extends Equatable {
  /// 警报ID
  final String id;

  /// 警报类型
  final AlertType type;

  /// 警报标题
  final String title;

  /// 警报消息
  final String message;

  /// 相关模块ID
  final String? moduleId;

  /// 警报时间
  final DateTime timestamp;

  /// 严重程度
  final AlertSeverity severity;

  const SystemAlert({
    required this.id,
    required this.type,
    required this.title,
    required this.message,
    this.moduleId,
    required this.timestamp,
    required this.severity,
  });

  @override
  List<Object?> get props =>
      [id, type, title, message, moduleId, timestamp, severity];

  @override
  String toString() {
    return 'SystemAlert(id: $id, type: $type, severity: $severity)';
  }
}

/// 警报类型
enum AlertType {
  /// 信息
  info,

  /// 警告
  warning,

  /// 错误
  error,

  /// 严重错误
  critical,
}

/// 警报严重程度
enum AlertSeverity {
  /// 低
  low,

  /// 中等
  medium,

  /// 高
  high,

  /// 严重
  critical,
}

/// 性能趋势
class PerformanceTrend extends Equatable {
  /// 模块ID
  final String moduleId;

  /// 时间段
  final Duration period;

  /// 数据点列表
  final List<PerformanceDataPoint> dataPoints;

  /// 生成时间
  final DateTime generatedAt;

  const PerformanceTrend({
    required this.moduleId,
    required this.period,
    required this.dataPoints,
    required this.generatedAt,
  });

  /// 创建空趋势
  factory PerformanceTrend.empty(String moduleId, Duration period) {
    return PerformanceTrend(
      moduleId: moduleId,
      period: period,
      dataPoints: [],
      generatedAt: DateTime.now(),
    );
  }

  /// 获取平均 CPU 使用率
  double get averageCpuUsage {
    if (dataPoints.isEmpty) return 0.0;
    return dataPoints.fold<double>(0.0, (sum, point) => sum + point.cpuUsage) /
        dataPoints.length;
  }

  /// 获取平均内存使用量
  double get averageMemoryUsage {
    if (dataPoints.isEmpty) return 0.0;
    return dataPoints.fold<double>(
            0.0, (sum, point) => sum + point.memoryUsage) /
        dataPoints.length;
  }

  @override
  List<Object?> get props => [moduleId, period, dataPoints, generatedAt];

  @override
  String toString() {
    return 'PerformanceTrend(moduleId: $moduleId, dataPoints: ${dataPoints.length})';
  }
}

/// 性能数据点
class PerformanceDataPoint extends Equatable {
  /// 时间戳
  final DateTime timestamp;

  /// CPU 使用率
  final double cpuUsage;

  /// 内存使用量
  final int memoryUsage;

  /// 网络发送量
  final int networkSent;

  /// 网络接收量
  final int networkReceived;

  const PerformanceDataPoint({
    required this.timestamp,
    required this.cpuUsage,
    required this.memoryUsage,
    required this.networkSent,
    required this.networkReceived,
  });

  @override
  List<Object?> get props =>
      [timestamp, cpuUsage, memoryUsage, networkSent, networkReceived];

  @override
  String toString() {
    return 'PerformanceDataPoint(timestamp: $timestamp, cpu: ${(cpuUsage * 100).toStringAsFixed(1)}%)';
  }
}

/// 健康检查结果
class HealthCheckResult extends Equatable {
  /// 总体健康状态
  final HealthStatus overallStatus;

  /// 检查项目列表
  final List<HealthCheckItem> checks;

  /// 检查时间
  final DateTime timestamp;

  const HealthCheckResult({
    required this.overallStatus,
    required this.checks,
    required this.timestamp,
  });

  /// 创建失败结果
  factory HealthCheckResult.failed() {
    return HealthCheckResult(
      overallStatus: HealthStatus.critical,
      checks: [
        const HealthCheckItem(
          name: '系统检查',
          status: HealthStatus.critical,
          message: '健康检查执行失败',
          details: {},
        ),
      ],
      timestamp: DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [overallStatus, checks, timestamp];

  @override
  String toString() {
    return 'HealthCheckResult(overallStatus: $overallStatus, checks: ${checks.length})';
  }
}

/// 健康检查项目
class HealthCheckItem extends Equatable {
  /// 检查项目名称
  final String name;

  /// 健康状态
  final HealthStatus status;

  /// 状态消息
  final String message;

  /// 详细信息
  final Map<String, dynamic> details;

  const HealthCheckItem({
    required this.name,
    required this.status,
    required this.message,
    required this.details,
  });

  @override
  List<Object?> get props => [name, status, message, details];

  @override
  String toString() {
    return 'HealthCheckItem(name: $name, status: $status)';
  }
}

/// 健康状态
enum HealthStatus {
  /// 健康
  healthy,

  /// 警告
  warning,

  /// 严重
  critical,
}

/// 仪表板事件
class DashboardEvent extends Equatable {
  /// 事件类型
  final DashboardEventType type;

  /// 相关数据
  final dynamic data;

  /// 事件时间
  final DateTime timestamp;

  const DashboardEvent._({
    required this.type,
    this.data,
    required this.timestamp,
  });

  /// 监控开始事件
  factory DashboardEvent.monitoringStarted() {
    return DashboardEvent._(
      type: DashboardEventType.monitoringStarted,
      timestamp: DateTime.now(),
    );
  }

  /// 监控停止事件
  factory DashboardEvent.monitoringStopped() {
    return DashboardEvent._(
      type: DashboardEventType.monitoringStopped,
      timestamp: DateTime.now(),
    );
  }

  /// 概览更新事件
  factory DashboardEvent.overviewUpdated(DashboardOverview overview) {
    return DashboardEvent._(
      type: DashboardEventType.overviewUpdated,
      data: overview,
      timestamp: DateTime.now(),
    );
  }

  /// 警报生成事件
  factory DashboardEvent.alertsGenerated(List<SystemAlert> alerts) {
    return DashboardEvent._(
      type: DashboardEventType.alertsGenerated,
      data: alerts,
      timestamp: DateTime.now(),
    );
  }

  /// 健康检查完成事件
  factory DashboardEvent.healthCheckCompleted(HealthCheckResult result) {
    return DashboardEvent._(
      type: DashboardEventType.healthCheckCompleted,
      data: result,
      timestamp: DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [type, data, timestamp];

  @override
  String toString() {
    return 'DashboardEvent(type: $type, timestamp: $timestamp)';
  }
}

/// 仪表板事件类型
enum DashboardEventType {
  /// 监控开始
  monitoringStarted,

  /// 监控停止
  monitoringStopped,

  /// 概览更新
  overviewUpdated,

  /// 警报生成
  alertsGenerated,

  /// 健康检查完成
  healthCheckCompleted,
}
