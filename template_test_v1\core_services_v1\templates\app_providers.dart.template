/*
---------------------------------------------------------------
File name:          app_providers.dart
Author:             Ming Status CLI
Date created:       2025-07-14
Last modified:      2025-07-14
Dart Version:       3.2+
Description:        core_services_v1应用程序状态管理Provider
---------------------------------------------------------------
Change History:
    2025-07-14: Initial creation - core_services_v1应用程序状态管理Provider;
---------------------------------------------------------------
*/


import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dio/dio.dart';
import '../models/user.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';

part 'app_providers.g.dart';

/// Dio HTTP客户端Provider
///
/// 提供配置好的Dio实例
@riverpod
Dio dio(DioRef ref) {
  final dio = Dio();
  
  // 基础配置
  dio.options.baseUrl = 'https://api.example.com';
  dio.options.connectTimeout = const Duration(seconds: 30);
  dio.options.receiveTimeout = const Duration(seconds: 30);
  
  // 添加拦截器
  dio.interceptors.add(
    LogInterceptor(
      requestBody: true,
      responseBody: true,
    ),
  );
  
  return dio;
}

/// API服务Provider
///
/// 提供API服务实例
@riverpod
ApiService apiService(ApiServiceRef ref) {
  final dio = ref.watch(dioProvider);
  return ApiService(dio);
}

/// 存储服务Provider
///
/// 提供本地存储服务
@riverpod
Future<StorageService> storageService(StorageServiceRef ref) async {
  final prefs = await ref.watch(sharedPreferencesProvider.future);
  return StorageService(prefs);
}

/// 用户认证Provider
///
/// 管理用户认证状态
@riverpod
class AuthNotifier extends _$AuthNotifier {
  @override
  Future<User?> build() async {
    // 从本地存储加载用户信息
    final storage = await ref.watch(storageServiceProvider.future);
    final userData = await storage.getUser();
    return userData;
  }

  /// 登录
  Future<void> login(String email, String password) async {
    state = const AsyncValue.loading();
    
    try {
      final apiService = ref.read(apiServiceProvider);
      final user = await apiService.login(email, password);
      
      // 保存用户信息到本地
      final storage = await ref.read(storageServiceProvider.future);
      await storage.saveUser(user);
      
      state = AsyncValue.data(user);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// 登出
  Future<void> logout() async {
    state = const AsyncValue.loading();
    
    try {
      final storage = await ref.read(storageServiceProvider.future);
      await storage.clearUser();
      
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// 刷新用户信息
  Future<void> refreshUser() async {
    final currentUser = state.value;
    if (currentUser == null) return;
    
    state = const AsyncValue.loading();
    
    try {
      final apiService = ref.read(apiServiceProvider);
      final user = await apiService.getCurrentUser();
      
      final storage = await ref.read(storageServiceProvider.future);
      await storage.saveUser(user);
      
      state = AsyncValue.data(user);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

/// 应用状态Provider
///
/// 管理应用程序的全局状态
@riverpod
class AppState extends _$AppState {
  @override
  Map<String, dynamic> build() {
    return {
      'isLoading': false,
      'isOnline': true,
      'lastSync': null,
      'notifications': <String>[],
    };
  }

  /// 设置加载状态
  void setLoading(bool isLoading) {
    state = {...state, 'isLoading': isLoading};
  }

  /// 设置在线状态
  void setOnlineStatus(bool isOnline) {
    state = {...state, 'isOnline': isOnline};
  }

  /// 更新最后同步时间
  void updateLastSync() {
    state = {...state, 'lastSync': DateTime.now()};
  }

  /// 添加通知
  void addNotification(String message) {
    final notifications = List<String>.from(state['notifications']);
    notifications.add(message);
    state = {...state, 'notifications': notifications};
  }

  /// 清除通知
  void clearNotifications() {
    state = {...state, 'notifications': <String>[]};
  }
}
