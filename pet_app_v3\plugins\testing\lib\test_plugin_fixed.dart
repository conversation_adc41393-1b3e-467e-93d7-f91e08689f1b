/*
---------------------------------------------------------------
File name:          test_plugin_fixed.dart
Author:             Pet App V3 Team
Date created:       2025-07-25
Last modified:      2025-07-25
Dart Version:       ^3.2.0
Description:        Test Plugin Fixed - A new Flutter project created with Ming Status CLI - 主入口文件
---------------------------------------------------------------
Change History:
    2025-07-25: Initial creation - A new Flutter project created with Ming Status CLI;
---------------------------------------------------------------
*/

/// Test Plugin Fixed 插件库
/// 
/// 这是Test Plugin Fixed插件的主入口文件，提供了插件的所有公共API。
/// 
/// 使用示例:
/// ```dart
/// import 'package:test_plugin_fixed/test_plugin_fixed.dart';
/// 
/// // 创建插件实例
/// final plugin = TestPluginFixedPlugin();
/// 
/// // 初始化并启动插件
/// await plugin.initialize();
/// await plugin.start();
/// ```
library test_plugin_fixed;

// 导出核心插件类
export 'src/plugin_core.dart';

// 导出主入口
export 'plugin_main.dart';
