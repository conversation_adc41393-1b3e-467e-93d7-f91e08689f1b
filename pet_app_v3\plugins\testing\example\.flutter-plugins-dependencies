{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "test_plugin_fixed", "path": "D:\\\\狗py\\\\pythonProject\\\\CRUD\\\\Tasks_organizing\\\\pet_app_v3\\\\plugins\\\\testing\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "test_plugin_fixed", "path": "D:\\\\狗py\\\\pythonProject\\\\CRUD\\\\Tasks_organizing\\\\pet_app_v3\\\\plugins\\\\testing\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "test_plugin_fixed", "path": "D:\\\\狗py\\\\pythonProject\\\\CRUD\\\\Tasks_organizing\\\\pet_app_v3\\\\plugins\\\\testing\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "test_plugin_fixed", "path": "D:\\\\狗py\\\\pythonProject\\\\CRUD\\\\Tasks_organizing\\\\pet_app_v3\\\\plugins\\\\testing\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "windows": [{"name": "test_plugin_fixed", "path": "D:\\\\狗py\\\\pythonProject\\\\CRUD\\\\Tasks_organizing\\\\pet_app_v3\\\\plugins\\\\testing\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "web": [{"name": "test_plugin_fixed", "path": "D:\\\\狗py\\\\pythonProject\\\\CRUD\\\\Tasks_organizing\\\\pet_app_v3\\\\plugins\\\\testing\\\\", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "test_plugin_fixed", "dependencies": []}], "date_created": "2025-08-02 18:20:14.509195", "version": "3.32.4", "swift_package_manager_enabled": {"ios": false, "macos": false}}