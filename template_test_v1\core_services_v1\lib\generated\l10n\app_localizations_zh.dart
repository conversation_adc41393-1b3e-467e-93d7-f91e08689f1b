/// Generated file. Do not edit.
///
/// To regenerate, run: `flutter gen-l10n`
///
/// Project: core_services_v1
/// Generated: 2025-07-14

// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appTitle => '我的应用';

  @override
  String get welcome => '欢迎';

  @override
  String hello(String name) {
    return '你好，$name！';
  }

  @override
  String get settings => '设置';

  @override
  String get about => '关于';

  @override
  String get ok => '确定';

  @override
  String get cancel => '取消';

  @override
  String get home => '首页';

  @override
  String get profile => '个人资料';

  @override
  String get notifications => '通知';

  @override
  String get search => '搜索';

  @override
  String get loading => '加载中...';

  @override
  String get error => '错误';

  @override
  String get retry => '重试';

  @override
  String get save => '保存';

  @override
  String get delete => '删除';

  @override
  String get edit => '编辑';

  @override
  String itemCount(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$countString个项目',
      one: '1个项目',
      zero: '没有项目',
    );
    return '$_temp0';
  }

  @override
  String get login => '登录';

  @override
  String get logout => '登出';

  @override
  String get register => '注册';

  @override
  String get forgotPassword => '忘记密码';

  @override
  String get email => '邮箱';

  @override
  String get password => '密码';

  @override
  String get confirmPassword => '确认密码';

  @override
  String get firstName => '名字';

  @override
  String get lastName => '姓氏';

  @override
  String get phoneNumber => '电话号码';

  @override
  String get address => '地址';

  @override
  String get city => '城市';

  @override
  String get country => '国家';

  @override
  String get language => '语言';

  @override
  String get theme => '主题';

  @override
  String get darkMode => '暗色模式';

  @override
  String get lightMode => '亮色模式';

  @override
  String get systemMode => '跟随系统';

  @override
  String lastUpdated(DateTime date) {
    final intl.DateFormat dateDateFormat = intl.DateFormat.yMd(localeName);
    final String dateString = dateDateFormat.format(date);

    return '最后更新：$dateString';
  }

  @override
  String get version => '版本';

  @override
  String get privacyPolicy => '隐私政策';

  @override
  String get termsOfService => '服务条款';

  @override
  String get contactUs => '联系我们';

  @override
  String get feedback => '反馈';

  @override
  String get rateApp => '评价应用';

  @override
  String get shareApp => '分享应用';

  @override
  String get appName => 'core_services_v1';

  @override
  String get appDescription => '一个使用Flutter构建的现代化应用程序';
}
