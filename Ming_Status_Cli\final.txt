Analyzing Ming_Status_Cli...

  error - test\integration\performance_optimization_simple_test.dart:20:8 - Target of URI doesn't exist: 'package:ming_status_cli/src/core/performance_monitor.dart'. Try creating the file referenced by the URI, or try using a URI for a file that does exist. - uri_does_not_exist
  error - test\integration\performance_optimization_simple_test.dart:46:25 - The function 'PerformanceMonitor' isn't defined. Try importing the library that defines 'PerformanceMonitor', correcting the name to the name of an existing function, or defining a function named 'PerformanceMonitor'. - undefined_function
  error - test\integration\performance_optimization_simple_test.dart:53:25 - The function 'PerformanceMonitor' isn't defined. Try importing the library that defines 'PerformanceMonitor', correcting the name to the name of an existing function, or defining a function named 'PerformanceMonitor'. - undefined_function
  error - test\integration\performance_optimization_simple_test.dart:65:25 - The function 'PerformanceMonitor' isn't defined. Try importing the library that defines 'PerformanceMonitor', correcting the name to the name of an existing function, or defining a function named 'PerformanceMonitor'. - undefined_function
  error - test\integration\performance_optimization_simple_test.dart:68:24 - The function 'PerformanceMetric' isn't defined. Try importing the library that defines 'PerformanceMetric', correcting the name to the name of an existing function, or defining a function named 'PerformanceMetric'. - undefined_function
  error - test\integration\performance_optimization_simple_test.dart:70:17 - Undefined name 'PerformanceMetricType'. Try correcting the name to one that is defined, or defining the name. - undefined_identifier
  error - test\integration\performance_optimization_simple_test.dart:84:25 - The function 'PerformanceMonitor' isn't defined. Try importing the library that defines 'PerformanceMonitor', correcting the name to the name of an existing function, or defining a function named 'PerformanceMonitor'. - undefined_function
  error - test\integration\performance_optimization_simple_test.dart:103:25 - The function 'PerformanceMonitor' isn't defined. Try importing the library that defines 'PerformanceMonitor', correcting the name to the name of an existing function, or defining a function named 'PerformanceMonitor'. - undefined_function
  error - test\integration\performance_optimization_simple_test.dart:108:11 - The function 'PerformanceMetric' isn't defined. Try importing the library that defines 'PerformanceMetric', correcting the name to the name of an existing function, or defining a function named 'PerformanceMetric'. - undefined_function
  error - test\integration\performance_optimization_simple_test.dart:110:19 - Undefined name 'PerformanceMetricType'. Try correcting the name to one that is defined, or defining the name. - undefined_identifier
  error - test\integration\performance_optimization_simple_test.dart:128:25 - The function 'PerformanceMonitor' isn't defined. Try importing the library that defines 'PerformanceMonitor', correcting the name to the name of an existing function, or defining a function named 'PerformanceMonitor'. - undefined_function
  error - test\integration\performance_optimization_simple_test.dart:132:11 - The function 'PerformanceMetric' isn't defined. Try importing the library that defines 'PerformanceMetric', correcting the name to the name of an existing function, or defining a function named 'PerformanceMetric'. - undefined_function
  error - test\integration\performance_optimization_simple_test.dart:134:19 - Undefined name 'PerformanceMetricType'. Try correcting the name to one that is defined, or defining the name. - undefined_identifier
  error - test\integration\performance_optimization_simple_test.dart:386:25 - The function 'PerformanceMonitor' isn't defined. Try importing the library that defines 'PerformanceMonitor', correcting the name to the name of an existing function, or defining a function named 'PerformanceMonitor'. - undefined_function
  error - test\integration\performance_optimization_simple_test.dart:394:13 - The function 'PerformanceMetric' isn't defined. Try importing the library that defines 'PerformanceMetric', correcting the name to the name of an existing function, or defining a function named 'PerformanceMetric'. - undefined_function
  error - test\integration\performance_optimization_simple_test.dart:396:21 - Undefined name 'PerformanceMetricType'. Try correcting the name to one that is defined, or defining the name. - undefined_identifier
  error - test\integration\performance_optimization_test.dart:19:8 - Target of URI doesn't exist: 'package:ming_status_cli/src/core/performance_monitor.dart'. Try creating the file referenced by the URI, or try using a URI for a file that does exist. - uri_does_not_exist
  error - test\integration\performance_optimization_test.dart:44:12 - Undefined class 'PerformanceMonitor'. Try changing the name to the name of an existing class, or creating a class with the name 'PerformanceMonitor'. - undefined_class
  error - test\integration\performance_optimization_test.dart:47:19 - The function 'PerformanceMonitor' isn't defined. Try importing the library that defines 'PerformanceMonitor', correcting the name to the name of an existing function, or defining a function named 'PerformanceMonitor'. - undefined_function
  error - test\integration\performance_optimization_test.dart:70:24 - The function 'PerformanceMetric' isn't defined. Try importing the library that defines 'PerformanceMetric', correcting the name to the name of an existing function, or defining a function named 'PerformanceMetric'. - undefined_function
  error - test\integration\performance_optimization_test.dart:72:17 - Undefined name 'PerformanceMetricType'. Try correcting the name to one that is defined, or defining the name. - undefined_identifier
  error - test\integration\performance_optimization_test.dart:110:11 - The function 'PerformanceMetric' isn't defined. Try importing the library that defines 'PerformanceMetric', correcting the name to the name of an existing function, or defining a function named 'PerformanceMetric'. - undefined_function
  error - test\integration\performance_optimization_test.dart:112:19 - Undefined name 'PerformanceMetricType'. Try correcting the name to one that is defined, or defining the name. - undefined_identifier
  error - test\integration\performance_optimization_test.dart:120:11 - The function 'PerformanceMetric' isn't defined. Try importing the library that defines 'PerformanceMetric', correcting the name to the name of an existing function, or defining a function named 'PerformanceMetric'. - undefined_function
  error - test\integration\performance_optimization_test.dart:122:19 - Undefined name 'PerformanceMetricType'. Try correcting the name to one that is defined, or defining the name. - undefined_identifier
  error - test\integration\performance_optimization_test.dart:430:25 - The function 'PerformanceMonitor' isn't defined. Try importing the library that defines 'PerformanceMonitor', correcting the name to the name of an existing function, or defining a function named 'PerformanceMonitor'. - undefined_function
  error - test\integration\performance_optimization_test.dart:485:25 - The function 'PerformanceMonitor' isn't defined. Try importing the library that defines 'PerformanceMonitor', correcting the name to the name of an existing function, or defining a function named 'PerformanceMonitor'. - undefined_function
  error - test\integration\phase2_preparation_test.dart:18:8 - Target of URI doesn't exist: 'package:ming_status_cli/src/core/compatibility_manager.dart'. Try creating the file referenced by the URI, or try using a URI for a file that does exist. - uri_does_not_exist
  error - test\integration\phase2_preparation_test.dart:243:12 - Undefined class 'CompatibilityManager'. Try changing the name to the name of an existing class, or creating a class with the name 'CompatibilityManager'. - undefined_class
  error - test\integration\phase2_preparation_test.dart:246:19 - The function 'CompatibilityManager' isn't defined. Try importing the library that defines 'CompatibilityManager', correcting the name to the name of an existing function, or defining a function named 'CompatibilityManager'. - undefined_function
  error - test\integration\phase2_preparation_test.dart:264:34 - The name 'CompatibilityLevel' isn't a type, so it can't be used as a type argument. Try correcting the name to an existing type, or defining a type named 'CompatibilityLevel'. - non_type_as_type_argument
  error - test\integration\phase2_preparation_test.dart:291:33 - Const variables must be initialized with a constant value. Try changing the initializer to be a constant expression. - const_initialized_with_non_constant_value
  error - test\integration\phase2_preparation_test.dart:291:33 - The function 'DeprecationInfo' isn't defined. Try importing the library that defines 'DeprecationInfo', correcting the name to the name of an existing function, or defining a function named 'DeprecationInfo'. - undefined_function
  error - test\integration\phase2_preparation_test.dart:330:33 - Const variables must be initialized with a constant value. Try changing the initializer to be a constant expression. - const_initialized_with_non_constant_value
  error - test\integration\phase2_preparation_test.dart:330:33 - The function 'DeprecationInfo' isn't defined. Try importing the library that defines 'DeprecationInfo', correcting the name to the name of an existing function, or defining a function named 'DeprecationInfo'. - undefined_function
  error - test\integration\phase2_preparation_test.dart:341:47 - The name 'DeprecationInfo' isn't a type, so it can't be used as a type argument. Try correcting the name to an existing type, or defining a type named 'DeprecationInfo'. - non_type_as_type_argument
  error - test\integration\phase2_preparation_test.dart:387:24 - Const variables must be initialized with a constant value. Try changing the initializer to be a constant expression. - const_initialized_with_non_constant_value
  error - test\integration\phase2_preparation_test.dart:387:24 - Undefined name 'CompatibilityLevel'. Try correcting the name to one that is defined, or defining the name. - undefined_identifier
  error - test\integration\phase2_preparation_test.dart:389:33 - Undefined name 'CompatibilityLevel'. Try correcting the name to one that is defined, or defining the name. - undefined_identifier
  error - test\integration\phase2_preparation_test.dart:390:33 - Undefined name 'CompatibilityLevel'. Try correcting the name to one that is defined, or defining the name. - undefined_identifier
  error - test\integration\phase2_preparation_test.dart:391:33 - Undefined name 'CompatibilityLevel'. Try correcting the name to one that is defined, or defining the name. - undefined_identifier
  error - test\integration\phase2_preparation_test.dart:392:33 - Undefined name 'CompatibilityLevel'. Try correcting the name to one that is defined, or defining the name. - undefined_identifier
  error - test\unit\template_system_test.dart:157:26 - 'TemplateDependency' isn't a function. Try correcting the name to match an existing function, or define a method or function named 'TemplateDependency'. - invocation_of_non_function
  error - test\unit\template_system_test.dart:157:26 - Const variables must be initialized with a constant value. Try changing the initializer to be a constant expression. - const_initialized_with_non_constant_value
  error - test\unit\template_system_test.dart:157:26 - The name 'TemplateDependency' is defined in the libraries 'package:ming_status_cli/src/core/template_system/template_metadata.dart' and 'package:ming_status_cli/src/core/template_system/template_registry.dart'. Try using 'as prefix' for one of the import directives, or hiding the name from all but one of the imports. - ambiguous_import
  error - test\unit\template_system_test.dart:165:38 - The name 'DependencyType' is defined in the libraries 'package:ming_status_cli/src/core/template_system/template_metadata.dart' and 'package:ming_status_cli/src/core/template_system/template_registry.dart'. Try using 'as prefix' for one of the import directives, or hiding the name from all but one of the imports. - ambiguous_import
  error - test\unit\template_system_test.dart:170:26 - 'TemplateDependency' isn't a function. Try correcting the name to match an existing function, or define a method or function named 'TemplateDependency'. - invocation_of_non_function
  error - test\unit\template_system_test.dart:170:26 - Const variables must be initialized with a constant value. Try changing the initializer to be a constant expression. - const_initialized_with_non_constant_value
  error - test\unit\template_system_test.dart:170:26 - The name 'TemplateDependency' is defined in the libraries 'package:ming_status_cli/src/core/template_system/template_metadata.dart' and 'package:ming_status_cli/src/core/template_system/template_registry.dart'. Try using 'as prefix' for one of the import directives, or hiding the name from all but one of the imports. - ambiguous_import
  error - test\unit\template_system_test.dart:173:15 - The name 'DependencyType' is defined in the libraries 'package:ming_status_cli/src/core/template_system/template_metadata.dart' and 'package:ming_status_cli/src/core/template_system/template_registry.dart'. Try using 'as prefix' for one of the import directives, or hiding the name from all but one of the imports. - ambiguous_import
  error - test\unit\template_system_test.dart:181:25 - The name 'TemplateDependency' is defined in the libraries 'package:ming_status_cli/src/core/template_system/template_metadata.dart' and 'package:ming_status_cli/src/core/template_system/template_registry.dart'. Try using 'as prefix' for one of the import directives, or hiding the name from all but one of the imports. - ambiguous_import
  error - test\unit\template_system_test.dart:266:23 - 'PerformanceMetrics' isn't a function. Try correcting the name to match an existing function, or define a method or function named 'PerformanceMetrics'. - invocation_of_non_function
  error - test\unit\template_system_test.dart:266:23 - The name 'PerformanceMetrics' is defined in the libraries 'package:ming_status_cli/src/core/template_system/advanced_template.dart' and 'package:ming_status_cli/src/core/template_system/template_registry.dart'. Try using 'as prefix' for one of the import directives, or hiding the name from all but one of the imports. - ambiguous_import
  error - test\unit\template_system_test.dart:281:23 - 'PerformanceMetrics' isn't a function. Try correcting the name to match an existing function, or define a method or function named 'PerformanceMetrics'. - invocation_of_non_function
  error - test\unit\template_system_test.dart:281:23 - The name 'PerformanceMetrics' is defined in the libraries 'package:ming_status_cli/src/core/template_system/advanced_template.dart' and 'package:ming_status_cli/src/core/template_system/template_registry.dart'. Try using 'as prefix' for one of the import directives, or hiding the name from all but one of the imports. - ambiguous_import
warning - lib\src\core\distribution\template_downloader.dart:391:53 - The left operand can't be null, so the right operand is never executed. Try removing the operator and the right operand. - dead_null_aware_expression
warning - test\integration\performance_optimization_integration_test.dart:147:15 - The value of the local variable 'beforeUsage' isn't used. Try removing the variable or using it. - unused_local_variable
warning - test\integration\performance_optimization_test.dart:143:17 - The type argument(s) of the constructor 'Future.delayed' can't be inferred. Use explicit type argument(s) for 'Future.delayed'. - inference_failure_on_instance_creation
warning - test\integration\performance_optimization_test.dart:406:59 - The generic type 'Map<dynamic, dynamic>' should have explicit type arguments but doesn't. Use explicit type arguments for 'Map<dynamic, dynamic>'. - strict_raw_type
warning - test\integration\performance_optimization_test.dart:407:61 - The generic type 'Map<dynamic, dynamic>' should have explicit type arguments but doesn't. Use explicit type arguments for 'Map<dynamic, dynamic>'. - strict_raw_type
warning - test\integration\performance_optimization_test.dart:445:47 - The type argument(s) of the function 'get' can't be inferred. Use explicit type argument(s) for 'get'. - inference_failure_on_function_invocation
warning - test\integration\performance_optimization_test.dart:458:19 - The type argument(s) of the constructor 'Future.delayed' can't be inferred. Use explicit type argument(s) for 'Future.delayed'. - inference_failure_on_instance_creation
warning - test\integration\performance_optimization_test.dart:472:37 - The generic type 'Map<dynamic, dynamic>' should have explicit type arguments but doesn't. Use explicit type arguments for 'Map<dynamic, dynamic>'. - strict_raw_type
warning - test\integration\performance_optimization_test.dart:507:23 - The type argument(s) of the constructor 'Future.delayed' can't be inferred. Use explicit type argument(s) for 'Future.delayed'. - inference_failure_on_instance_creation
warning - test\integration\phase2_1_integration_test.dart:65:11 - The type argument(s) of the constructor 'Future.delayed' can't be inferred. Use explicit type argument(s) for 'Future.delayed'. - inference_failure_on_instance_creation
warning - test\integration\phase2_1_integration_test.dart:71:11 - The type argument(s) of the constructor 'Future.delayed' can't be inferred. Use explicit type argument(s) for 'Future.delayed'. - inference_failure_on_instance_creation
warning - test\integration\phase2_1_integration_test.dart:80:11 - The type argument(s) of the constructor 'Future.delayed' can't be inferred. Use explicit type argument(s) for 'Future.delayed'. - inference_failure_on_instance_creation
warning - test\integration\phase2_1_integration_test.dart:185:13 - The value of the local variable 'template' isn't used. Try removing the variable or using it. - unused_local_variable
warning - test\integration\phase2_1_integration_test.dart:300:13 - The value of the local variable 'inheritanceEngine' isn't used. Try removing the variable or using it. - unused_local_variable
warning - test\stress\extended_stress_test.dart:153:31 - The value of the field '_startupOptimizer' isn't used. Try removing the field, or using it. - unused_field
warning - test\stress\extended_stress_test.dart:207:17 - The type argument(s) of the constructor 'Future.delayed' can't be inferred. Use explicit type argument(s) for 'Future.delayed'. - inference_failure_on_instance_creation
warning - test\stress\extended_stress_test.dart:359:17 - The type argument(s) of the constructor 'Future.delayed' can't be inferred. Use explicit type argument(s) for 'Future.delayed'. - inference_failure_on_instance_creation
warning - test\unit\enterprise_parameter_system_test.dart:308:13 - The value of the local variable 'session' isn't used. Try removing the variable or using it. - unused_local_variable
warning - test\unit\inheritance_system_test.dart:75:28 - The value of the local variable 'inheritanceEngine' isn't used. Try removing the variable or using it. - unused_local_variable
warning - test\unit\inheritance_system_test.dart:175:29 - The value of the local variable 'resolver' isn't used. Try removing the variable or using it. - unused_local_variable
warning - test\unit\inheritance_system_test.dart:262:28 - The value of the local variable 'compositionEngine' isn't used. Try removing the variable or using it. - unused_local_variable
warning - test\unit\inheritance_system_test.dart:351:31 - The value of the local variable 'validator' isn't used. Try removing the variable or using it. - unused_local_variable
warning - test\unit\smart_conditional_system_test.dart:572:40 - The generic type 'List<dynamic>' should have explicit type arguments but doesn't. Use explicit type arguments for 'List<dynamic>'. - strict_raw_type
warning - test\unit\smart_conditional_system_test.dart:579:44 - The generic type 'List<dynamic>' should have explicit type arguments but doesn't. Use explicit type arguments for 'List<dynamic>'. - strict_raw_type
   info - lib\src\cli_app.dart:114:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\cli_app.dart:117:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\commands\help_command.dart:53:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\optimize_command.dart:284:16 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\commands\registry_add_command.dart:115:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:116:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:149:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:173:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:175:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:179:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:208:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:209:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:210:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:211:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:212:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:213:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:214:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:215:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:216:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:217:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:218:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:219:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:280:9 - To-do comment doesn't follow the Flutter style. Try following the Flutter style for to-do comments. - flutter_style_todos
   info - lib\src\commands\registry_add_command.dart:290:7 - To-do comment doesn't follow the Flutter style. Try following the Flutter style for to-do comments. - flutter_style_todos
   info - lib\src\commands\registry_add_command.dart:295:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:296:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:297:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:298:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:306:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:307:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:308:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:309:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:310:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:311:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:312:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:313:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:314:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:315:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:317:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:318:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:319:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_add_command.dart:320:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:211:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:223:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:224:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:225:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:229:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:230:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:235:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:254:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:255:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:256:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:257:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:261:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:262:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:263:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:264:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:265:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:271:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:272:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:273:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:274:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:277:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:283:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:364:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:365:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:371:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:372:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:373:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:389:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:390:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:391:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:392:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:393:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:394:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:404:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:405:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_list_command.dart:409:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:114:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:115:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:118:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:119:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:120:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:121:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:122:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:123:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:124:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\registry_stats_command.dart:125:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:127:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:128:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:136:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:137:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:141:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:144:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:148:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:151:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:155:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:157:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:164:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:165:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:166:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:167:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:168:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:169:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:170:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\registry_stats_command.dart:171:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:178:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:179:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:180:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:181:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:182:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\registry_stats_command.dart:183:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:184:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:185:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:198:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:199:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:202:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:203:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:204:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:205:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:206:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:207:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\registry_stats_command.dart:209:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:214:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:220:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:223:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_stats_command.dart:226:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:112:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:113:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:114:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:115:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:116:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:128:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:129:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:130:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:131:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:132:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:133:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:147:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:159:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:162:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:164:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:166:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:168:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:171:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:173:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:174:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:175:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:177:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:179:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:182:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:184:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:186:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:188:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:200:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:203:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:204:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:205:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:206:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:208:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:209:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:210:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\registry_sync_command.dart:213:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_benchmark_command.dart:214:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_benchmark_command.dart:215:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_benchmark_command.dart:218:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_benchmark_command.dart:219:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_benchmark_command.dart:220:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_benchmark_command.dart:221:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_benchmark_command.dart:222:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_benchmark_command.dart:223:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_benchmark_command.dart:224:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_benchmark_command.dart:227:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_benchmark_command.dart:233:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_benchmark_command.dart:234:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_benchmark_command.dart:240:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_benchmark_command.dart:245:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_benchmark_command.dart:246:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_benchmark_command.dart:251:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_benchmark_command.dart:258:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_benchmark_command.dart:264:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_benchmark_command.dart:269:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_benchmark_command.dart:318:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:319:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:320:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:321:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:324:11 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:325:14 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:341:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:352:28 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:353:18 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:354:18 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:355:18 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:356:18 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:357:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:358:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:359:14 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:360:24 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:365:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:366:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:367:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:368:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:371:11 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:372:14 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:385:14 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:401:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:407:13 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:408:13 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:409:13 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_benchmark_command.dart:410:14 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_command.dart:154:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_conditional_command.dart:171:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_conditional_command.dart:172:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_conditional_command.dart:173:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_conditional_command.dart:174:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_conditional_command.dart:175:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_conditional_command.dart:252:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_conditional_command.dart:293:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_conditional_command.dart:294:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_conditional_command.dart:297:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_conditional_command.dart:300:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:268:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:269:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:270:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:271:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:274:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:276:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:281:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:283:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:291:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:310:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:312:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:314:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:320:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:322:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:324:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:330:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:332:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:334:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:340:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:342:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:344:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:350:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:352:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:357:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:358:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:359:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:360:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:361:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_create_command.dart:362:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:269:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:288:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:289:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:311:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:312:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:313:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:314:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:315:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:318:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:325:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:326:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:329:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:346:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:347:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:359:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:360:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:361:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:362:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:364:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:365:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:366:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:367:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:368:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:370:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:371:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:372:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:376:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:393:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:394:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:403:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:404:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:405:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:406:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:417:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:425:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:427:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:431:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:449:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:450:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:458:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:459:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:460:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:470:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:471:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:475:15 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:476:15 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:502:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:519:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:520:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:543:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:544:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:545:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:546:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:547:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:556:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:557:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:558:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:563:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:566:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:568:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:569:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:570:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:571:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_enterprise_command.dart:573:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:578:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:595:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:596:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:604:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:605:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:606:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:607:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:618:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:619:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:620:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:624:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:625:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:626:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:627:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:630:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:647:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:648:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:654:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:655:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:656:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:657:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:658:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:659:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:660:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:666:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:667:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:668:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:672:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:673:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:674:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:678:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:695:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:696:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:703:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:706:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:708:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:709:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:711:15 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:717:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:730:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:731:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:732:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:733:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:734:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:737:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:738:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:739:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:740:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:747:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:748:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:749:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:753:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:754:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:755:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:756:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_enterprise_command.dart:759:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:760:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:761:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:762:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:772:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:778:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:779:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:780:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:781:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:783:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:784:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:786:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:787:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:788:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:789:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:793:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:795:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:801:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:802:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:803:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:805:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:806:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:807:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:817:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:823:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:824:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:825:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:826:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:828:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:829:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:830:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:831:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:833:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:834:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:835:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:836:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:840:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:842:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:846:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:848:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:859:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:860:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:861:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:862:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:863:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_enterprise_command.dart:865:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:866:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:869:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:870:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:871:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:872:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:874:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:875:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:876:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:877:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:878:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:881:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:884:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:886:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:890:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:892:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:894:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:898:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:901:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:902:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:903:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:906:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:917:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:918:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:919:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:920:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:921:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:922:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:928:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:930:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:933:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:938:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_enterprise_command.dart:968:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_generate_command.dart:216:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:217:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:218:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:219:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:220:29 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:221:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:222:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:223:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:224:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:225:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:226:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:227:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:256:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:257:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:260:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:262:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:263:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:264:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:267:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:268:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:269:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:270:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:271:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:273:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:274:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:275:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:276:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:278:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:279:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:280:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:281:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:283:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:285:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:287:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:288:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:307:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:308:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:332:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:333:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:334:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:335:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:336:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:339:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:340:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:341:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:342:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:343:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:344:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:345:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:348:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:349:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:350:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:351:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:355:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:356:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:357:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:358:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:361:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:367:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:368:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:405:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:406:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_generate_command.dart:409:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:410:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:414:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:420:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:421:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:424:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:425:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:426:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:427:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:428:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:432:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:433:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:434:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:435:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:436:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:440:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:441:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:442:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:443:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:444:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:451:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:452:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:455:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:456:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:457:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:458:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:459:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:460:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:461:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:463:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:464:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:465:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:466:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:467:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:470:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:471:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:472:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:473:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:474:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:475:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:478:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_generate_command.dart:479:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:112:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:133:9 - Missing an 'await' for the 'Future' computed by this expression. Try adding an 'await' or wrapping the expression with 'unawaited'. - unawaited_futures
   info - lib\src\commands\template_info_command.dart:172:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:173:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:176:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:177:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:178:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:179:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:180:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:181:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:183:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:185:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:186:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:187:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:188:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:191:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:192:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:193:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:194:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:195:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:196:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:197:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:201:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:202:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:203:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:208:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:209:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:210:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:215:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:216:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:217:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:218:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:223:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:224:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:226:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:229:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:232:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:239:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:240:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:242:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:245:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:251:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:252:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:255:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:258:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:265:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:266:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:273:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:274:13 - Use an if-null operator to convert a 'null' to a 'bool'. Try using an if-null operator. - use_if_null_to_convert_nulls_to_bools
   info - lib\src\commands\template_info_command.dart:275:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:283:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:284:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:285:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:286:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:289:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:290:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:295:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:296:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:298:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:300:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:303:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:308:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:315:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:316:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:317:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:319:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:321:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:323:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:325:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:333:5 - To-do comment doesn't follow the Flutter style. Try following the Flutter style for to-do comments. - flutter_style_todos
   info - lib\src\commands\template_info_command.dart:334:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:339:5 - To-do comment doesn't follow the Flutter style. Try following the Flutter style for to-do comments. - flutter_style_todos
   info - lib\src\commands\template_info_command.dart:340:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:358:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:361:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_info_command.dart:365:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:212:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:213:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:214:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:216:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:218:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:221:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:222:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:232:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:233:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:234:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:237:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:239:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:244:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:250:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:253:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:255:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:269:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:270:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:271:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:274:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:276:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:279:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:283:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:300:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:301:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:304:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:307:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:311:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:316:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:319:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_inherit_command.dart:320:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:123:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:124:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:154:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:164:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:169:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:203:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:204:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:205:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:206:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:207:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:208:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:209:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:210:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:211:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:212:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:239:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:243:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:245:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:247:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:248:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:250:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:259:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:261:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:263:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:267:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:269:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:271:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:278:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:280:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:286:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:288:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:363:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:364:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:377:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:384:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:388:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:390:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:394:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:396:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:448:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:457:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:458:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:459:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:474:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:475:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:476:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:482:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:483:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:484:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:485:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:486:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:487:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:489:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:490:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:491:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_install_command.dart:492:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:29:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_library_command.dart:160:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_library_command.dart:163:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:164:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:203:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:206:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:207:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:208:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:209:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:212:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:215:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:219:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_library_command.dart:222:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:223:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:224:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:225:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:226:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:229:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:230:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:231:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:232:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:233:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:234:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:235:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:237:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:238:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:239:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:240:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:241:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:242:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:243:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:248:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_library_command.dart:251:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:252:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:253:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:254:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:257:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:258:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:263:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:264:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:265:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:266:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:267:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:268:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:269:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:271:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:272:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:273:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:274:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:275:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:276:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:277:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:282:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_library_command.dart:285:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:286:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:287:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:288:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:291:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:292:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:293:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:294:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:295:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:296:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:298:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:299:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:300:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:301:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:302:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:303:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:304:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:309:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_library_command.dart:312:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:313:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:318:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:321:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:322:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:323:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:324:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:326:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:329:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:333:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:346:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:347:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:348:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:349:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:350:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:351:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:354:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:355:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:356:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:357:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:358:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:359:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:360:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:362:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:363:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:364:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:365:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:366:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:367:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:368:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:369:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:382:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:383:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:384:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:385:26 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:386:28 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:387:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:390:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:391:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:392:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:393:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:394:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:395:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:396:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:398:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:399:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:400:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:401:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:402:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:403:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:404:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_library_command.dart:405:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_list_command.dart:21:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\commands\template_list_command.dart:60:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_list_command.dart:61:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_list_command.dart:65:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_list_command.dart:66:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_list_command.dart:67:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_list_command.dart:68:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_list_command.dart:69:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_list_command.dart:73:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_list_command.dart:74:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_network_command.dart:209:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:226:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:227:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:243:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:248:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:249:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:271:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:272:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:279:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:296:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:297:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:304:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:311:11 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\commands\template_network_command.dart:312:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:330:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:331:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:337:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:340:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:345:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:347:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:365:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:366:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:385:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:386:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:392:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:396:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:397:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:398:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:399:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:402:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:404:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:408:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:423:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:424:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:431:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:432:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:436:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:444:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:470:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:471:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:480:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:499:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:500:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:501:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:502:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:506:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:507:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:508:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:509:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:510:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:511:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_network_command.dart:518:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:519:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:520:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:521:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:522:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:523:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:526:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:527:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:528:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:529:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:530:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:531:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:544:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:545:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:546:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:547:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:548:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:549:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:550:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:553:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:554:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:555:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:556:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:558:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:559:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:560:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:561:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:562:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:563:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:576:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:577:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:578:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:579:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:580:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:583:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:584:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:585:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:586:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:587:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:589:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:590:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:591:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:592:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:593:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:594:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_network_command.dart:608:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:609:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:610:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:611:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:612:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:617:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:618:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:619:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:620:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:621:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:622:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_network_command.dart:639:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:640:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:641:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:642:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:643:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:644:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:657:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:658:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:659:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_network_command.dart:661:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:662:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:665:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:666:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:667:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:668:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_network_command.dart:669:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_params_command.dart:157:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:158:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:159:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:160:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:208:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:208:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_params_command.dart:209:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:213:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:217:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:220:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:223:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:224:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:239:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:240:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:241:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:242:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:245:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:248:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:252:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:253:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:254:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:255:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:256:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:274:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:275:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:277:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:282:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:283:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:288:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:294:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:295:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:296:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:304:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:305:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:306:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:307:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:312:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:313:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:315:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:319:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:322:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:328:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:329:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:339:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:340:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:355:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:356:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:357:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:358:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:359:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:374:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:378:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:379:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:380:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:381:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:393:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:394:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:395:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:396:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:399:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:400:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:402:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:403:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:404:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:405:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:406:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:407:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:410:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:411:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:412:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:413:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_params_command.dart:414:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:156:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:271:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:272:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:296:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:297:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:298:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:299:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:300:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:301:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:303:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:305:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:306:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:307:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:310:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:311:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_search_command.dart:313:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:324:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:325:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_search_command.dart:336:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:344:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:355:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:356:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_search_command.dart:359:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:366:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:367:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:368:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:369:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:370:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:375:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:377:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_search_command.dart:379:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:183:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:184:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:207:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:208:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:209:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:211:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:213:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:217:16 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\commands\template_security_command.dart:218:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:223:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:224:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:225:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:226:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:264:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:272:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:273:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:278:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:279:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:280:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:281:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:282:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:285:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:289:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:291:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:295:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:299:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:322:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:330:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:331:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:332:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:333:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:334:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:335:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:336:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:337:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:340:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:342:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:343:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:344:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:345:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:346:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:351:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:353:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:355:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:359:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:361:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:363:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:374:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:380:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:381:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:383:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:384:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:385:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:386:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:387:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:388:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:391:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:393:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:395:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:399:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:403:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:413:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:421:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:422:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:423:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:424:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:425:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:426:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:427:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:435:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:436:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:437:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:438:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:439:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:440:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:441:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:442:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:445:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:446:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:457:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:461:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:462:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:463:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:464:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:465:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:466:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:469:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:470:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:471:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:472:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:473:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:478:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:480:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:482:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:486:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:489:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:491:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:496:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:497:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:506:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:507:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:512:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:518:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:519:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:521:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:524:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:527:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:529:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:535:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:536:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:541:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:547:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:549:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:552:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:554:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:561:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:562:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:563:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:564:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:565:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:569:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:572:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:575:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:578:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:581:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:586:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:589:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:590:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:591:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:592:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:596:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:597:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:601:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:602:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:603:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:604:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:607:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:633:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:634:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:639:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:640:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:641:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:642:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:643:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:644:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:645:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:647:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:649:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:651:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:654:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_security_command.dart:656:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:169:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:170:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:171:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_update_command.dart:190:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:198:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:202:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:203:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:221:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:224:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:225:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:226:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:227:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:228:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:229:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:230:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:245:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:254:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:260:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:267:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:269:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:270:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_update_command.dart:272:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:273:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:282:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:289:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:297:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:298:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:299:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:300:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:301:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:302:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:305:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:307:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:309:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:313:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:316:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:321:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:329:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:330:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_update_command.dart:332:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:333:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_update_command.dart:336:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:345:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:346:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:356:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:357:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:358:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:359:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:360:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:363:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\template_update_command.dart:411:7 - Invalid use of 'default' member in a switch. Try enumerating all the possible values of the switch expression. - no_default_cases
   info - lib\src\commands\template_update_command.dart:443:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\commands\template_update_command.dart:463:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\commands\validate_command.dart:695:19 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\commands\validate_command.dart:713:19 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\condition_evaluator.dart:73:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\condition_evaluator.dart:74:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\condition_evaluator.dart:75:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\condition_evaluator.dart:76:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\condition_evaluator.dart:79:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\condition_evaluator.dart:80:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\condition_evaluator.dart:81:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\condition_evaluator.dart:82:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\condition_evaluator.dart:83:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\condition_evaluator.dart:84:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\condition_evaluator.dart:87:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\condition_evaluator.dart:88:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\condition_evaluator.dart:89:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\condition_evaluator.dart:90:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\condition_evaluator.dart:93:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\condition_evaluator.dart:94:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\condition_evaluator.dart:97:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\condition_evaluator.dart:98:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\condition_evaluator.dart:99:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\condition_evaluator.dart:100:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\condition_evaluator.dart:101:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\condition_evaluator.dart:107:16 - Unnecessary use of an abstract class. Try making 'evaluate' a top-level function and removing the class. - one_member_abstracts
   info - lib\src\core\conditional\conditional_renderer.dart:167:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\conditional_renderer.dart:173:32 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\conditional_renderer.dart:174:18 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\conditional_renderer.dart:175:30 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\conditional_renderer.dart:180:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\conditional_renderer.dart:189:18 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\conditional_renderer.dart:190:18 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\conditional_renderer.dart:191:18 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\conditional_renderer.dart:192:13 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\conditional_renderer.dart:193:13 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\conditional_renderer.dart:194:13 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\conditional\conditional_renderer.dart:689:5 - Assignment could be inlined in 'return' statement. Try inlining the assigned value in the 'return' statement. - join_return_with_assignment
   info - lib\src\core\conditional\feature_detector.dart:421:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\feature_detector.dart:501:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\feature_detector.dart:559:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\feature_detector.dart:597:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\feature_detector.dart:624:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\feature_detector.dart:683:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\feature_detector.dart:688:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\feature_detector.dart:693:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\feature_detector.dart:698:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\feature_detector.dart:699:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\feature_detector.dart:704:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\feature_detector.dart:709:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\platform_detector.dart:361:9 - Invalid use of 'default' member in a switch. Try enumerating all the possible values of the switch expression. - no_default_cases
   info - lib\src\core\conditional\platform_detector.dart:438:19 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\platform_detector.dart:487:17 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\platform_detector.dart:509:17 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\platform_detector.dart:525:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\platform_detector.dart:534:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\platform_detector.dart:564:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\platform_detector.dart:679:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\platform_detector.dart:684:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\platform_detector.dart:689:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\platform_detector.dart:690:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\platform_detector.dart:700:7 - Invalid use of 'default' member in a switch. Try enumerating all the possible values of the switch expression. - no_default_cases
   info - lib\src\core\conditional\platform_detector.dart:718:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\platform_detector.dart:726:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\platform_detector.dart:746:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\platform_detector.dart:754:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\conditional\smart_recommendation_engine.dart:261:7 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\conditional\smart_recommendation_engine.dart:270:7 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\conditional\smart_recommendation_engine.dart:279:7 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\conditional\smart_recommendation_engine.dart:288:7 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\conditional\smart_recommendation_engine.dart:297:7 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\conditional\smart_recommendation_engine.dart:306:7 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\conditional\smart_recommendation_engine.dart:315:7 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\conditional\smart_recommendation_engine.dart:436:7 - Invalid use of 'default' member in a switch. Try enumerating all the possible values of the switch expression. - no_default_cases
   info - lib\src\core\conditional\smart_recommendation_engine.dart:563:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\conditional\smart_recommendation_engine.dart:649:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\creation\enterprise_template_creator.dart:458:7 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\creation\enterprise_template_creator.dart:491:7 - Invalid use of 'default' member in a switch. Try enumerating all the possible values of the switch expression. - no_default_cases
   info - lib\src\core\creation\enterprise_template_creator.dart:516:16 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\creation\enterprise_template_creator.dart:679:18 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\creation\enterprise_template_creator.dart:688:20 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\creation\enterprise_template_creator.dart:697:20 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\creation\enterprise_template_creator.dart:740:18 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\creation\enterprise_template_creator.dart:804:17 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\creation\enterprise_template_creator.dart:876:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\creation\enterprise_template_creator.dart:893:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\creation\enterprise_template_creator.dart:1078:16 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\creation\enterprise_template_creator.dart:1129:18 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\creation\enterprise_template_creator.dart:1187:16 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\creation\enterprise_template_creator.dart:1205:20 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\creation\enterprise_template_creator.dart:1258:17 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\creation\enterprise_template_creator.dart:1305:16 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\creation\enterprise_template_creator.dart:1315:20 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\creation\enterprise_template_creator.dart:1334:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\creation\template_library_manager.dart:146:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\creation\template_library_manager.dart:304:18 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\creation\template_library_manager.dart:484:17 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\creation\template_library_manager.dart:537:17 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\creation\template_library_manager.dart:565:18 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\creation\template_library_manager.dart:769:16 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\creation\template_library_manager.dart:778:16 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\creation\template_library_manager.dart:791:20 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\distribution\cache_strategy.dart:68:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\distribution\cache_strategy.dart:162:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\distribution\cache_strategy.dart:342:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\distribution\cache_strategy.dart:413:7 - Invalid use of 'default' member in a switch. Try enumerating all the possible values of the switch expression. - no_default_cases
   info - lib\src\core\distribution\cache_strategy.dart:481:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\distribution\cache_strategy.dart:510:16 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\distribution\cache_strategy.dart:544:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\distribution\cache_strategy.dart:600:16 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\distribution\cache_strategy.dart:626:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\distribution\dependency_resolver.dart:56:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\distribution\dependency_resolver.dart:127:3 - The method '==' should not be overridden in classes not annotated with '@immutable'. Try removing the override or annotating the class with '@immutable'. - avoid_equals_and_hash_code_on_mutable_classes
   info - lib\src\core\distribution\dependency_resolver.dart:132:3 - The method 'hashCode' should not be overridden in classes not annotated with '@immutable'. Try removing the override or annotating the class with '@immutable'. - avoid_equals_and_hash_code_on_mutable_classes
   info - lib\src\core\distribution\dependency_resolver.dart:137:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\distribution\dependency_resolver.dart:290:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\distribution\dependency_resolver.dart:337:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\distribution\dependency_resolver.dart:359:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\distribution\dependency_resolver.dart:570:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\distribution\dependency_resolver.dart:588:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\distribution\template_downloader.dart:58:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\distribution\template_downloader.dart:98:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\distribution\template_downloader.dart:140:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\distribution\template_downloader.dart:306:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\distribution\template_downloader.dart:323:16 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\distribution\template_downloader.dart:342:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\distribution\template_downloader.dart:375:45 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\distribution\template_downloader.dart:432:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\distribution\update_manager.dart:74:3 - The constant name 'rolling_back' isn't a lowerCamelCase identifier. Try changing the name to follow the lowerCamelCase style. - constant_identifier_names
   info - lib\src\core\distribution\update_manager.dart:77:3 - The constant name 'rolled_back' isn't a lowerCamelCase identifier. Try changing the name to follow the lowerCamelCase style. - constant_identifier_names
   info - lib\src\core\distribution\update_manager.dart:82:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\distribution\update_manager.dart:134:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\distribution\update_manager.dart:177:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\distribution\update_manager.dart:207:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\distribution\update_manager.dart:420:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\distribution\update_manager.dart:445:16 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\enterprise\access_control.dart:103:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\enterprise\access_control.dart:165:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\enterprise\access_control.dart:229:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\enterprise\access_control.dart:281:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\enterprise\access_control.dart:327:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\enterprise\access_control.dart:372:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\enterprise\access_control.dart:726:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\enterprise\compliance_checker.dart:79:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\enterprise\compliance_checker.dart:133:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\enterprise\compliance_checker.dart:205:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\enterprise\compliance_checker.dart:237:18 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\enterprise\compliance_checker.dart:306:42 - 'bool' parameters should be named parameters. Try converting the parameter to a named parameter. - avoid_positional_boolean_parameters
   info - lib\src\core\enterprise\compliance_checker.dart:545:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\enterprise\compliance_checker.dart:837:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\enterprise\lifecycle_manager.dart:100:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\enterprise\lifecycle_manager.dart:170:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\enterprise\lifecycle_manager.dart:242:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\enterprise\lifecycle_manager.dart:288:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\enterprise\lifecycle_manager.dart:562:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\enterprise\lifecycle_manager.dart:664:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\enterprise\lifecycle_manager.dart:827:7 - Invalid use of 'default' member in a switch. Try enumerating all the possible values of the switch expression. - no_default_cases
   info - lib\src\core\enterprise\private_registry.dart:91:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\enterprise\private_registry.dart:158:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\enterprise\private_registry.dart:212:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\enterprise\private_registry.dart:597:59 - Missing a required trailing comma. Try adding a trailing comma. - require_trailing_commas
   info - lib\src\core\error_handling\error_recovery_system.dart:25:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:26:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:27:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:28:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:33:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:34:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:35:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:36:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:41:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:42:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:43:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:44:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:45:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:50:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:57:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:58:23 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:59:26 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:60:30 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:61:30 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:69:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:75:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:76:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:77:33 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:78:14 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:83:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:93:29 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:105:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:106:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:107:18 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:108:30 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:109:22 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:110:22 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:111:29 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:113:24 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:126:11 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\error_recovery_system.dart:312:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\error_recovery_system.dart:313:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\error_recovery_system.dart:314:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\error_recovery_system.dart:319:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\error_recovery_system.dart:321:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\error_recovery_system.dart:332:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\error_recovery_system.dart:337:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\error_recovery_system.dart:347:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\error_recovery_system.dart:353:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\error_recovery_system.dart:356:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\error_recovery_system.dart:359:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\error_recovery_system.dart:363:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\error_recovery_system.dart:370:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\error_recovery_system.dart:371:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\error_recovery_system.dart:372:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\error_recovery_system.dart:376:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\error_recovery_system.dart:380:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\error_recovery_system.dart:382:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:27:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:28:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:29:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:30:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:31:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:32:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:33:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:34:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:35:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:40:14 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:41:21 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:42:15 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:43:28 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:44:20 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:45:12 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:53:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:65:22 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:83:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:96:17 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:97:17 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:135:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:148:17 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:149:17 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:168:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:181:17 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:182:17 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:201:11 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\error_handling\exception_handler.dart:247:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:266:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:288:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:289:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:304:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:307:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:311:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:313:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:325:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:326:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:328:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:329:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:331:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:332:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:333:7 - Invalid use of 'default' member in a switch. Try enumerating all the possible values of the switch expression. - no_default_cases
   info - lib\src\core\error_handling\exception_handler.dart:383:7 - Invalid use of 'default' member in a switch. Try enumerating all the possible values of the switch expression. - no_default_cases
   info - lib\src\core\error_handling\exception_handler.dart:417:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:418:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:457:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:461:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:462:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:464:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:465:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:467:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:468:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:470:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:471:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:472:7 - Invalid use of 'default' member in a switch. Try enumerating all the possible values of the switch expression. - no_default_cases
   info - lib\src\core\error_handling\exception_handler.dart:473:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:474:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:475:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\error_handling\exception_handler.dart:539:7 - Invalid use of 'default' member in a switch. Try enumerating all the possible values of the switch expression. - no_default_cases
   info - lib\src\core\error_handling\exception_handler.dart:558:26 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\index.dart:56:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\index.dart:63:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\index.dart:67:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\index.dart:75:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\inheritance\composition_engine.dart:126:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\inheritance\inheritance_validator.dart:92:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\inheritance\inheritance_validator.dart:196:7 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\inheritance\inheritance_validator.dart:199:7 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\inheritance\inheritance_validator.dart:412:7 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\inheritance\inheritance_validator.dart:458:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\library\library_index.dart:411:7 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\library\library_manager.dart:310:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\library\library_manager.dart:723:18 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\library\library_manager.dart:754:18 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\library\metadata_manager.dart:730:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\library\metadata_manager.dart:738:5 - The parameter 'metadataPath' is not used in the constructor. Try using the parameter or removing it. - avoid_unused_constructor_parameters
   info - lib\src\core\library\version_manager.dart:47:3 - The constant name 'release_candidate' isn't a lowerCamelCase identifier. Try changing the name to follow the lowerCamelCase style. - constant_identifier_names
   info - lib\src\core\library\version_manager.dart:234:3 - The method '==' should not be overridden in classes not annotated with '@immutable'. Try removing the override or annotating the class with '@immutable'. - avoid_equals_and_hash_code_on_mutable_classes
   info - lib\src\core\library\version_manager.dart:239:3 - The method 'hashCode' should not be overridden in classes not annotated with '@immutable'. Try removing the override or annotating the class with '@immutable'. - avoid_equals_and_hash_code_on_mutable_classes
   info - lib\src\core\library\version_manager.dart:385:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\library\version_manager.dart:553:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\library\version_manager.dart:733:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\library\version_manager.dart:790:18 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\library\version_manager.dart:831:18 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\library\version_manager.dart:882:22 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\library\version_manager.dart:910:7 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\managers\async_manager.dart:271:45 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\async_manager.dart:282:19 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\cache_manager.dart:130:17 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\cache_manager.dart:140:17 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\cache_manager.dart:180:17 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\hook_manager.dart:84:34 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\hook_manager.dart:90:21 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\hook_manager.dart:110:11 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\hook_manager.dart:110:11 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\hook_manager.dart:126:11 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\hook_manager.dart:126:11 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\hook_manager.dart:167:9 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\hook_manager.dart:167:9 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\hook_manager.dart:169:9 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\hook_manager.dart:169:9 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\hook_manager.dart:172:30 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\hook_manager.dart:172:30 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\hook_manager.dart:172:30 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\hook_manager.dart:178:9 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\hook_manager.dart:178:9 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\hook_manager.dart:186:31 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\hook_manager.dart:187:32 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\hook_manager.dart:188:22 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\hook_manager.dart:188:22 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\hook_manager.dart:188:40 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\ux_manager.dart:96:43 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\ux_manager.dart:157:17 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\ux_manager.dart:170:21 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\ux_manager.dart:279:11 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\ux_manager.dart:284:21 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\ux_manager.dart:284:21 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\ux_manager.dart:287:32 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\ux_manager.dart:288:32 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\ux_manager.dart:307:13 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\ux_manager.dart:352:11 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\ux_manager.dart:358:17 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\ux_manager.dart:387:11 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\ux_manager.dart:563:34 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\ux_manager.dart:566:26 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\managers\ux_manager.dart:649:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:655:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:656:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:657:18 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:662:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:668:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:669:17 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:670:18 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:671:11 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:672:13 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:673:11 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:708:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\managers\ux_manager.dart:723:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:729:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:730:18 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:731:18 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:736:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:744:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:745:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:746:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:747:30 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:748:28 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:752:27 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:752:32 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:752:40 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:756:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:762:14 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:763:17 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\managers\ux_manager.dart:764:23 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\network\bandwidth_manager.dart:66:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\network\bandwidth_manager.dart:110:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\network\bandwidth_manager.dart:244:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\network\bandwidth_manager.dart:251:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\network\bandwidth_manager.dart:335:8 - The method is used to change a property. Try converting the method to a setter. - use_setters_to_change_properties
   info - lib\src\core\network\bandwidth_manager.dart:335:19 - 'bool' parameters should be named parameters. Try converting the parameter to a named parameter. - avoid_positional_boolean_parameters
   info - lib\src\core\network\bandwidth_manager.dart:421:9 - Use an if-null operator to convert a 'null' to a 'bool'. Try using an if-null operator. - use_if_null_to_convert_nulls_to_bools
   info - lib\src\core\network\http_client.dart:67:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\network\http_client.dart:113:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\network\http_client.dart:165:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\network\http_client.dart:263:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\network\offline_support.dart:84:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\network\offline_support.dart:141:27 - Static method should be a constructor. Try converting the method into a constructor. - prefer_constructors_over_static_methods
   info - lib\src\core\network\offline_support.dart:155:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\network\offline_support.dart:156:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\network\offline_support.dart:157:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\network\offline_support.dart:165:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\network\offline_support.dart:216:21 - Static method should be a constructor. Try converting the method into a constructor. - prefer_constructors_over_static_methods
   info - lib\src\core\network\offline_support.dart:226:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\network\offline_support.dart:227:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\network\offline_support.dart:237:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\network\retry_strategy.dart:71:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\network\retry_strategy.dart:113:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\network\retry_strategy.dart:176:5 - 'bool' parameters should be named parameters. Try converting the parameter to a named parameter. - avoid_positional_boolean_parameters
   info - lib\src\core\network\retry_strategy.dart:541:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\network\retry_strategy.dart:542:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\parameters\enterprise_parameter_validator.dart:425:7 - Invalid use of 'default' member in a switch. Try enumerating all the possible values of the switch expression. - no_default_cases
   info - lib\src\core\parameters\enterprise_parameter_validator.dart:532:9 - Invalid use of 'default' member in a switch. Try enumerating all the possible values of the switch expression. - no_default_cases
   info - lib\src\core\parameters\enterprise_template_parameter.dart:21:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\parameters\enterprise_template_parameter.dart:22:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\parameters\enterprise_template_parameter.dart:23:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\parameters\enterprise_template_parameter.dart:24:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\parameters\enterprise_template_parameter.dart:28:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\parameters\enterprise_template_parameter.dart:29:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\parameters\enterprise_template_parameter.dart:30:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\parameters\enterprise_template_parameter.dart:34:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\parameters\enterprise_template_parameter.dart:35:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\parameters\enterprise_template_parameter.dart:36:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\parameters\enterprise_template_parameter.dart:40:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\parameters\enterprise_template_parameter.dart:41:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\parameters\enterprise_template_parameter.dart:42:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\parameters\enterprise_template_parameter.dart:477:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\parameters\parameter_preset_manager.dart:199:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\parameters\parameter_preset_manager.dart:264:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\parameters\parameter_preset_manager.dart:617:16 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\parameters\parameter_preset_manager.dart:629:16 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\parameters\parameter_preset_manager.dart:646:16 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\parameters\parameter_preset_manager.dart:681:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\parameters\parameter_preset_manager.dart:689:16 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\parameters\smart_parameter_collector.dart:448:7 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\parameters\smart_parameter_collector.dart:595:17 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\parameters\smart_parameter_collector.dart:643:9 - Invalid use of 'default' member in a switch. Try enumerating all the possible values of the switch expression. - no_default_cases
   info - lib\src\core\performance\memory_optimizer.dart:310:23 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\performance\memory_optimizer.dart:311:17 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\performance\memory_optimizer.dart:318:12 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\performance\memory_optimizer.dart:443:40 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\performance\performance_optimizer.dart:335:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\performance\performance_optimizer.dart:338:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\performance\performance_optimizer.dart:350:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\performance\performance_optimizer.dart:353:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\performance\performance_optimizer.dart:364:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\performance\performance_optimizer.dart:367:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\performance\performance_optimizer.dart:378:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\performance\performance_optimizer.dart:381:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\performance\performance_optimizer.dart:392:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\performance\performance_optimizer.dart:395:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\performance\startup_optimizer.dart:301:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\registry\metadata_standard.dart:50:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\metadata_standard.dart:58:23 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\metadata_standard.dart:83:24 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\metadata_standard.dart:96:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\metadata_standard.dart:104:23 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\metadata_standard.dart:129:24 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\metadata_standard.dart:142:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\metadata_standard.dart:151:24 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\metadata_standard.dart:181:24 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\metadata_standard.dart:195:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\metadata_standard.dart:203:26 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\metadata_standard.dart:228:24 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\metadata_standard.dart:241:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\metadata_standard.dart:248:29 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\metadata_standard.dart:275:24 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\metadata_standard.dart:287:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\metadata_standard.dart:295:28 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\metadata_standard.dart:320:24 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\metadata_standard.dart:333:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\metadata_standard.dart:359:30 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\metadata_standard.dart:465:24 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\registry_client.dart:39:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\registry_client.dart:77:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\registry_client.dart:107:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\registry_client.dart:146:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\registry_client.dart:196:8 - The method is used to change a property. Try converting the method to a setter. - use_setters_to_change_properties
   info - lib\src\core\registry\registry_client.dart:201:8 - The method is used to change a property. Try converting the method to a setter. - use_setters_to_change_properties
   info - lib\src\core\registry\registry_client.dart:297:7 - Assignment could be inlined in 'return' statement. Try inlining the assigned value in the 'return' statement. - join_return_with_assignment
   info - lib\src\core\registry\registry_client.dart:321:23 - To-do comment doesn't follow the Flutter style. Try following the Flutter style for to-do comments. - flutter_style_todos
   info - lib\src\core\registry\registry_client.dart:487:11 - To-do comment doesn't follow the Flutter style. Try following the Flutter style for to-do comments. - flutter_style_todos
   info - lib\src\core\registry\registry_client.dart:520:7 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\registry\registry_data_service.dart:21:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\registry_data_service.dart:51:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\registry_data_service.dart:77:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\registry_index.dart:21:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\registry_index.dart:43:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\registry_index.dart:69:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\registry_index.dart:127:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\registry_index.dart:128:18 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\registry_index.dart:129:18 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\registry_index.dart:134:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\registry_index.dart:309:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\registry\registry_index.dart:316:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\registry\registry_index.dart:375:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\registry\registry_index.dart:376:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\registry\registry_index.dart:377:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\registry\registry_index.dart:378:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\registry\registry_index.dart:379:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\registry\registry_index.dart:790:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\registry\registry_index.dart:803:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\registry\registry_index.dart:816:11 - Use an if-null operator to convert a 'null' to a 'bool'. Try using an if-null operator. - use_if_null_to_convert_nulls_to_bools
   info - lib\src\core\registry\registry_index.dart:825:9 - Use an if-null operator to convert a 'null' to a 'bool'. Try using an if-null operator. - use_if_null_to_convert_nulls_to_bools
   info - lib\src\core\registry\registry_index.dart:834:11 - Use an if-null operator to convert a 'null' to a 'bool'. Try using an if-null operator. - use_if_null_to_convert_nulls_to_bools
   info - lib\src\core\registry\template_registry.dart:49:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\template_registry.dart:133:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\template_registry.dart:167:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\template_registry.dart:198:18 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\registry\template_registry.dart:337:5 - To-do comment doesn't follow the Flutter style. Try following the Flutter style for to-do comments. - flutter_style_todos
   info - lib\src\core\registry\template_registry.dart:347:7 - To-do comment doesn't follow the Flutter style. Try following the Flutter style for to-do comments. - flutter_style_todos
   info - lib\src\core\security\digital_signature.dart:63:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security\digital_signature.dart:117:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security\digital_signature.dart:153:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security\digital_signature.dart:179:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security\digital_signature.dart:541:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\security\malware_detector.dart:88:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security\malware_detector.dart:140:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security\malware_detector.dart:190:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security\malware_detector.dart:234:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security\malware_detector.dart:343:18 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\security\malware_detector.dart:490:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\security\malware_detector.dart:559:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\security\security_validator.dart:66:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security\security_validator.dart:137:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security\security_validator.dart:179:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security\security_validator.dart:370:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\security\security_validator.dart:503:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\security\security_validator.dart:529:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\security\security_validator.dart:734:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\security\trusted_source_manager.dart:55:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security\trusted_source_manager.dart:164:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security\trusted_source_manager.dart:198:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\compatibility_manager.dart:23:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\compatibility_manager.dart:24:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\compatibility_manager.dart:25:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\compatibility_manager.dart:26:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\compatibility_manager.dart:31:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\compatibility_manager.dart:57:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\compatibility_manager.dart:91:11 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\compatibility_manager.dart:318:29 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\security_system\compatibility_manager.dart:490:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\compatibility_manager.dart:516:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\compatibility_manager.dart:550:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\compatibility_manager.dart:576:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\compatibility_manager.dart:599:24 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\dependency_security_checker.dart:26:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\dependency_security_checker.dart:27:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\dependency_security_checker.dart:28:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\dependency_security_checker.dart:29:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\dependency_security_checker.dart:30:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\dependency_security_checker.dart:35:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\dependency_security_checker.dart:66:24 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\dependency_security_checker.dart:79:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\dependency_security_checker.dart:114:24 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\dependency_security_checker.dart:128:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\dependency_security_checker.dart:159:24 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\dependency_security_checker.dart:174:11 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\dependency_security_checker.dart:354:5 - Invalid assignment to the parameter 'version1'. Try using a local variable in place of the parameter. - parameter_assignments
   info - lib\src\core\security_system\dependency_security_checker.dart:355:5 - Invalid assignment to the parameter 'version2'. Try using a local variable in place of the parameter. - parameter_assignments
   info - lib\src\core\security_system\dependency_security_checker.dart:481:7 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\security_system\dependency_security_checker.dart:482:7 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\security_system\file_security_manager.dart:26:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\file_security_manager.dart:27:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\file_security_manager.dart:28:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\file_security_manager.dart:29:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\file_security_manager.dart:30:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\file_security_manager.dart:31:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\file_security_manager.dart:36:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\file_security_manager.dart:95:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\file_security_manager.dart:126:24 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\file_security_manager.dart:139:11 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\file_security_manager.dart:251:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\security_system\security_validator.dart:23:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\security_validator.dart:24:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\security_validator.dart:25:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\security_validator.dart:26:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\security_system\security_validator.dart:31:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\strategies\default_hooks.dart:21:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\strategies\default_hooks.dart:55:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\strategies\error_recovery_strategies.dart:149:17 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\strategies\error_recovery_strategies.dart:220:17 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\strategies\error_recovery_strategies.dart:619:7 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\system_management\cache_manager.dart:27:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\cache_manager.dart:28:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\cache_manager.dart:29:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\cache_manager.dart:30:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\cache_manager.dart:35:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\cache_manager.dart:44:9 - Use an initializing formal to assign a parameter to a field. Try using an initialing formal ('this.expiresAt') to initialize the field. - prefer_initializing_formals
   info - lib\src\core\system_management\cache_manager.dart:84:24 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\cache_manager.dart:124:24 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\cache_manager.dart:137:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\cache_manager.dart:272:7 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\system_management\cache_manager.dart:374:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\cache_manager.dart:487:30 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\system_management\cache_manager.dart:556:11 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\cache_manager.dart:616:9 - To-do comment doesn't follow the Flutter style. Try following the Flutter style for to-do comments. - flutter_style_todos
   info - lib\src\core\system_management\extension_manager.dart:24:11 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\extension_manager.dart:237:9 - Invalid use of 'default' member in a switch. Try enumerating all the possible values of the switch expression. - no_default_cases
   info - lib\src\core\system_management\extension_manager.dart:368:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\extension_manager.dart:383:24 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\extension_manager.dart:424:24 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\performance_monitor.dart:24:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\performance_monitor.dart:25:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\performance_monitor.dart:26:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\performance_monitor.dart:27:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\performance_monitor.dart:28:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\performance_monitor.dart:29:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\performance_monitor.dart:34:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\performance_monitor.dart:43:29 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\performance_monitor.dart:74:24 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\performance_monitor.dart:86:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\performance_monitor.dart:113:24 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\performance_monitor.dart:125:11 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\performance_monitor.dart:369:7 - Invalid use of 'default' member in a switch. Try enumerating all the possible values of the switch expression. - no_default_cases
   info - lib\src\core\system_management\performance_monitor.dart:428:7 - Invalid use of 'default' member in a switch. Try enumerating all the possible values of the switch expression. - no_default_cases
   info - lib\src\core\system_management\performance_monitor.dart:458:7 - Invalid use of 'default' member in a switch. Try enumerating all the possible values of the switch expression. - no_default_cases
   info - lib\src\core\system_management\resource_manager.dart:24:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\resource_manager.dart:25:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\resource_manager.dart:26:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\resource_manager.dart:27:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\resource_manager.dart:28:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\resource_manager.dart:29:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\resource_manager.dart:34:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\resource_manager.dart:35:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\resource_manager.dart:36:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\resource_manager.dart:37:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\resource_manager.dart:42:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\resource_manager.dart:94:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\resource_manager.dart:121:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\resource_manager.dart:148:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\resource_manager.dart:175:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\resource_manager.dart:202:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\system_management\resource_manager.dart:240:7 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\system_management\resource_manager.dart:340:11 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\template_creator\configuration_wizard.dart:438:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:473:7 - Invalid use of 'default' member in a switch. Try enumerating all the possible values of the switch expression. - no_default_cases
   info - lib\src\core\template_creator\configuration_wizard.dart:499:7 - Invalid use of 'default' member in a switch. Try enumerating all the possible values of the switch expression. - no_default_cases
   info - lib\src\core\template_creator\configuration_wizard.dart:530:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:531:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:532:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:533:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:534:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:535:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:540:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:541:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:546:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:550:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:555:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:563:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:564:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:565:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:566:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:567:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:568:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:569:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:574:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:576:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:577:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:578:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:579:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:580:16 - Use an if-null operator to convert a 'null' to a 'bool'. Try using an if-null operator. - use_if_null_to_convert_nulls_to_bools
   info - lib\src\core\template_creator\configuration_wizard.dart:582:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:583:16 - Use an if-null operator to convert a 'null' to a 'bool'. Try using an if-null operator. - use_if_null_to_convert_nulls_to_bools
   info - lib\src\core\template_creator\configuration_wizard.dart:583:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\template_creator\configuration_wizard.dart:585:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:586:16 - Use an if-null operator to convert a 'null' to a 'bool'. Try using an if-null operator. - use_if_null_to_convert_nulls_to_bools
   info - lib\src\core\template_creator\configuration_wizard.dart:586:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\template_creator\configuration_wizard.dart:588:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:589:18 - Use an if-null operator to convert a 'null' to a 'bool'. Try using an if-null operator. - use_if_null_to_convert_nulls_to_bools
   info - lib\src\core\template_creator\configuration_wizard.dart:589:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\template_creator\configuration_wizard.dart:593:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\configuration_wizard.dart:595:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\template_creator\template_scaffold.dart:233:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\template_creator\template_validator.dart:102:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\template_creator\template_validator.dart:312:16 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\template_creator\template_validator.dart:329:18 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\template_creator\template_validator.dart:346:18 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\template_creator\template_validator.dart:385:16 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\template_creator\template_validator.dart:545:15 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\template_creator\template_validator.dart:565:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\template_creator\template_validator.dart:654:7 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\template_creator\template_validator.dart:670:16 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\template_creator\template_validator.dart:684:16 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\template_creator\template_validator.dart:697:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\template_creator\template_validator.dart:705:16 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\template_creator\template_validator.dart:719:16 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\template_engine\template_engine.dart:1568:7 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\template_engine\template_engine.dart:1571:7 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\template_engine\template_engine.dart:1574:7 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\template_engine\template_engine.dart:1596:9 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\template_engine\template_engine.dart:1606:7 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\template_engine\template_engine.dart:1620:9 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\template_engine\template_engine.dart:1623:9 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\template_engine\template_engine.dart:1630:9 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\template_engine\template_engine.dart:1635:9 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\template_engine\template_engine.dart:1640:7 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\template_engine\template_engine.dart:1659:9 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\template_engine\template_engine.dart:1662:9 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\template_engine\template_engine.dart:1668:9 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\template_engine\template_engine.dart:1672:9 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\template_engine\template_engine.dart:1677:7 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\template_engine\template_engine.dart:1679:9 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\template_engine\template_engine.dart:1685:7 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\template_engine\template_engine.dart:1686:7 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\template_engine\template_engine.dart:1691:9 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\template_engine\template_engine.dart:1695:7 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\template_engine\template_engine.dart:1891:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\template_engine\template_engine.dart:2294:7 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\core\template_engine\template_exceptions.dart:228:40 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - lib\src\core\template_system\index.dart:18:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\template_system\template_metadata.dart:342:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\core\template_system\template_metadata.dart:346:3 - The method '==' should not be overridden in classes not annotated with '@immutable'. Try removing the override or annotating the class with '@immutable'. - avoid_equals_and_hash_code_on_mutable_classes
   info - lib\src\core\template_system\template_metadata.dart:354:3 - The method 'hashCode' should not be overridden in classes not annotated with '@immutable'. Try removing the override or annotating the class with '@immutable'. - avoid_equals_and_hash_code_on_mutable_classes
   info - lib\src\core\template_system\template_registry.dart:201:18 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\template_system\template_registry.dart:273:18 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\template_system\template_registry.dart:425:18 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\template_system\template_registry.dart:458:21 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\template_system\template_registry.dart:539:27 - To-do comment doesn't follow the Flutter style. Try following the Flutter style for to-do comments. - flutter_style_todos
   info - lib\src\core\template_system\template_registry.dart:555:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\template_system\template_registry.dart:561:26 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\template_system\template_registry.dart:562:34 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\template_system\template_registry.dart:563:29 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\template_system\template_registry.dart:564:28 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\template_system\template_registry.dart:569:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\template_system\template_registry.dart:575:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\template_system\template_registry.dart:576:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\template_system\template_registry.dart:577:24 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\template_system\template_registry.dart:578:17 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\template_system\template_registry.dart:583:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\template_system\template_registry.dart:584:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\template_system\template_registry.dart:585:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\template_system\template_registry.dart:590:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\template_system\template_registry.dart:596:18 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\template_system\template_registry.dart:597:13 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\template_system\template_registry.dart:598:13 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\template_system\template_registry.dart:599:13 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\template_system\template_registry.dart:604:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\template_system\template_registry.dart:610:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\template_system\template_registry.dart:611:17 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\template_system\template_registry.dart:612:22 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\template_system\template_registry.dart:613:29 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\validation_system\diagnostic_system.dart:25:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\validation_system\diagnostic_system.dart:26:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\validation_system\diagnostic_system.dart:27:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\validation_system\diagnostic_system.dart:28:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\validation_system\diagnostic_system.dart:33:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\validation_system\diagnostic_system.dart:43:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\validation_system\diagnostic_system.dart:44:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\validation_system\diagnostic_system.dart:45:25 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\validation_system\diagnostic_system.dart:46:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\validation_system\diagnostic_system.dart:47:17 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\validation_system\diagnostic_system.dart:48:22 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\validation_system\diagnostic_system.dart:49:14 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\validation_system\diagnostic_system.dart:50:34 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\validation_system\diagnostic_system.dart:52:14 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\validation_system\diagnostic_system.dart:65:24 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\validation_system\diagnostic_system.dart:78:14 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\validation_system\diagnostic_system.dart:79:14 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\validation_system\diagnostic_system.dart:80:34 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\validation_system\diagnostic_system.dart:231:26 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - lib\src\core\validation_system\diagnostic_system.dart:490:11 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\core\validation_system\diagnostic_system.dart:554:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\validation_system\diagnostic_system.dart:566:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\validation_system\diagnostic_system.dart:569:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\validation_system\diagnostic_system.dart:572:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\validation_system\diagnostic_system.dart:576:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\validation_system\diagnostic_system.dart:578:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\validation_system\diagnostic_system.dart:583:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\validation_system\diagnostic_system.dart:599:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\validation_system\diagnostic_system.dart:600:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\validation_system\diagnostic_system.dart:601:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\validation_system\diagnostic_system.dart:602:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\validation_system\diagnostic_system.dart:603:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\validation_system\diagnostic_system.dart:612:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\validation_system\diagnostic_system.dart:616:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\validation_system\diagnostic_system.dart:621:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\validation_system\diagnostic_system.dart:624:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\validation_system\diagnostic_system.dart:627:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\validation_system\diagnostic_system.dart:630:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\validation_system\diagnostic_system.dart:634:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\validation_system\diagnostic_system.dart:653:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - lib\src\core\validation_system\validation_report_generator.dart:254:24 - Missing a newline at the beginning of a multiline string. Try adding a newline at the beginning of the string. - leading_newlines_in_multiline_strings
   info - lib\src\core\validation_system\validation_report_generator.dart:306:29 - Missing a newline at the beginning of a multiline string. Try adding a newline at the beginning of the string. - leading_newlines_in_multiline_strings
   info - lib\src\interfaces\extension_interface.dart:18:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:19:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:20:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:21:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:22:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:23:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:29:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:129:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:130:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:131:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:132:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:133:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:134:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:176:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:198:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:223:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:224:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:225:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:226:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:250:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:272:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:290:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:329:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:355:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:380:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:402:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:454:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\interfaces\extension_interface.dart:472:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\models\user_config.g.dart:12:55 - Missing a required trailing comma. Try adding a trailing comma. - require_trailing_commas
   info - lib\src\models\user_config.g.dart:19:56 - Missing a required trailing comma. Try adding a trailing comma. - require_trailing_commas
   info - lib\src\models\workspace_config.g.dart:26:60 - Missing a required trailing comma. Try adding a trailing comma. - require_trailing_commas
   info - lib\src\models\workspace_config.g.dart:33:59 - Missing a required trailing comma. Try adding a trailing comma. - require_trailing_commas
   info - lib\src\models\workspace_config.g.dart:37:58 - Missing a required trailing comma. Try adding a trailing comma. - require_trailing_commas
   info - lib\src\models\workspace_config.g.dart:68:52 - Unnecessary use of a null check ('!'). Try removing the null check. - unnecessary_null_checks
   info - lib\src\models\workspace_config.g.dart:88:57 - Unnecessary use of a null check ('!'). Try removing the null check. - unnecessary_null_checks
   info - lib\src\models\workspace_config.g.dart:146:64 - Missing a required trailing comma. Try adding a trailing comma. - require_trailing_commas
   info - lib\src\models\workspace_config.g.dart:150:66 - Missing a required trailing comma. Try adding a trailing comma. - require_trailing_commas
   info - lib\src\models\workspace_config.g.dart:154:66 - Missing a required trailing comma. Try adding a trailing comma. - require_trailing_commas
   info - lib\src\models\workspace_config.g.dart:188:37 - Missing a required trailing comma. Try adding a trailing comma. - require_trailing_commas
   info - lib\src\models\workspace_config.g.dart:192:65 - Unnecessary use of a null check ('!'). Try removing the null check. - unnecessary_null_checks
   info - lib\src\models\workspace_config.g.dart:205:55 - Missing a required trailing comma. Try adding a trailing comma. - require_trailing_commas
   info - lib\src\models\workspace_config.g.dart:208:56 - Missing a required trailing comma. Try adding a trailing comma. - require_trailing_commas
   info - lib\src\models\workspace_config.g.dart:227:54 - Unnecessary use of a null check ('!'). Try removing the null check. - unnecessary_null_checks
   info - lib\src\models\workspace_config.g.dart:255:37 - Missing a required trailing comma. Try adding a trailing comma. - require_trailing_commas
   info - lib\src\models\workspace_config.g.dart:258:62 - Unnecessary use of a null check ('!'). Try removing the null check. - unnecessary_null_checks
   info - lib\src\models\workspace_config.g.dart:286:66 - Missing a required trailing comma. Try adding a trailing comma. - require_trailing_commas
   info - lib\src\models\workspace_config.g.dart:295:76 - Unnecessary use of a null check ('!'). Try removing the null check. - unnecessary_null_checks
   info - lib\src\models\workspace_config.g.dart:306:34 - Missing a required trailing comma. Try adding a trailing comma. - require_trailing_commas
   info - lib\src\models\workspace_config.g.dart:321:40 - Missing a required trailing comma. Try adding a trailing comma. - require_trailing_commas
   info - lib\src\models\workspace_config.g.dart:330:34 - Missing a required trailing comma. Try adding a trailing comma. - require_trailing_commas
   info - lib\src\models\workspace_config.g.dart:341:42 - Missing a required trailing comma. Try adding a trailing comma. - require_trailing_commas
   info - lib\src\models\workspace_config.g.dart:356:64 - Missing a required trailing comma. Try adding a trailing comma. - require_trailing_commas
   info - lib\src\models\workspace_config.g.dart:360:37 - Missing a required trailing comma. Try adding a trailing comma. - require_trailing_commas
   info - lib\src\utils\enhanced_error_handler.dart:22:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:23:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:24:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:25:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:30:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:31:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:32:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:33:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:34:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:35:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:36:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:37:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:42:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:43:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:44:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:45:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:50:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:63:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:64:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:65:23 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:66:23 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:67:17 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:68:22 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:69:24 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:70:17 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:71:26 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:72:17 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:77:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:84:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:85:33 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:86:14 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:87:14 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:92:23 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_error_handler.dart:112:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\utils\enhanced_error_handler.dart:173:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\utils\enhanced_error_handler.dart:343:7 - Invalid use of 'default' member in a switch. Try enumerating all the possible values of the switch expression. - no_default_cases
   info - lib\src\utils\enhanced_progress.dart:22:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:23:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:24:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:25:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:26:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:31:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:32:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:33:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:34:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:39:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:50:23 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:51:14 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:52:14 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:53:14 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:54:14 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:55:13 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:56:13 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:57:14 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:62:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:68:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:69:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:70:17 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:71:8 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:72:8 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:73:13 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:74:13 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:75:11 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:80:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:86:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:87:32 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:88:28 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\enhanced_progress.dart:138:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\utils\enhanced_progress.dart:160:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\utils\enhanced_progress.dart:161:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\utils\enhanced_progress.dart:187:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\utils\enhanced_progress.dart:188:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\utils\enhanced_progress.dart:189:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - lib\src\utils\smart_help_system.dart:20:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\smart_help_system.dart:21:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\smart_help_system.dart:22:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\smart_help_system.dart:23:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\smart_help_system.dart:24:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\smart_help_system.dart:25:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\smart_help_system.dart:30:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\smart_help_system.dart:31:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\smart_help_system.dart:32:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\smart_help_system.dart:33:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\smart_help_system.dart:38:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\smart_help_system.dart:39:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\smart_help_system.dart:40:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\smart_help_system.dart:41:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\smart_help_system.dart:42:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\smart_help_system.dart:43:3 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\smart_help_system.dart:48:9 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\smart_help_system.dart:59:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\smart_help_system.dart:60:16 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\smart_help_system.dart:61:25 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\smart_help_system.dart:62:24 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\smart_help_system.dart:63:22 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\smart_help_system.dart:64:22 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\smart_help_system.dart:65:22 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\smart_help_system.dart:66:22 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - lib\src\utils\smart_help_system.dart:82:15 - The method is used to change a property. Try converting the method to a setter. - use_setters_to_change_properties
   info - lib\src\utils\smart_help_system.dart:314:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\utils\smart_help_system.dart:315:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\utils\smart_help_system.dart:437:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\version.dart:88:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - lib\src\version.dart:172:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - pubspec.yaml:20:3 - Dependencies not sorted alphabetically. Try sorting the dependencies alphabetically (A to Z). - sort_pub_dependencies
   info - pubspec.yaml:27:3 - Dependencies not sorted alphabetically. Try sorting the dependencies alphabetically (A to Z). - sort_pub_dependencies
   info - scripts\release.dart:31:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:41:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:55:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:56:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:57:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:59:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:66:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:73:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:83:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:84:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:86:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:89:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:94:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:109:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:110:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:115:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:140:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:141:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:142:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:146:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:147:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:152:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:184:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:185:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:190:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:215:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:216:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:221:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:224:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:225:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:226:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:227:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:228:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:229:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:230:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:231:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:232:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:233:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:234:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:235:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:236:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:237:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:238:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:239:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:240:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:241:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:242:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:243:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:245:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:246:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:247:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:248:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:249:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:250:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:251:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:252:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:253:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:256:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:257:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:258:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:259:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:260:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:261:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\release.dart:266:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\release.dart:267:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\version_manager.dart:125:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\version_manager.dart:140:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - scripts\version_manager.dart:145:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\version_manager.dart:157:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\version_manager.dart:162:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - scripts\version_manager.dart:190:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\version_manager.dart:191:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\version_manager.dart:192:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\version_manager.dart:193:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\version_manager.dart:194:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\version_manager.dart:195:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\version_manager.dart:195:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - scripts\version_manager.dart:196:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\version_manager.dart:197:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\version_manager.dart:198:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\version_manager.dart:213:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\version_manager.dart:214:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\version_manager.dart:215:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\version_manager.dart:216:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\version_manager.dart:217:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\version_manager.dart:218:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\version_manager.dart:219:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\version_manager.dart:220:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\version_manager.dart:221:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\version_manager.dart:222:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\version_manager.dart:223:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\version_manager.dart:224:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\version_manager.dart:225:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\version_manager.dart:226:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\version_manager.dart:227:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\version_manager.dart:228:5 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - scripts\version_manager.dart:271:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\version_manager.dart:272:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\version_manager.dart:273:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\version_manager.dart:274:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\version_manager.dart:275:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\version_manager.dart:276:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\version_manager.dart:277:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\version_manager.dart:278:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\version_manager.dart:279:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\version_manager.dart:280:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\version_manager.dart:290:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\version_manager.dart:294:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\version_manager.dart:310:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\version_manager.dart:328:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\version_manager.dart:330:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\version_manager.dart:335:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\version_manager.dart:338:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - scripts\version_manager.dart:342:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\complete_acceptance_test.dart:57:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\complete_acceptance_test.dart:58:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\complete_acceptance_test.dart:65:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\complete_acceptance_test.dart:82:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\complete_acceptance_test.dart:96:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\complete_acceptance_test.dart:122:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\complete_acceptance_test.dart:161:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\complete_acceptance_test.dart:201:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\complete_acceptance_test.dart:202:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\complete_acceptance_test.dart:211:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\complete_acceptance_test.dart:235:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\complete_acceptance_test.dart:305:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\complete_acceptance_test.dart:333:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\complete_acceptance_test.dart:404:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\complete_acceptance_test.dart:463:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\complete_acceptance_test.dart:530:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\complete_acceptance_test.dart:587:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\complete_acceptance_test.dart:588:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\complete_acceptance_test.dart:622:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\complete_acceptance_test.dart:666:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\config_system_integration_test.dart:226:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - test\integration\core_acceptance_test.dart:34:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:35:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:42:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:60:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:74:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:87:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:101:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:131:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:154:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:155:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:157:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:165:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:166:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:177:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:197:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:198:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:208:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:249:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:250:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:255:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:267:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:268:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:281:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:282:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:288:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:330:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:331:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:342:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:343:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:350:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:361:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:362:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:367:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:384:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:419:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:441:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:442:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:469:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\core_acceptance_test.dart:470:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\cross_platform_test.dart:38:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\cross_platform_test.dart:39:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\cross_platform_test.dart:46:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\cross_platform_test.dart:61:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\cross_platform_test.dart:77:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\cross_platform_test.dart:109:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\cross_platform_test.dart:135:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\cross_platform_test.dart:156:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\cross_platform_test.dart:172:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\cross_platform_test.dart:218:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\cross_platform_test.dart:238:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\cross_platform_test.dart:257:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\cross_platform_test.dart:267:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\cross_platform_test.dart:277:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:41:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:66:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:81:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:113:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:140:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:165:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:196:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:234:15 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:238:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:266:15 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:270:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:302:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:321:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:345:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:356:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:357:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:371:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:372:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:373:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:374:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:375:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:378:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:379:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:380:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:381:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:382:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:383:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\documentation_system_test.dart:394:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\end_to_end_validation_test.dart:41:7 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\end_to_end_validation_test.dart:92:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\end_to_end_validation_test.dart:100:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\end_to_end_validation_test.dart:103:11 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\end_to_end_validation_test.dart:135:11 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\end_to_end_validation_test.dart:148:11 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\end_to_end_validation_test.dart:233:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\end_to_end_validation_test.dart:239:11 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\end_to_end_validation_test.dart:265:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\end_to_end_validation_test.dart:280:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\end_to_end_validation_test.dart:293:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\end_to_end_validation_test.dart:296:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\end_to_end_validation_test.dart:299:11 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\end_to_end_validation_test.dart:301:11 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\end_to_end_validation_test.dart:352:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\end_to_end_validation_test.dart:396:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - test\integration\end_to_end_validation_test.dart:413:11 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\end_to_end_validation_test.dart:439:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - test\integration\end_to_end_validation_test.dart:543:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - test\integration\end_to_end_validation_test.dart:608:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\end_to_end_validation_test.dart:609:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\end_to_end_validation_test.dart:612:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\end_to_end_validation_test.dart:613:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - test\integration\end_to_end_validation_test.dart:620:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - test\integration\end_to_end_validation_test.dart:631:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - test\integration\end_to_end_workflow_test.dart:34:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\end_to_end_workflow_test.dart:41:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\end_to_end_workflow_test.dart:57:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\end_to_end_workflow_test.dart:97:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\end_to_end_workflow_test.dart:108:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\end_to_end_workflow_test.dart:120:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\end_to_end_workflow_test.dart:137:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\end_to_end_workflow_test.dart:149:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_simple_test.dart:32:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_simple_test.dart:39:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_simple_test.dart:47:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_simple_test.dart:61:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_simple_test.dart:75:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_simple_test.dart:90:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_simple_test.dart:119:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_simple_test.dart:127:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_simple_test.dart:146:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_simple_test.dart:164:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_simple_test.dart:181:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_simple_test.dart:183:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_simple_test.dart:193:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_simple_test.dart:209:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_simple_test.dart:225:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_simple_test.dart:240:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_simple_test.dart:255:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_simple_test.dart:286:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_simple_test.dart:305:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_simple_test.dart:337:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_simple_test.dart:338:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_test.dart:48:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_test.dart:55:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_test.dart:79:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_test.dart:107:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_test.dart:128:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\error_handling_test.dart:144:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_test.dart:167:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_test.dart:184:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_test.dart:198:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_test.dart:216:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_test.dart:241:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_test.dart:257:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_test.dart:267:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_test.dart:292:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_test.dart:309:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_test.dart:350:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_test.dart:368:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_test.dart:398:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_test.dart:399:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\error_handling_test.dart:420:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:45:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:46:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:77:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:102:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:135:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:172:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:199:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:235:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:263:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:284:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:310:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:336:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:361:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:391:17 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:396:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:428:15 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:432:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:455:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:466:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:467:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:482:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:483:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:484:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:485:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:486:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:489:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:490:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:491:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:492:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:493:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:494:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\examples_and_tutorials_test.dart:513:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:63:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:73:17 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\integration\performance_optimization_integration_test.dart:77:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:82:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:122:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:126:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:135:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:141:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:159:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:163:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:176:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:180:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:189:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:195:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:207:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:211:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:221:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:227:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:235:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:239:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:265:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:269:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:275:22 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\integration\performance_optimization_integration_test.dart:280:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:286:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:300:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:304:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:311:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:317:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:330:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:334:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:347:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:351:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_integration_test.dart:364:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_simple_test.dart:33:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_simple_test.dart:40:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_simple_test.dart:49:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_simple_test.dart:61:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_simple_test.dart:80:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_simple_test.dart:99:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_simple_test.dart:124:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_simple_test.dart:148:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_simple_test.dart:159:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_simple_test.dart:165:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\performance_optimization_simple_test.dart:172:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_simple_test.dart:178:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\performance_optimization_simple_test.dart:196:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_simple_test.dart:202:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\performance_optimization_simple_test.dart:210:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_simple_test.dart:217:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\performance_optimization_simple_test.dart:218:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\performance_optimization_simple_test.dart:221:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\performance_optimization_simple_test.dart:222:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\performance_optimization_simple_test.dart:232:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_simple_test.dart:244:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_simple_test.dart:260:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_simple_test.dart:281:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_simple_test.dart:299:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_simple_test.dart:307:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_simple_test.dart:312:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\performance_optimization_simple_test.dart:328:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_simple_test.dart:333:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\performance_optimization_simple_test.dart:352:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_simple_test.dart:357:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\performance_optimization_simple_test.dart:377:16 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - test\integration\performance_optimization_simple_test.dart:380:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_simple_test.dart:414:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_simple_test.dart:415:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_simple_test.dart:442:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_simple_test.dart:443:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:32:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:39:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:64:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:81:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:102:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:135:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:157:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:187:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:205:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:216:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:225:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:230:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:233:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:237:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:238:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:242:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:246:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:260:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:278:16 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - test\integration\performance_optimization_test.dart:279:16 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - test\integration\performance_optimization_test.dart:281:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:306:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:307:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:316:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\performance_optimization_test.dart:337:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:357:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:388:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:408:16 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - test\integration\performance_optimization_test.dart:410:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:423:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:468:18 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - test\integration\performance_optimization_test.dart:474:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:525:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:526:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\performance_optimization_test.dart:527:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_1_integration_test.dart:126:17 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\integration\phase2_1_integration_test.dart:161:20 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\integration\phase2_1_integration_test.dart:165:20 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\integration\phase2_1_integration_test.dart:488:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_1_integration_test.dart:489:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_1_integration_test.dart:490:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:29:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:35:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:57:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:79:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:95:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:111:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:135:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:165:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:186:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:199:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:208:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:228:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:238:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:253:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:270:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:287:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:309:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:326:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:344:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:359:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:372:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:383:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:394:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:414:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:430:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:446:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:461:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:478:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\phase2_preparation_test.dart:493:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:31:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:38:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:46:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:54:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:60:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:73:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:79:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:97:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:106:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:119:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:136:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:145:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\security_stability_test.dart:146:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\security_stability_test.dart:164:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:185:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:203:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:214:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:224:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:238:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:250:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:263:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:297:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:329:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:374:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:406:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:433:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:446:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:468:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:469:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\security_stability_test.dart:474:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\integration\security_stability_test.dart:497:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\test_modules\problematic_module\lib\bad_code.dart:1:7 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - test\integration\test_modules\problematic_module\lib\bad_code.dart:1:7 - The type name 'badClass' isn't an UpperCamelCase identifier. Try changing the name to follow the UpperCamelCase style. - camel_case_types
   info - test\integration\test_modules\problematic_module\lib\bad_code.dart:2:11 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - test\integration\test_modules\problematic_module\lib\bad_code.dart:4:7 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - test\integration\test_modules\problematic_module\lib\bad_code.dart:4:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - test\integration\test_modules\problematic_module\lib\bad_code.dart:64:8 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - test\integration\test_modules\problematic_module\lib\bad_code.dart:65:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\test_modules\problematic_module\lib\bad_code.dart:69:8 - Missing documentation for a public member. Try adding documentation for the member. - public_member_api_docs
   info - test\integration\test_modules\problematic_module\lib\bad_code.dart:70:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - test\integration\test_modules\valid_module\lib\src\models\user.dart:41:3 - The method '==' should not be overridden in classes not annotated with '@immutable'. Try removing the override or annotating the class with '@immutable'. - avoid_equals_and_hash_code_on_mutable_classes
   info - test\integration\test_modules\valid_module\lib\src\models\user.dart:51:3 - The method 'hashCode' should not be overridden in classes not annotated with '@immutable'. Try removing the override or annotating the class with '@immutable'. - avoid_equals_and_hash_code_on_mutable_classes
   info - test\integration\test_modules\valid_module\lib\src\services\user_service.dart:6:8 - Use 'package:' imports for files in the 'lib' directory. Try converting the URI to a 'package:' URI. - always_use_package_imports
   info - test\integration\test_modules\valid_module\lib\src\widgets\user_widget.dart:1:8 - Use 'package:' imports for files in the 'lib' directory. Try converting the URI to a 'package:' URI. - always_use_package_imports
   info - test\integration\test_modules\valid_module\lib\src\widgets\user_widget.dart:21:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - test\integration\user_experience_test.dart:34:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\user_experience_test.dart:41:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\user_experience_test.dart:72:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\user_experience_test.dart:98:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\user_experience_test.dart:126:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\user_experience_test.dart:142:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\user_experience_test.dart:169:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\user_experience_test.dart:195:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\user_experience_test.dart:217:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\user_experience_test.dart:218:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\user_experience_test.dart:234:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\user_experience_test.dart:272:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\user_experience_test.dart:286:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\user_experience_test.dart:304:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\user_experience_test.dart:306:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\user_experience_test.dart:309:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\user_experience_test.dart:325:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\integration\user_experience_test.dart:344:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\config_performance_test.dart:371:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:37:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:44:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:93:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:94:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:95:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:134:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:135:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:145:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:165:15 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:170:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:206:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:207:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:208:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:209:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:210:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - test\performance\performance_benchmark_test.dart:212:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:213:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:214:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:218:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:222:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:253:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:279:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:280:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:281:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:282:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:283:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:290:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:356:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:357:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:358:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:359:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:360:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:361:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:362:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - test\performance\performance_benchmark_test.dart:393:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:396:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:398:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:400:11 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:406:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:408:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:411:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:413:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:420:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:422:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:425:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:427:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:434:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:436:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:439:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:441:13 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\performance\performance_benchmark_test.dart:447:5 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\src\commands\validate_command_test.dart:32:17 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\src\core\template_engine_hooks_test.dart:169:24 - Use the constant 'HookResult.successResult' rather than a constructor returning the same object. Try using 'HookResult.successResult'. - use_named_constants
   info - test\src\validators\structure_validator_test.dart:32:17 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\stress\extended_stress_test.dart:551:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - test\stress\extended_stress_test.dart:587:16 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\stress\simple_stress_test.dart:42:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:48:7 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:53:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:64:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:65:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:66:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:67:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:68:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:72:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:83:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:84:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:85:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:91:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:120:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:121:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:122:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:126:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:153:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:154:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:155:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:156:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:162:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:185:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:186:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:187:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:188:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:189:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:193:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:213:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:214:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:220:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:250:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:251:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:252:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:258:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:273:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:274:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:275:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:276:9 - Don't invoke 'print' in production code. Try using a logging framework. - avoid_print
   info - test\stress\simple_stress_test.dart:312:3 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\unit\enterprise_library_management_test.dart:34:17 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\unit\enterprise_library_management_test.dart:212:17 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\unit\enterprise_parameter_system_test.dart:237:17 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\unit\enterprise_parameter_system_test.dart:359:17 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\unit\enterprise_template_creation_test.dart:33:17 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\unit\enterprise_template_creation_test.dart:274:17 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\unit\enterprise_template_creation_test.dart:502:20 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\unit\inheritance_system_test.dart:237:7 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\unit\inheritance_system_test.dart:309:7 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\unit\smart_conditional_system_test.dart:35:17 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\unit\smart_conditional_system_test.dart:138:17 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\unit\smart_conditional_system_test.dart:214:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - test\unit\smart_conditional_system_test.dart:304:81 - The line length exceeds the 80-character limit. Try breaking the line across multiple lines. - lines_longer_than_80_chars
   info - test\unit\task_2_2_2_security_test.dart:157:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\unit\task_2_2_2_security_test.dart:159:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\unit\task_2_2_2_security_test.dart:160:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\unit\task_2_2_4_enterprise_test.dart:496:16 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - test\unit\task_2_2_4_enterprise_test.dart:497:16 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - test\unit\task_2_2_4_enterprise_test.dart:498:16 - Method invocation or property access on a 'dynamic' target. Try giving the target a type. - avoid_dynamic_calls
   info - test\unit\task_2_2_5_network_test.dart:255:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\unit\task_2_2_5_network_test.dart:357:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\unit\task_2_2_5_network_test.dart:359:9 - Unnecessary duplication of receiver. Try using a cascade to avoid the duplication. - cascade_invocations
   info - test\unit\template_creator_test.dart:34:17 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\unit\template_creator_test.dart:118:20 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\unit\template_creator_test.dart:123:20 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\unit\template_creator_test.dart:126:20 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\unit\template_creator_test.dart:129:20 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\unit\template_creator_test.dart:134:20 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\unit\template_creator_test.dart:137:20 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\unit\template_creator_test.dart:166:20 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\unit\template_creator_test.dart:190:20 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\unit\template_creator_test.dart:194:20 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\unit\template_creator_test.dart:218:20 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\unit\template_creator_test.dart:222:20 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\unit\template_creator_test.dart:246:20 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\unit\template_creator_test.dart:250:20 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io
   info - test\unit\template_creator_test.dart:290:17 - Use of an async 'dart:io' method. Try using the synchronous version of the method. - avoid_slow_async_io

2939 issues found.
