# 宏观计划

本文档基于**项目发展蓝图V2.0**，概述了 `桌宠AI助理平台` 项目的高级计划、主要里程碑和战略目标。

## 🎯 项目核心定位
**桌宠AI助理平台** - 一个以"桌宠"为情感与交互核心的、高度模块化和可扩展的个人桌面AI助理平台。

**设计哲学**: "是桌宠，更是应用 (A Pet, and an App)" - 桌宠为平台注入生命和情感，强大的功能模块赋予平台真正的实用价值。

## 🏗️ 核心技术架构
- **架构范式**: "桌宠-总线"插件式架构 + "包驱动Monorepo"
- **技术栈**: Flutter + RxDart + get_it + go_router + Drift
- **安全模型**: "城堡防御模型"(多层纵深防御)
- **双端交互范式**:
  - **PC端**: "空间化OS (Spatial OS)"模式 - 浮窗卡片形式
  - **移动端**: "沉浸式标准应用"模式 - 全屏原生体验

---

## 🎉 Phase 0: 奠基与MVP ✅ (已完成)

- **目标**: 建立一个健壮且可扩展的"桌宠-总线"架构，为后续功能开发奠定坚实基础。
- **状态**: `[已完成]` ✅ 2025-01-27
- **关键交付物**:
  - ✅ 基于 RxDart 的核心事件总线系统 (`EventBus`)
  - ✅ 为未来插件设计的清晰模块接口 (`PetModuleInterface`)
  - ✅ 基于 `ChangeNotifier` 的中央状态管理核心 (`PetCore`)
  - ✅ 一个可工作的"打卡"模块，完整验证了端到端的数据流
  - ✅ 符合 v6 协议的完整项目结构和文档
  - ✅ 通过所有代码质量检查和测试验证
- **技术验证结果**:
  - ✅ 事件流完整性: UI交互 → 事件发布 → 订阅处理 → 状态更新 → UI响应
  - ✅ 模块化架构: 模块注册、初始化、启动流程正常工作
  - ✅ 依赖管理: get_it服务定位器和RxDart事件流集成成功
  - ✅ 测试覆盖: 核心功能测试通过，linter检查无问题

## 🏗️ Phase 1: 平台骨架构建 ✅ (已完成)

- **目标**: 基于Phase 0验证的架构，构建可承载所有功能模块的应用主框架，建立完整的技术基础设施。
- **状态**: `[已完成]` ✅ 2025-06-25
- **重大成就**: 
  - 💥 **内存崩溃问题彻底解决**: 修复了集成测试中的StreamController泄漏和无限递归问题
  - 🧪 **测试基础设施建立**: 单元测试+集成测试，10个测试用例100%通过
  - 🏗️ **企业级架构**: 混合架构(事件驱动+依赖注入)完整实现
- **关键交付物**:
  - ✅ **Part A-B-C-D**: 依赖管理、核心服务架构、安全基础、UI框架 (Steps 1-15)
    - go_router声明式路由、Repository模式数据层、模块管理服务
    - JWT认证、加密服务、API客户端安全栈
    - Material Design 3主题、响应式布局、导航系统、页面容器
  - ✅ **Part E**: 模块占位符系统 (Steps 16-18)
    - NotesHub事务中心、Workshop创意工坊、PunchIn打卡系统重构
    - 完整的模块生命周期管理和事件驱动通信
  - ✅ **Part F**: 服务集成与启动 (Steps 19-21)
    - ServiceLocator依赖注入、AppBootstrap企业级启动器、AppConfig配置管理
  - ✅ **Part G**: 测试与文档 (Steps 22-23)
    - 单元测试(3个核心服务)、集成测试(10个端到端用例)、13个API文档体系

## 🎨 Phase 1.5: 架构升级与i18n集成 ✅ (已完成)

- **目标**: 集中进行架构升级与基础设施建设，实现包驱动模块化架构和全面国际化支持。
- **状态**: `[已完成]` ✅ 2025-06-26
- **关键交付物**:
  - ✅ **源码架构升级**: "包驱动Monorepo"架构，7个独立包物理隔离
  - ✅ **国际化集成**: 完整i18n体系，94个键值对双语支持
  - ✅ **核心基础设施**: 4大企业级服务框架(Logging/ErrorHandling/Performance/Theme)
  - ✅ **质量保障**: 零错误零警告代码质量，完整API文档体系

## 🔧 Phase 1.6: Finalization ✅ (已完成)

- **目标**: 消除所有已知TODO，实现企业级生产就绪状态，为Phase 2.0奠定高质量基础。
- **状态**: `[已完成]` ✅ 2025-06-27
- **重大成就**:
  - ✅ **NavigationDrawer完整本地化**: 12个字段，真正双语支持
  - ✅ **LoggingService文件写入功能**: 异步IO、错误处理、文件轮转
  - ✅ **技术债务清理**: Issues.md追踪体系建立，P1.6问题全部解决
  - ✅ **诚实评估**: 完成度从声称A+(95%)修正为实际B(75%)
  - ✅ **Phase 2.0详细规划**: 基于功能缺陷分析的完整实施方案

---

## 🚀 Phase 2.0: 平台UI框架与核心体验构建 (当前目标)

### 📋 **核心目标** (基于项目蓝图V2.0)
构建"双端自适应UI框架"，实现差异化的交互体验：
- **PC端**: "空间化OS (Spatial OS)"模式 - 桌宠作为智能助手，功能模块以浮窗卡片形式存在
- **移动端**: "沉浸式标准应用"模式 - 遵循Material Design的全屏原生体验

### 🎯 **具体交付物**
1. **构建"手机OS"风格的桌面UI (SpatialOsShell)**
2. **构建"沉浸式标准应用"的移动UI (StandardAppShell)**  
3. **将所有规划中的模块实现为可点击的"App图标"**
4. **实现点击图标后，以"浮窗卡片"或全屏页面形式打开模块的UI占位符**

### 🛠️ **实施策略**
- **"先具体后抽象"** + **"移动端先行验证逻辑"**
- **4个Sprint分阶段执行**，每个Sprint都有用户验证检查点

### 📋 **详细Sprint规划**

#### **Sprint 2.0a: 移动端验证与基础奠基** (1周)
- [ ] 核心服务接口定义和最简实现
- [ ] ui_framework中ThemeService接口建立  
- [ ] 实现StandardAppShell（移动端外壳），加载模块占位符UI
- [ ] 修改app_router，Android平台默认加载StandardAppShell
- [ ] 建立技术债务信用系统和追踪机制
- [ ] **用户验证点**: Android设备导航和模块切换体验验证

#### **Sprint 2.0b: 空间化桌面核心实现** (2周)
- [ ] 创建desktop_environment新包
- [ ] 实现SpatialOsShell基础静态布局和AppDock
- [ ] 实现WindowManager类和FloatingWindow组件，基础拖拽功能
- [ ] AppDock点击事件连接WindowManager，实现浮窗打开
- [ ] 定义性能基准线(窗口拖拽≥60fps，模块启动≤300ms)
- [ ] **用户验证点**: Windows基础图标启动、浮窗拖拽体验验证

#### **Sprint 2.0c: 交互深化与服务集成** (1周)  
- [ ] FloatingWindow边缘缩放和最小化功能
- [ ] WindowManager智能吸附或网格对齐逻辑
- [ ] 状态管理路径明确(Local State → EventBus → Sync Interface)
- [ ] 预留前瞻性接口(热插拔、性能调度器)
- [ ] **用户验证点**: PC端窗口交互完整性和流畅性验证

#### **Sprint 2.0d: 质量保障与开发者工具** (1周)
- [ ] 分层测试策略执行，覆盖率≥80%
- [ ] DevPanel MVP开发(实时显示FloatingWindow状态)
- [ ] 技术债务审查和偿还计划
- [ ] 更新项目和API文档
- [ ] **用户验证点**: Alpha Test完整体验和反馈收集

### 🎯 **成功标准**
- **性能**: 窗口拖拽≥60fps，模块启动≤300ms，浮窗内存≤50MB
- **质量**: 核心服务测试覆盖≥80%，代码零错误零警告
- **体验**: 移动端导航≥4.0分，PC端交互≥3.5分，整体满意度≥4.2分

### **状态**: `[ready-to-start]` - Phase 1.6基础设施完全就绪，可立即开始Sprint 2.0a

---

## 🎯 Phase 2.1: 核心功能完善与持久化 (规划中)

### 📋 **核心目标**  
基于Phase 2.0的UI框架，深化核心功能实现和数据持久化升级

### 🎯 **主要交付物**
- **Drift数据库集成**: 替换InMemoryRepository，实现真正的数据持久化
- **事务管理中心CRUD完善**: 实现完整的编辑功能，解决P2.0-001关键缺陷
- **创意工坊编辑功能**: 移除"功能待实现"占位符，解决P2.0-002关键缺陷
- **数据架构升级**: 向后兼容的数据格式升级，版本控制机制

### 📊 **质量目标**
- 数据可靠性从40%提升到95%
- CRUD操作完整性100%
- 业务模块功能从60%提升到90%

---

## 🎨 Phase 2.2: 系统应用与体验增强 (进行中)

### 📋 **核心目标**
债务危机管理与核心功能闭环，系统级应用功能填充和用户体验精修

### 🎯 **主要交付物**

#### **Phase 2.2A: 债务危机管理** ✅ (已完成)
- ✅ **编辑功能完全修复**: UniversalEditDialog组件，Workshop/NotesHub完整CRUD
- ✅ **设置导航三端实现**: 移动端/桌面端/Web端统一访问路径  
- ✅ **技术债务控制**: 从47个TODO降至35个，信用额度从94%恢复到70%
- ✅ **测试套件建立**: 30+测试案例，关键功能≥80%覆盖率

#### **Phase 2.2B: 核心服务建设与i18n重建** ✅ (已完成) 
- ✅ **Drift数据库集成**: 真正SQLite数据持久化，替换InMemoryRepository
- ✅ **i18n系统修复**: 业务模块分布式国际化，硬编码清零
- ✅ **跨平台兼容性**: Web平台智能降级方案，原生平台SQLite支持  
- ✅ **数据库架构**: BaseItems/SyncStatus表，8个索引优化，完整迁移机制
- ✅ **Repository抽象层**: IPersistenceRepository统一接口，透明跨平台存储

#### **Phase 2.2C: 架构转型与模块化基础设施建设** (当前目标) ✅ 战略决策完成
- ✅ **设置应用核心功能**: 主题切换、语言设置、模块管理界面 (已完成)
- [ ] **兼容性迁移+新架构验证策略**: 4阶段渐进式模块化转型
  - [ ] **阶段1: 模块化基础设施先行** - 脚手架工具 + 验证器 + 创意工坊MVP
  - [ ] **阶段2: 现有包适配层** - 保持当前结构，添加模块接口适配
  - [ ] **阶段3: 主题系统验证案例** - 第一个真正模块化实现
  - [ ] **阶段4: 逐步迁移评估** - 基于验证结果决定其他包迁移策略
- [ ] **数据管理增强**: 基于Drift数据库的数据导出/导入功能、统计视图、完整性工具
- [ ] **用户体验优化**: Web端布局问题修复、三端一致性体验、错误提示优化

#### **Phase 2.2D: 质量保障与交付准备** (规划中)
- [ ] **全面集成测试**: 端到端用户流程，数据持久化稳定性验证
- [ ] **性能基准建立**: 启动时间、拖拽性能、内存使用基准测试
- [ ] **开发者工具脚手架**: 模块开发脚手架工具、质量检查工具(从2.2C延后)
- [ ] **富文本支持**: Markdown编辑器集成，内容编辑能力增强(从2.2C延后)
- [ ] **用户验证检查点**: 三端设备实际部署验证，反馈收集

### 🎯 **Phase 2.2 总体成果** (部分完成)
- **数据持久化能力**: 从内存存储升级到SQLite，支持Web智能降级 ✅
- **国际化完整性**: 分布式i18n体系，中英文语言切换正常 ✅
- **编辑功能完整性**: 核心CRUD功能100%可用，无"待实现"占位符 ✅
- **技术债务健康度**: 从临界94%恢复到安全70%，项目稳定性显著提升 ✅
- **跨平台兼容性**: Web+Windows+macOS+Linux+Android+iOS全平台支持 ✅

---

## 🌟 Phase 3.0: 生态系统启动 (远期规划)

### 📋 **核心目标**
构建完整的应用生态和智能化能力

### 🎯 **主要交付物**
- **创意工坊核心功能**: 皮肤管理、插件管理、应用商店
- **PC端工作区**: 布局保存/恢复功能，多任务工作流支持
- **桌宠动画系统**: 引入Rive，复杂行为动画和状态机
- **插件开发框架**: 第三方开发者工具和脚手架

---

## 🤖 Phase 4.0+: 后端与智能服务 (远期规划)

### 📋 **核心目标**
后端服务建设和AI能力集成

### 🎯 **主要交付物**
- **后端架构**: FastAPI + Docker + Kubernetes微服务
- **用户系统**: 账号管理、数据同步、云端备份
- **AI模块集成**: RAG知识库、OCR功能、自动化工作流
- **开发者生态**: 插件市场、开发者工具、社区建设

---

## 📊 应用生态规划

### 核心系统应用 (不可卸载)
- **设置 (Settings)**: 平台控制中心，外观/模块管理/语言设置
- **文件系统 (Virtual File System)**: IPersistenceRepository可视化前端
- **创意工坊 (Creative Workshop)**: 平台"应用商店"和"主题商店"

### 初始功能应用 (理论上可卸载)  
- **事务管理中心 (Notes & To-do Hub)**: 核心价值模块，笔记与待办
- **健康系统 (Health System)**: 久坐提醒等健康关怀功能
- **打卡模块 (Punch-in Module)**: 轻量级习惯追踪工具

### 长期生态规划
- **开发者工具**: 插件开发脚手架，API文档智能问答
- **AI集成**: RAG文档助手、OCR模块、n8n自动化
- **第三方生态**: 插件权限模型、应用审核机制

---

**注释**: 基于项目发展蓝图V2.0，各阶段的具体范围和时间线将根据实际开发进度和用户反馈动态调整。Phase 1.6的成功完成为后续开发奠定了坚实的技术基础和高质量的项目管理标准。

# 系统应用集成路线图优化版

## 核心设计原则：按需演进，渐进填充
采用"恰逢其时"的功能注入策略，避免过度设计和资源浪费。

## 优化后的分阶段规划

### Phase 2.1: 移动端核心体验
**设置应用**:
- ✅ 导航框架和功能分类占位符
- 🆕 添加"开发进度可视化"，显示各功能的Phase计划
- 🆕 实现"用户反馈收集"，了解功能优先级需求

**文件系统**: 
- 占位符状态，显示Phase 3.0计划信息

**创意工坊**: 
- 占位符状态，显示Phase 3.0-3.1计划信息

### Phase 2.2: Web端响应式门户
**设置应用**:
- 响应式布局适配
- 🆕 **数据概览页面**: 简单统计视图(笔记数量、模块使用情况)

**文件系统**: 
- 继续占位符状态，但提供数据概览预览

### Phase 2.3: 统一体验与模式切换
**设置应用**:
- ⭐ **显示模式切换功能**(核心交付)
- 🆕 **基础模块管理**: 开关现有模块(notes_hub, workshop, punch_in)
- 外观个性化基础设置

**文件系统**: 
- 🆕 **数据浏览器Beta**: 基于Phase 2.2的数据概览升级为简单的数据浏览

**创意工坊**: 
- 继续占位符，但显示与模块管理的关联预告

### Phase 3.0: 核心功能与持久化
**设置应用**:
- 完善外观设置(主题、字体、语言等)
- 数据管理设置(备份、导出等)

**文件系统**:
- ⭐ **完整文件系统v1.0**(核心交付): 完整的只读浏览器
- 支持文件夹结构、搜索、预览
- 数据分类展示(笔记、设置、模块数据等)

**创意工坊**:
- ⭐ **皮肤管理系统**(核心交付): 皮肤浏览、切换、预览

### Phase 3.1: 生态与个性化启动
**设置应用**:
- 开发者模式开关
- 高级功能设置(性能、调试等)

**文件系统**:
- 🆕 **文件操作功能**: 重命名、移动、删除、导出
- 批量操作和文件管理优化

**创意工坊**:
- ⭐ **插件管理系统**(核心交付): 模块启用/禁用、参数配置
- 🆕 **皮肤编辑器Beta**: 简单的皮肤自定义功能

### Phase 4+: 智能服务与开放生态
**设置应用**:
- 云同步设置和账户管理
- 智能推荐和个性化设置

**文件系统**:
- 云端文件同步和版本控制
- 协作功能和共享管理

**创意工坊**:
- ⭐ **插件商店**(核心交付): 社区插件浏览、下载、安装
- 完整皮肤编辑器和社区分享

## 关键改进说明

### 1. 用户期望管理优化
- 所有占位符都显示明确的开发计划和进度
- 提供用户反馈渠道，参与优先级决策
- 每个阶段都有可用功能，避免"空壳"印象

### 2. 数据验证能力前置
- Phase 2.2就引入数据概览，建立用户信心
- Phase 2.3提供简单数据浏览，满足早期需求
- Phase 3.0实现完整文件系统时有充分的功能基础

### 3. 模块管理架构前置
- Phase 2.3就引入基础模块管理，为显示模式切换提供支撑
- 避免Phase 3.1时的架构重构风险
- 为插件生态建设奠定技术基础

### 4. 开发风险控制
- 每个阶段都有独立可验证的交付价值
- 技术复杂度渐进增长，避免大爆炸式开发
- 保持用户反馈循环，及时调整优先级

这个优化版路线图在保持"按需演进"原则的同时，解决了用户期望管理、技术债务控制和开发节奏等关键挑战。 

# 项目发展蓝图 v3.0 批判性分析与优化

## v3.0 战略分析

### ✅ 核心优势
1. **实战验证导向**: "在战争中学习战争"的策略转变非常正确
2. **架构演进合理**: 通过真实应用开发验证和迭代核心服务
3. **价值聚焦明确**: 三个核心应用都是平台生态基石

### ⚠️ 关键风险识别

#### 风险1: 并行开发复杂度管理
**问题**: 同时开发三个应用可能导致质量控制困难
**解决方案**: 采用"串行MVP + 并行迭代"策略

#### 风险2: 核心服务API稳定性
**问题**: 频繁的核心服务变更可能导致应用开发不稳定
**解决方案**: 建立"API变更管理机制"

#### 风险3: 技术债务累积
**问题**: 快速迭代可能牺牲代码质量
**解决方案**: 强制性代码审查和重构周期

## 🚀 优化后的Phase 2.3实施策略

### 2.3.1: 事务管理中心先导开发 (Week 1-2)
**目标**: 作为核心服务的第一个实战用户
**关键产出**:
- 完整CRUD功能实现
- Drift数据库实战集成
- ModuleManager、NavigationService实战验证
- **核心服务迭代日志 v1.0**

### 2.3.2: 设置应用核心功能 (Week 3-4)  
**目标**: 验证配置管理和UI框架切换
**关键产出**:
- DisplayModeService实战实现
- i18n服务完整验证
- ThemeService基础实现
- **核心服务迭代日志 v2.0**

### 2.3.3: 创意工坊模块管理 (Week 5-6)
**目标**: 验证插件生态基础架构
**关键产出**:
- 模块管理器完整实现  
- Shell-Module交互契约验证
- 动态模块加载/卸载机制
- **核心服务迭代日志 v3.0**

### 2.3.4: 三端UI集成与优化 (Week 7-8)
**目标**: 将三个应用集成到所有UI Shell中
**关键产出**:
- SuperAppShell、ResponsiveWebShell、SpatialOsShell完整适配
- 跨平台一致性验证
- 性能优化和用户体验统一
- **Phase 2.3最终交付报告**

## 🛡️ 风险控制机制

### 1. API变更管理
```yaml
核心服务变更流程:
  1. 提出变更RFC文档
  2. 影响面评估(涉及哪些应用)
  3. 向后兼容性分析
  4. 迁移策略制定
  5. 所有应用同步更新
```

### 2. 质量保障机制
```yaml
开发质量控制:
  - 每个应用独立的测试覆盖率 >80%
  - 核心服务接口稳定性测试
  - 每周代码审查和重构评估
  - UI/UX一致性检查清单
```

### 3. 技术债务管理
```yaml
债务控制策略:
  - 每个迭代周期强制20%时间用于重构
  - 建立技术债务追踪系统
  - 代码质量指标持续监控
  - 架构设计文档同步更新
```

## 📊 成功标准定义

### Phase 2.3 完成标准:
1. **功能完整性**: 三个应用的MVP功能100%可用
2. **架构稳定性**: 核心服务API稳定，无破坏性变更
3. **性能达标**: 应用启动时间<2s，操作响应时间<500ms
4. **测试覆盖**: 整体测试覆盖率>85%，关键路径100%
5. **文档完备**: Shell-Module契约、API文档、部署指南完整

### 风险预警机制:
- 如果任一应用开发周期超过计划50%，立即触发风险评估
- 核心服务变更频率超过每周2次，启动API稳定性专项治理
- 测试覆盖率低于70%，暂停新功能开发，专注质量提升

## 💡 最终评价

v3.0蓝图的**战略思维转变是卓越的**，但需要在**执行层面**增强风险控制。优化后的策略在保持"实战验证"核心理念的同时，通过分阶段实施、风险管理机制和质量保障体系，确保项目既能快速迭代又能保持高质量交付。

这个优化版本可以作为Phase 2.1+的最终实施指南，兼顾了创新速度与工程质量的平衡。 