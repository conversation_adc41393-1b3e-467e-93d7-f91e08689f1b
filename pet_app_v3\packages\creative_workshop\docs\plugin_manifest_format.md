# Plugin Manifest Format (plugin.yaml)

## 概述

`plugin.yaml` 是插件清单文件，定义了插件的元数据、依赖关系、权限要求等信息。每个插件都必须包含此文件。

## 完整格式定义

```yaml
# ============================================================================
# 基础信息 (必需)
# ============================================================================

# 插件唯一标识符 (必需)
id: "my_awesome_plugin"

# 插件显示名称 (必需)
name: "我的超棒插件"

# 插件版本 (必需，遵循语义化版本)
version: "1.2.3"

# 插件描述 (必需)
description: "这是一个功能强大的插件，提供了丰富的创作工具"

# 插件作者 (必需)
author: "开发者名称"

# 插件类别 (必需)
# 可选值: system, ui, tool, game, theme, widget, service
category: "tool"

# ============================================================================
# 扩展信息 (可选)
# ============================================================================

# 插件主页
homepage: "https://example.com/my-plugin"

# 源码仓库
repository: "https://github.com/user/my-plugin"

# 许可证
license: "MIT"

# 搜索关键词
keywords:
  - "创作工具"
  - "画笔"
  - "设计"

# 插件图标 (相对路径)
icon: "assets/icon.png"

# 截图列表
screenshots:
  - "assets/screenshot1.png"
  - "assets/screenshot2.png"
  - "assets/screenshot3.png"

# ============================================================================
# 兼容性信息 (可选)
# ============================================================================

# 最低应用版本要求
min_app_version: "5.0.0"

# 最高应用版本支持
max_app_version: "6.0.0"

# 支持的平台
platforms:
  - "android"
  - "ios"
  - "windows"
  - "macos"
  - "linux"
  - "web"

# ============================================================================
# 权限声明 (可选)
# ============================================================================

# 插件所需权限
permissions:
  - "fileSystem"      # 文件系统访问
  - "network"         # 网络访问
  - "notifications"   # 系统通知
  - "clipboard"       # 剪贴板访问
  - "camera"          # 相机访问
  - "microphone"      # 麦克风访问
  - "location"        # 位置信息
  - "deviceInfo"      # 设备信息

# ============================================================================
# 依赖关系 (可选)
# ============================================================================

# 插件依赖
dependencies:
  - id: "base_utils"
    version: "^1.0.0"
    required: true
    description: "基础工具库"
  
  - id: "math_helper"
    version: ">=2.0.0 <3.0.0"
    required: false
    description: "数学计算辅助库"

# ============================================================================
# 入口点定义 (必需)
# ============================================================================

# 主入口文件
main: "lib/main.dart"

# 资源文件目录
assets:
  - "assets/"
  - "locales/"

# ============================================================================
# 配置选项 (可选)
# ============================================================================

# 插件配置
config:
  # 是否支持热重载
  hot_reload: true
  
  # 是否自动更新
  auto_update: true
  
  # 是否在后台运行
  background: false
  
  # 最大内存使用 (MB)
  max_memory: 128
  
  # 网络超时 (秒)
  network_timeout: 30

# ============================================================================
# 本地化支持 (可选)
# ============================================================================

# 支持的语言
locales:
  - "zh_CN"
  - "en_US"
  - "ja_JP"

# 默认语言
default_locale: "zh_CN"

# ============================================================================
# 开发信息 (可选)
# ============================================================================

# 开发者信息
developer:
  name: "开发者名称"
  email: "<EMAIL>"
  website: "https://developer.example.com"

# 支持信息
support:
  email: "<EMAIL>"
  website: "https://support.example.com"
  documentation: "https://docs.example.com"

# 更新日志
changelog: "CHANGELOG.md"

# ============================================================================
# 元数据 (自动生成，不需要手动填写)
# ============================================================================

# 创建时间 (自动生成)
# created_at: "2025-01-20T10:30:00Z"

# 最后修改时间 (自动生成)
# updated_at: "2025-01-22T15:45:00Z"

# 文件大小 (自动计算)
# size: 2048000

# 文件哈希 (自动计算)
# hash: "sha256:abc123..."
```

## 字段说明

### 必需字段

| 字段 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `id` | String | 插件唯一标识符，只能包含字母、数字、下划线 | `"my_plugin"` |
| `name` | String | 插件显示名称 | `"我的插件"` |
| `version` | String | 语义化版本号 | `"1.0.0"` |
| `description` | String | 插件功能描述 | `"功能描述"` |
| `author` | String | 插件作者 | `"开发者"` |
| `category` | String | 插件类别 | `"tool"` |
| `main` | String | 主入口文件路径 | `"lib/main.dart"` |

### 权限类型

| 权限 | 说明 | 用途 |
|------|------|------|
| `fileSystem` | 文件系统访问 | 读写文件、创建目录 |
| `network` | 网络访问 | HTTP请求、下载文件 |
| `notifications` | 系统通知 | 发送通知消息 |
| `clipboard` | 剪贴板访问 | 读写剪贴板内容 |
| `camera` | 相机访问 | 拍照、录像功能 |
| `microphone` | 麦克风访问 | 录音功能 |
| `location` | 位置信息 | 获取地理位置 |
| `deviceInfo` | 设备信息 | 获取设备硬件信息 |

### 插件类别

| 类别 | 说明 | 示例 |
|------|------|------|
| `system` | 系统级插件 | 系统工具、性能监控 |
| `ui` | UI组件插件 | 自定义控件、主题 |
| `tool` | 工具类插件 | 画笔工具、形状工具 |
| `game` | 游戏插件 | 小游戏、娱乐功能 |
| `theme` | 主题插件 | 界面主题、配色方案 |
| `widget` | 小部件插件 | 桌面小部件 |
| `service` | 服务类插件 | 后台服务、数据同步 |

### 版本约束格式

| 格式 | 说明 | 示例 |
|------|------|------|
| `"1.0.0"` | 精确版本 | 必须是 1.0.0 |
| `"^1.0.0"` | 兼容版本 | >=1.0.0 <2.0.0 |
| `"~1.0.0"` | 近似版本 | >=1.0.0 <1.1.0 |
| `">=1.0.0"` | 最低版本 | 大于等于 1.0.0 |
| `">=1.0.0 <2.0.0"` | 版本范围 | 1.0.0 到 2.0.0 之间 |

## 验证规则

1. **必需字段检查**: 所有必需字段都必须存在且非空
2. **ID格式验证**: 只能包含字母、数字、下划线，长度3-50字符
3. **版本格式验证**: 必须符合语义化版本规范
4. **权限验证**: 权限名称必须在预定义列表中
5. **类别验证**: 类别必须在预定义列表中
6. **依赖循环检查**: 不能存在循环依赖
7. **文件路径验证**: 入口文件和资源文件必须存在

## 示例文件

参见 `examples/` 目录下的示例插件清单文件。
