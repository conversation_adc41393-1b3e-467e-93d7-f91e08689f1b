/*
---------------------------------------------------------------
File name:          module_resource.dart
Author:             lgnorant-lu
Date created:       2025-07-24
Last modified:      2025-07-24
Dart Version:       3.2+
Description:        模块资源模型
---------------------------------------------------------------
Change History:
    2025-07-24: Phase ******* - 实现模块资源模型;
---------------------------------------------------------------
*/

import 'package:equatable/equatable.dart';

/// 模块资源使用情况
class ModuleResourceUsage extends Equatable {
  /// 模块ID
  final String moduleId;
  
  /// CPU 使用率 (0.0 - 1.0)
  final double cpuUsage;
  
  /// 内存使用量 (字节)
  final int memoryUsage;
  
  /// 网络发送字节数
  final int networkSent;
  
  /// 网络接收字节数
  final int networkReceived;
  
  /// 磁盘使用量 (字节)
  final int diskUsage;
  
  /// 最后更新时间
  final DateTime lastUpdated;

  const ModuleResourceUsage({
    required this.moduleId,
    required this.cpuUsage,
    required this.memoryUsage,
    required this.networkSent,
    required this.networkReceived,
    required this.diskUsage,
    required this.lastUpdated,
  });

  /// 创建初始资源使用情况
  factory ModuleResourceUsage.initial(String moduleId) {
    return ModuleResourceUsage(
      moduleId: moduleId,
      cpuUsage: 0.0,
      memoryUsage: 0,
      networkSent: 0,
      networkReceived: 0,
      diskUsage: 0,
      lastUpdated: DateTime.now(),
    );
  }

  /// 创建资源使用副本
  ModuleResourceUsage copyWith({
    String? moduleId,
    double? cpuUsage,
    int? memoryUsage,
    int? networkSent,
    int? networkReceived,
    int? diskUsage,
    DateTime? lastUpdated,
  }) {
    return ModuleResourceUsage(
      moduleId: moduleId ?? this.moduleId,
      cpuUsage: cpuUsage ?? this.cpuUsage,
      memoryUsage: memoryUsage ?? this.memoryUsage,
      networkSent: networkSent ?? this.networkSent,
      networkReceived: networkReceived ?? this.networkReceived,
      diskUsage: diskUsage ?? this.diskUsage,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// 格式化 CPU 使用率
  String get formattedCpuUsage {
    return '${(cpuUsage * 100).toStringAsFixed(1)}%';
  }

  /// 格式化内存使用量
  String get formattedMemoryUsage {
    return _formatBytes(memoryUsage);
  }

  /// 格式化磁盘使用量
  String get formattedDiskUsage {
    return _formatBytes(diskUsage);
  }

  /// 格式化网络发送量
  String get formattedNetworkSent {
    return _formatBytes(networkSent);
  }

  /// 格式化网络接收量
  String get formattedNetworkReceived {
    return _formatBytes(networkReceived);
  }

  /// 格式化字节数
  String _formatBytes(int bytes) {
    if (bytes < 1024) {
      return '${bytes}B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)}KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
    }
  }

  @override
  List<Object?> get props => [
    moduleId,
    cpuUsage,
    memoryUsage,
    networkSent,
    networkReceived,
    diskUsage,
    lastUpdated,
  ];

  @override
  String toString() {
    return 'ModuleResourceUsage(moduleId: $moduleId, cpu: $formattedCpuUsage, memory: $formattedMemoryUsage)';
  }
}

/// 模块缓存信息
class ModuleCache extends Equatable {
  /// 模块ID
  final String moduleId;
  
  /// 缓存大小 (字节)
  final int size;
  
  /// 缓存文件数量
  final int fileCount;
  
  /// 最后清理时间
  final DateTime? lastCleared;
  
  /// 创建时间
  final DateTime createdAt;

  const ModuleCache({
    required this.moduleId,
    required this.size,
    required this.fileCount,
    this.lastCleared,
    required this.createdAt,
  });

  /// 创建初始缓存信息
  factory ModuleCache.initial(String moduleId) {
    return ModuleCache(
      moduleId: moduleId,
      size: 0,
      fileCount: 0,
      lastCleared: null,
      createdAt: DateTime.now(),
    );
  }

  /// 创建缓存副本
  ModuleCache copyWith({
    String? moduleId,
    int? size,
    int? fileCount,
    DateTime? lastCleared,
    DateTime? createdAt,
  }) {
    return ModuleCache(
      moduleId: moduleId ?? this.moduleId,
      size: size ?? this.size,
      fileCount: fileCount ?? this.fileCount,
      lastCleared: lastCleared ?? this.lastCleared,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// 格式化缓存大小
  String get formattedSize {
    if (size < 1024) {
      return '${size}B';
    } else if (size < 1024 * 1024) {
      return '${(size / 1024).toStringAsFixed(1)}KB';
    } else if (size < 1024 * 1024 * 1024) {
      return '${(size / (1024 * 1024)).toStringAsFixed(1)}MB';
    } else {
      return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
    }
  }

  @override
  List<Object?> get props => [moduleId, size, fileCount, lastCleared, createdAt];

  @override
  String toString() {
    return 'ModuleCache(moduleId: $moduleId, size: $formattedSize, fileCount: $fileCount)';
  }
}

/// 系统资源概览
class SystemResourceOverview extends Equatable {
  /// 模块总数
  final int totalModules;
  
  /// 总 CPU 使用率
  final double totalCpuUsage;
  
  /// 总内存使用量
  final int totalMemoryUsage;
  
  /// 总网络发送量
  final int totalNetworkSent;
  
  /// 总网络接收量
  final int totalNetworkReceived;
  
  /// 总缓存大小
  final int totalCacheSize;
  
  /// 总缓存文件数
  final int totalCacheFiles;
  
  /// 最后更新时间
  final DateTime lastUpdated;

  const SystemResourceOverview({
    required this.totalModules,
    required this.totalCpuUsage,
    required this.totalMemoryUsage,
    required this.totalNetworkSent,
    required this.totalNetworkReceived,
    required this.totalCacheSize,
    required this.totalCacheFiles,
    required this.lastUpdated,
  });

  /// 创建空概览
  factory SystemResourceOverview.empty() {
    return SystemResourceOverview(
      totalModules: 0,
      totalCpuUsage: 0.0,
      totalMemoryUsage: 0,
      totalNetworkSent: 0,
      totalNetworkReceived: 0,
      totalCacheSize: 0,
      totalCacheFiles: 0,
      lastUpdated: DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [
    totalModules,
    totalCpuUsage,
    totalMemoryUsage,
    totalNetworkSent,
    totalNetworkReceived,
    totalCacheSize,
    totalCacheFiles,
    lastUpdated,
  ];
}

/// 缓存操作结果
class CacheOperationResult extends Equatable {
  /// 模块ID
  final String moduleId;
  
  /// 是否成功
  final bool isSuccess;
  
  /// 结果消息
  final String message;
  
  /// 错误类型（失败时）
  final CacheOperationError? error;
  
  /// 操作时间
  final DateTime timestamp;

  const CacheOperationResult._({
    required this.moduleId,
    required this.isSuccess,
    required this.message,
    this.error,
    required this.timestamp,
  });

  /// 创建成功结果
  factory CacheOperationResult.success(String moduleId, String message) {
    return CacheOperationResult._(
      moduleId: moduleId,
      isSuccess: true,
      message: message,
      timestamp: DateTime.now(),
    );
  }

  /// 创建失败结果
  factory CacheOperationResult.failure(
    String moduleId,
    String message,
    CacheOperationError error,
  ) {
    return CacheOperationResult._(
      moduleId: moduleId,
      isSuccess: false,
      message: message,
      error: error,
      timestamp: DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [moduleId, isSuccess, message, error, timestamp];
}

/// 缓存操作错误
enum CacheOperationError {
  cacheNotFound,
  operationFailed,
}

/// 资源优化结果
class ResourceOptimizationResult extends Equatable {
  /// 模块ID
  final String moduleId;
  
  /// 是否成功
  final bool isSuccess;
  
  /// 结果消息
  final String message;
  
  /// 优化项目列表
  final List<String> optimizations;
  
  /// 操作时间
  final DateTime timestamp;

  const ResourceOptimizationResult._({
    required this.moduleId,
    required this.isSuccess,
    required this.message,
    required this.optimizations,
    required this.timestamp,
  });

  /// 创建成功结果
  factory ResourceOptimizationResult.success(
    String moduleId,
    String message,
    List<String> optimizations,
  ) {
    return ResourceOptimizationResult._(
      moduleId: moduleId,
      isSuccess: true,
      message: message,
      optimizations: optimizations,
      timestamp: DateTime.now(),
    );
  }

  /// 创建失败结果
  factory ResourceOptimizationResult.failure(String moduleId, String message) {
    return ResourceOptimizationResult._(
      moduleId: moduleId,
      isSuccess: false,
      message: message,
      optimizations: [],
      timestamp: DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [moduleId, isSuccess, message, optimizations, timestamp];
}

/// 资源事件
class ResourceEvent extends Equatable {
  /// 模块ID
  final String moduleId;
  
  /// 事件类型
  final ResourceEventType type;
  
  /// 相关数据
  final dynamic data;
  
  /// 事件时间
  final DateTime timestamp;

  const ResourceEvent._({
    required this.moduleId,
    required this.type,
    this.data,
    required this.timestamp,
  });

  /// 使用情况更新事件
  factory ResourceEvent.usageUpdated(String moduleId, ModuleResourceUsage usage) {
    return ResourceEvent._(
      moduleId: moduleId,
      type: ResourceEventType.usageUpdated,
      data: usage,
      timestamp: DateTime.now(),
    );
  }

  /// 缓存清理事件
  factory ResourceEvent.cacheCleared(String moduleId) {
    return ResourceEvent._(
      moduleId: moduleId,
      type: ResourceEventType.cacheCleared,
      timestamp: DateTime.now(),
    );
  }

  /// 资源优化事件
  factory ResourceEvent.optimized(String moduleId, List<String> optimizations) {
    return ResourceEvent._(
      moduleId: moduleId,
      type: ResourceEventType.optimized,
      data: optimizations,
      timestamp: DateTime.now(),
    );
  }

  /// 阈值超过事件
  factory ResourceEvent.thresholdExceeded(
    String moduleId,
    ResourceThresholdType thresholdType,
    double value,
  ) {
    return ResourceEvent._(
      moduleId: moduleId,
      type: ResourceEventType.thresholdExceeded,
      data: {'thresholdType': thresholdType, 'value': value},
      timestamp: DateTime.now(),
    );
  }

  /// 监控开始事件
  factory ResourceEvent.monitoringStarted(String moduleId) {
    return ResourceEvent._(
      moduleId: moduleId,
      type: ResourceEventType.monitoringStarted,
      timestamp: DateTime.now(),
    );
  }

  /// 监控停止事件
  factory ResourceEvent.monitoringStopped(String moduleId) {
    return ResourceEvent._(
      moduleId: moduleId,
      type: ResourceEventType.monitoringStopped,
      timestamp: DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [moduleId, type, data, timestamp];
}

/// 资源事件类型
enum ResourceEventType {
  usageUpdated,
  cacheCleared,
  optimized,
  thresholdExceeded,
  monitoringStarted,
  monitoringStopped,
}

/// 资源阈值类型
enum ResourceThresholdType {
  cpu,
  memory,
  disk,
  network,
}
