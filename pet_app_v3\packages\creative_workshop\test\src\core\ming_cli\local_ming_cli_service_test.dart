/*
---------------------------------------------------------------
File name:          local_ming_cli_service_test.dart
Author:             lgnorant-lu
Date created:       2025-07-30
Last modified:      2025-07-30
Dart Version:       3.2+
Description:        LocalMingCliService测试
---------------------------------------------------------------
Change History:
    2025-07-30: 创建LocalMingCliService测试;
---------------------------------------------------------------
*/

import 'package:flutter_test/flutter_test.dart';
import 'package:creative_workshop/src/core/ming_cli/local_ming_cli_service.dart';
import 'package:creative_workshop/src/core/ming_cli/ming_cli_result.dart';

void main() {
  group('LocalMingCliService Tests', () {
    late LocalMingCliService service;

    setUp(() {
      service = LocalMingCliService.instance;
    });

    test('should be singleton', () {
      final service1 = LocalMingCliService.instance;
      final service2 = LocalMingCliService.instance;
      expect(service1, same(service2));
    });

    test('should initialize successfully', () async {
      await service.initialize();
      expect(service.currentMode, isNotNull);
    });

    test('should execute fallback command when no CLI available', () async {
      await service.initialize();

      final result = await service.executeCommand(['--version']);

      expect(result, isNotNull);
      expect(result.mode, isNotNull);
      expect(result.executionTime, isNotNull);
    });

    test('should handle create command in fallback mode', () async {
      await service.initialize();

      final result = await service.executeCommand(['create', 'test-plugin']);

      expect(result, isNotNull);
      expect(result.output, contains('降级模式'));
      expect(result.success, isFalse); // 降级模式下create命令不支持
    });

    test('should handle doctor command in fallback mode', () async {
      await service.initialize();

      final result = await service.executeCommand(['doctor']);

      expect(result, isNotNull);
      expect(result.output, contains('环境检查'));
      expect(result.success, isTrue);
    });

    test('should provide detailed status', () async {
      await service.initialize();

      final status = service.getDetailedStatus();

      expect(status, isA<Map<String, dynamic>>());
      expect(status['isInstalled'], isA<bool>());
      expect(status['version'], isA<String>());
      expect(status['currentMode'], isA<String>());
      expect(status['statusDescription'], isA<String>());
      expect(status['timestamp'], isA<String>());
    });

    test('should refresh status', () async {
      await service.initialize();

      await service.refresh();

      expect(service.currentMode, isNotNull);
      // 模式可能相同，但刷新操作应该成功
    });

    test('should handle mode detection', () async {
      await service.initialize();

      // 验证模式检测功能
      expect(service.currentMode, isNotNull);
      expect(service.currentMode, isA<MingCliMode>());

      // 刷新检测
      await service.refresh();
      expect(service.currentMode, isNotNull);
    });

    test('should have Git clone integration capability', () async {
      await service.initialize();

      // 验证服务具备Git克隆能力（通过检查常量定义）
      // 这是一个静态测试，不会执行实际的Git操作
      expect(service.statusDescription, isNotNull);
      expect(service.currentMode, isNotNull);

      // 验证服务在各种模式下都能正常工作
      final status = service.getDetailedStatus();
      expect(status['isInstalled'], isA<bool>());
      expect(status['currentMode'], isA<String>());
    });

    group('MingCliMode Tests', () {
      test('should have all expected modes', () {
        expect(MingCliMode.values, hasLength(4));
        expect(MingCliMode.values, contains(MingCliMode.systemInstalled));
        expect(MingCliMode.values, contains(MingCliMode.localBuilt));
        expect(MingCliMode.values, contains(MingCliMode.dartRun));
        expect(MingCliMode.values, contains(MingCliMode.fallback));
      });
    });
  });
}
