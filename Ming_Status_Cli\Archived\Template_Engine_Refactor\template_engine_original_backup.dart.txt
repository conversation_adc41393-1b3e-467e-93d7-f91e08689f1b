// /*
// ---------------------------------------------------------------
// File name:          template_engine.dart
// Author:             lgnorant-lu
// Date created:       2025/06/29
// Last modified:      2025/06/29
// Dart Version:       3.2+
// Description:        模板引擎管理器 (Template engine manager)
// ---------------------------------------------------------------
// Change History:
//     2025/06/29: Initial creation - 基础模板引擎功能;
// ---------------------------------------------------------------
// */

// import 'dart:async';
// import 'dart:convert';
// import 'dart:io';
// import 'dart:math' as math;

// import 'package:mason/mason.dart';
// import 'package:ming_status_cli/src/core/template_parameter_system.dart';
// import 'package:ming_status_cli/src/models/template_variable.dart';
// import 'package:ming_status_cli/src/utils/file_utils.dart';
// import 'package:ming_status_cli/src/utils/logger.dart' as cli_logger;
// import 'package:ming_status_cli/src/utils/string_utils.dart';
// import 'package:path/path.dart' as path;

// /// 模板引擎错误类型枚举
// enum TemplateEngineErrorType {
//   /// 模板不存在
//   templateNotFound,
//   /// 模板格式无效
//   invalidTemplateFormat,
//   /// 变量验证失败
//   variableValidationFailed,
//   /// 输出路径冲突
//   outputPathConflict,
//   /// 文件系统错误
//   fileSystemError,
//   /// Mason包错误
//   masonError,
//   /// 钩子执行错误
//   hookExecutionError,
//   /// 网络错误
//   networkError,
//   /// 权限错误
//   permissionError,
//   /// 版本兼容性错误
//   versionCompatibilityError,
//   /// 依赖兼容性错误
//   dependencyCompatibilityError,
//   /// 平台兼容性错误
//   platformCompatibilityError,
//   /// 模板标准合规性错误
//   templateComplianceError,
//   /// 未知错误
//   unknown,
// }

// /// 模板兼容性检查结果
// /// 
// /// 封装模板兼容性检查的详细结果，提供多层次的反馈信息：
// /// - 总体兼容性状态
// /// - 阻塞性错误列表
// /// - 警告信息列表  
// /// - 优化建议列表
// /// - 检查过程的元数据
// /// 
// /// 用于帮助开发者了解模板在当前环境的可用性和潜在问题。
// class CompatibilityCheckResult {

//   /// 创建成功结果
//   CompatibilityCheckResult.success({
//     List<String> warnings = const [],
//     List<String> suggestions = const [],
//     Map<String, dynamic> metadata = const {},
//   }) : this(
//       isCompatible: true,
//       warnings: warnings,
//       suggestions: suggestions,
//       metadata: metadata,
//     );

//   /// 创建失败结果
//   CompatibilityCheckResult.failure({
//     required List<String> errors,
//     List<String> warnings = const [],
//     List<String> suggestions = const [],
//     Map<String, dynamic> metadata = const {},
//   }) : this(
//       isCompatible: false,
//       errors: errors,
//       warnings: warnings,
//       suggestions: suggestions,
//       metadata: metadata,
//     );
//   /// 创建兼容性检查结果
//   /// 
//   /// 参数：
//   /// - [isCompatible] 是否兼容
//   /// - [errors] 错误列表
//   /// - [warnings] 警告列表
//   /// - [suggestions] 建议列表
//   /// - [metadata] 元数据
//   const CompatibilityCheckResult({
//     required this.isCompatible,
//     this.errors = const [],
//     this.warnings = const [],
//     this.suggestions = const [],
//     this.metadata = const {},
//   });

//   /// 是否兼容
//   final bool isCompatible;
//   /// 错误列表
//   final List<String> errors;
//   /// 警告列表
//   final List<String> warnings;
//   /// 建议列表
//   final List<String> suggestions;
//   /// 元数据
//   final Map<String, dynamic> metadata;
// }

// /// 模板版本信息
// /// 
// /// 描述模板对各种工具和环境的版本要求。
// /// 用于版本兼容性检查，确保模板能在符合要求的环境中正常运行。
// /// 
// /// 支持的版本约束：
// /// - 模板自身版本标识
// /// - CLI工具版本范围
// /// - Dart SDK版本范围  
// /// - Mason构建工具版本范围
// class TemplateVersionInfo {
//   /// 创建模板版本信息
//   /// 
//   /// 参数：
//   /// - [version] 模板版本
//   /// - [minCliVersion] 最小CLI版本要求
//   /// - [maxCliVersion] 最大CLI版本要求
//   /// - [minDartVersion] 最小Dart版本要求
//   /// - [maxDartVersion] 最大Dart版本要求
//   /// - [minMasonVersion] 最小Mason版本要求
//   /// - [maxMasonVersion] 最大Mason版本要求
//   const TemplateVersionInfo({
//     required this.version,
//     this.minCliVersion,
//     this.maxCliVersion,
//     this.minDartVersion,
//     this.maxDartVersion,
//     this.minMasonVersion,
//     this.maxMasonVersion,
//   });

//   /// 模板版本
//   final String version;
//   /// 最小CLI版本要求
//   final String? minCliVersion;
//   /// 最大CLI版本要求
//   final String? maxCliVersion;
//   /// 最小Dart版本要求
//   final String? minDartVersion;
//   /// 最大Dart版本要求
//   final String? maxDartVersion;
//   /// 最小Mason版本要求
//   final String? minMasonVersion;
//   /// 最大Mason版本要求
//   final String? maxMasonVersion;
// }

// /// 模板平台信息
// /// 
// /// 定义模板对操作系统平台和功能特性的支持情况。
// /// 用于平台兼容性检查，确保模板生成的代码能在目标平台正常运行。
// /// 
// /// 包含信息：
// /// - 支持的操作系统平台（Windows、macOS、Linux等）
// /// - 明确不支持的平台  
// /// - 必需的平台功能特性
// /// - 可选的增强功能特性
// class TemplatePlatformInfo {
//   /// 创建模板平台信息
//   /// 
//   /// 参数：
//   /// - [supportedPlatforms] 支持的平台列表
//   /// - [unsupportedPlatforms] 不支持的平台列表
//   /// - [requiredFeatures] 必需功能列表
//   /// - [optionalFeatures] 可选功能列表
//   const TemplatePlatformInfo({
//     this.supportedPlatforms = const [],
//     this.unsupportedPlatforms = const [],
//     this.requiredFeatures = const [],
//     this.optionalFeatures = const [],
//   });

//   /// 支持的平台列表
//   final List<String> supportedPlatforms;
//   /// 不支持的平台列表
//   final List<String> unsupportedPlatforms;
//   /// 必需功能列表
//   final List<String> requiredFeatures;
//   /// 可选功能列表
//   final List<String> optionalFeatures;
// }

// /// 模板依赖信息
// /// 
// /// 描述模板所需的外部依赖包和版本约束。
// /// 用于依赖兼容性检查，确保生成的项目具有正确的依赖配置。
// /// 
// /// 依赖类型：
// /// - 必需依赖：模板正常运行所必须的包和版本
// /// - 可选依赖：提供额外功能的可选包
// /// - 冲突依赖：与模板不兼容的包列表
// /// 
// /// 支持语义化版本约束（如 "^1.0.0", ">=2.0.0 <3.0.0"）
// class TemplateDependencyInfo {
//   /// 创建模板依赖信息
//   /// 
//   /// 参数：
//   /// - [requiredDependencies] 必需依赖映射
//   /// - [optionalDependencies] 可选依赖映射
//   /// - [conflictingDependencies] 冲突依赖列表
//   const TemplateDependencyInfo({
//     this.requiredDependencies = const {},
//     this.optionalDependencies = const {},
//     this.conflictingDependencies = const [],
//   });

//   /// 必需依赖 (依赖名 -> 版本约束)
//   final Map<String, String> requiredDependencies;
//   /// 可选依赖 (依赖名 -> 版本约束)
//   final Map<String, String> optionalDependencies;
//   /// 冲突依赖列表
//   final List<String> conflictingDependencies;
// }

// /// 模板引擎异常类
// class TemplateEngineException implements Exception {

//   /// 创建模板不存在错误
//   TemplateEngineException.templateNotFound(
//     String templateName, {
//     String? recovery,
//   }) : this(
//       type: TemplateEngineErrorType.templateNotFound,
//       message: '模板不存在: $templateName',
//       details: {'templateName': templateName},
//       recovery: recovery ?? 
//           '请检查模板名称是否正确，或使用 ming template list 查看可用模板',
//     );

//   /// 创建Mason包错误
//   TemplateEngineException.masonError(
//     String operation,
//     dynamic error, {
//     String? recovery,
//   }) : this(
//       type: TemplateEngineErrorType.masonError,
//       message: 'Mason包操作失败: $operation',
//       details: {'operation': operation},
//       innerException: error,
//       recovery: recovery ?? '请检查模板格式是否正确，或尝试重新安装模板',
//     );

//   /// 创建文件系统错误
//   TemplateEngineException.fileSystemError(
//     String operation,
//     String path,
//     dynamic error, {
//     String? recovery,
//   }) : this(
//       type: TemplateEngineErrorType.fileSystemError,
//       message: '文件系统操作失败: $operation',
//       details: {'operation': operation, 'path': path},
//       innerException: error,
//       recovery: recovery ?? '请检查文件路径和权限是否正确',
//     );

//   /// 创建变量验证错误
//   TemplateEngineException.variableValidationError(
//     Map<String, String> validationErrors, {
//     String? recovery,
//   }) : this(
//       type: TemplateEngineErrorType.variableValidationFailed,
//       message: '模板变量验证失败',
//       details: {'validationErrors': validationErrors},
//       recovery: recovery ?? '请检查并修正模板变量值',
//     );
//   /// 创建模板引擎异常
//   /// 
//   /// 参数：
//   /// - [type] 错误类型
//   /// - [message] 错误消息
//   /// - [details] 错误详情
//   /// - [innerException] 内部异常
//   /// - [recovery] 恢复建议
//   const TemplateEngineException({
//     required this.type,
//     required this.message,
//     this.details,
//     this.innerException,
//     this.recovery,
//   });

//   /// 错误类型
//   final TemplateEngineErrorType type;
//   /// 错误消息
//   final String message;
//   /// 错误详情
//   final Map<String, dynamic>? details;
//   /// 内部异常
//   final dynamic innerException;
//   /// 恢复建议
//   final String? recovery;

//   @override
//   String toString() {
//     var result = 'TemplateEngineException: $message';
//     if (details != null && details!.isNotEmpty) {
//       result += '\nDetails: $details';
//     }
//     if (recovery != null) {
//       result += '\nRecovery: $recovery';
//     }
//     if (innerException != null) {
//       result += '\nCaused by: $innerException';
//     }
//     return result;
//   }
// }

// /// 错误恢复结果
// class ErrorRecoveryResult {

//   /// 成功恢复
//   ErrorRecoveryResult.createSuccess({String? message, dynamic value}) 
//     : this(
//       success: true,
//       message: message,
//       recoveredValue: value,
//     );

//   /// 恢复失败
//   ErrorRecoveryResult.createFailure(String message)
//     : this(
//       success: false,
//       message: message,
//     );
//   /// 创建错误恢复结果
//   /// 
//   /// 参数：
//   /// - [success] 恢复是否成功
//   /// - [message] 恢复消息
//   /// - [recoveredValue] 恢复后的值
//   const ErrorRecoveryResult({
//     required this.success,
//     this.message,
//     this.recoveredValue,
//   });

//   /// 恢复是否成功
//   final bool success;
//   /// 恢复消息
//   final String? message;
//   /// 恢复后的值
//   final dynamic recoveredValue;
// }

// /// 错误恢复策略接口
// abstract class ErrorRecoveryStrategy {
//   /// 尝试恢复错误
//   Future<ErrorRecoveryResult> recover(TemplateEngineException error);
  
//   /// 是否可以处理该类型的错误
//   bool canHandle(TemplateEngineErrorType errorType);
// }

// /// 模板不存在错误恢复策略
// class TemplateNotFoundRecoveryStrategy implements ErrorRecoveryStrategy {
//   /// 创建模板不存在错误恢复策略
//   /// 
//   /// 参数：
//   /// - [templateEngine] 模板引擎实例
//   const TemplateNotFoundRecoveryStrategy(this.templateEngine);
  
//   /// 模板引擎实例引用
//   final TemplateEngine templateEngine;

//   @override
//   bool canHandle(TemplateEngineErrorType errorType) {
//     return errorType == TemplateEngineErrorType.templateNotFound;
//   }

//   @override
//   Future<ErrorRecoveryResult> recover(TemplateEngineException error) async {
//     try {
//       // 尝试查找相似的模板名称
//       final availableTemplates = await templateEngine.getAvailableTemplates();
//       final targetTemplate = error.details?['templateName'] as String?;
      
//       if (targetTemplate != null && availableTemplates.isNotEmpty) {
//         // 简单的相似性匹配
//         final suggestions = availableTemplates
//             .where((template) => 
//                 template.toLowerCase().contains(targetTemplate.toLowerCase()) ||
//                 targetTemplate.toLowerCase().contains(template.toLowerCase()),)
//             .toList();
            
//         if (suggestions.isNotEmpty) {
//           return ErrorRecoveryResult.createSuccess(
//             message: '找到相似模板: ${suggestions.join(", ")}',
//             value: suggestions,
//           );
//         }
//       }
      
//       return ErrorRecoveryResult.createSuccess(
//         message: '可用模板: ${availableTemplates.join(", ")}',
//         value: availableTemplates,
//       );
//     } catch (e) {
//       return ErrorRecoveryResult.createFailure('无法获取模板列表: $e');
//     }
//   }
// }

// /// 文件系统错误恢复策略
// class FileSystemErrorRecoveryStrategy implements ErrorRecoveryStrategy {
//   @override
//   bool canHandle(TemplateEngineErrorType errorType) {
//     return errorType == TemplateEngineErrorType.fileSystemError ||
//            errorType == TemplateEngineErrorType.permissionError;
//   }

//   @override
//   Future<ErrorRecoveryResult> recover(TemplateEngineException error) async {
//     try {
//       final operation = error.details?['operation'] as String?;
//       final path = error.details?['path'] as String?;
      
//       if (operation == 'createDirectory' && path != null) {
//         // 尝试创建父目录
//         final parentDir = Directory(path).parent;
//         if (!parentDir.existsSync()) {
//           parentDir.createSync(recursive: true);
//           await Directory(path).create();
//           return ErrorRecoveryResult.createSuccess(
//             message: '成功创建目录: $path',
//           );
//         }
//       }
      
//       if (operation == 'writeFile' && path != null) {
//         // 检查父目录是否存在
//         final file = File(path);
//         final parentDir = file.parent;
//         if (!parentDir.existsSync()) {
//           parentDir.createSync(recursive: true);
//           return ErrorRecoveryResult.createSuccess(
//             message: '成功创建父目录: ${parentDir.path}',
//           );
//         }
//       }
      
//       return ErrorRecoveryResult.createFailure('无法自动恢复文件系统错误');
//     } catch (e) {
//       return ErrorRecoveryResult.createFailure('恢复过程中发生错误: $e');
//     }
//   }
// }

// /// 错误恢复管理器
// class ErrorRecoveryManager {
//   /// 创建错误恢复管理器
//   /// 
//   /// 参数：
//   /// - [templateEngine] 模板引擎实例
//   ErrorRecoveryManager(this.templateEngine) {
//     // 注册默认恢复策略
//     _strategies.addAll([
//       TemplateNotFoundRecoveryStrategy(templateEngine),
//       FileSystemErrorRecoveryStrategy(),
//     ]);
//   }
  
//   /// 模板引擎实例引用，用于错误恢复策略
//   final TemplateEngine templateEngine;
//   final List<ErrorRecoveryStrategy> _strategies = [];

//   /// 注册恢复策略
//   void registerStrategy(ErrorRecoveryStrategy strategy) {
//     _strategies.add(strategy);
//   }

//   /// 尝试恢复错误
//   Future<ErrorRecoveryResult> tryRecover(TemplateEngineException error) async {
//     for (final strategy in _strategies) {
//       if (strategy.canHandle(error.type)) {
//         try {
//           final result = await strategy.recover(error);
//           if (result.success) {
//             cli_logger.Logger.info('错误恢复成功: ${result.message}');
//             return result;
//           }
//         } catch (e) {
//           cli_logger.Logger.warning('恢复策略执行失败: $e');
//         }
//       }
//     }
    
//     return ErrorRecoveryResult.createFailure('无可用的恢复策略');
//   }
// }

// /// 生成钩子类型枚举
// enum HookType {
//   /// 生成前钩子
//   preGeneration,
//   /// 生成后钩子
//   postGeneration,
//   /// 验证前钩子
//   preValidation,
//   /// 验证后钩子
//   postValidation,
// }

// /// 钩子执行上下文
// class HookContext {
//   /// 创建钩子执行上下文
//   const HookContext({
//     required this.templateName,
//     required this.outputPath,
//     required this.variables,
//     this.metadata = const {},
//   });

//   /// 模板名称
//   final String templateName;
//   /// 输出路径
//   final String outputPath;
//   /// 模板变量
//   final Map<String, dynamic> variables;
//   /// 元数据
//   final Map<String, dynamic> metadata;
// }

// /// 钩子执行结果
// class HookResult {
  
//   /// 创建失败结果
//   HookResult.failure(String message) : 
//       this(success: false, message: message);
      
//   /// 创建停止结果
//   HookResult.stop(String message) : 
//       this(
//         success: true, 
//         message: message, 
//         shouldContinue: false,
//       );
//   /// 创建钩子执行结果
//   const HookResult({
//     required this.success,
//     this.message,
//     this.modifiedVariables,
//     this.shouldContinue = true,
//   });

//   /// 执行是否成功
//   final bool success;
//   /// 结果消息
//   final String? message;
//   /// 修改后的变量
//   final Map<String, dynamic>? modifiedVariables;
//   /// 是否应该继续执行
//   final bool shouldContinue;

//   /// 成功结果的常量
//   static const HookResult successResult = HookResult(success: true);
// }

// /// 抽象钩子基类
// abstract class TemplateHook {
//   /// 创建模板钩子
//   const TemplateHook({required this.name});

//   /// 钩子名称
//   final String name;
  
//   /// 执行钩子
//   Future<HookResult> execute(HookContext context);
  
//   /// 钩子类型
//   HookType get type;
  
//   /// 钩子优先级 (数值越小优先级越高)
//   int get priority => 100;
// }

// /// 钩子注册表
// class HookRegistry {
//   final Map<HookType, List<TemplateHook>> _hooks = {};

//   /// 注册钩子
//   void register(TemplateHook hook) {
//     _hooks.putIfAbsent(hook.type, () => []).add(hook);
//     // 按优先级排序
//     _hooks[hook.type]!.sort((a, b) => a.priority.compareTo(b.priority));
//   }

//   /// 注销钩子
//   void unregister(String hookName, HookType type) {
//     _hooks[type]?.removeWhere((hook) => hook.name == hookName);
//   }

//   /// 获取指定类型的钩子
//   List<TemplateHook> getHooks(HookType type) {
//     return _hooks[type] ?? [];
//   }

//   /// 清空所有钩子
//   void clear() {
//     _hooks.clear();
//   }
// }

// /// 模板生成结果
// class GenerationResult {

//   /// 创建失败结果
//   GenerationResult.failure(String message, {String? outputPath})
//     : this(
//       success: false,
//       outputPath: outputPath ?? '',
//       message: message,
//     );
//   /// 创建模板生成结果
//   /// 
//   /// 参数：
//   /// - [success] 生成是否成功
//   /// - [outputPath] 输出路径
//   /// - [generatedFiles] 生成的文件列表
//   /// - [message] 结果消息
//   /// - [duration] 生成耗时
//   /// - [metadata] 结果元数据
//   const GenerationResult({
//     required this.success,
//     required this.outputPath,
//     this.generatedFiles = const [],
//     this.message,
//     this.duration,
//     this.metadata = const {},
//   });

//   /// 生成是否成功
//   final bool success;
//   /// 输出路径
//   final String outputPath;
//   /// 生成的文件列表
//   final List<String> generatedFiles;
//   /// 结果消息
//   final String? message;
//   /// 生成耗时
//   final Duration? duration;
//   /// 结果元数据
//   final Map<String, dynamic> metadata;
// }

// /// 模板继承配置
// class TemplateInheritance {
//   /// 创建模板继承配置
//   const TemplateInheritance({
//     required this.baseTemplate,
//     this.overrides = const {},
//     this.excludeFiles = const [],
//   });

//   /// 基础模板名称
//   final String baseTemplate;
//   /// 变量覆盖配置
//   final Map<String, dynamic> overrides;
//   /// 排除文件列表
//   final List<String> excludeFiles;
// }

// /// 模板引擎管理器
// /// 
// /// 企业级模板引擎，负责模板的完整生命周期管理，包括：
// /// - 模板加载、验证和缓存管理
// /// - 高性能的代码生成和文件处理
// /// - 钩子系统支持，提供扩展能力
// /// - 错误恢复和重试机制
// /// - 异步生成和批量处理
// /// - 模板兼容性检查和版本管理
// /// - 用户体验优化和进度反馈
// /// 
// /// 支持Mason模板格式，提供丰富的变量处理、继承机制和插件扩展能力。
// /// 设计用于高频使用场景，具备完善的缓存策略和性能监控。
// class TemplateEngine {
//   /// 创建模板引擎实例
//   /// 
//   /// [workingDirectory] 工作目录路径，用于确定模板和输出文件的基础路径
//   /// 默认为当前目录
//   TemplateEngine({String? workingDirectory})
//       : workingDirectory = workingDirectory ?? Directory.current.path,
//         hookRegistry = HookRegistry() {
//     // 在构造函数体中初始化错误恢复管理器
//     errorRecoveryManager = ErrorRecoveryManager(this);
//     // 初始化高级钩子管理器
//     advancedHookManager = AdvancedHookManager(this);
//     // 初始化缓存管理器
//     cacheManager = AdvancedTemplateCacheManager(this);
//     // 初始化异步生成管理器
//     asyncManager = AsyncTemplateGenerationManager(this);
//   }

//   /// 配置管理器引用
//   final String workingDirectory;

//   /// 钩子注册表
//   final HookRegistry hookRegistry;

//   /// 错误恢复管理器
//   late final ErrorRecoveryManager errorRecoveryManager;

//   /// 高级钩子管理器
//   late final AdvancedHookManager advancedHookManager;

//   /// 高级缓存管理器
//   late final AdvancedTemplateCacheManager cacheManager;

//   /// 异步生成管理器
//   late final AsyncTemplateGenerationManager asyncManager;

//   /// Mason生成器缓存
//   final Map<String, MasonGenerator> _generatorCache = {};
  
//   /// 模板继承缓存
//   final Map<String, TemplateInheritance> _inheritanceCache = {};

//   /// 模板元数据缓存
//   final Map<String, Map<String, dynamic>> _metadataCache = {};

//   /// 模板参数系统缓存
//   final Map<String, TemplateParameterSystem> _parameterSystemCache = {};

//   /// 重试配置
//   static const int _maxRetries = 3;
//   static const Duration _retryDelay = Duration(milliseconds: 500);

//   /// 性能监控
//   final Map<String, Duration> _performanceMetrics = {};

//   /// 初始化模板引擎
//   /// 
//   /// 执行模板引擎的初始化流程，包括缓存预热、钩子注册等准备工作。
//   /// 建议在使用模板引擎功能前调用此方法以获得最佳性能。
//   /// 
//   /// 参数：
//   /// - [templatesPath] 模板目录路径（可选，默认使用工作目录下的templates文件夹）
//   /// - [configManager] 配置管理器实例（可选）
//   /// 
//   /// 抛出：
//   /// - [TemplateEngineException] 当初始化过程中发生错误时
//   Future<void> initialize({
//     String? templatesPath,
//     dynamic configManager,
//   }) async {
//     try {
//       cli_logger.Logger.info('正在初始化模板引擎...');
      
//       // 预热缓存
//       await cacheManager.warmUpCache();
      
//       // 注册默认钩子
//       registerDefaultHooks();
      
//       cli_logger.Logger.success('模板引擎初始化完成');
//     } catch (e) {
//       cli_logger.Logger.error('模板引擎初始化失败', error: e);
//       rethrow;
//     }
//   }

//   /// 获取可用模板列表
//   /// 
//   /// 扫描模板目录，返回所有有效的Mason模板名称列表。
//   /// 只返回包含有效brick.yaml文件的模板目录。
//   /// 
//   /// 返回：
//   /// - [List<String>] 可用模板名称列表，如果目录不存在或没有有效模板则返回空列表
//   /// 
//   /// 示例：
//   /// ```dart
//   /// final templates = await engine.getAvailableTemplates();
//   /// print('可用模板: ${templates.join(', ')}');
//   /// ```
//   Future<List<String>> getAvailableTemplates() async {
//     try {
//       final templatesPath = path.join(workingDirectory, 'templates');

//       if (!FileUtils.directoryExists(templatesPath)) {
//         cli_logger.Logger.warning('模板目录不存在: $templatesPath');
//         return [];
//       }

//       final entities = FileUtils.listDirectory(templatesPath);
//       final templates = <String>[];

//       for (final entity in entities) {
//         if (entity is Directory) {
//           final templateName = path.basename(entity.path);
//           // 检查是否是有效的Mason模板
//           final brickPath = path.join(entity.path, 'brick.yaml');
//           if (FileUtils.fileExists(brickPath)) {
//             templates.add(templateName);
//           }
//         }
//       }

//       cli_logger.Logger.debug('找到 ${templates.length} 个可用模板');
//       return templates;
//     } catch (e) {
//       cli_logger.Logger.error('获取模板列表失败', error: e);
//       return [];
//     }
//   }

//   /// 检查模板是否存在
//   bool isTemplateAvailable(String templateName) {
//     final templatePath = getTemplatePath(templateName);
//     final brickPath = path.join(templatePath, 'brick.yaml');
//     return FileUtils.fileExists(brickPath);
//   }

//   /// 获取模板路径
//   String getTemplatePath(String templateName) {
//     return path.join(workingDirectory, 'templates', templateName);
//   }

//   /// 加载模板生成器（优化版本）
//   /// 
//   /// 加载指定名称的模板并创建Mason生成器实例。
//   /// 包含智能缓存、重试机制和错误恢复功能。
//   /// 
//   /// 特性：
//   /// - 自动缓存已加载的生成器，提升后续访问性能
//   /// - 内置重试机制，提高加载成功率
//   /// - 智能错误恢复，提供模板建议和修复提示
//   /// - 性能监控和指标记录
//   /// 
//   /// 参数：
//   /// - [templateName] 模板名称，必须是有效的已存在模板
//   /// 
//   /// 返回：
//   /// - [MasonGenerator?] 模板生成器实例，加载失败时返回null
//   /// 
//   /// 抛出：
//   /// - [TemplateEngineException] 当模板不存在、格式无效或加载失败时
//   /// 
//   /// 示例：
//   /// ```dart
//   /// final generator = await engine.loadTemplate('flutter_package');
//   /// if (generator != null) {
//   ///   // 使用生成器进行代码生成
//   /// }
//   /// ```
//   Future<MasonGenerator?> loadTemplate(String templateName) async {
//     final stopwatch = Stopwatch()..start();
    
//     try {
//       // 检查缓存
//       if (_generatorCache.containsKey(templateName)) {
//         cli_logger.Logger.debug('使用缓存的模板生成器: $templateName');
//         _recordPerformance('loadTemplate_cached', stopwatch.elapsed);
//         return _generatorCache[templateName];
//       }

//       // 验证模板存在性
//       if (!isTemplateAvailable(templateName)) {
//         final error = TemplateEngineException.templateNotFound(templateName);
        
//         // 尝试错误恢复
//         final recoveryResult = await errorRecoveryManager.tryRecover(error);
//         if (recoveryResult.success) {
//           cli_logger.Logger.info('模板恢复建议: ${recoveryResult.message}');
//         }
        
//         throw error;
//       }

//       final templatePath = getTemplatePath(templateName);
//       cli_logger.Logger.debug('正在加载模板: $templatePath');

//       // 使用重试机制加载模板
//       MasonGenerator? generator;
//       for (var attempt = 1; attempt <= _maxRetries; attempt++) {
//         try {
//           // 检查brick.yaml是否有效
//           await _validateBrickYaml(templatePath);

//       // 创建Mason生成器
//       final brick = Brick.path(templatePath);
//           generator = await MasonGenerator.fromBrick(brick);
          
//           // 预热generator - 获取变量信息
//           final _ = generator.vars;
          
//           break; // 成功加载，跳出重试循环
//         } on MasonException catch (e) {
//           if (attempt == _maxRetries) {
//             throw TemplateEngineException.masonError(
//               'fromBrick',
//               e,
//               recovery: '请检查模板的brick.yaml文件格式是否正确',
//             );
//           }
          
//           cli_logger.Logger.warning(
//             '模板加载失败 (尝试 $attempt/$_maxRetries): $e',
//           );
//           await Future<void>.delayed(_retryDelay);
//         } catch (e) {
//           if (attempt == _maxRetries) {
//             throw TemplateEngineException.masonError('loadTemplate', e);
//           }
          
//           cli_logger.Logger.warning(
//             '模板加载异常 (尝试 $attempt/$_maxRetries): $e',
//           );
//           await Future<void>.delayed(_retryDelay);
//         }
//       }

//       if (generator == null) {
//         throw TemplateEngineException.masonError(
//           'loadTemplate',
//           'Unable to create generator after $_maxRetries attempts',
//         );
//       }

//       // 缓存生成器和元数据
//       _generatorCache[templateName] = generator;
//       await _cacheTemplateMetadata(templateName, templatePath);

//       _recordPerformance('loadTemplate_new', stopwatch.elapsed);
//       cli_logger.Logger.success(
//           '模板加载成功: $templateName (${stopwatch.elapsedMilliseconds}ms)',
//       );
      
//       return generator;

//     } on TemplateEngineException {
//       _recordPerformance('loadTemplate_error', stopwatch.elapsed);
//       rethrow;
//     } catch (e) {
//       _recordPerformance('loadTemplate_error', stopwatch.elapsed);
//       throw TemplateEngineException.masonError('loadTemplate', e);
//     }
//   }

//   /// 验证brick.yaml文件
//   Future<void> _validateBrickYaml(String templatePath) async {
//     try {
//       final brickPath = path.join(templatePath, 'brick.yaml');
      
//       if (!FileUtils.fileExists(brickPath)) {
//         throw TemplateEngineException(
//           type: TemplateEngineErrorType.invalidTemplateFormat,
//           message: 'brick.yaml文件不存在',
//           details: {'path': brickPath},
//         );
//       }

//       // 尝试解析YAML文件
//       final yamlData = await FileUtils.readYamlFile(brickPath);
      
//       // 验证YAML数据不为空
//       if (yamlData == null) {
//         throw TemplateEngineException(
//           type: TemplateEngineErrorType.invalidTemplateFormat,
//           message: 'brick.yaml文件为空或无法解析',
//           details: {'path': brickPath},
//         );
//       }
      
//       // 验证必需字段
//       if (!yamlData.containsKey('name')) {
//         throw TemplateEngineException(
//           type: TemplateEngineErrorType.invalidTemplateFormat,
//           message: 'brick.yaml缺少必需的name字段',
//           details: {'path': brickPath},
//         );
//       }

//       // 验证__brick__目录存在
//       final brickDir = path.join(templatePath, '__brick__');
//       if (!FileUtils.directoryExists(brickDir)) {
//         throw TemplateEngineException(
//           type: TemplateEngineErrorType.invalidTemplateFormat,
//           message: '__brick__目录不存在',
//           details: {'path': brickDir},
//         );
//       }

//     } catch (e) {
//       if (e is TemplateEngineException) rethrow;
      
//       throw TemplateEngineException(
//         type: TemplateEngineErrorType.invalidTemplateFormat,
//         message: '验证brick.yaml失败',
//         innerException: e,
//       );
//     }
//   }

//   /// 全面的模板兼容性检查
//   /// 
//   /// 对指定模板进行多维度兼容性分析，确保模板能在当前环境正常运行。
//   /// 提供详细的检查报告，包含错误、警告和优化建议。
//   /// 
//   /// 检查维度：
//   /// - 版本兼容性：CLI、Dart、Mason版本要求
//   /// - 依赖兼容性：必需依赖可用性和冲突检测
//   /// - 平台兼容性：操作系统和架构支持
//   /// - 标准合规性：模板格式和最佳实践验证
//   /// 
//   /// 参数：
//   /// - [templateName] 要检查的模板名称
//   /// - [checkVersion] 是否进行版本兼容性检查（默认：true）
//   /// - [checkDependencies] 是否检查依赖兼容性（默认：true）
//   /// - [checkPlatform] 是否检查平台兼容性（默认：true）
//   /// - [checkCompliance] 是否检查标准合规性（默认：true）
//   /// 
//   /// 返回：
//   /// - [CompatibilityCheckResult] 包含详细检查结果的对象
//   /// 
//   /// 示例：
//   /// ```dart
//   /// final result = await engine.checkTemplateCompatibility('my_template');
//   /// if (result.isCompatible) {
//   ///   print('模板兼容');
//   /// } else {
//   ///   print('兼容性问题: ${result.errors.join(', ')}');
//   /// }
//   /// ```
//   Future<CompatibilityCheckResult> checkTemplateCompatibility(
//     String templateName, {
//     bool checkVersion = true,
//     bool checkDependencies = true,
//     bool checkPlatform = true,
//     bool checkCompliance = true,
//   }) async {
//     try {
//       final templatePath = getTemplatePath(templateName);
//       final brickPath = path.join(templatePath, 'brick.yaml');
      
//       if (!FileUtils.fileExists(brickPath)) {
//         return CompatibilityCheckResult.failure(
//           errors: ['模板不存在或brick.yaml文件缺失'],
//         );
//       }

//       final yamlData = await FileUtils.readYamlFile(brickPath);
//       if (yamlData == null) {
//         return CompatibilityCheckResult.failure(
//           errors: ['无法解析brick.yaml文件'],
//         );
//       }

//       final errors = <String>[];
//       final warnings = <String>[];
//       final suggestions = <String>[];
//       final metadata = <String, dynamic>{};

//       // 版本兼容性检查
//       if (checkVersion) {
//         final versionResult = await _checkVersionCompatibility(yamlData);
//         errors.addAll(versionResult.errors);
//         warnings.addAll(versionResult.warnings);
//         suggestions.addAll(versionResult.suggestions);
//         metadata.addAll(versionResult.metadata);
//       }

//       // 依赖兼容性检查
//       if (checkDependencies) {
//         final dependencyResult = await _checkDependencyCompatibility(yamlData);
//         errors.addAll(dependencyResult.errors);
//         warnings.addAll(dependencyResult.warnings);
//         suggestions.addAll(dependencyResult.suggestions);
//         metadata.addAll(dependencyResult.metadata);
//       }

//       // 平台兼容性检查
//       if (checkPlatform) {
//         final platformResult = await _checkPlatformCompatibility(yamlData);
//         errors.addAll(platformResult.errors);
//         warnings.addAll(platformResult.warnings);
//         suggestions.addAll(platformResult.suggestions);
//         metadata.addAll(platformResult.metadata);
//       }

//       // 模板标准合规性检查
//       if (checkCompliance) {
//         final complianceResult = await _checkTemplateCompliance(
//           templatePath,
//           yamlData,
//         );
//         errors.addAll(complianceResult.errors);
//         warnings.addAll(complianceResult.warnings);
//         suggestions.addAll(complianceResult.suggestions);
//         metadata.addAll(complianceResult.metadata);
//       }

//       // 综合结果
//       if (errors.isNotEmpty) {
//         return CompatibilityCheckResult.failure(
//           errors: errors,
//           warnings: warnings,
//           suggestions: suggestions,
//           metadata: metadata,
//         );
//       }

//       return CompatibilityCheckResult.success(
//         warnings: warnings,
//         suggestions: suggestions,
//         metadata: metadata,
//       );

//     } catch (e) {
//       cli_logger.Logger.error('模板兼容性检查失败', error: e);
//       return CompatibilityCheckResult.failure(
//         errors: ['兼容性检查过程中发生异常: $e'],
//       );
//     }
//   }

//   /// 版本兼容性检查
//   Future<CompatibilityCheckResult> _checkVersionCompatibility(
//     Map<String, dynamic> yamlData,
//   ) async {
//     final errors = <String>[];
//     final warnings = <String>[];
//     final suggestions = <String>[];
//     final metadata = <String, dynamic>{};

//     try {
//       // 解析版本信息
//       final versionInfo = _parseVersionInfo(yamlData);
//       metadata['versionInfo'] = {
//         'templateVersion': versionInfo.version,
//         'minCliVersion': versionInfo.minCliVersion,
//         'maxCliVersion': versionInfo.maxCliVersion,
//         'minDartVersion': versionInfo.minDartVersion,
//         'maxDartVersion': versionInfo.maxDartVersion,
//         'minMasonVersion': versionInfo.minMasonVersion,
//         'maxMasonVersion': versionInfo.maxMasonVersion,
//       };

//       // 检查当前CLI版本兼容性
//       const currentCliVersion = '1.0.0'; // 从pubspec.yaml读取实际版本
//       if (versionInfo.minCliVersion != null) {
//         if (_compareVersions(
//             currentCliVersion, versionInfo.minCliVersion!,) < 0) {
//           errors.add(
//             'CLI版本过低: 当前$currentCliVersion < 要求${versionInfo.minCliVersion}',
//           );
//           suggestions.add('请升级CLI到${versionInfo.minCliVersion}或更高版本',);
//         }
//       }

//       if (versionInfo.maxCliVersion != null) {
//         if (_compareVersions(
//             currentCliVersion, versionInfo.maxCliVersion!,) > 0) {
//           warnings.add(
//             'CLI版本可能过高: 当前$currentCliVersion > 建议${versionInfo.maxCliVersion}',
//           );
//           suggestions.add('考虑降级CLI或验证模板兼容性',);
//         }
//       }

//       // 检查Dart版本兼容性
//       final currentDartVersion = _getCurrentDartVersion();
//       if (currentDartVersion != null) {
//         metadata['currentDartVersion'] = currentDartVersion;
        
//         if (versionInfo.minDartVersion != null) {
//           if (_compareVersions(
//                   currentDartVersion, versionInfo.minDartVersion!,
//                   ) < 0) {
//             errors.add(
//               'Dart版本过低: 当前$currentDartVersion < '
//               '要求${versionInfo.minDartVersion}',
//             );
//             suggestions.add(
//               '请升级Dart到${versionInfo.minDartVersion}或更高版本',
//             );
//           }
//         }

//         if (versionInfo.maxDartVersion != null) {
//           if (_compareVersions(
//                   currentDartVersion, versionInfo.maxDartVersion!,
//                   ) > 0) {
//             warnings.add(
//               'Dart版本可能不兼容: 当前$currentDartVersion > '
//               '建议${versionInfo.maxDartVersion}',
//             );
//           }
//         }
//       }

//       // 检查Mason版本兼容性
//       final currentMasonVersion = _getCurrentMasonVersion();
//       if (currentMasonVersion != null) {
//         metadata['currentMasonVersion'] = currentMasonVersion;
        
//         if (versionInfo.minMasonVersion != null) {
//           if (_compareVersions(
//                   currentMasonVersion, versionInfo.minMasonVersion!,
//                   ) < 0) {
//             errors.add(
//               'Mason版本过低: 当前$currentMasonVersion < '
//               '要求${versionInfo.minMasonVersion}',
//             );
//             suggestions.add(
//               '请升级Mason到${versionInfo.minMasonVersion}或更高版本',
//             );
//           }
//         }
//       }

//     } catch (e) {
//       errors.add('版本兼容性检查失败: $e');
//     }

//     return CompatibilityCheckResult(
//       isCompatible: errors.isEmpty,
//       errors: errors,
//       warnings: warnings,
//       suggestions: suggestions,
//       metadata: metadata,
//     );
//   }

//   /// 依赖兼容性检查
//   Future<CompatibilityCheckResult> _checkDependencyCompatibility(
//     Map<String, dynamic> yamlData,
//   ) async {
//     final errors = <String>[];
//     final warnings = <String>[];
//     final suggestions = <String>[];
//     final metadata = <String, dynamic>{};

//     try {
//       // 解析依赖信息
//       final dependencyInfo = _parseDependencyInfo(yamlData);
//       metadata['dependencyInfo'] = {
//         'requiredDependencies': dependencyInfo.requiredDependencies,
//         'optionalDependencies': dependencyInfo.optionalDependencies,
//         'conflictingDependencies': dependencyInfo.conflictingDependencies,
//       };

//       // 检查必需依赖
//       for (final entry in dependencyInfo.requiredDependencies.entries) {
//         final dependencyName = entry.key;
//         final requiredVersion = entry.value;
        
//         final isAvailable = await _checkDependencyAvailability(
//           dependencyName,
//           requiredVersion,
//         );
        
//         if (!isAvailable) {
//           errors.add('缺少必需依赖: $dependencyName (版本: $requiredVersion)');
//           suggestions.add('请安装依赖: dart pub add $dependencyName');
//         }
//       }

//       // 检查冲突依赖
//       for (final conflictingDep in dependencyInfo.conflictingDependencies) {
//         final hasConflict = await _checkDependencyConflict(conflictingDep);
//         if (hasConflict) {
//           warnings.add('检测到冲突依赖: $conflictingDep');
//           suggestions.add('请移除或替换冲突依赖: $conflictingDep');
//         }
//       }

//       // 检查可选依赖
//       for (final entry in dependencyInfo.optionalDependencies.entries) {
//         final dependencyName = entry.key;
//         final version = entry.value;
        
//         final isAvailable = await _checkDependencyAvailability(
//           dependencyName,
//           version,
//         );
        
//         if (!isAvailable) {
//           warnings.add('可选依赖不可用: $dependencyName (版本: $version)');
//           suggestions.add('考虑安装可选依赖以获得完整功能: dart pub add $dependencyName');
//         }
//       }

//     } catch (e) {
//       errors.add('依赖兼容性检查失败: $e');
//     }

//     return CompatibilityCheckResult(
//       isCompatible: errors.isEmpty,
//       errors: errors,
//       warnings: warnings,
//       suggestions: suggestions,
//       metadata: metadata,
//     );
//   }

//   /// 平台兼容性检查
//   Future<CompatibilityCheckResult> _checkPlatformCompatibility(
//     Map<String, dynamic> yamlData,
//   ) async {
//     final errors = <String>[];
//     final warnings = <String>[];
//     final suggestions = <String>[];
//     final metadata = <String, dynamic>{};

//     try {
//       // 解析平台信息
//       final platformInfo = _parsePlatformInfo(yamlData);
//       metadata['platformInfo'] = {
//         'supportedPlatforms': platformInfo.supportedPlatforms,
//         'unsupportedPlatforms': platformInfo.unsupportedPlatforms,
//         'requiredFeatures': platformInfo.requiredFeatures,
//         'optionalFeatures': platformInfo.optionalFeatures,
//       };

//       // 获取当前平台信息
//       final currentPlatform = _getCurrentPlatform();
//       metadata['currentPlatform'] = currentPlatform;

//       // 检查平台支持
//       if (platformInfo.supportedPlatforms.isNotEmpty &&
//           !platformInfo.supportedPlatforms.contains(currentPlatform)) {
//         errors.add('当前平台不受支持: $currentPlatform');
//         suggestions.add(
//           '支持的平台: ${platformInfo.supportedPlatforms.join(", ")}',
//         );
//       }

//       // 检查平台排除
//       if (platformInfo.unsupportedPlatforms.contains(currentPlatform)) {
//         errors.add('当前平台被明确排除: $currentPlatform');
//         suggestions.add('请在支持的平台上使用此模板');
//       }

//       // 检查必需功能
//       for (final feature in platformInfo.requiredFeatures) {
//         final isSupported = _checkPlatformFeature(feature);
//         if (!isSupported) {
//           errors.add('缺少必需的平台功能: $feature');
//           suggestions.add('请确保平台支持功能: $feature');
//         }
//       }

//       // 检查可选功能
//       for (final feature in platformInfo.optionalFeatures) {
//         final isSupported = _checkPlatformFeature(feature);
//         if (!isSupported) {
//           warnings.add('可选平台功能不可用: $feature');
//           suggestions.add('某些功能可能受限，因为缺少: $feature');
//         }
//       }

//     } catch (e) {
//       errors.add('平台兼容性检查失败: $e');
//     }

//     return CompatibilityCheckResult(
//       isCompatible: errors.isEmpty,
//       errors: errors,
//       warnings: warnings,
//       suggestions: suggestions,
//       metadata: metadata,
//     );
//   }

//   /// 模板标准合规性检查
//   Future<CompatibilityCheckResult> _checkTemplateCompliance(
//     String templatePath,
//     Map<String, dynamic> yamlData,
//   ) async {
//     final errors = <String>[];
//     final warnings = <String>[];
//     final suggestions = <String>[];
//     final metadata = <String, dynamic>{};

//     try {
//       // 检查必需字段
//       final requiredFields = ['name', 'description', 'version'];
//       for (final field in requiredFields) {
//         if (!yamlData.containsKey(field) || 
//             StringUtils.isBlank(yamlData[field]?.toString())) {
//           errors.add('缺少必需字段: $field');
//           suggestions.add('请在brick.yaml中添加$field字段');
//         }
//       }

//       // 检查版本格式
//       if (yamlData.containsKey('version')) {
//         final version = yamlData['version']?.toString() ?? '';
//         if (!_isValidVersionFormat(version)) {
//           errors.add('版本格式无效: $version');
//           suggestions.add('请使用语义版本格式 (例如: 1.0.0)');
//         }
//       }

//       // 检查变量定义
//       if (yamlData.containsKey('vars')) {
//         final vars = Map<String, dynamic>.from(yamlData['vars'] as Map? ?? {});
//         for (final entry in vars.entries) {
//           final varName = entry.key;
//           final varConfig = Map<String, dynamic>.from(
//             entry.value as Map? ?? {},
//           );
          
//           // 检查变量类型
//           if (!varConfig.containsKey('type')) {
//             warnings.add('变量缺少类型定义: $varName');
//             suggestions.add('建议为变量$varName添加type字段');
//           }
          
//           // 检查变量描述
//           if (!varConfig.containsKey('description')) {
//             warnings.add('变量缺少描述: $varName');
//             suggestions.add('建议为变量$varName添加description字段');
//           }
//         }
//       }

//       // 检查__brick__目录结构
//       final brickDir = path.join(templatePath, '__brick__');
//       if (FileUtils.directoryExists(brickDir)) {
//         final fileCount = await _countTemplateFiles(templatePath);
//         metadata['templateFileCount'] = fileCount;
        
//         if (fileCount == 0) {
//           warnings.add('__brick__目录为空');
//           suggestions.add('请添加模板文件到__brick__目录');
//         }
//       }

//       // 检查README文件
//       final readmePath = path.join(templatePath, 'README.md');
//       if (!FileUtils.fileExists(readmePath)) {
//         warnings.add('缺少README.md文件');
//         suggestions.add('建议添加README.md文档说明模板用法');
//       }

//       // 检查许可证文件
//       final licensePath = path.join(templatePath, 'LICENSE');
//       if (!FileUtils.fileExists(licensePath)) {
//         warnings.add('缺少LICENSE文件');
//         suggestions.add('建议添加LICENSE文件说明许可证信息');
//       }

//       // 检查CHANGELOG文件
//       final changelogPath = path.join(templatePath, 'CHANGELOG.md');
//       if (!FileUtils.fileExists(changelogPath)) {
//         warnings.add('缺少CHANGELOG.md文件');
//         suggestions.add('建议添加CHANGELOG.md文件记录版本变更');
//       }

//     } catch (e) {
//       errors.add('模板合规性检查失败: $e');
//     }

//     return CompatibilityCheckResult(
//       isCompatible: errors.isEmpty,
//       errors: errors,
//       warnings: warnings,
//       suggestions: suggestions,
//       metadata: metadata,
//     );
//   }

//   /// 缓存模板元数据
//   Future<void> _cacheTemplateMetadata(
//     String templateName,
//     String templatePath,
//   ) async {
//     try {
//       final brickPath = path.join(templatePath, 'brick.yaml');
//       final yamlData = await FileUtils.readYamlFile(brickPath);
      
//       // 检查YAML数据是否有效
//       if (yamlData == null) {
//         cli_logger.Logger.warning('无法读取模板元数据: $templateName');
//         return;
//       }
      
//       // 添加额外的元数据
//       final metadata = Map<String, dynamic>.from(yamlData);
//       metadata['_cached_at'] = DateTime.now().toIso8601String();
//       metadata['_template_path'] = templatePath;
//       metadata['_file_count'] = await _countTemplateFiles(templatePath);
      
//       _metadataCache[templateName] = metadata;
      
//     } catch (e) {
//       cli_logger.Logger.warning('缓存模板元数据失败: $e');
//     }
//   }

//   /// 计算模板文件数量
//   Future<int> _countTemplateFiles(String templatePath) async {
//     try {
//       final brickDir = path.join(templatePath, '__brick__');
//       if (!FileUtils.directoryExists(brickDir)) return 0;
      
//       var count = 0;
//       final entities = FileUtils.listDirectory(brickDir);
      
//       for (final entity in entities) {
//         if (entity is File) {
//           count++;
//         } else if (entity is Directory) {
//           // 递归计算子目录文件
//           count += await _countTemplateFiles(entity.path);
//         }
//       }
      
//       return count;
//     } catch (e) {
//       return 0;
//     }

//   }

//   /// 记录性能指标
//   void _recordPerformance(String operation, Duration duration) {
//     _performanceMetrics[operation] = duration;
//     cli_logger.Logger.debug('性能指标: $operation = ${duration.inMilliseconds}ms');
//   }

//   /// 生成模块代码（优化版本）
//   Future<bool> generateModule({
//     required String templateName,
//     required String outputPath,
//     required Map<String, dynamic> variables,
//     bool overwrite = false,
//   }) async {
//     final stopwatch = Stopwatch()..start();
    
//     try {
//       cli_logger.Logger.info('正在生成模块: $templateName -> $outputPath');

//       // 验证和预处理变量
//       final validationErrors = validateTemplateVariables(
//         templateName: templateName,
//         variables: variables,
//       );
      
//       if (validationErrors.isNotEmpty) {
//         final error = TemplateEngineException.variableValidationError(
//           validationErrors,
//         );
//         throw error;
//       }

//       // 预处理变量
//       final processedVariables = preprocessVariables(variables);

//       // 加载模板（这个方法已经优化过了）
//       final generator = await loadTemplate(templateName);
//       if (generator == null) {
//         throw TemplateEngineException.templateNotFound(templateName);
//       }

//       // 检查输出目录冲突
//       if (FileUtils.directoryExists(outputPath) && !overwrite) {
//         throw TemplateEngineException(
//           type: TemplateEngineErrorType.outputPathConflict,
//           message: '输出目录已存在且未启用覆盖模式',
//           details: {'outputPath': outputPath, 'overwrite': overwrite},
//           recovery: '请使用 --overwrite 参数或选择不同的输出路径',
//         );
//       }

//       // 创建输出目录（带错误处理）
//       await _createOutputDirectoryWithRecovery(outputPath);

//       // 执行代码生成（带重试机制）
//       await _executeGenerationWithRetry(
//         generator,
//         outputPath,
//         processedVariables,
//       );

//       _recordPerformance('generateModule', stopwatch.elapsed);
//       cli_logger.Logger.success(
//         '模块生成完成: $outputPath (${stopwatch.elapsedMilliseconds}ms)',
//       );
//       return true;

//     } on TemplateEngineException catch (e) {
//       _recordPerformance('generateModule_error', stopwatch.elapsed);
      
//       // 尝试错误恢复
//       final recoveryResult = await errorRecoveryManager.tryRecover(e);
//       if (recoveryResult.success) {
//         cli_logger.Logger.info('错误恢复建议: ${recoveryResult.message}');
//       }
      
//       cli_logger.Logger.error('模块生成失败: ${e.message}');
//       if (e.recovery != null) {
//         cli_logger.Logger.info('建议: ${e.recovery}');
//       }
//       return false;
      
//     } catch (e) {
//       _recordPerformance('generateModule_error', stopwatch.elapsed);
//       cli_logger.Logger.error('模块生成失败', error: e);
//         return false;
//     }
//       }

//   /// 创建输出目录（带错误恢复）
//   Future<void> _createOutputDirectoryWithRecovery(String outputPath) async {
//     try {
//       await FileUtils.createDirectory(outputPath);
//     } catch (e) {
//       final error = TemplateEngineException.fileSystemError(
//         'createDirectory',
//         outputPath,
//         e,
//       );
      
//       // 尝试恢复
//       final recoveryResult = await errorRecoveryManager.tryRecover(error);
//       if (!recoveryResult.success) {
//         throw error;
//       }
      
//       // 重新尝试创建目录
//       try {
//         await FileUtils.createDirectory(outputPath);
//       } catch (retryError) {
//         throw TemplateEngineException.fileSystemError(
//           'createDirectory_retry',
//           outputPath,
//           retryError,
//         );
//       }
//     }
//   }

//   /// 执行生成（带重试机制）
//   Future<void> _executeGenerationWithRetry(
//     MasonGenerator generator,
//     String outputPath,
//     Map<String, dynamic> variables,
//   ) async {
//     for (var attempt = 1; attempt <= _maxRetries; attempt++) {
//       try {
//       // 准备生成上下文
//       final target = DirectoryGeneratorTarget(Directory(outputPath));

//       // 执行代码生成
//       await generator.generate(target, vars: variables);

//         // 验证生成结果
//         await _validateGenerationResult(outputPath);
        
//         return; // 成功，退出重试循环
        
//       } on MasonException catch (e) {
//         if (attempt == _maxRetries) {
//           throw TemplateEngineException.masonError(
//             'generate',
//             e,
//             recovery: '请检查模板变量是否正确，或模板文件是否有效',
//           );
//         }
        
//         cli_logger.Logger.warning(
//           'Mason生成失败 (尝试 $attempt/$_maxRetries): $e',
//         );
        
//         // 清理失败的生成结果
//         await _cleanupFailedGeneration(outputPath);
//         await Future<void>.delayed(_retryDelay);
        
//     } catch (e) {
//         if (attempt == _maxRetries) {
//           throw TemplateEngineException(
//             type: TemplateEngineErrorType.unknown,
//             message: '代码生成失败',
//             innerException: e,
//           );
//         }
        
//         cli_logger.Logger.warning(
//           '生成异常 (尝试 $attempt/$_maxRetries): $e',
//         );
        
//         await _cleanupFailedGeneration(outputPath);
//         await Future<void>.delayed(_retryDelay);
//       }
//     }
//   }

//   /// 验证生成结果
//   Future<void> _validateGenerationResult(String outputPath) async {
//     try {
//       // 检查输出目录是否存在
//       if (!FileUtils.directoryExists(outputPath)) {
//         throw TemplateEngineException(
//           type: TemplateEngineErrorType.fileSystemError,
//           message: '生成的输出目录不存在',
//           details: {'outputPath': outputPath},
//         );
//       }

//       // 检查是否有文件生成
//       final entities = FileUtils.listDirectory(outputPath);
//       if (entities.isEmpty) {
//         throw TemplateEngineException(
//           type: TemplateEngineErrorType.masonError,
//           message: '没有生成任何文件',
//           details: {'outputPath': outputPath},
//           recovery: '请检查模板配置和变量是否正确',
//         );
//       }

//       // 基本的文件内容检查
//       for (final entity in entities) {
//         if (entity is File) {
//           final content = await entity.readAsString();
          
//           // 检查是否还有未替换的变量
//           if (content.contains('{{') && content.contains('}}')) {
//             cli_logger.Logger.warning(
//               '检测到未替换的模板变量: ${entity.path}',
//             );
//           }
//         }
//       }

//     } catch (e) {
//       if (e is TemplateEngineException) rethrow;
      
//       throw TemplateEngineException(
//         type: TemplateEngineErrorType.unknown,
//         message: '验证生成结果失败',
//         innerException: e,
//       );
//     }
//   }

//   /// 清理失败的生成结果
//   Future<void> _cleanupFailedGeneration(String outputPath) async {
//     try {
//       if (FileUtils.directoryExists(outputPath)) {
//         // 删除部分生成的文件
//         final directory = Directory(outputPath);
//         await directory.delete(recursive: true);
//         cli_logger.Logger.debug('已清理失败的生成结果: $outputPath');
//       }
//     } catch (e) {
//       cli_logger.Logger.warning('清理失败的生成结果时发生错误: $e');
//     }
//   }

//   /// 获取模板变量名称列表
//   Future<List<String>?> getTemplateVariables(String templateName) async {
//     try {
//       final generator = await loadTemplate(templateName);
//       if (generator == null) {
//         return null;
//       }

//       // Mason的generator.vars实际返回List<String>（变量名列表）
//       return generator.vars;
//     } catch (e) {
//       cli_logger.Logger.error('获取模板变量失败', error: e);
//       return null;
//     }
//   }

//   /// 验证模板变量
//   Map<String, String> validateTemplateVariables({
//     required String templateName,
//     required Map<String, dynamic> variables,
//   }) {
//     final errors = <String, String>{};

//     try {
//       // 如果是测试模板，使用宽松验证
//       if (templateName.contains('test') || templateName.contains('cache')) {
//         return errors; // 测试模板跳过严格验证
//       }

//       // 检查必需的变量
//       final requiredVars = ['module_id', 'module_name'];
//       for (final varName in requiredVars) {
//         if (!variables.containsKey(varName) ||
//             StringUtils.isBlank(variables[varName]?.toString())) {
//           errors[varName] = '必需变量未提供或为空';
//         }
//       }

//       // 验证模块ID格式
//       if (variables.containsKey('module_id')) {
//         final moduleId = variables['module_id']?.toString() ?? '';
//         if (moduleId.isNotEmpty && !StringUtils.isValidIdentifier(moduleId)) {
//           errors['module_id'] = '模块ID格式无效，必须是有效的标识符';
//         }
//       }

//       // 验证类名格式
//       if (variables.containsKey('class_name')) {
//         final className = variables['class_name']?.toString() ?? '';
//         if (className.isNotEmpty && !StringUtils.isValidClassName(className)) {
//           errors['class_name'] = '类名格式无效，必须以大写字母开头';
//         }
//       }
//     } catch (e) {
//       cli_logger.Logger.error('验证模板变量时发生异常', error: e);
//       errors['_general'] = '验证过程中发生异常: $e';
//     }

//     return errors;
//   }

//   /// 预处理模板变量
//   Map<String, dynamic> preprocessVariables(Map<String, dynamic> variables) {
//     final processed = Map<String, dynamic>.from(variables);

//     try {
//       // 自动生成相关变量
//       if (processed.containsKey('module_id')) {
//         final moduleId = processed['module_id'].toString();

//         // 生成类名（如果未提供）
//         if (!processed.containsKey('class_name') ||
//             StringUtils.isBlank(processed['class_name']?.toString())) {
//           processed['class_name'] = StringUtils.toPascalCase(moduleId);
//         }

//         // 生成文件名
//         processed['file_name'] = StringUtils.toSnakeCase(moduleId);
//         processed['kebab_name'] = StringUtils.toKebabCase(moduleId);
//         processed['camel_name'] = StringUtils.toCamelCase(moduleId);
//       }

//       // 生成时间戳
//       final now = DateTime.now();
//       processed['generated_date'] = now.toIso8601String().substring(0, 10);
//       processed['generated_time'] = now.toIso8601String().substring(11, 19);
//       processed['generated_year'] = now.year.toString();

//       // 默认作者信息
//       if (!processed.containsKey('author') ||
//           StringUtils.isBlank(processed['author']?.toString())) {
//         processed['author'] = 'lgnorant-lu';
//       }

//       // 默认版本
//       if (!processed.containsKey('version') ||
//           StringUtils.isBlank(processed['version']?.toString())) {
//         processed['version'] = '1.0.0';
//       }
//     } catch (e) {
//       cli_logger.Logger.error('预处理模板变量时发生异常', error: e);
//     }

//     return processed;
//   }

//   /// 创建基础模板
//   Future<bool> createBaseTemplate(String templateName) async {
//     try {
//       final templatePath = getTemplatePath(templateName);

//       if (FileUtils.directoryExists(templatePath)) {
//         cli_logger.Logger.warning('模板已存在: $templateName');
//         return false;
//       }

//       // 创建模板目录
//       await FileUtils.createDirectory(templatePath);

//       // 创建brick.yaml文件
//       final brickContent = _generateBrickYaml(templateName);
//       final brickPath = path.join(templatePath, 'brick.yaml');
//       await FileUtils.writeFileAsString(brickPath, brickContent);

//       // 创建基础模板文件
//       await _createBasicTemplateFiles(templatePath);

//       cli_logger.Logger.success('基础模板创建成功: $templateName');
//       return true;
//     } catch (e) {
//       cli_logger.Logger.error('创建基础模板失败', error: e);
//       return false;
//     }
//   }

//   /// 生成brick.yaml内容
//   String _generateBrickYaml(String templateName) {
//     return '''
// name: $templateName
// description: Ming Status CLI生成的模板
// version: 0.1.0+1

// vars:
//   module_id:
//     type: string
//     description: 模块唯一标识符
//     prompt: 请输入模块ID
//   module_name:
//     type: string
//     description: 模块显示名称
//     prompt: 请输入模块名称
//   author:
//     type: string
//     description: 作者名称
//     default: lgnorant-lu
//   description:
//     type: string
//     description: 模块描述
//     default: 模块描述
// ''';
//   }

//   /// 创建基础模板文件
//   Future<void> _createBasicTemplateFiles(String templatePath) async {
//     // 创建__brick__目录
//     final brickDir = path.join(templatePath, '__brick__');
//     await FileUtils.createDirectory(brickDir);

//     // 创建基础模块文件模板
//     const moduleTemplate = '''
// /*
// ---------------------------------------------------------------
// File name:          {{file_name}}.dart
// Author:             {{author}}
// Date created:       {{generated_date}}
// Last modified:      {{generated_date}}
// Dart Version:       3.2+
// Description:        {{description}}
// ---------------------------------------------------------------
// */

// /// {{module_name}}模块
// class {{class_name}} {
//   /// 模块ID
//   static const String moduleId = '{{module_id}}';
  
//   /// 模块名称
//   static const String moduleName = '{{module_name}}';
  
//   /// 初始化模块
//   void initialize() {
//     // TODO: 实现模块初始化逻辑
//   }
// }
// ''';

//     final moduleFilePath = path.join(brickDir, '{{file_name}}.dart');
//     await FileUtils.writeFileAsString(moduleFilePath, moduleTemplate);

//     cli_logger.Logger.debug('基础模板文件创建完成');
//   }

//   /// 清理缓存
//   void clearCache() {
//     _generatorCache.clear();
//     _inheritanceCache.clear();
//     _metadataCache.clear();
//     _parameterSystemCache.clear();
//     _performanceMetrics.clear();
//     cli_logger.Logger.debug('模板引擎缓存已清理');
//   }

//   /// 获取性能报告
//   Map<String, dynamic> getPerformanceReport() {
//     return {
//       'cache_stats': {
//         'generators_cached': _generatorCache.length,
//         'metadata_cached': _metadataCache.length,
//         'inheritance_cached': _inheritanceCache.length,
//       },
//       'performance_metrics': _performanceMetrics
//           .map((key, value) => MapEntry(key, value.inMilliseconds)),
//       'average_times': _calculateAveragePerformance(),
//     };
//   }

//   /// 计算平均性能指标
//   Map<String, double> _calculateAveragePerformance() {
//     final averages = <String, double>{};
//     final groupedMetrics = <String, List<Duration>>{};

//     // 按操作类型分组
//     for (final entry in _performanceMetrics.entries) {
//       final baseOperation = entry.key.split('_').first;
//       groupedMetrics.putIfAbsent(baseOperation, () => []).add(entry.value);
//     }

//     // 计算平均值
//     for (final entry in groupedMetrics.entries) {
//       final totalMs = entry.value.fold<int>(
//         0,
//         (sum, duration) => sum + duration.inMilliseconds,
//       );
//       averages[entry.key] = totalMs / entry.value.length;
//     }

//     return averages;
//   }

//   /// 预热引擎（加载常用模板）
//   Future<void> warmup({List<String>? templateNames}) async {
//     final stopwatch = Stopwatch()..start();
    
//     try {
//       final templates = templateNames ?? await getAvailableTemplates();
//       cli_logger.Logger.info('预热模板引擎，加载 ${templates.length} 个模板...');

//       // 并发加载模板
//       final futures = templates.map((templateName) async {
//         try {
//           await loadTemplate(templateName);
//           return templateName;
//         } catch (e) {
//           cli_logger.Logger.warning('预热模板失败: $templateName - $e');
//           return null;
//         }
//       });

//       final results = await Future.wait(futures);
//       final successCount = results.where((result) => result != null).length;

//       stopwatch.stop();
//       _recordPerformance('warmup', stopwatch.elapsed);
      
//       cli_logger.Logger.success(
//         '模板引擎预热完成: $successCount/${templates.length} 个模板 '
//         '(${stopwatch.elapsedMilliseconds}ms)',
//       );
//     } catch (e) {
//       stopwatch.stop();
//       cli_logger.Logger.error('模板引擎预热失败', error: e);
//     }
//   }

//   /// 批量验证模板
//   Future<Map<String, List<String>>> validateAllTemplates() async {
//     final results = <String, List<String>>{};
//     final templates = await getAvailableTemplates();

//     for (final templateName in templates) {
//       try {
//         await _validateBrickYaml(getTemplatePath(templateName));
//         results[templateName] = []; // 空列表表示无错误
//       } catch (e) {
//         results[templateName] = [e.toString()];
//       }
//     }

//     return results;
//   }

//   /// 批量兼容性检查模板（增强版本）
//   Future<Map<String, CompatibilityCheckResult>> 
//       validateAllTemplatesCompatibility({
//     bool checkVersion = true,
//     bool checkDependencies = true,
//     bool checkPlatform = true,
//     bool checkCompliance = true,
//   }) async {
//     final results = <String, CompatibilityCheckResult>{};
//     final templates = await getAvailableTemplates();

//     cli_logger.Logger.info('开始批量兼容性检查，共 ${templates.length} 个模板...');

//     for (final templateName in templates) {
//       try {
//         final result = await checkTemplateCompatibility(
//           templateName,
//           checkVersion: checkVersion,
//           checkDependencies: checkDependencies,
//           checkPlatform: checkPlatform,
//           checkCompliance: checkCompliance,
//         );
//         results[templateName] = result;

//         if (result.isCompatible) {
//           cli_logger.Logger.debug('✓ $templateName: 兼容性检查通过');
//         } else {
//           cli_logger.Logger.warning(
//             '✗ $templateName: 兼容性检查失败 (${result.errors.length} 个错误)',
//           );
//         }
//       } catch (e) {
//         results[templateName] = CompatibilityCheckResult.failure(
//           errors: ['兼容性检查异常: $e'],
//         );
//         cli_logger.Logger.error('模板兼容性检查异常: $templateName', error: e);
//       }
//     }

//     final compatibleCount = results.values.where((r) => r.isCompatible).length;
//     cli_logger.Logger.info(
//       '批量兼容性检查完成: $compatibleCount/${templates.length} 个模板兼容',
//     );

//     return results;
//   }

//   /// 模板健康状态检查（增强版本）
//   Future<Map<String, dynamic>> checkTemplateSystemHealth() async {
//     final health = <String, dynamic>{
//       'status': 'healthy',
//       'checks': <String, dynamic>{},
//       'warnings': <String>[],
//       'errors': <String>[],
//       'templates': <String, dynamic>{},
//     };

//     try {
//       // 基础健康检查
//       final basicHealth = await checkHealth();
//       health['checks'].addAll(
//         Map<String, dynamic>.from(basicHealth['checks'] as Map? ?? {}),
//       );
//       health['warnings'].addAll(
//         (basicHealth['warnings'] as List<dynamic>?)?.cast<String>() ?? [],
//       );
//       health['errors'].addAll(
//         (basicHealth['errors'] as List<dynamic>?)?.cast<String>() ?? [],
//       );

//       // 模板兼容性健康检查
//       final compatibilityResults = await validateAllTemplatesCompatibility();
      
//       var compatibleCount = 0;
//       var totalErrors = 0;
//       var totalWarnings = 0;

//       for (final entry in compatibilityResults.entries) {
//         final templateName = entry.key;
//         final result = entry.value;

//         if (result.isCompatible) {
//           compatibleCount++;
//         }

//         totalErrors += result.errors.length;
//         totalWarnings += result.warnings.length;

//         health['templates'][templateName] = {
//           'compatible': result.isCompatible,
//           'errors': result.errors.length,
//           'warnings': result.warnings.length,
//           'platform_supported': 
//               (result.metadata['currentPlatform'] as Object?) != null,
//         };
//       }

//       // 更新整体健康状态
//       health['checks']['template_compatibility'] = {
//         'total_templates': compatibilityResults.length,
//         'compatible_templates': compatibleCount,
//         'compatibility_rate': compatibilityResults.isEmpty 
//             ? 0.0 
//             : (compatibleCount / compatibilityResults.length * 100)
//                 .toStringAsFixed(1),
//         'total_errors': totalErrors,
//         'total_warnings': totalWarnings,
//       };

//       // 根据兼容性结果调整健康状态
//       if (totalErrors > 0) {
//         health['status'] = 'unhealthy';
//         health['errors'].add('检测到 $totalErrors 个模板兼容性错误');
//       } else if (totalWarnings > 0) {
//         if (health['status'] == 'healthy') health['status'] = 'warning';
//         health['warnings'].add('检测到 $totalWarnings 个模板兼容性警告');
//       }

//       // 兼容性率检查
//       final compatibilityRate = compatibleCount / compatibilityResults.length;
//       if (compatibilityRate < 0.8) {
//         health['status'] = 'unhealthy';
//         health['errors'].add(
//           '模板兼容性率过低: ${(compatibilityRate * 100).toStringAsFixed(1)}%',
//         );
//       } else if (compatibilityRate < 0.9) {
//         if (health['status'] == 'healthy') health['status'] = 'warning';
//         health['warnings'].add(
//           '模板兼容性率较低: ${(compatibilityRate * 100).toStringAsFixed(1)}%',
//         );
//       }

//     } catch (e) {
//       health['errors'].add('模板系统健康检查失败: $e');
//       health['status'] = 'unhealthy';
//     }

//     return health;
//   }

//   /// 检查模板健康状态
//   Future<Map<String, dynamic>> checkHealth() async {
//     final health = <String, dynamic>{
//       'status': 'healthy',
//       'checks': <String, dynamic>{},
//       'warnings': <String>[],
//       'errors': <String>[],
//     };

//     try {
//       // 检查工作目录
//       if (!FileUtils.directoryExists(workingDirectory)) {
//         health['errors'].add('工作目录不存在: $workingDirectory');
//         health['status'] = 'unhealthy';
//       } else {
//         health['checks']['working_directory'] = 'ok';
//       }

//       // 检查模板目录
//       final templatesPath = path.join(workingDirectory, 'templates');
//       if (!FileUtils.directoryExists(templatesPath)) {
//         health['warnings'].add('模板目录不存在: $templatesPath');
//         health['status'] = health['status'] == 'unhealthy' 
//             ? 'unhealthy' 
//             : 'warning';
//       } else {
//         health['checks']['templates_directory'] = 'ok';
//       }

//       // 检查可用模板
//       final templates = await getAvailableTemplates();
//       health['checks']['available_templates'] = templates.length;
//       if (templates.isEmpty) {
//         health['warnings'].add('没有找到可用的模板');
//         health['status'] = health['status'] == 'unhealthy' ? 'unhealthy' : 'warning';
//       }

//       // 检查缓存状态
//       health['checks']['cache_size'] = _generatorCache.length;
//       health['checks']['metadata_cache_size'] = _metadataCache.length;

//       // 检查性能指标
//       if (_performanceMetrics.isNotEmpty) {
//         final avgMetrics = _calculateAveragePerformance();
//         health['checks']['average_load_time'] = 
//             avgMetrics['loadTemplate']?.toStringAsFixed(2) ?? 'N/A';
//       }

//     } catch (e) {
//       health['errors'].add('健康检查失败: $e');
//       health['status'] = 'unhealthy';
//     }

//     return health;
//   }

//   /// 重建模板缓存
//   Future<void> rebuildCache() async {
//     cli_logger.Logger.info('正在重建模板缓存...');
    
//     // 清理现有缓存
//     clearCache();
    
//     // 重新加载所有模板
//     await warmup();
    
//     cli_logger.Logger.success('模板缓存重建完成');
//   }

//   /// 获取模板信息
//   Future<Map<String, dynamic>?> getTemplateInfo(String templateName) async {
//     try {
//       final templatePath = getTemplatePath(templateName);
//       final brickPath = path.join(templatePath, 'brick.yaml');

//       if (!FileUtils.fileExists(brickPath)) {
//         return null;
//       }

//       final yamlData = await FileUtils.readYamlFile(brickPath);
//       return yamlData;
//     } catch (e) {
//       cli_logger.Logger.error('获取模板信息失败', error: e);
//       return null;
//     }
//   }

//   // ==================== 高级特性方法 ====================

//   /// 带钩子支持的高级生成方法
//   Future<GenerationResult> generateWithHooks({
//     required String templateName,
//     required String outputPath,
//     required Map<String, dynamic> variables,
//     bool overwrite = false,
//     List<TemplateHook>? additionalHooks,
//     TemplateInheritance? inheritance,
//   }) async {
//     final stopwatch = Stopwatch()..start();
//     var currentVariables = Map<String, dynamic>.from(variables);

//     try {
//       // 注册临时钩子
//       if (additionalHooks != null) {
//         for (final hook in additionalHooks) {
//           hookRegistry.register(hook);
//         }
//       }

//       // 处理模板继承
//       if (inheritance != null) {
//         currentVariables = await _processTemplateInheritance(
//           templateName,
//           inheritance,
//           currentVariables,
//         );
//       }

//       // 创建钩子上下文
//       final context = HookContext(
//         templateName: templateName,
//         outputPath: outputPath,
//         variables: currentVariables,
//         metadata: {
//           'startTime': DateTime.now().toIso8601String(),
//           'inheritance': inheritance != null,
//         },
//       );

//       // 执行pre-generation钩子
//       final preResult = await _executeHooks(HookType.preGeneration, context);
//       if (!preResult.success || !preResult.shouldContinue) {
//         stopwatch.stop();
//         return GenerationResult.failure(
//           preResult.message ?? '预生成钩子执行失败',
//           outputPath: outputPath,
//         );
//       }

//       // 如果钩子修改了变量，使用修改后的变量
//       if (preResult.modifiedVariables != null) {
//         currentVariables.addAll(preResult.modifiedVariables!);
//       }

//       // 执行实际的模板生成
//       final generationSuccess = await generateModule(
//         templateName: templateName,
//         outputPath: outputPath,
//         variables: currentVariables,
//         overwrite: overwrite,
//       );

//       if (!generationSuccess) {
//         stopwatch.stop();
//         return GenerationResult.failure(
//           '模板生成失败',
//           outputPath: outputPath,
//         );
//       }

//       // 获取生成的文件列表
//       final generatedFiles = await _getGeneratedFiles(outputPath);

//       // 执行post-generation钩子
//       final updatedContext = HookContext(
//         templateName: templateName,
//         outputPath: outputPath,
//         variables: currentVariables,
//         metadata: {
//           ...context.metadata,
//           'generatedFiles': generatedFiles,
//           'endTime': DateTime.now().toIso8601String(),
//         },
//       );

//       final postResult = await _executeHooks(HookType.postGeneration, updatedContext);
//       if (!postResult.success) {
//         cli_logger.Logger.warning('后生成钩子执行失败: ${postResult.message}');
//       }

//       stopwatch.stop();

//       return GenerationResult(
//         success: true,
//         outputPath: outputPath,
//         generatedFiles: generatedFiles,
//         duration: stopwatch.elapsed,
//         metadata: {
//           'templateName': templateName,
//           'inheritanceUsed': inheritance != null,
//           'hooksExecuted': true,
//         },
//       );

//     } catch (e) {
//       stopwatch.stop();
//       cli_logger.Logger.error('高级生成过程中发生异常', error: e);
//       return GenerationResult.failure(
//         '生成过程中发生异常: $e',
//         outputPath: outputPath,
//       );
//     } finally {
//       // 清理临时钩子
//       if (additionalHooks != null) {
//         for (final hook in additionalHooks) {
//           hookRegistry.unregister(hook.name, hook.type);
//         }
//       }
//     }
//   }

//   /// 异步并发生成多个模块
//   Future<List<GenerationResult>> generateConcurrent({
//     required List<Map<String, dynamic>> generationTasks,
//     int concurrency = 3,
//   }) async {
//     final results = <GenerationResult>[];

//     for (var i = 0; i < generationTasks.length; i += concurrency) {
//       final batch = generationTasks.skip(i).take(concurrency);
      
//       final batchFutures = batch.map((task) async {
//         return generateWithHooks(
//           templateName: task['templateName'] as String,
//           outputPath: task['outputPath'] as String,
//           variables: task['variables'] as Map<String, dynamic>,
//           overwrite: task['overwrite'] as bool? ?? false,
//           inheritance: task['inheritance'] as TemplateInheritance?,
//         );
//       });

//       final batchResults = await Future.wait(batchFutures);
//       results.addAll(batchResults);
//     }

//     return results;
//   }

//   /// 注册预设钩子
//   void registerDefaultHooks() {
//     // 注册默认的验证钩子
//     hookRegistry.register(_DefaultValidationHook());
    
//     // 注册默认的日志钩子
//     hookRegistry.register(_DefaultLoggingHook());
    
//     cli_logger.Logger.debug('已注册默认钩子');
//   }

//   /// 执行指定类型的钩子
//   Future<HookResult> _executeHooks(
//       HookType type, 
//       HookContext context,
//   ) async {
//     final hooks = hookRegistry.getHooks(type);
//     final modifiedVariables = <String, dynamic>{};
    
//     for (final hook in hooks) {
//       try {
//         final result = await hook.execute(context);
        
//         if (!result.success) {
//           cli_logger.Logger.error(
//             '钩子执行失败: ${hook.name} - ${result.message}',
//           );
//           return result;
//         }
        
//         if (!result.shouldContinue) {
//           cli_logger.Logger.info(
//             '钩子请求停止: ${hook.name} - ${result.message}',
//           );
//           return result;
//         }
        
//         // 合并修改的变量
//         if (result.modifiedVariables != null) {
//           modifiedVariables.addAll(result.modifiedVariables!);
//         }
        
//       } catch (e) {
//         cli_logger.Logger.error('钩子执行异常: ${hook.name}', error: e);
//         return HookResult.failure('钩子执行异常: $e');
//       }
//     }
    
//     return HookResult(
//       success: true, 
//       modifiedVariables: 
//           modifiedVariables.isNotEmpty ? modifiedVariables : null,
//     );
//   }

//   /// 处理模板继承
//   Future<Map<String, dynamic>> _processTemplateInheritance(
//     String templateName,
//     TemplateInheritance inheritance,
//     Map<String, dynamic> variables,
//   ) async {
//     try {
//       // 加载基础模板信息
//       final baseInfo = await getTemplateInfo(inheritance.baseTemplate);
//       if (baseInfo == null) {
//         cli_logger.Logger.warning(
//           '基础模板不存在: ${inheritance.baseTemplate}',
//         );
//         return variables;
//       }

//       // 合并变量
//       final mergedVariables = Map<String, dynamic>.from(variables);
      
//       // 应用基础模板的默认变量
//       if (baseInfo.containsKey('vars')) {
//         final baseVars = Map<String, dynamic>.from(baseInfo['vars'] as Map? ?? {});
//         for (final entry in baseVars.entries) {
//           if (!mergedVariables.containsKey(entry.key)) {
//             final varConfig = 
//                 Map<String, dynamic>.from(entry.value as Map? ?? {});
//             if (varConfig.containsKey('default')) {
//               mergedVariables[entry.key] = varConfig['default'];
//             }
//           }
//         }
//       }

//       // 应用继承覆盖
//       mergedVariables.addAll(inheritance.overrides);

//       cli_logger.Logger.debug(
//         '模板继承处理完成: ${inheritance.baseTemplate} -> $templateName',
//       );
//       return mergedVariables;

//     } catch (e) {
//       cli_logger.Logger.error('处理模板继承时发生异常', error: e);
//       return variables;
//     }
//   }

//   /// 获取生成的文件列表
//   Future<List<String>> _getGeneratedFiles(String outputPath) async {
//     try {
//       if (!FileUtils.directoryExists(outputPath)) {
//         return [];
//       }

//       final entities = FileUtils.listDirectory(outputPath);
//       final files = <String>[];

//       for (final entity in entities) {
//         if (entity is File) {
//           files.add(path.relative(entity.path, from: outputPath));
//         } else if (entity is Directory) {
//           // 递归获取子目录文件
//           final subFiles = await _getGeneratedFiles(entity.path);
//           files.addAll(
//             subFiles.map(
//               (f) => path.join(
//                 path.relative(entity.path, from: outputPath),
//                 f,
//               ),
//             ),
//           );
//         }
//       }

//       return files;
//     } catch (e) {
//       cli_logger.Logger.error('获取生成文件列表失败', error: e);
//       return [];
//     }
//   }

//   // ==================== 兼容性检查辅助方法 ====================

//   /// 解析版本信息
//   TemplateVersionInfo _parseVersionInfo(Map<String, dynamic> yamlData) {
//     final version = yamlData['version']?.toString() ?? '0.1.0';
//     final environment = Map<String, dynamic>.from(yamlData['environment'] as Map? ?? {});
//     final compatibility = Map<String, dynamic>.from(yamlData['compatibility'] as Map? ?? {});

//     return TemplateVersionInfo(
//       version: version,
//       minCliVersion: compatibility['min_cli_version']?.toString(),
//       maxCliVersion: compatibility['max_cli_version']?.toString(),
//       minDartVersion: environment['sdk']?.toString().split(' ').first,
//       maxDartVersion: environment['sdk']?.toString().split(' ').last,
//       minMasonVersion: compatibility['min_mason_version']?.toString(),
//       maxMasonVersion: compatibility['max_mason_version']?.toString(),
//     );
//   }

//   /// 比较版本号
//   int _compareVersions(String version1, String version2) {
//     try {
//       final v1Parts = version1.split('.').map(int.parse).toList();
//       final v2Parts = version2.split('.').map(int.parse).toList();

//       // 确保两个版本具有相同的部分数
//       final maxLength = math.max(v1Parts.length, v2Parts.length);
//       while (v1Parts.length < maxLength) {
//         v1Parts.add(0);
//       }
//       while (v2Parts.length < maxLength) {
//         v2Parts.add(0);
//       }

//       for (var i = 0; i < maxLength; i++) {
//         if (v1Parts[i] < v2Parts[i]) return -1;
//         if (v1Parts[i] > v2Parts[i]) return 1;
//       }
//       return 0;
//     } catch (e) {
//       // 如果版本解析失败，认为相等
//       return 0;
//     }
//   }

//   /// 获取当前Dart版本
//   String? _getCurrentDartVersion() {
//     try {
//       // 从Platform.version中提取Dart版本
//       final versionString = Platform.version;
//       final match = RegExp(r'(\d+\.\d+\.\d+)').firstMatch(versionString);
//       return match?.group(1);
//     } catch (e) {
//       cli_logger.Logger.warning('无法获取Dart版本: $e');
//       return null;
//     }
//   }

//   /// 获取当前Mason版本
//   String? _getCurrentMasonVersion() {
//     try {
//       // 这里应该通过运行mason --version命令来获取
//       // 为了简化，返回已知版本
//       return '0.1.1';
//     } catch (e) {
//       cli_logger.Logger.warning('无法获取Mason版本: $e');
//       return null;
//     }
//   }

//   /// 解析依赖信息
//   TemplateDependencyInfo _parseDependencyInfo(Map<String, dynamic> yamlData) {
//     final dependencies = Map<String, dynamic>.from(yamlData['dependencies'] as Map? ?? {});
//     final conflicts = (yamlData['conflicts'] as List?)?.cast<dynamic>() ?? [];

//     final requiredDeps = <String, String>{};
//     final optionalDeps = <String, String>{};

//     // 处理必需依赖
//     if (dependencies.containsKey('required')) {
//       final required = Map<String, dynamic>.from(dependencies['required'] as Map? ?? {});
//       for (final entry in required.entries) {
//         requiredDeps[entry.key] = entry.value?.toString() ?? 'any';
//       }
//     }

//     // 处理可选依赖
//     if (dependencies.containsKey('optional')) {
//       final optional = Map<String, dynamic>.from(dependencies['optional'] as Map? ?? {});
//       for (final entry in optional.entries) {
//         optionalDeps[entry.key] = entry.value?.toString() ?? 'any';
//       }
//     }

//     return TemplateDependencyInfo(
//       requiredDependencies: requiredDeps,
//       optionalDependencies: optionalDeps,
//       conflictingDependencies: conflicts.map((e) => e.toString()).toList(),
//     );
//   }

//   /// 检查依赖可用性
//   Future<bool> _checkDependencyAvailability(String dependencyName, String version) async {
//     try {
//       // 这里应该检查pub.dev或本地pubspec.yaml
//       // 为了简化，假设常见依赖可用
//       final commonDependencies = [
//         'flutter', 'dart', 'path', 'yaml', 'json_annotation',
//         'build_runner', 'test', 'very_good_analysis', 'mason',
//       ];
//       return commonDependencies.contains(dependencyName);
//     } catch (e) {
//       return false;
//     }
//   }

//   /// 检查依赖冲突
//   Future<bool> _checkDependencyConflict(String dependencyName) async {
//     try {
//       // 这里应该检查当前项目的pubspec.yaml
//       // 为了简化，假设没有冲突
//       return false;
//     } catch (e) {
//       return false;
//     }
//   }

//   /// 解析平台信息
//   TemplatePlatformInfo _parsePlatformInfo(Map<String, dynamic> yamlData) {
//     final platforms = Map<String, dynamic>.from(yamlData['platforms'] as Map? ?? {});
    
//     return TemplatePlatformInfo(
//       supportedPlatforms: (platforms['supported'] as List?)
//           ?.cast<dynamic>().map((e) => e.toString()).toList() ?? [],
//       unsupportedPlatforms: (platforms['unsupported'] as List?)
//           ?.cast<dynamic>().map((e) => e.toString()).toList() ?? [],
//       requiredFeatures: (platforms['required_features'] as List?)
//           ?.cast<dynamic>().map((e) => e.toString()).toList() ?? [],
//       optionalFeatures: (platforms['optional_features'] as List?)
//           ?.cast<dynamic>().map((e) => e.toString()).toList() ?? [],
//     );
//   }

//   /// 获取当前平台
//   String _getCurrentPlatform() {
//     if (Platform.isWindows) return 'windows';
//     if (Platform.isMacOS) return 'macos';
//     if (Platform.isLinux) return 'linux';
//     if (Platform.isAndroid) return 'android';
//     if (Platform.isIOS) return 'ios';
//     if (Platform.isFuchsia) return 'fuchsia';
//     return 'unknown';
//   }

//   /// 检查平台功能
//   bool _checkPlatformFeature(String feature) {
//     switch (feature.toLowerCase()) {
//       case 'file_system':
//         return true;
//       case 'network':
//         return true;
//       case 'console':
//         return !Platform.isAndroid && !Platform.isIOS;
//       case 'gui':
//         return Platform.isWindows || Platform.isMacOS || Platform.isLinux;
//       case 'mobile':
//         return Platform.isAndroid || Platform.isIOS;
//       case 'desktop':
//         return Platform.isWindows || Platform.isMacOS || Platform.isLinux;
//       default:
//         return false; // 未知功能假设不支持
//     }
//   }

//   /// 验证版本格式
//   bool _isValidVersionFormat(String version) {
//     // 支持语义版本格式: x.y.z 或 x.y.z+build 或 x.y.z-pre+build
//     final semanticVersionRegex = RegExp(
//       r'^(0|[1-9]\d*)\.(0|[1-9]\d*)\.(0|[1-9]\d*)(?:-((?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\.(?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?$',
//     );
    
//     return semanticVersionRegex.hasMatch(version);
//   }

//   /// 获取模板统计信息
//   Future<Map<String, dynamic>> getTemplateStats() async {
//     final stats = <String, dynamic>{};
//     final templates = await getAvailableTemplates();

//     stats['total_templates'] = templates.length;
//     stats['cached_templates'] = _generatorCache.length;
    
//     final templateDetails = <String, Map<String, dynamic>>{};
    
//     for (final templateName in templates) {
//       try {
//         final metadata = _metadataCache[templateName] ?? 
//                         await getTemplateInfo(templateName);
        
//         if (metadata != null) {
//           templateDetails[templateName] = {
//             'version': metadata['version'] ?? 'unknown',
//             'description': metadata['description'] ?? '',
//             'file_count': metadata['_file_count'] ?? 0,
//             'cached': _generatorCache.containsKey(templateName),
//           };
//         }
//       } catch (e) {
//         templateDetails[templateName] = {
//           'error': e.toString(),
//           'cached': false,
//         };
//       }
//     }
    
//     stats['templates'] = templateDetails;
//     return stats;
//   }

//   /// 获取模板参数系统
//   Future<TemplateParameterSystem> getParameterSystem(String templateName) async {
//     // 检查缓存
//     if (_parameterSystemCache.containsKey(templateName)) {
//       return _parameterSystemCache[templateName]!;
//     }

//     try {
//       final templatePath = getTemplatePath(templateName);
//       final brickPath = path.join(templatePath, 'brick.yaml');
      
//       if (!FileUtils.fileExists(brickPath)) {
//         throw TemplateEngineException.templateNotFound(templateName);
//       }

//       final yamlData = await FileUtils.readYamlFile(brickPath);
//       if (yamlData == null) {
//         throw TemplateEngineException(
//           type: TemplateEngineErrorType.invalidTemplateFormat,
//           message: '无法解析模板配置文件: $templateName',
//         );
//       }

//       // 创建参数系统并加载配置
//       final parameterSystem = TemplateParameterSystem();
//       parameterSystem.loadFromBrickYaml(Map<String, dynamic>.from(yamlData));

//       // 缓存参数系统
//       _parameterSystemCache[templateName] = parameterSystem;

//       cli_logger.Logger.debug('模板参数系统加载成功: $templateName (${parameterSystem.variableCount} 个变量)');
//       return parameterSystem;

//     } catch (e) {
//       cli_logger.Logger.error('加载模板参数系统失败: $templateName', error: e);
//       if (e is TemplateEngineException) rethrow;
//       throw TemplateEngineException.masonError('getParameterSystem', e);
//     }
//   }

//   /// 处理和验证模板变量
//   Future<TemplateParameterProcessingResult> processTemplateVariables(
//     String templateName,
//     Map<String, dynamic> inputVariables,
//   ) async {
//     try {
//       final parameterSystem = await getParameterSystem(templateName);
//       return parameterSystem.processVariables(inputVariables);
//     } catch (e) {
//       return TemplateParameterProcessingResult.failure(
//         ['处理模板变量失败: $e'],
//       );
//     }
//   }

//   /// 生成模块的增强版本（集成参数系统）
//   Future<bool> generateModuleWithParameters({
//     required String templateName,
//     required String outputPath,
//     required Map<String, dynamic> variables,
//     bool overwrite = false,
//     List<TemplateHook>? additionalHooks,
//     TemplateInheritance? inheritance,
//   }) async {
//     final stopwatch = Stopwatch()..start();

//     try {
//       cli_logger.Logger.info('开始生成模块: $templateName -> $outputPath');

//       // 1. 处理模板变量
//       final processingResult = await processTemplateVariables(templateName, variables);
//       if (!processingResult.success) {
//         cli_logger.Logger.error('变量处理失败: ${processingResult.errors.join(", ")}');
//         return false;
//       }

//       final processedVariables = processingResult.getAllVariables();
//       cli_logger.Logger.debug('变量处理完成: ${processedVariables.keys.length} 个变量');

//       // 2. 使用高级生成方法
//       final result = await generateWithHooks(
//         templateName: templateName,
//         outputPath: outputPath,
//         variables: processedVariables,
//         overwrite: overwrite,
//         additionalHooks: additionalHooks,
//         inheritance: inheritance,
//       );

//       if (result.success) {
//         cli_logger.Logger.success('模块生成成功: $outputPath (${stopwatch.elapsedMilliseconds}ms)');
        
//         // 记录生成的变量信息
//         if (processingResult.generatedVariables.isNotEmpty) {
//           cli_logger.Logger.debug('生成的派生变量: ${processingResult.generatedVariables.keys.join(", ")}');
//         }
        
//         return true;
//       } else {
//         cli_logger.Logger.error('模块生成失败: ${result.message}');
//         return false;
//       }

//     } catch (e) {
//       stopwatch.stop();
//       cli_logger.Logger.error('模块生成异常', error: e);
//       return false;
//     }
//   }

//   /// 获取模板变量定义
//   Future<List<TemplateVariable>> getTemplateVariableDefinitions(String templateName) async {
//     try {
//       final parameterSystem = await getParameterSystem(templateName);
//       return parameterSystem.getAllVariableDefinitions();
//     } catch (e) {
//       cli_logger.Logger.error('获取模板变量定义失败: $templateName', error: e);
//       return [];
//     }
//   }

//   /// 获取模板的默认变量值
//   Future<Map<String, dynamic>> getTemplateDefaultValues(String templateName) async {
//     try {
//       final parameterSystem = await getParameterSystem(templateName);
//       return parameterSystem.getDefaultValues();
//     } catch (e) {
//       cli_logger.Logger.error('获取模板默认值失败: $templateName', error: e);
//       return {};
//     }
//   }

//   /// 获取模板的用户提示信息
//   Future<Map<String, String>> getTemplatePrompts(String templateName) async {
//     try {
//       final parameterSystem = await getParameterSystem(templateName);
//       return parameterSystem.getPrompts();
//     } catch (e) {
//       cli_logger.Logger.error('获取模板提示信息失败: $templateName', error: e);
//       return {};
//     }
//   }

//   /// 验证模板变量值（使用参数系统）
//   Future<TemplateParameterValidationResult> validateTemplateVariablesWithSystem(
//     String templateName,
//     Map<String, dynamic> variables,
//   ) async {
//     try {
//       final parameterSystem = await getParameterSystem(templateName);
//       return parameterSystem.validateVariables(variables);
//     } catch (e) {
//       return TemplateParameterValidationResult(
//         isValid: false,
//         errors: ['验证模板变量失败: $e'],
//         warnings: [],
//         validatedVariables: {},
//       );
//     }
//   }

//   /// 插值模板字符串
//   Future<String> interpolateTemplateString(
//     String templateName,
//     String template,
//     Map<String, dynamic> variables,
//   ) async {
//     try {
//       final parameterSystem = await getParameterSystem(templateName);
//       return parameterSystem.interpolateTemplate(template, variables);
//     } catch (e) {
//       cli_logger.Logger.error('模板字符串插值失败', error: e);
//       return template; // 返回原始模板
//     }
//   }

//   /// 获取模板变量摘要
//   Future<Map<String, dynamic>> getTemplateVariableSummary(String templateName) async {
//     try {
//       final parameterSystem = await getParameterSystem(templateName);
//       return parameterSystem.generateVariableSummary();
//     } catch (e) {
//       cli_logger.Logger.error('获取模板变量摘要失败: $templateName', error: e);
//       return {};
//     }
//   }

//   // ==================== Task 33.* 高级钩子公共方法 ====================

//   /// 加载模板的钩子配置
//   Future<void> loadTemplateHooks(String templateName) async {
//     await advancedHookManager.loadHooksFromBrickConfig(templateName);
//   }

//   /// 验证钩子配置
//   List<String> validateHookConfiguration(Map<String, dynamic> hookConfig) {
//     return advancedHookManager.validateHookConfig(hookConfig);
//   }

//   /// 获取钩子执行统计信息
//   Map<String, dynamic> getHookStatistics() {
//     return advancedHookManager.getHookStatistics();
//   }

//   /// 注册条件钩子
//   void registerConditionalHook(String name, String condition, TemplateHook hook) {
//     final conditionalHook = ConditionalHook(
//       name: name,
//       condition: condition,
//       wrappedHook: hook,
//     );
//     hookRegistry.register(conditionalHook);
//   }

//   /// 注册超时钩子
//   void registerTimeoutHook(String name, Duration timeout, TemplateHook hook) {
//     final timeoutHook = TimeoutHook(
//       name: name,
//       timeout: timeout,
//       wrappedHook: hook,
//     );
//     hookRegistry.register(timeoutHook);
//   }

//   /// 注册错误恢复钩子
//   void registerErrorRecoveryHook(
//     String name,
//     TemplateHook hook,
//     Future<HookResult> Function(HookResult) recoveryAction, {
//     bool ignoreErrors = false,
//   }) {
//     final recoveryHook = ErrorRecoveryHook(
//       name: name,
//       wrappedHook: hook,
//       recoveryAction: recoveryAction,
//       ignoreErrors: ignoreErrors,
//     );
//     hookRegistry.register(recoveryHook);
//   }

//   /// 注册脚本执行钩子
//   void registerScriptHook(ScriptHookConfig config, HookType hookType, {int priority = 100}) {
//     final scriptHook = ScriptExecutionHook(
//       config: config,
//       hookType: hookType,
//       hookPriority: priority,
//     );
//     hookRegistry.register(scriptHook);
//   }

//   /// 带钩子加载的高级生成方法
//   Future<GenerationResult> generateModuleWithLoadedHooks({
//     required String templateName,
//     required String outputPath,
//     required Map<String, dynamic> variables,
//     bool overwrite = false,
//     List<TemplateHook>? additionalHooks,
//     TemplateInheritance? inheritance,
//   }) async {
//     // 首先加载模板的钩子配置
//     await loadTemplateHooks(templateName);

//     // 然后执行带钩子的生成
//     return generateWithHooks(
//       templateName: templateName,
//       outputPath: outputPath,
//       variables: variables,
//       overwrite: overwrite,
//       additionalHooks: additionalHooks,
//       inheritance: inheritance,
//     );
//   }

//   /// 清理钩子注册表
//   void clearAllHooks() {
//     hookRegistry.clear();
//   }

//   /// 获取钩子详细信息
//   Map<String, dynamic> getHookDetails() {
//     final stats = getHookStatistics();
//     final preHooks = hookRegistry.getHooks(HookType.preGeneration);
//     final postHooks = hookRegistry.getHooks(HookType.postGeneration);

//     return {
//       ...stats,
//       'pre_generation_hook_names': preHooks.map((h) => h.name).toList(),
//       'post_generation_hook_names': postHooks.map((h) => h.name).toList(),
//       'script_execution_hooks': [
//         ...preHooks.whereType<ScriptExecutionHook>(),
//         ...postHooks.whereType<ScriptExecutionHook>(),
//       ].map((h) => {
//         'name': h.name,
//         'type': h.type.toString(),
//         'priority': h.priority,
//         'description': h.config.description,
//         'script': h.config.script,
//       },).toList(),
//     };
//   }
// }

// // ==================== 默认钩子实现 ====================

// /// 默认验证钩子
// class _DefaultValidationHook extends TemplateHook {
//   _DefaultValidationHook() : super(name: 'default_validation');

//   @override
//   HookType get type => HookType.preGeneration;

//   @override
//   int get priority => 10; // 高优先级，优先执行

//   @override
//   Future<HookResult> execute(HookContext context) async {
//     try {
//       // 验证必需变量
//       final requiredVars = ['module_id', 'module_name'];
//       for (final varName in requiredVars) {
//         if (!context.variables.containsKey(varName) ||
//             StringUtils.isBlank(context.variables[varName]?.toString())) {
//           return HookResult.failure('必需变量缺失: $varName');
//         }
//       }

//       // 验证输出路径
//       if (StringUtils.isBlank(context.outputPath)) {
//         return HookResult.failure('输出路径不能为空');
//       }

//       return HookResult.successResult;
//     } catch (e) {
//       return HookResult.failure('验证过程中发生异常: $e');
//     }
//   }
// }

// /// 默认日志钩子
// class _DefaultLoggingHook extends TemplateHook {
//   _DefaultLoggingHook() : super(name: 'default_logging');

//   @override
//   HookType get type => HookType.postGeneration;

//   @override
//   int get priority => 900; // 低优先级，最后执行

//   @override
//   Future<HookResult> execute(HookContext context) async {
//     try {
//       final metadata = context.metadata;
//       final duration = metadata.containsKey('startTime') && 
//               metadata.containsKey('endTime')
//           ? DateTime.parse(metadata['endTime'] as String)
//               .difference(DateTime.parse(metadata['startTime'] as String))
//           : null;

//       cli_logger.Logger.success(
//         '模板生成完成: ${context.templateName} -> ${context.outputPath}',
//       );

//       if (duration != null) {
//         cli_logger.Logger.debug('生成耗时: ${duration.inMilliseconds}ms');
//       }

//       if (metadata.containsKey('generatedFiles')) {
//         final files = metadata['generatedFiles'] as List<String>? ?? [];
//         cli_logger.Logger.debug('生成文件数量: ${files.length}');
//       }

//       return HookResult.successResult;
//     } catch (e) {
//       // 日志钩子失败不应该影响整体流程
//       cli_logger.Logger.warning('日志钩子执行异常: $e');
//       return HookResult.successResult;
//     }
//   }
// }

// // ==================== Task 33.* 高级钩子实现 ====================

// /// 脚本执行钩子配置
// class ScriptHookConfig {
//   /// 创建脚本钩子配置实例
//   const ScriptHookConfig({
//     required this.description,
//     required this.script,
//     this.condition,
//     this.timeout = 30000,
//     this.ignoreErrors = false,
//     this.workingDirectory,
//     this.environment,
//   });

//   /// 从Map创建配置
//   factory ScriptHookConfig.fromMap(Map<String, dynamic> map) {
//     return ScriptHookConfig(
//       description: map['description'] as String,
//       script: map['script'] as String,
//       condition: map['condition'] as String?,
//       timeout: map['timeout'] as int? ?? 30000,
//       ignoreErrors: map['ignore_errors'] as bool? ?? false,
//       workingDirectory: map['working_directory'] as String?,
//       environment: map['environment'] != null 
//           ? Map<String, String>.from(map['environment'] as Map)
//           : null,
//     );
//   }

//   /// 钩子描述
//   final String description;
//   /// 执行脚本
//   final String script;
//   /// 执行条件
//   final String? condition;
//   /// 超时时间（毫秒）
//   final int timeout;
//   /// 是否忽略错误
//   final bool ignoreErrors;
//   /// 工作目录
//   final String? workingDirectory;
//   /// 环境变量
//   final Map<String, String>? environment;
// }

// /// 脚本执行钩子
// class ScriptExecutionHook extends TemplateHook {
//   /// 创建脚本执行钩子实例
//   ScriptExecutionHook({
//     required this.config,
//     required this.hookType,
//     this.hookPriority = 100,
//   }) : super(name: '${hookType.toString().split('.').last}_script_${config.description.hashCode}');

//   /// 脚本钩子配置
//   final ScriptHookConfig config;
//   /// 钩子类型
//   final HookType hookType;
//   /// 钩子优先级
//   final int hookPriority;

//   @override
//   HookType get type => hookType;

//   @override
//   int get priority => hookPriority;

//   @override
//   Future<HookResult> execute(HookContext context) async {
//     try {
//       // 检查执行条件
//       if (config.condition != null && !await _evaluateCondition(config.condition!, context)) {
//         cli_logger.Logger.debug('钩子条件不满足，跳过执行: ${config.description}');
//         return HookResult.successResult;
//       }

//       cli_logger.Logger.info('执行钩子: ${config.description}');

//       // 处理脚本中的变量插值
//       final processedScript = await _interpolateScript(config.script, context);
      
//       // 执行脚本
//       final result = await _executeScript(
//         processedScript,
//         context,
//         config.workingDirectory ?? context.outputPath,
//         config.timeout,
//       );

//       if (result.success || config.ignoreErrors) {
//         cli_logger.Logger.success('钩子执行完成: ${config.description}');
//         return HookResult(
//           success: true,
//           message: result.output,
//         );
//       } else {
//         cli_logger.Logger.error('钩子执行失败: ${config.description} - ${result.error}');
//         return HookResult.failure('脚本执行失败: ${result.error}');
//       }

//     } catch (e) {
//       if (config.ignoreErrors) {
//         cli_logger.Logger.warning('钩子执行异常(已忽略): ${config.description} - $e');
//         return HookResult.successResult;
//       } else {
//         cli_logger.Logger.error('钩子执行异常: ${config.description}', error: e);
//         return HookResult.failure('钩子执行异常: $e');
//       }
//     }
//   }

//   /// 评估条件表达式
//   Future<bool> _evaluateCondition(String condition, HookContext context) async {
//     try {
//       // 简单的条件评估实现
//       // 支持基本的布尔运算和变量替换
//       var processedCondition = condition;
      
//       // 替换变量
//       for (final entry in context.variables.entries) {
//         final value = entry.value;
//         final valueStr = value is bool ? value.toString() : 
//                         value is String ? '"$value"' :
//                         value?.toString() ?? 'null';
//         processedCondition = processedCondition.replaceAll(
//           '{{${entry.key}}}', 
//           valueStr,
//         );
//       }

//       // 处理特殊关键字
//       processedCondition = processedCondition
//           .replaceAll('success', 'true')
//           .replaceAll('true', 'true')
//           .replaceAll('false', 'false');

//       // 简单的布尔表达式评估
//       if (processedCondition == 'true') return true;
//       if (processedCondition == 'false') return false;
      
//       // 更复杂的条件可以在这里扩展
//       return processedCondition.isNotEmpty;
      
//     } catch (e) {
//       cli_logger.Logger.warning('条件评估失败，默认为true: $condition - $e');
//       return true;
//     }
//   }

//   /// 插值脚本中的变量
//   Future<String> _interpolateScript(String script, HookContext context) async {
//     var processedScript = script;
    
//     // 替换模板变量
//     for (final entry in context.variables.entries) {
//       processedScript = processedScript.replaceAll(
//         '{{${entry.key}}}',
//         entry.value?.toString() ?? '',
//       );
//     }

//     // 替换上下文变量
//     processedScript = processedScript
//         .replaceAll('{{template_name}}', context.templateName)
//         .replaceAll('{{output_path}}', context.outputPath)
//         .replaceAll('{{output.path}}', context.outputPath);

//     return processedScript;
//   }

//   /// 执行脚本命令
//   Future<ScriptExecutionResult> _executeScript(
//     String script,
//     HookContext context,
//     String workingDirectory,
//     int timeoutMs,
//   ) async {
//     try {
//       // 设置工作目录
//       final workDir = Directory(workingDirectory);
//       if (!workDir.existsSync()) {
//         workDir.createSync(recursive: true);
//       }

//       // 分解命令和参数
//       final parts = script.split(' ');
//       final command = parts.first;
//       final arguments = parts.skip(1).toList();

//       // 执行命令
//       final process = await Process.start(
//         command,
//         arguments,
//         workingDirectory: workingDirectory,
//         environment: {
//           ...Platform.environment,
//           if (config.environment != null) ...config.environment!,
//         },
//       );

//       // 设置超时
//       final timeout = Duration(milliseconds: timeoutMs);
//       final exitCode = await process.exitCode.timeout(timeout);

//       // 读取输出
//       final stdout = await process.stdout.transform(utf8.decoder).join();
//       final stderr = await process.stderr.transform(utf8.decoder).join();

//       if (exitCode == 0) {
//         return ScriptExecutionResult.createSuccess(stdout);
//       } else {
//         return ScriptExecutionResult.createFailure(stderr.isNotEmpty ? stderr : stdout);
//       }

//     } on TimeoutException {
//       return ScriptExecutionResult.createFailure('脚本执行超时 (${timeoutMs}ms)');
//     } catch (e) {
//       return ScriptExecutionResult.createFailure('脚本执行异常: $e');
//     }
//   }
// }

// /// 脚本执行结果
// class ScriptExecutionResult {

//   /// 创建成功结果
//   ScriptExecutionResult.createSuccess(String output) 
//     : this(success: true, output: output);
      
//   /// 创建失败结果
//   ScriptExecutionResult.createFailure(String error) 
//     : this(success: false, error: error);
//   /// 创建脚本执行结果实例
//   const ScriptExecutionResult({
//     required this.success,
//     this.output,
//     this.error,
//   });

//   /// 执行是否成功
//   final bool success;
//   /// 标准输出内容
//   final String? output;
//   /// 错误信息
//   final String? error;
// }

// /// 高级钩子管理器
// class AdvancedHookManager {
//   /// 创建高级钩子管理器实例
//   AdvancedHookManager(this.templateEngine);

//   /// 模板引擎实例
//   final TemplateEngine templateEngine;

//   /// 从brick.yaml配置加载钩子
//   Future<void> loadHooksFromBrickConfig(String templateName) async {
//     try {
//       final templateInfo = await templateEngine.getTemplateInfo(templateName);
//       if (templateInfo == null) {
//         cli_logger.Logger.warning('模板信息不存在，无法加载钩子: $templateName');
//         return;
//       }

//       final hooks = templateInfo['hooks'] as Map<String, dynamic>?;
//       if (hooks == null) {
//         cli_logger.Logger.debug('模板无钩子配置: $templateName');
//         return;
//       }

//       // 清除现有钩子
//       _clearTemplateHooks(templateName);

//       // 加载pre_gen钩子
//       if (hooks.containsKey('pre_gen')) {
//         final preGenHooks = hooks['pre_gen'] as List<dynamic>? ?? [];
//         for (final hookData in preGenHooks) {
//           final config = ScriptHookConfig.fromMap(hookData as Map<String, dynamic>);
//           final hook = ScriptExecutionHook(
//             config: config,
//             hookType: HookType.preGeneration,
//             hookPriority: 50, // 高优先级
//           );
//           templateEngine.hookRegistry.register(hook);
//         }
//         cli_logger.Logger.debug('加载了 ${preGenHooks.length} 个pre_gen钩子');
//       }

//       // 加载post_gen钩子
//       if (hooks.containsKey('post_gen')) {
//         final postGenHooks = hooks['post_gen'] as List<dynamic>? ?? [];
//         for (final hookData in postGenHooks) {
//           final config = ScriptHookConfig.fromMap(hookData as Map<String, dynamic>);
//           final hook = ScriptExecutionHook(
//             config: config,
//             hookType: HookType.postGeneration,
//             hookPriority: 150, // 中等优先级
//           );
//           templateEngine.hookRegistry.register(hook);
//         }
//         cli_logger.Logger.debug('加载了 ${postGenHooks.length} 个post_gen钩子');
//       }

//     } catch (e) {
//       cli_logger.Logger.error('加载模板钩子失败: $templateName', error: e);
//     }
//   }

//   /// 清除模板相关的钩子
//   void _clearTemplateHooks(String templateName) {
//     // 由于当前的HookRegistry不支持按模板清除，这里只是记录
//     // 实际实现中可能需要扩展HookRegistry的功能
//     cli_logger.Logger.debug('清除模板钩子: $templateName');
//   }

//   /// 验证钩子配置
//   List<String> validateHookConfig(Map<String, dynamic> hookConfig) {
//     final errors = <String>[];

//     if (!hookConfig.containsKey('description')) {
//       errors.add('钩子缺少description字段');
//     }

//     if (!hookConfig.containsKey('script')) {
//       errors.add('钩子缺少script字段');
//     }

//     if (hookConfig.containsKey('timeout')) {
//       final timeout = hookConfig['timeout'];
//       if (timeout is! int || timeout <= 0) {
//         errors.add('timeout必须是正整数');
//       }
//     }

//     return errors;
//   }

//   /// 获取钩子执行统计
//   Map<String, dynamic> getHookStatistics() {
//     final preHooks = templateEngine.hookRegistry.getHooks(HookType.preGeneration);
//     final postHooks = templateEngine.hookRegistry.getHooks(HookType.postGeneration);

//     return {
//       'pre_generation_hooks': preHooks.length,
//       'post_generation_hooks': postHooks.length,
//       'total_hooks': preHooks.length + postHooks.length,
//       'script_hooks': [
//         ...preHooks.whereType<ScriptExecutionHook>(),
//         ...postHooks.whereType<ScriptExecutionHook>(),
//       ].length,
//     };
//   }
// }

// /// 条件钩子（基于表达式）
// class ConditionalHook extends TemplateHook {
//   /// 创建条件钩子实例
//   ConditionalHook({
//     required super.name,
//     required this.condition,
//     required this.wrappedHook,
//   });

//   /// 执行条件表达式
//   final String condition;
//   /// 被包装的钩子实例
//   final TemplateHook wrappedHook;

//   @override
//   HookType get type => wrappedHook.type;

//   @override
//   int get priority => wrappedHook.priority;

//   @override
//   Future<HookResult> execute(HookContext context) async {
//     // 评估条件
//     if (await _evaluateCondition(condition, context)) {
//       return wrappedHook.execute(context);
//     } else {
//       cli_logger.Logger.debug('条件不满足，跳过钩子: $name');
//       return HookResult.successResult;
//     }
//   }

//   Future<bool> _evaluateCondition(String condition, HookContext context) async {
//     // 复用ScriptExecutionHook的条件评估逻辑
//     final scriptHook = ScriptExecutionHook(
//       config: const ScriptHookConfig(
//         description: 'temp',
//         script: 'echo',
//       ),
//       hookType: HookType.preGeneration,
//     );
//     return scriptHook._evaluateCondition(condition, context);
//   }
// }

// /// 超时钩子包装器
// class TimeoutHook extends TemplateHook {
//   /// 创建超时钩子实例
//   TimeoutHook({
//     required super.name,
//     required this.timeout,
//     required this.wrappedHook,
//   });

//   /// 超时时长
//   final Duration timeout;
//   /// 被包装的钩子实例
//   final TemplateHook wrappedHook;

//   @override
//   HookType get type => wrappedHook.type;

//   @override
//   int get priority => wrappedHook.priority;

//   @override
//   Future<HookResult> execute(HookContext context) async {
//     try {
//       return await wrappedHook.execute(context).timeout(timeout);
//     } on TimeoutException {
//       cli_logger.Logger.error('钩子执行超时: $name (${timeout.inMilliseconds}ms)');
//       return HookResult.failure('钩子执行超时');
//     }
//   }
// }

// /// 错误恢复钩子
// class ErrorRecoveryHook extends TemplateHook {
//   /// 创建错误恢复钩子实例
//   ErrorRecoveryHook({
//     required super.name,
//     required this.wrappedHook,
//     required this.recoveryAction,
//     this.ignoreErrors = false,
//   });

//   /// 被包装的钩子实例
//   final TemplateHook wrappedHook;
//   /// 错误恢复操作
//   final Future<HookResult> Function(HookResult failedResult) recoveryAction;
//   /// 是否忽略错误
//   final bool ignoreErrors;

//   @override
//   HookType get type => wrappedHook.type;

//   @override
//   int get priority => wrappedHook.priority;

//   @override
//   Future<HookResult> execute(HookContext context) async {
//     try {
//       final result = await wrappedHook.execute(context);
      
//       if (!result.success && !ignoreErrors) {
//         cli_logger.Logger.warning('钩子执行失败，尝试恢复: $name');
//         return await recoveryAction(result);
//       }
      
//       return result;
//     } catch (e) {
//       if (ignoreErrors) {
//         cli_logger.Logger.warning('钩子执行异常(已忽略): $name - $e');
//         return HookResult.successResult;
//       } else {
//         cli_logger.Logger.error('钩子执行异常: $name', error: e);
//         return HookResult.failure('钩子执行异常: $e');
//       }
//     }
//   }
// }

// // ==================== Task 36.* 模板系统最终优化实现 ====================

// /// 预编译模板数据结构
// class PrecompiledTemplate {
//   /// 创建预编译模板实例
//   PrecompiledTemplate({
//     required this.templateName,
//     required this.generator,
//     required this.metadata,
//     required this.variables,
//     required this.compilationTime,
//     required this.lastAccessed,
//   });

//   /// 模板名称
//   final String templateName;
//   /// Mason生成器实例
//   final MasonGenerator generator;
//   /// 模板元数据
//   final Map<String, dynamic> metadata;
//   /// 模板变量列表
//   final List<String> variables;  // 修复类型错误：使用List<String>而不是List<BrickVariableProperties>
//   /// 编译时间
//   final DateTime compilationTime;
//   /// 最后访问时间
//   DateTime lastAccessed;
  
//   /// 缓存过期时间
//   static const Duration cacheExpiry = Duration(hours: 2);
  
//   /// 是否已过期
//   bool get isExpired => 
//       DateTime.now().difference(compilationTime) > cacheExpiry;
// }

// /// 缓存访问统计
// class CacheAccessStats {
//   /// 创建缓存访问统计实例
//   CacheAccessStats({
//     this.hitCount = 0,
//     this.missCount = 0,
//     this.precompileCount = 0,
//   });

//   /// 缓存命中次数
//   int hitCount;
//   /// 缓存未命中次数
//   int missCount;
//   /// 预编译次数
//   int precompileCount;
  
//   /// 缓存命中率（命中次数 / 总访问次数）
//   double get hitRate => 
//       hitCount + missCount > 0 ? hitCount / (hitCount + missCount) : 0.0;
// }

// /// Task 36.1: 高级模板缓存和预编译优化系统
// class AdvancedTemplateCacheManager {
//   /// 创建高级模板缓存管理器实例
//   AdvancedTemplateCacheManager(this.templateEngine);

//   /// 模板引擎实例
//   final TemplateEngine templateEngine;
  
//   /// 预编译模板缓存
//   final Map<String, PrecompiledTemplate> _precompiledCache = {};
  
//   /// 缓存访问统计
//   final Map<String, CacheAccessStats> _cacheStats = {};
  
//   /// 预热任务队列
//   final Set<String> _preheatingQueue = {};
  
//   /// 缓存配置
//   /// 最大缓存大小
//   static const int maxCacheSize = 50;
//   /// 预热任务间隔时间
//   static const Duration preheatingInterval = Duration(minutes: 5);
//   /// 缓存过期时间
//   static const Duration cacheExpiry = Duration(hours: 2);

//   /// 预编译单个模板
//   Future<PrecompiledTemplate?> precompileTemplate(String templateName) async {
//     try {
//       final stopwatch = Stopwatch()..start();
      
//       cli_logger.Logger.debug('开始预编译模板: $templateName');
      
//       // 检查现有缓存
//       if (_precompiledCache.containsKey(templateName)) {
//         final cached = _precompiledCache[templateName]!;
//         if (!cached.isExpired) {
//           cached.lastAccessed = DateTime.now();
//           _updateStats(templateName, hit: true);
//           cli_logger.Logger.debug('使用已预编译的模板: $templateName');
//           return cached;
//         }
//       }

//       // 加载并预编译模板
//       final generator = await templateEngine.loadTemplate(templateName);
//       if (generator == null) {
//         _updateStats(templateName, miss: true);
//         return null;
//       }

//       // 获取模板元数据
//       final metadata = await templateEngine.getTemplateInfo(templateName) ?? {};
      
//       // 获取变量定义
//       final variables = generator.vars;

//       // 创建预编译模板
//       final precompiled = PrecompiledTemplate(
//         templateName: templateName,
//         generator: generator,
//         metadata: metadata,
//         variables: variables,
//         compilationTime: DateTime.now(),
//         lastAccessed: DateTime.now(),
//       );

//       // 缓存管理
//       await _manageCacheSize();
//       _precompiledCache[templateName] = precompiled;
      
//       _updateStats(templateName, precompile: true);
      
//       cli_logger.Logger.success(
//         '模板预编译完成: $templateName (${stopwatch.elapsedMilliseconds}ms)',
//       );
      
//       return precompiled;

//     } catch (e) {
//       cli_logger.Logger.error('模板预编译失败: $templateName', error: e);
//       _updateStats(templateName, miss: true);
//       return null;
//     }
//   }

//   /// 批量预编译常用模板
//   Future<void> precompileFrequentTemplates() async {
//     try {
//       final availableTemplates = await templateEngine.getAvailableTemplates();
      
//       // 根据访问统计确定频繁使用的模板
//       final frequentTemplates = _getFrequentTemplates(availableTemplates);
      
//       cli_logger.Logger.info('开始批量预编译 ${frequentTemplates.length} 个常用模板');
      
//       final futures = frequentTemplates.map((template) => 
//           _preheatingQueue.add(template) ? precompileTemplate(template) : null,
//       ).where((future) => future != null).cast<Future<PrecompiledTemplate?>>();
      
//       final results = await Future.wait(futures);
//       final successCount = results.where((result) => result != null).length;
      
//       cli_logger.Logger.success(
//         '批量预编译完成: $successCount/${frequentTemplates.length} 个模板成功',
//       );
      
//       _preheatingQueue.clear();

//     } catch (e) {
//       cli_logger.Logger.error('批量预编译失败', error: e);
//       _preheatingQueue.clear();
//     }
//   }

//   /// 获取预编译模板（缓存优先）
//   Future<PrecompiledTemplate?> getPrecompiledTemplate(String templateName) async {
//     // 检查预编译缓存
//     if (_precompiledCache.containsKey(templateName)) {
//       final cached = _precompiledCache[templateName]!;
//       if (!cached.isExpired) {
//         cached.lastAccessed = DateTime.now();
//         _updateStats(templateName, hit: true);
//         return cached;
//       } else {
//         // 移除过期缓存
//         _precompiledCache.remove(templateName);
//       }
//     }

//     // 实时预编译
//     return precompileTemplate(templateName);
//   }

//   /// 缓存预热任务
//   Future<void> warmUpCache() async {
//     cli_logger.Logger.info('开始缓存预热...');
    
//     final stopwatch = Stopwatch()..start();
    
//     // 预编译常用模板
//     await precompileFrequentTemplates();
    
//     // 清理过期缓存
//     await _cleanExpiredCache();
    
//     cli_logger.Logger.success(
//       '缓存预热完成 (${stopwatch.elapsedMilliseconds}ms)',
//     );
//   }

//   /// 获取缓存统计信息
//   Map<String, dynamic> getCacheStatistics() {
//     final totalHits = _cacheStats.values.fold(0, (sum, stats) => sum + stats.hitCount);
//     final totalMisses = _cacheStats.values.fold(0, (sum, stats) => sum + stats.missCount);
//     final totalPrecompiles = _cacheStats.values.fold(0, (sum, stats) => sum + stats.precompileCount);
    
//     return {
//       'cache_size': _precompiledCache.length,
//       'max_cache_size': maxCacheSize,
//       'cache_utilization': _precompiledCache.length / maxCacheSize,
//       'total_hits': totalHits,
//       'total_misses': totalMisses,
//       'total_precompiles': totalPrecompiles,
//       'hit_rate': totalHits + totalMisses > 0 
//           ? totalHits / (totalHits + totalMisses) 
//           : 0.0,
//       'templates_in_cache': _precompiledCache.keys.toList(),
//       'expired_count': _precompiledCache.values.where((t) => t.isExpired).length,
//       'recent_activity': _getRecentCacheActivity(),
//     };
//   }

//   /// 清理缓存
//   Future<void> clearCache() async {
//     final beforeSize = _precompiledCache.length;
//     _precompiledCache.clear();
//     _cacheStats.clear();
//     _preheatingQueue.clear();
    
//     cli_logger.Logger.info('缓存已清理，移除了 $beforeSize 个预编译模板');
//   }

//   // 私有辅助方法

//   /// 更新缓存统计
//   void _updateStats(String templateName, {bool hit = false, bool miss = false, bool precompile = false}) {
//     final stats = _cacheStats.putIfAbsent(templateName, CacheAccessStats.new);
    
//     if (hit) stats.hitCount++;
//     if (miss) stats.missCount++;
//     if (precompile) stats.precompileCount++;
//   }

//   /// 管理缓存大小
//   Future<void> _manageCacheSize() async {
//     if (_precompiledCache.length >= maxCacheSize) {
//       // 移除最旧的和最少使用的模板
//       final sortedTemplates = _precompiledCache.entries.toList()
//         ..sort((a, b) {
//           // 首先按过期状态排序
//           if (a.value.isExpired && !b.value.isExpired) return -1;
//           if (!a.value.isExpired && b.value.isExpired) return 1;
          
//           // 然后按最后访问时间排序
//           return a.value.lastAccessed.compareTo(b.value.lastAccessed);
//         });

//       // 移除25%的旧缓存
//       final removeCount = (maxCacheSize * 0.25).ceil();
//       for (var i = 0; i < removeCount && i < sortedTemplates.length; i++) {
//         final templateName = sortedTemplates[i].key;
//         _precompiledCache.remove(templateName);
//         cli_logger.Logger.debug('移除旧缓存: $templateName');
//       }
//     }
//   }

//   /// 获取频繁使用的模板
//   List<String> _getFrequentTemplates(List<String> availableTemplates) {
//     // 根据访问统计排序
//     final templatesWithStats = availableTemplates.map((template) {
//       final stats = _cacheStats[template];
//       final score = stats != null 
//           ? stats.hitCount + stats.missCount + stats.precompileCount
//           : 0;
//       return MapEntry(template, score);
//     }).toList()
//       ..sort((a, b) => b.value.compareTo(a.value));

//     // 返回前70%或至少前3个
//     final topCount = math.max(3, (availableTemplates.length * 0.7).ceil());
//     return templatesWithStats
//         .take(topCount)
//         .map((entry) => entry.key)
//         .toList();
//   }

//   /// 清理过期缓存
//   Future<void> _cleanExpiredCache() async {
//     final expiredTemplates = _precompiledCache.entries
//         .where((entry) => entry.value.isExpired)
//         .map((entry) => entry.key)
//         .toList();

//     for (final templateName in expiredTemplates) {
//       _precompiledCache.remove(templateName);
//     }

//     if (expiredTemplates.isNotEmpty) {
//       cli_logger.Logger.debug('清理了 ${expiredTemplates.length} 个过期缓存');
//     }
//   }

//   /// 获取最近缓存活动
//   List<Map<String, dynamic>> _getRecentCacheActivity() {
//     return _precompiledCache.entries
//         .map((entry) => {
//           'template': entry.key,
//           'last_accessed': entry.value.lastAccessed.toIso8601String(),
//           'compilation_time': entry.value.compilationTime.toIso8601String(),
//           'is_expired': entry.value.isExpired,
//         },)
//         .toList()
//       ..sort((a, b) => (b['last_accessed']! as String)
//           .compareTo(a['last_accessed']! as String),);
//   }
// }

// /// 生成任务数据结构
// class GenerationTask {
//   /// 创建生成任务实例
//   GenerationTask({
//     required this.id,
//     required this.templateName,
//     required this.outputPath,
//     required this.variables,
//     required this.completer,
//     this.priority = 0,
//     this.hooks,
//   });

//   /// 任务ID
//   final String id;
//   /// 模板名称
//   final String templateName;
//   /// 输出路径
//   final String outputPath;
//   /// 模板变量
//   final Map<String, dynamic> variables;
//   /// 异步完成器
//   final Completer<GenerationResult> completer;
//   /// 任务优先级
//   final int priority;
//   /// 钩子列表
//   final List<TemplateHook>? hooks;
  
//   /// 任务创建时间
//   DateTime get createdAt => DateTime.now();
// }

// /// Task 36.2: 异步生成和并发处理系统
// class AsyncTemplateGenerationManager {
//   /// 创建异步模板生成管理器实例
//   AsyncTemplateGenerationManager(this.templateEngine);

//   /// 模板引擎实例
//   final TemplateEngine templateEngine;
  
//   /// 最大并发生成数量
//   static const int maxConcurrentGenerations = 5;
//   /// 单个生成任务超时时间
//   static const Duration generationTimeout = Duration(minutes: 10);
  
//   /// 当前正在执行的生成任务
//   final Map<String, Future<GenerationResult>> _activeGenerations = {};
  
//   /// 生成任务队列
//   final List<GenerationTask> _generationQueue = [];
  
//   /// 并发控制信号量
//   int _activeTasks = 0;

//   /// 异步生成模板（支持并发）
//   Future<GenerationResult> generateTemplateAsync({
//     required String templateName,
//     required String outputPath,
//     required Map<String, dynamic> variables,
//     List<TemplateHook>? hooks,
//     int priority = 0,
//     bool skipQueue = false,
//   }) async {
//     final taskId = _generateTaskId(templateName, outputPath);
    
//     // 检查是否已有相同任务
//     if (_activeGenerations.containsKey(taskId)) {
//       cli_logger.Logger.debug('重用已有的生成任务: $taskId');
//       return _activeGenerations[taskId]!;
//     }

//     final completer = Completer<GenerationResult>();
//     final task = GenerationTask(
//       id: taskId,
//       templateName: templateName,
//       outputPath: outputPath,
//       variables: variables,
//       completer: completer,
//       priority: priority,
//       hooks: hooks,
//     );

//     if (skipQueue || _activeTasks < maxConcurrentGenerations) {
//       // 直接执行
//       unawaited(_executeGenerationTask(task));
//     } else {
//       // 加入队列
//       _generationQueue.add(task);
//       _generationQueue.sort((a, b) => b.priority.compareTo(a.priority));
//       cli_logger.Logger.debug('任务加入队列: $taskId (队列长度: ${_generationQueue.length})');
//     }

//     _activeGenerations[taskId] = completer.future;
//     return completer.future;
//   }

//   /// 批量异步生成多个模板
//   Future<List<GenerationResult>> generateMultipleTemplatesAsync({
//     required List<TemplateGenerationSpec> specs,
//     bool allowPartialFailure = true,
//     Duration? timeout,
//   }) async {
//     try {
//       cli_logger.Logger.info('开始批量异步生成 ${specs.length} 个模板');
      
//       final futures = specs.map((spec) => generateTemplateAsync(
//         templateName: spec.templateName,
//         outputPath: spec.outputPath,
//         variables: spec.variables,
//         hooks: spec.hooks,
//         priority: spec.priority,
//       ),).toList();

//       if (timeout != null) {
//         final results = await Future.wait(futures, eagerError: !allowPartialFailure)
//             .timeout(timeout);
//         return results;
//       } else {
//         return await Future.wait(futures, eagerError: !allowPartialFailure);
//       }

//     } catch (e) {
//       cli_logger.Logger.error('批量异步生成失败', error: e);
//       rethrow;
//     }
//   }

//   /// 并行生成多个模板（流式处理）
//   Stream<GenerationResult> generateTemplatesStream({
//     required List<TemplateGenerationSpec> specs,
//     int? maxConcurrency,
//   }) async* {
//     final actualMaxConcurrency = maxConcurrency ?? maxConcurrentGenerations;
    
//     cli_logger.Logger.info('开始流式生成 ${specs.length} 个模板 (并发度: $actualMaxConcurrency)');
    
//     // 使用信号量控制并发度
//     final semaphore = Semaphore(actualMaxConcurrency);
    
//     final futures = specs.map((spec) async {
//       await semaphore.acquire();
//       try {
//         return await generateTemplateAsync(
//           templateName: spec.templateName,
//           outputPath: spec.outputPath,
//           variables: spec.variables,
//           hooks: spec.hooks,
//           priority: spec.priority,
//           skipQueue: true, // 流式处理跳过队列
//         );
//       } finally {
//         semaphore.release();
//       }
//     });

//     // 流式返回结果
//     for (final future in futures) {
//       yield await future;
//     }
//   }

//   /// 获取生成统计信息
//   Map<String, dynamic> getGenerationStatistics() {
//     return {
//       'active_generations': _activeGenerations.length,
//       'queued_tasks': _generationQueue.length,
//       'active_tasks': _activeTasks,
//       'max_concurrent_generations': maxConcurrentGenerations,
//       'queue_utilization': _generationQueue.length / 10, // 假设队列容量为10
//       'active_task_ids': _activeGenerations.keys.toList(),
//       'queued_task_priorities': _generationQueue.map((t) => t.priority).toList(),
//     };
//   }

//   /// 取消所有待处理的任务
//   Future<void> cancelAllPendingTasks() async {
//     final cancelledCount = _generationQueue.length;
    
//     // 取消队列中的任务
//     for (final task in _generationQueue) {
//       task.completer.complete(GenerationResult.failure('任务已取消'));
//     }
//     _generationQueue.clear();
    
//     cli_logger.Logger.info('已取消 $cancelledCount 个待处理任务');
//   }

//   /// 等待所有活动任务完成
//   Future<void> waitForAllTasks() async {
//     if (_activeGenerations.isNotEmpty) {
//       cli_logger.Logger.info('等待 ${_activeGenerations.length} 个活动任务完成...');
//       await Future.wait(_activeGenerations.values);
//       cli_logger.Logger.info('所有活动任务已完成');
//     }
//   }

//   // 私有方法

//   /// 执行生成任务
//   Future<void> _executeGenerationTask(GenerationTask task) async {
//     _activeTasks++;
    
//     try {
//       cli_logger.Logger.debug('开始执行生成任务: ${task.id}');
      
//       final result = await _performGeneration(task)
//           .timeout(generationTimeout);
      
//       task.completer.complete(result);
      
//     } catch (e) {
//       final errorResult = GenerationResult.failure(
//         '生成任务执行失败: $e',
//         outputPath: task.outputPath,
//       );
//       task.completer.complete(errorResult);
      
//     } finally {
//       _activeTasks--;
//       _activeGenerations.remove(task.id);
      
//       // 处理下一个队列任务
//       _processNextQueuedTask();
//     }
//   }

//   /// 执行实际的模板生成
//   Future<GenerationResult> _performGeneration(GenerationTask task) async {
//     try {
//              // 如果有钩子，使用钩子生成
//        if (task.hooks != null && task.hooks!.isNotEmpty) {
//          return await templateEngine.generateWithHooks(
//            templateName: task.templateName,
//            outputPath: task.outputPath,
//            variables: task.variables,
//            additionalHooks: task.hooks,
//          );
//       } else {
//         // 标准生成
//         final success = await templateEngine.generateModule(
//           templateName: task.templateName,
//           outputPath: task.outputPath,
//           variables: task.variables,
//         );
        
//         return GenerationResult(
//           success: success,
//           outputPath: task.outputPath,
//           message: success ? '模板生成成功' : '模板生成失败',
//         );
//       }
      
//     } catch (e) {
//       return GenerationResult.failure(
//         '模板生成异常: $e',
//         outputPath: task.outputPath,
//       );
//     }
//   }

//   /// 处理下一个队列任务
//   void _processNextQueuedTask() {
//     if (_generationQueue.isNotEmpty && _activeTasks < maxConcurrentGenerations) {
//       final nextTask = _generationQueue.removeAt(0);
//       unawaited(_executeGenerationTask(nextTask));
//     }
//   }

//   /// 生成任务ID
//   String _generateTaskId(String templateName, String outputPath) {
//     return '${templateName}_${outputPath.hashCode}';
//   }
// }

// /// 模板生成规格
// class TemplateGenerationSpec {
//   /// 创建模板生成规格实例
//   const TemplateGenerationSpec({
//     required this.templateName,
//     required this.outputPath,
//     required this.variables,
//     this.hooks,
//     this.priority = 0,
//   });

//   /// 模板名称
//   final String templateName;
//   /// 输出路径
//   final String outputPath;
//   /// 模板变量映射
//   final Map<String, dynamic> variables;
//   /// 可选的钩子列表
//   final List<TemplateHook>? hooks;
//   /// 任务优先级
//   final int priority;
// }

// /// 简单信号量实现
// class Semaphore {
//   /// 创建信号量实例，指定最大许可数量
//   Semaphore(this.maxCount) : _currentCount = maxCount;

//   /// 最大许可数量
//   final int maxCount;
//   /// 当前可用许可数量
//   int _currentCount;
//   /// 等待队列
//   final List<Completer<void>> _waitQueue = [];

//   /// 获取许可
//   Future<void> acquire() async {
//     if (_currentCount > 0) {
//       _currentCount--;
//       return;
//     }

//     final completer = Completer<void>();
//     _waitQueue.add(completer);
//     return completer.future;
//   }

//   /// 释放许可
//   void release() {
//     if (_waitQueue.isNotEmpty) {
//       final completer = _waitQueue.removeAt(0);
//       completer.complete();
//     } else {
//       _currentCount++;
//     }
//   }
// }

// // ==================== Task 36.3: 完善的错误恢复机制 ====================

// /// 智能错误恢复管理器
// class IntelligentErrorRecoveryManager {
//   /// 创建智能错误恢复管理器实例
//   IntelligentErrorRecoveryManager(this.templateEngine);

//   /// 模板引擎实例引用
//   final TemplateEngine templateEngine;
  
//   /// 错误恢复历史记录
//   final List<ErrorRecoveryRecord> _recoveryHistory = [];
  
//   /// 错误模式分析器
//   final ErrorPatternAnalyzer _patternAnalyzer = ErrorPatternAnalyzer();

//   /// 智能错误恢复
//   Future<ErrorRecoveryResult> intelligentRecover(
//     TemplateEngineException error,
//     HookContext? context,
//   ) async {
//     try {
//       cli_logger.Logger.info('开始智能错误恢复: ${error.type}');
      
//       // 1. 记录错误
//       final record = ErrorRecoveryRecord(
//         error: error,
//         timestamp: DateTime.now(),
//         context: context,
//       );
//       _recoveryHistory.add(record);
      
//       // 2. 分析错误模式
//       final pattern = await _patternAnalyzer.analyzeError(error, _recoveryHistory);
      
//       // 3. 选择恢复策略
//       final strategy = await _selectRecoveryStrategy(error, pattern, context);
      
//       // 4. 执行恢复
//       final result = await strategy.recover(error);
      
//       // 5. 更新恢复记录
//       record.recoveryResult = result;
//       record.recoveryStrategy = strategy.runtimeType.toString();
      
//       if (result.success) {
//         cli_logger.Logger.success('智能错误恢复成功: ${result.message}');
//       } else {
//         cli_logger.Logger.warning('智能错误恢复失败: ${result.message}');
//       }
      
//       return result;
      
//     } catch (e) {
//       cli_logger.Logger.error('智能错误恢复过程异常', error: e);
//       return ErrorRecoveryResult.createFailure('恢复过程异常: $e');
//     }
//   }

//   /// 获取错误恢复统计
//   Map<String, dynamic> getRecoveryStatistics() {
//     if (_recoveryHistory.isEmpty) {
//       return <String, dynamic>{
//         'total_attempts': 0,
//         'success_rate': 0.0,
//         'common_errors': <Map<String, dynamic>>[],
//         'recovery_trends': <String, dynamic>{},
//       };
//     }

//     final totalAttempts = _recoveryHistory.length;
//     final successfulRecoveries = _recoveryHistory
//         .where((r) => r.recoveryResult?.success == true)
//         .length;
    
//     final errorTypeCounts = <String, int>{};
//     final strategySuccess = <String, int>{};
    
//     for (final record in _recoveryHistory) {
//       final errorType = record.error.type.toString();
//       errorTypeCounts[errorType] = (errorTypeCounts[errorType] ?? 0) + 1;
      
//       if (record.recoveryResult?.success == true && record.recoveryStrategy != null) {
//         final strategy = record.recoveryStrategy!;
//         strategySuccess[strategy] = (strategySuccess[strategy] ?? 0) + 1;
//       }
//     }

//     return <String, dynamic>{
//       'total_attempts': totalAttempts,
//       'successful_recoveries': successfulRecoveries,
//       'success_rate': successfulRecoveries / totalAttempts,
//       'common_errors': errorTypeCounts.entries
//           .map((e) => <String, dynamic>{'type': e.key, 'count': e.value})
//           .toList()
//         ..sort((a, b) => (b['count']! as int).compareTo(a['count']! as int)),
//       'strategy_effectiveness': strategySuccess,
//       'recent_recovery_trend': _getRecentRecoveryTrend(),
//     };
//   }

//   /// 清理恢复历史
//   void cleanupHistory({int maxRecords = 1000}) {
//     if (_recoveryHistory.length > maxRecords) {
//       final removeCount = _recoveryHistory.length - maxRecords;
//       _recoveryHistory.removeRange(0, removeCount);
//       cli_logger.Logger.debug('清理错误恢复历史: 移除了 $removeCount 条记录');
//     }
//   }

//   // 私有方法

//   /// 选择恢复策略
//   Future<ErrorRecoveryStrategy> _selectRecoveryStrategy(
//     TemplateEngineException error,
//     ErrorPattern pattern,
//     HookContext? context,
//   ) async {
//     // 根据错误类型和模式选择最佳策略
//     switch (error.type) {
//       case TemplateEngineErrorType.templateNotFound:
//         return AdaptiveTemplateNotFoundStrategy(templateEngine, pattern);
      
//       case TemplateEngineErrorType.fileSystemError:
//         return IntelligentFileSystemRecoveryStrategy(pattern);
      
//       case TemplateEngineErrorType.variableValidationFailed:
//         return SmartVariableRecoveryStrategy(templateEngine, pattern);
      
//       case TemplateEngineErrorType.masonError:
//         return MasonErrorRecoveryStrategy(templateEngine, pattern);
      
//       case TemplateEngineErrorType.networkError:
//         return NetworkErrorRecoveryStrategy(pattern);
      
//       default:
//         return FallbackRecoveryStrategy(pattern);
//     }
//   }

//   /// 获取最近恢复趋势
//   List<Map<String, dynamic>> _getRecentRecoveryTrend() {
//     final recentRecords = _recoveryHistory
//         .where((r) => DateTime.now().difference(r.timestamp).inDays <= 7)
//         .toList();
    
//     final dailyStats = <String, Map<String, int>>{};
    
//     for (final record in recentRecords) {
//       final day = record.timestamp.toIso8601String().substring(0, 10);
//       dailyStats[day] ??= <String, int>{'attempts': 0, 'successes': 0};
//       dailyStats[day]!['attempts'] = dailyStats[day]!['attempts']! + 1;
      
//       if (record.recoveryResult?.success == true) {
//         dailyStats[day]!['successes'] = dailyStats[day]!['successes']! + 1;
//       }
//     }
    
//     return dailyStats.entries
//         .map((e) => <String, dynamic>{
//           'date': e.key,
//           'attempts': e.value['attempts'],
//           'successes': e.value['successes'],
//           'success_rate': e.value['attempts']! > 0 
//               ? e.value['successes']! / e.value['attempts']!
//               : 0.0,
//         },)
//         .toList()
//       ..sort((a, b) => (a['date']! as String).compareTo(b['date']! as String));
//   }
// }

// /// 错误恢复记录
// class ErrorRecoveryRecord {
//   /// 创建错误恢复记录实例
//   ErrorRecoveryRecord({
//     required this.error,
//     required this.timestamp,
//     this.context,
//   });

//   /// 错误异常信息
//   final TemplateEngineException error;
//   /// 错误发生时间戳
//   final DateTime timestamp;
//   /// 钩子上下文（可选）
//   final HookContext? context;
//   /// 恢复结果（可选）
//   ErrorRecoveryResult? recoveryResult;
//   /// 恢复策略名称（可选）
//   String? recoveryStrategy;
// }

// /// 错误模式分析器
// class ErrorPatternAnalyzer {
//   /// 分析错误模式
//   Future<ErrorPattern> analyzeError(
//     TemplateEngineException error,
//     List<ErrorRecoveryRecord> history,
//   ) async {
//     final recentSimilarErrors = history
//         .where((r) => r.error.type == error.type)
//         .where((r) => DateTime.now().difference(r.timestamp).inMinutes <= 30)
//         .toList();

//     final frequency = recentSimilarErrors.length;
//     final recentSuccessRate = recentSimilarErrors.isEmpty ? 0.0 :
//         recentSimilarErrors.where((r) => r.recoveryResult?.success == true).length /
//         recentSimilarErrors.length;

//     return ErrorPattern(
//       errorType: error.type,
//       frequency: frequency,
//       recentSuccessRate: recentSuccessRate,
//       isRecurring: frequency > 2,
//       severity: _calculateSeverity(error, frequency),
//       suggestedApproach: _suggestApproach(error.type, frequency, recentSuccessRate),
//     );
//   }

//   /// 计算错误严重程度
//   ErrorSeverity _calculateSeverity(TemplateEngineException error, int frequency) {
//     if (frequency > 5) return ErrorSeverity.critical;
//     if (frequency > 2) return ErrorSeverity.high;
    
//     switch (error.type) {
//       case TemplateEngineErrorType.fileSystemError:
//       case TemplateEngineErrorType.permissionError:
//         return ErrorSeverity.high;
//       case TemplateEngineErrorType.templateNotFound:
//       case TemplateEngineErrorType.variableValidationFailed:
//         return ErrorSeverity.medium;
//       default:
//         return ErrorSeverity.low;
//     }
//   }

//   /// 建议恢复方法
//   RecoveryApproach _suggestApproach(
//     TemplateEngineErrorType errorType,
//     int frequency,
//     double recentSuccessRate,
//   ) {
//     if (frequency > 3 && recentSuccessRate < 0.5) {
//       return RecoveryApproach.preventive;
//     }
    
//     if (recentSuccessRate > 0.8) {
//       return RecoveryApproach.retry;
//     }
    
//     return RecoveryApproach.adaptive;
//   }
// }

// /// 错误模式
// class ErrorPattern {
//   const ErrorPattern({
//     required this.errorType,
//     required this.frequency,
//     required this.recentSuccessRate,
//     required this.isRecurring,
//     required this.severity,
//     required this.suggestedApproach,
//   });

//   /// 错误类型
//   final TemplateEngineErrorType errorType;
//   /// 错误发生频率
//   final int frequency;
//   /// 最近成功率
//   final double recentSuccessRate;
//   /// 是否为重复发生的错误
//   final bool isRecurring;
//   /// 错误严重程度
//   final ErrorSeverity severity;
//   /// 建议的恢复方法
//   final RecoveryApproach suggestedApproach;
// }

// /// 错误严重程度
// enum ErrorSeverity { 
//   /// 低严重程度
//   low, 
//   /// 中等严重程度
//   medium, 
//   /// 高严重程度
//   high, 
//   /// 严重程度
//   critical 
// }

// /// 恢复方法
// enum RecoveryApproach { 
//   /// 重试方法
//   retry, 
//   /// 自适应方法
//   adaptive, 
//   /// 预防性方法
//   preventive, 
//   /// 手动方法
//   manual 
// }

// // ==================== 智能恢复策略实现 ====================

// /// 自适应模板未找到恢复策略
// class AdaptiveTemplateNotFoundStrategy implements ErrorRecoveryStrategy {
//   AdaptiveTemplateNotFoundStrategy(this.templateEngine, this.pattern);

//   /// 模板引擎实例
//   final TemplateEngine templateEngine;
//   /// 错误模式
//   final ErrorPattern pattern;

//   @override
//   bool canHandle(TemplateEngineErrorType errorType) {
//     return errorType == TemplateEngineErrorType.templateNotFound;
//   }

//   @override
//   Future<ErrorRecoveryResult> recover(TemplateEngineException error) async {
//     try {
//       final targetTemplate = error.details?['templateName'] as String?;
//       if (targetTemplate == null) {
//         return ErrorRecoveryResult.createFailure('无法确定目标模板名称');
//       }

//       // 1. 智能模板匹配
//       final suggestions = await _findSimilarTemplates(targetTemplate);
      
//       if (suggestions.isNotEmpty) {
//         // 2. 如果频繁出错，尝试自动创建基础模板
//         if (pattern.isRecurring && pattern.frequency > 3) {
//           final created = await _tryCreateBasicTemplate(targetTemplate);
//           if (created) {
//             return ErrorRecoveryResult.createSuccess(
//               message: '自动创建了基础模板: $targetTemplate',
//               value: targetTemplate,
//             );
//           }
//         }

//         return ErrorRecoveryResult.createSuccess(
//           message: '找到相似模板: ${suggestions.join(", ")}',
//           value: suggestions,
//         );
//       }

//       // 3. 尝试从模板库下载
//       if (await _tryDownloadTemplate(targetTemplate)) {
//         return ErrorRecoveryResult.createSuccess(
//           message: '成功下载模板: $targetTemplate',
//           value: targetTemplate,
//         );
//       }

//       return ErrorRecoveryResult.createFailure('无法恢复模板: $targetTemplate');

//     } catch (e) {
//       return ErrorRecoveryResult.createFailure('模板恢复异常: $e');
//     }
//   }

//   /// 查找相似模板
//   Future<List<String>> _findSimilarTemplates(String targetTemplate) async {
//     try {
//       final availableTemplates = await templateEngine.getAvailableTemplates();
//       final target = targetTemplate.toLowerCase();
      
      
//       // 使用多种匹配算法
//       final suggestions = <String>[];
      
//       // 1. 精确部分匹配
//       suggestions.addAll(availableTemplates.where((template) =>
//           template.toLowerCase().contains(target) ||
//           target.contains(template.toLowerCase()),),);
      
//       // 2. 模糊匹配（基于编辑距离）
//       for (final template in availableTemplates) {
//         if (suggestions.contains(template)) continue;
        
//         final distance = _calculateEditDistance(target, template.toLowerCase());
//         final maxLength = math.max(target.length, template.length);
//         final similarity = 1.0 - (distance / maxLength);
        
//         if (similarity > 0.6) {
//           suggestions.add(template);
//         }
//       }
      
//       return suggestions.take(5).toList();
//     } catch (e) {
//       return [];
//     }
//   }

//   /// 计算编辑距离
//   int _calculateEditDistance(String s1, String s2) {
//     final dp = List<List<int>>.generate(
//       s1.length + 1,
//       (i) => List<int>.filled(s2.length + 1, 0),
//     );

//     for (var i = 0; i <= s1.length; i++) {
//       dp[i][0] = i;
//     }
//     for (var j = 0; j <= s2.length; j++) {
//       dp[0][j] = j;
//     }

//     for (var i = 1; i <= s1.length; i++) {
//       for (var j = 1; j <= s2.length; j++) {
//         if (s1[i - 1] == s2[j - 1]) {
//           dp[i][j] = dp[i - 1][j - 1];
//         } else {
//           dp[i][j] = 1 + math.min(
//             math.min(dp[i - 1][j], dp[i][j - 1]),
//             dp[i - 1][j - 1],
//           );
//         }
//       }
//     }

//     return dp[s1.length][s2.length];
//   }

//   /// 尝试创建基础模板
//   Future<bool> _tryCreateBasicTemplate(String templateName) async {
//     try {
//       cli_logger.Logger.info('尝试自动创建基础模板: $templateName');
//       return await templateEngine.createBaseTemplate(templateName);
//     } catch (e) {
//       cli_logger.Logger.warning('自动创建模板失败: $e');
//       return false;
//     }
//   }

//   /// 尝试下载模板
//   Future<bool> _tryDownloadTemplate(String templateName) async {
//     try {
//       // 这里可以实现从远程模板库下载逻辑
//       cli_logger.Logger.debug('尝试下载模板: $templateName (暂未实现)');
//       return false;
//     } catch (e) {
//       return false;
//     }
//   }
// }

// /// 智能变量恢复策略
// class SmartVariableRecoveryStrategy implements ErrorRecoveryStrategy {
//   SmartVariableRecoveryStrategy(this.templateEngine, this.pattern);

//   /// 模板引擎实例
//   final TemplateEngine templateEngine;
//   /// 错误模式
//   final ErrorPattern pattern;

//   @override
//   bool canHandle(TemplateEngineErrorType errorType) {
//     return errorType == TemplateEngineErrorType.variableValidationFailed;
//   }

//   @override
//   Future<ErrorRecoveryResult> recover(TemplateEngineException error) async {
//     try {
//       final validationErrors = error.details?['validationErrors'] as Map<String, String>?;
//       if (validationErrors == null || validationErrors.isEmpty) {
//         return ErrorRecoveryResult.createFailure('无法获取验证错误详情');
//       }

//       final recoveredVariables = <String, dynamic>{};
//       final recoveryMessages = <String>[];

//       for (final entry in validationErrors.entries) {
//         final varName = entry.key;
//         final errorMsg = entry.value;

//         final recovered = await _recoverVariable(varName, errorMsg);
//         if (recovered != null) {
//           recoveredVariables[varName] = recovered;
//           recoveryMessages.add('恢复变量 $varName: $recovered');
//         }
//       }

//       if (recoveredVariables.isNotEmpty) {
//         return ErrorRecoveryResult.createSuccess(
//           message: '成功恢复 ${recoveredVariables.length} 个变量: ${recoveryMessages.join(", ")}',
//           value: recoveredVariables,
//         );
//       }

//       return ErrorRecoveryResult.createFailure('无法恢复任何变量');

//     } catch (e) {
//       return ErrorRecoveryResult.createFailure('变量恢复异常: $e');
//     }
//   }

//   /// 恢复单个变量
//   Future<dynamic> _recoverVariable(String varName, String errorMsg) async {
//     try {
//       // 根据变量名和错误消息智能恢复
//       switch (varName) {
//         case 'module_id':
//           return _generateModuleId();
//         case 'module_name':
//           return _generateModuleName();
//         case 'class_name':
//           return _generateClassName();
//         case 'author':
//           return 'lgnorant-lu';
//         case 'version':
//           return '1.0.0';
//         case 'description':
//           return '自动生成的模块描述';
//         default:
//           return _generateGenericValue(varName, errorMsg);
//       }
//     } catch (e) {
//       return null;
//     }
//   }

//   /// 生成模块ID
//   String _generateModuleId() {
//     final timestamp = DateTime.now().millisecondsSinceEpoch;
//     return 'auto_module_$timestamp';
//   }

//   /// 生成模块名称
//   String _generateModuleName() {
//     final timestamp = DateTime.now().millisecondsSinceEpoch;
//     return 'Auto Module $timestamp';
//   }

//   /// 生成类名
//   String _generateClassName() {
//     final timestamp = DateTime.now().millisecondsSinceEpoch;
//     return 'AutoModule$timestamp';
//   }

//   /// 生成通用值
//   dynamic _generateGenericValue(String varName, String errorMsg) {
//     if (errorMsg.contains('空') || errorMsg.contains('empty')) {
//       return 'auto_$varName';
//     }
    
//     if (errorMsg.contains('格式') || errorMsg.contains('format')) {
//       if (varName.toLowerCase().contains('id')) {
//         return 'auto_${varName}_${DateTime.now().millisecondsSinceEpoch}';
//       }
//       if (varName.toLowerCase().contains('name')) {
//         return StringUtils.toPascalCase('auto_$varName');
//       }
//     }
    
//     return 'auto_value';
//   }
// }

// /// 智能文件系统恢复策略
// class IntelligentFileSystemRecoveryStrategy implements ErrorRecoveryStrategy {
//   IntelligentFileSystemRecoveryStrategy(this.pattern);

//   /// 错误模式
//   final ErrorPattern pattern;

//   @override
//   bool canHandle(TemplateEngineErrorType errorType) {
//     return errorType == TemplateEngineErrorType.fileSystemError ||
//            errorType == TemplateEngineErrorType.permissionError;
//   }

//   @override
//   Future<ErrorRecoveryResult> recover(TemplateEngineException error) async {
//     try {
//       final operation = error.details?['operation'] as String?;
//       final targetPath = error.details?['path'] as String?;

//       if (operation == null || targetPath == null) {
//         return ErrorRecoveryResult.createFailure('无法确定文件系统操作详情');
//       }

//       switch (operation) {
//         case 'createDirectory':
//           return await _recoverDirectoryCreation(targetPath);
//         case 'writeFile':
//           return await _recoverFileWrite(targetPath);
//         case 'readFile':
//           return await _recoverFileRead(targetPath);
//         default:
//           return await _attemptGenericRecovery(operation, targetPath);
//       }

//     } catch (e) {
//       return ErrorRecoveryResult.createFailure('文件系统恢复异常: $e');
//     }
//   }

//   /// 恢复目录创建
//   Future<ErrorRecoveryResult> _recoverDirectoryCreation(String dirPath) async {
//     try {
//       // 1. 尝试创建备用路径
//       final altPath = await _createAlternativePath(dirPath);
//       if (altPath != null) {
//         await Directory(altPath).create(recursive: true);
//         return ErrorRecoveryResult.createSuccess(
//           message: '在备用路径创建目录: $altPath',
//           value: altPath,
//         );
//       }

//       // 2. 尝试修复权限
//       if (await _tryFixPermissions(dirPath)) {
//         await Directory(dirPath).create(recursive: true);
//         return ErrorRecoveryResult.createSuccess(
//           message: '修复权限后成功创建目录: $dirPath',
//         );
//       }

//       return ErrorRecoveryResult.createFailure('无法恢复目录创建');

//     } catch (e) {
//       return ErrorRecoveryResult.createFailure('目录创建恢复失败: $e');
//     }
//   }

//   /// 恢复文件写入
//   Future<ErrorRecoveryResult> _recoverFileWrite(String filePath) async {
//     try {
//       // 1. 检查并创建父目录
//       final parentDir = Directory(path.dirname(filePath));
//       if (!parentDir.existsSync()) {
//         parentDir.createSync(recursive: true);
//       }

//       // 2. 尝试备用文件名
//       final altFile = await _createAlternativeFilePath(filePath);
//       if (altFile != null) {
//         return ErrorRecoveryResult.createSuccess(
//           message: '使用备用文件路径: $altFile',
//           value: altFile,
//         );
//       }

//       return ErrorRecoveryResult.createFailure('无法恢复文件写入');

//     } catch (e) {
//       return ErrorRecoveryResult.createFailure('文件写入恢复失败: $e');
//     }
//   }

//   /// 恢复文件读取
//   Future<ErrorRecoveryResult> _recoverFileRead(String filePath) async {
//     try {
//       // 1. 查找相似文件
//       final similarFiles = await _findSimilarFiles(filePath);
//       if (similarFiles.isNotEmpty) {
//         return ErrorRecoveryResult.createSuccess(
//           message: '找到相似文件: ${similarFiles.first}',
//           value: similarFiles.first,
//         );
//       }

//       // 2. 创建默认文件
//       if (await _createDefaultFile(filePath)) {
//         return ErrorRecoveryResult.createSuccess(
//           message: '创建默认文件: $filePath',
//         );
//       }

//       return ErrorRecoveryResult.createFailure('无法恢复文件读取');

//     } catch (e) {
//       return ErrorRecoveryResult.createFailure('文件读取恢复失败: $e');
//     }
//   }

//   /// 通用恢复
//   Future<ErrorRecoveryResult> _attemptGenericRecovery(
//     String operation,
//     String targetPath,
//   ) async {
//     try {
//       cli_logger.Logger.info('尝试通用文件系统恢复: $operation -> $targetPath');
      
//       // 基本的重试机制
//       await Future<void>.delayed(const Duration(milliseconds: 100));
      
//       return ErrorRecoveryResult.createSuccess(
//         message: '完成通用文件系统恢复尝试',
//       );

//     } catch (e) {
//       return ErrorRecoveryResult.createFailure('通用恢复失败: $e');
//     }
//   }

//   // 私有辅助方法

//   /// 创建替代路径
//   Future<String?> _createAlternativePath(String originalPath) async {
//     try {
//       final timestamp = DateTime.now().millisecondsSinceEpoch;
//       final parentDir = path.dirname(originalPath);
//       final dirName = path.basename(originalPath);
      
//       final altPath = path.join(parentDir, '${dirName}_$timestamp');
      
//       if (!Directory(altPath).existsSync()) {
//         return altPath;
//       }
      
//       return null;
//     } catch (e) {
//       return null;
//     }
//   }

//   /// 创建替代文件路径
//   Future<String?> _createAlternativeFilePath(String originalPath) async {
//     try {
//       final timestamp = DateTime.now().millisecondsSinceEpoch;
//       final dir = path.dirname(originalPath);
//       final fileName = path.basenameWithoutExtension(originalPath);
//       final extension = path.extension(originalPath);
      
//       final altPath = path.join(dir, '${fileName}_$timestamp$extension');
      
//       if (!File(altPath).existsSync()) {
//         return altPath;
//       }
      
//       return null;
//     } catch (e) {
//       return null;
//     }
//   }

//   /// 尝试修复权限
//   Future<bool> _tryFixPermissions(String targetPath) async {
//     try {
//       // 在实际实现中，这里可以尝试修改文件权限
//       // 为了简化，这里只返回false
//       return false;
//     } catch (e) {
//       return false;
//     }
//   }

//   /// 查找相似文件
//   Future<List<String>> _findSimilarFiles(String targetPath) async {
//     try {
//       final dir = Directory(path.dirname(targetPath));
//       final targetName = path.basename(targetPath);
      
//       if (!dir.existsSync()) return [];
      
//       final files = <String>[];
//       await for (final entity in dir.list()) {
//         if (entity is File) {
//           final fileName = path.basename(entity.path);
//           if (fileName.toLowerCase().contains(targetName.toLowerCase()) ||
//               targetName.toLowerCase().contains(fileName.toLowerCase())) {
//             files.add(entity.path);
//           }
//         }
//       }
      
//       return files;
//     } catch (e) {
//       return [];
//     }
//   }

//   /// 创建默认文件
//   Future<bool> _createDefaultFile(String filePath) async {
//     try {
//       final file = File(filePath);
//       const defaultContent = '# 自动生成的默认文件\n';
//       await file.writeAsString(defaultContent);
//       return true;
//     } catch (e) {
//       return false;
//     }
//   }
// }

// /// Mason错误恢复策略
// class MasonErrorRecoveryStrategy implements ErrorRecoveryStrategy {
//   MasonErrorRecoveryStrategy(this.templateEngine, this.pattern);

//   /// 模板引擎实例
//   final TemplateEngine templateEngine;
//   /// 错误模式
//   final ErrorPattern pattern;

//   @override
//   bool canHandle(TemplateEngineErrorType errorType) {
//     return errorType == TemplateEngineErrorType.masonError;
//   }

//   @override
//   Future<ErrorRecoveryResult> recover(TemplateEngineException error) async {
//     try {
//       final operation = error.details?['operation'] as String?;
      
//       switch (operation) {
//         case 'fromBrick':
//           return await _recoverBrickLoading(error);
//         case 'generate':
//           return await _recoverGeneration(error);
//         default:
//           return await _recoverGenericMasonError(error);
//       }

//     } catch (e) {
//       return ErrorRecoveryResult.createFailure('Mason错误恢复异常: $e');
//     }
//   }

//   /// 恢复Brick加载错误
//   Future<ErrorRecoveryResult> _recoverBrickLoading(TemplateEngineException error) async {
//     try {
//       // 1. 清理并重新加载模板缓存
//       templateEngine.clearCache();
      
//       // 2. 等待一段时间后重试
//       await Future<void>.delayed(const Duration(milliseconds: 500));
      
//       return ErrorRecoveryResult.createSuccess(
//         message: '清理缓存并准备重试Brick加载',
//       );

//     } catch (e) {
//       return ErrorRecoveryResult.createFailure('Brick加载恢复失败: $e');
//     }
//   }

//   /// 恢复生成错误
//   Future<ErrorRecoveryResult> _recoverGeneration(TemplateEngineException error) async {
//     try {
//       return ErrorRecoveryResult.createSuccess(
//         message: '准备重试Mason生成操作',
//       );

//     } catch (e) {
//       return ErrorRecoveryResult.createFailure('生成错误恢复失败: $e');
//     }
//   }

//   /// 恢复通用Mason错误
//   Future<ErrorRecoveryResult> _recoverGenericMasonError(TemplateEngineException error) async {
//     try {
//       return ErrorRecoveryResult.createSuccess(
//         message: '执行通用Mason错误恢复',
//       );

//     } catch (e) {
//       return ErrorRecoveryResult.createFailure('通用Mason错误恢复失败: $e');
//     }
//   }
// }

// /// 网络错误恢复策略
// class NetworkErrorRecoveryStrategy implements ErrorRecoveryStrategy {
//   NetworkErrorRecoveryStrategy(this.pattern);

//   final ErrorPattern pattern;

//   @override
//   bool canHandle(TemplateEngineErrorType errorType) {
//     return errorType == TemplateEngineErrorType.networkError;
//   }

//   @override
//   Future<ErrorRecoveryResult> recover(TemplateEngineException error) async {
//     try {
//       // 1. 检查网络连接
//       if (await _checkNetworkConnectivity()) {
//         return ErrorRecoveryResult.createSuccess(
//           message: '网络连接正常，可以重试',
//         );
//       }

//       // 2. 启用离线模式
//       return ErrorRecoveryResult.createSuccess(
//         message: '启用离线模式，使用本地资源',
//       );

//     } catch (e) {
//       return ErrorRecoveryResult.createFailure('网络错误恢复失败: $e');
//     }
//   }

//   /// 检查网络连接
//   Future<bool> _checkNetworkConnectivity() async {
//     try {
//       // 简单的网络检查实现
//       await Future<void>.delayed(const Duration(milliseconds: 100));
//       return true; // 假设网络正常
//     } catch (e) {
//       return false;
//     }
//   }
// }

// /// 后备恢复策略
// class FallbackRecoveryStrategy implements ErrorRecoveryStrategy {
//   FallbackRecoveryStrategy(this.pattern);

//   final ErrorPattern pattern;

//   @override
//   bool canHandle(TemplateEngineErrorType errorType) {
//     return true; // 可以处理任何类型的错误
//   }

//   @override
//   Future<ErrorRecoveryResult> recover(TemplateEngineException error) async {
//     try {
//       cli_logger.Logger.info('使用后备恢复策略: ${error.type}');
      
//       // 基本的重试建议
//       return ErrorRecoveryResult.createSuccess(
//         message: '建议检查错误详情并手动重试: ${error.message}',
//       );

//     } catch (e) {
//       return ErrorRecoveryResult.createFailure('后备恢复策略失败: $e');
//     }
//   }
// }

// // ==================== Task 36.4: 用户体验优化和反馈改进 ====================

// /// 用户体验优化管理器
// class UserExperienceManager {
//   UserExperienceManager(this.templateEngine);

//   final TemplateEngine templateEngine;
  
//   /// 进度反馈回调
//   void Function(ProgressUpdate)? onProgressUpdate;
  
//   /// 用户交互历史
//   final List<UserInteraction> _interactionHistory = [];
  
//   /// 性能指标收集器
//   final PerformanceMetricsCollector _metricsCollector = PerformanceMetricsCollector();

//   /// 增强的模板生成（带用户体验优化）
//   Future<GenerationResult> generateWithEnhancedUX({
//     required String templateName,
//     required String outputPath,
//     required Map<String, dynamic> variables,
//     bool overwrite = false,
//     List<TemplateHook>? hooks,
//     TemplateInheritance? inheritance,
//   }) async {
//     final interaction = UserInteraction(
//       action: 'generate_template',
//       templateName: templateName,
//       timestamp: DateTime.now(),
//     );
//     _interactionHistory.add(interaction);

//     try {
//       // 1. 预处理和验证（带进度反馈）
//       _updateProgress('正在验证模板和变量...', 0.1);
      
//       final validationResult = await _validateWithFeedback(templateName, variables);
//       if (!validationResult.success) {
//         interaction.result = 'validation_failed';
//         interaction.error = validationResult.message;
//         return GenerationResult.failure(validationResult.message!, outputPath: outputPath);
//       }

//       // 2. 智能预处理变量
//       _updateProgress('正在预处理变量...', 0.2);
//       final processedVariables = await _intelligentVariableProcessing(
//         templateName,
//         variables,
//       );

//       // 3. 预估生成时间
//       _updateProgress('正在分析模板复杂度...', 0.3);
//       final estimatedDuration = await _estimateGenerationTime(templateName);
//       _updateProgress(
//         '预计生成时间: ${estimatedDuration.inMilliseconds}ms',
//         0.4,
//       );

//       // 4. 执行生成（带详细进度）
//       _updateProgress('正在生成模板...', 0.5);
      
//       final stopwatch = Stopwatch()..start();
//       final result = await templateEngine.generateWithHooks(
//         templateName: templateName,
//         outputPath: outputPath,
//         variables: processedVariables,
//         overwrite: overwrite,
//         additionalHooks: hooks,
//         inheritance: inheritance,
//       );
//       stopwatch.stop();

//       // 5. 后处理和反馈
//       if (result.success) {
//         _updateProgress('正在验证生成结果...', 0.8);
//         await _postGenerationValidation(outputPath);
        
//         _updateProgress('正在收集性能指标...', 0.9);
//         await _collectPerformanceMetrics(templateName, stopwatch.elapsed);
        
//         _updateProgress('生成完成!', 1);
        
//         interaction.result = 'success';
//         interaction.duration = stopwatch.elapsed;
        
//         // 生成用户友好的成功消息
//         final enhancedResult = await _enhanceSuccessResult(result, stopwatch.elapsed);
//         return enhancedResult;
//       } else {
//         interaction.result = 'failed';
//         interaction.error = result.message;
        
//         // 提供智能的失败恢复建议
//         final enhancedResult = await _enhanceFailureResult(result);
//         return enhancedResult;
//       }

//     } catch (e) {
//       interaction.result = 'error';
//       interaction.error = e.toString();
      
//       _updateProgress('生成过程中发生异常', 0);
      
//       return GenerationResult.failure(
//         '生成过程异常: $e',
//         outputPath: outputPath,
//       );
//     }
//   }

//   /// 智能模板推荐
//   Future<List<TemplateRecommendation>> getIntelligentRecommendations({
//     String? context,
//     Map<String, dynamic>? userPreferences,
//   }) async {
//     try {
//       final recommendations = <TemplateRecommendation>[];
//       final availableTemplates = await templateEngine.getAvailableTemplates();

//       for (final templateName in availableTemplates) {
//         final score = await _calculateRecommendationScore(
//           templateName,
//           context,
//           userPreferences,
//         );
        
//         if (score > 0.3) {
//           final templateInfo = await templateEngine.getTemplateInfo(templateName);
//           final recommendation = TemplateRecommendation(
//             templateName: templateName,
//             score: score,
//             reason: await _generateRecommendationReason(templateName, score),
//             metadata: templateInfo ?? {},
//             estimatedComplexity: await _estimateTemplateComplexity(templateName),
//           );
//           recommendations.add(recommendation);
//         }
//       }

//       // 按评分排序
//       recommendations.sort((a, b) => b.score.compareTo(a.score));
      
//       return recommendations.take(5).toList();

//     } catch (e) {
//       cli_logger.Logger.error('生成模板推荐失败', error: e);
//       return [];
//     }
//   }

//   /// 获取用户体验报告
//   Map<String, dynamic> getUserExperienceReport() {
//     final totalInteractions = _interactionHistory.length;
//     if (totalInteractions == 0) {
//       return {
//         'summary': '暂无用户交互记录',
//         'recommendations': ['开始使用模板引擎以获得个性化体验建议'],
//       };
//     }

//     final successfulInteractions = _interactionHistory
//         .where((i) => i.result == 'success')
//         .length;
    
//     final averageDuration = _interactionHistory
//         .where((i) => i.duration != null)
//         .map((i) => i.duration!.inMilliseconds)
//         .fold(0, (a, b) => a + b) / totalInteractions;

//     final commonTemplates = <String, int>{};
//     for (final interaction in _interactionHistory) {
//       if (interaction.templateName != null) {
//         commonTemplates[interaction.templateName!] = 
//             (commonTemplates[interaction.templateName!] ?? 0) + 1;
//       }
//     }

//     final performanceReport = _metricsCollector.generateReport();

//     return {
//       'summary': {
//         'total_interactions': totalInteractions,
//         'success_rate': totalInteractions > 0 ? successfulInteractions / totalInteractions : 0.0,
//         'average_duration_ms': averageDuration,
//       },
//       'usage_patterns': {
//         'most_used_templates': commonTemplates.entries
//             .map((e) => {'template': e.key, 'usage_count': e.value})
//             .toList()
//           ..sort((a, b) => (b['usage_count']! as int).compareTo(a['usage_count']! as int)),
//         'peak_usage_hours': _analyzePeakUsageHours(),
//       },
//       'performance_metrics': performanceReport,
//       'recommendations': _generateUXRecommendations(),
//     };
//   }

//   /// 设置进度回调
//   void setProgressCallback(void Function(ProgressUpdate) callback) {
//     onProgressUpdate = callback;
//   }

//   // 私有方法

//   /// 更新进度
//   void _updateProgress(String message, double progress) {
//     final update = ProgressUpdate(
//       message: message,
//       progress: progress,
//       timestamp: DateTime.now(),
//     );
    
//     onProgressUpdate?.call(update);
//     cli_logger.Logger.info('进度更新: $message (${(progress * 100).toStringAsFixed(1)}%)');
//   }

//   /// 验证并提供反馈
//   Future<TemplateValidationResult> _validateWithFeedback(
//     String templateName,
//     Map<String, dynamic> variables,
//   ) async {
//     try {
//       // 1. 检查模板存在性
//       if (!templateEngine.isTemplateAvailable(templateName)) {
//         final suggestions = await templateEngine.errorRecoveryManager
//             .tryRecover(TemplateEngineException.templateNotFound(templateName));
        
//         return TemplateValidationResult(
//           success: false,
//           message: suggestions.success 
//               ? '模板不存在，建议使用: ${suggestions.message}'
//               : '模板不存在: $templateName',
//         );
//       }

//       // 2. 验证变量
//       final variableErrors = templateEngine.validateTemplateVariables(
//         templateName: templateName,
//         variables: variables,
//       );
      
//       if (variableErrors.isNotEmpty) {
//         final errorMessages = variableErrors.entries
//             .map((e) => '${e.key}: ${e.value}')
//             .join(', ');
        
//         return TemplateValidationResult(
//           success: false,
//           message: '变量验证失败: $errorMessages',
//           suggestions: await _generateVariableFixSuggestions(variableErrors),
//         );
//       }

//       return const TemplateValidationResult(success: true);

//     } catch (e) {
//       return TemplateValidationResult(
//         success: false,
//         message: '验证过程异常: $e',
//       );
//     }
//   }

//   /// 智能变量处理
//   Future<Map<String, dynamic>> _intelligentVariableProcessing(
//     String templateName,
//     Map<String, dynamic> variables,
//   ) async {
//     try {
//       // 1. 基础预处理
//       var processed = templateEngine.preprocessVariables(variables);

//       // 2. 智能补全缺失变量
//       final templateInfo = await templateEngine.getTemplateInfo(templateName);
//       if (templateInfo != null && templateInfo.containsKey('vars')) {
//         final templateVars = Map<String, dynamic>.from(templateInfo['vars'] as Map? ?? {});
        
//         for (final entry in templateVars.entries) {
//           final varName = entry.key;
//           final varConfig = Map<String, dynamic>.from(entry.value as Map? ?? {});
          
//           if (!processed.containsKey(varName) && varConfig.containsKey('default')) {
//             processed[varName] = varConfig['default'];
//             cli_logger.Logger.debug('自动补全变量 $varName: ${varConfig['default']}');
//           }
//         }
//       }

//       // 3. 智能类型转换
//       processed = await _smartTypeConversion(processed);

//       return processed;

//     } catch (e) {
//       cli_logger.Logger.warning('智能变量处理失败，使用原始变量: $e');
//       return templateEngine.preprocessVariables(variables);
//     }
//   }

//   /// 智能类型转换
//   Future<Map<String, dynamic>> _smartTypeConversion(Map<String, dynamic> variables) async {
//     final converted = <String, dynamic>{};

//     for (final entry in variables.entries) {
//       final key = entry.key;
//       final value = entry.value;

//       if (value is String) {
//         // 尝试智能转换字符串
//         if (value.toLowerCase() == 'true' || value.toLowerCase() == 'false') {
//           converted[key] = value.toLowerCase() == 'true';
//         } else if (RegExp(r'^\d+$').hasMatch(value)) {
//           converted[key] = int.tryParse(value) ?? value;
//         } else if (RegExp(r'^\d+\.\d+$').hasMatch(value)) {
//           converted[key] = double.tryParse(value) ?? value;
//         } else {
//           converted[key] = value;
//         }
//       } else {
//         converted[key] = value;
//       }
//     }

//     return converted;
//   }

//   /// 预估生成时间
//   Future<Duration> _estimateGenerationTime(String templateName) async {
//     try {
//       // 基于历史数据和模板复杂度估算
//       final complexity = await _estimateTemplateComplexity(templateName);
//       final baseTime = Duration(milliseconds: 100 + (complexity.index * 50));
      
//       return baseTime;
//         } catch (e) {
//       return const Duration(milliseconds: 150);
//     }
//   }

//   /// 后生成验证
//   Future<void> _postGenerationValidation(String outputPath) async {
//     try {
//       // 验证生成的文件结构
//       if (!Directory(outputPath).existsSync()) {
//         throw Exception('输出目录不存在');
//       }
      
//       // 基本的文件完整性检查
//       final files = await Directory(outputPath).list().toList();
//       cli_logger.Logger.debug('生成了 ${files.length} 个文件/目录');
      
//     } catch (e) {
//       cli_logger.Logger.warning('后生成验证失败: $e');
//     }
//   }

//   /// 收集性能指标
//   Future<void> _collectPerformanceMetrics(String templateName, Duration duration) async {
//     try {
//       _metricsCollector.recordGeneration(templateName, duration);
//     } catch (e) {
//       cli_logger.Logger.warning('收集性能指标失败: $e');
//     }
//   }

//   /// 增强成功结果
//   Future<GenerationResult> _enhanceSuccessResult(GenerationResult result, Duration duration) async {
//     try {
//       final enhancedMessage = '✅ 生成成功! 耗时: ${duration.inMilliseconds}ms';
//       return GenerationResult(
//         success: true,
//         outputPath: result.outputPath,
//         generatedFiles: result.generatedFiles,
//         message: enhancedMessage,
//         duration: duration,
//         metadata: {
//           ...result.metadata,
//           'enhanced': true,
//           'user_friendly': true,
//         },
//       );
//     } catch (e) {
//       return result; // 返回原始结果
//     }
//   }

//   /// 增强失败结果
//   Future<GenerationResult> _enhanceFailureResult(GenerationResult result) async {
//     try {
//       final enhancedMessage = '❌ ${result.message}\n💡 建议: 检查模板变量和输出路径';
//       return GenerationResult(
//         success: false,
//         outputPath: result.outputPath,
//         message: enhancedMessage,
//         metadata: {
//           ...result.metadata,
//           'enhanced': true,
//           'has_suggestions': true,
//         },
//       );
//     } catch (e) {
//       return result;
//     }
//   }

//   /// 计算推荐评分
//   Future<double> _calculateRecommendationScore(
//     String templateName,
//     String? context,
//     Map<String, dynamic>? userPreferences,
//   ) async {
//     try {
//       var score = 0.5; // 基础分数
      
//       // 基于使用历史
//       final usageCount = _interactionHistory
//           .where((i) => i.templateName == templateName)
//           .length;
//       score += (usageCount * 0.1).clamp(0.0, 0.3);
      
//       // 基于成功率
//       final successfulUsage = _interactionHistory
//           .where((i) => i.templateName == templateName && i.result == 'success')
//           .length;
//       if (usageCount > 0) {
//         final successRate = successfulUsage / usageCount;
//         score += successRate * 0.3;
//       }
      
//       // 基于上下文匹配
//       if (context != null && templateName.toLowerCase().contains(context.toLowerCase())) {
//         score += 0.2;
//       }
      
//       return score.clamp(0.0, 1.0);
//     } catch (e) {
//       return 0.5;
//     }
//   }

//   /// 生成推荐理由
//   Future<String> _generateRecommendationReason(String templateName, double score) async {
//     try {
//       if (score > 0.8) return '高度推荐: 使用频率高且成功率高';
//       if (score > 0.6) return '推荐: 适合当前需求';
//       if (score > 0.4) return '可选: 可能符合需求';
//       return '备选: 基础评分';
//     } catch (e) {
//       return '评分: ${score.toStringAsFixed(2)}';
//     }
//   }

//   /// 估算模板复杂度
//   Future<TemplateComplexity> _estimateTemplateComplexity(String templateName) async {
//     try {
//       final templateInfo = await templateEngine.getTemplateInfo(templateName);
//       if (templateInfo == null) return TemplateComplexity.low;
      
//       final varsCount = (templateInfo['vars'] as Map?)?.length ?? 0;
      
//       if (varsCount > 10) return TemplateComplexity.high;
//       if (varsCount > 5) return TemplateComplexity.medium;
//       return TemplateComplexity.low;
//     } catch (e) {
//       return TemplateComplexity.medium;
//     }
//   }

//   /// 生成变量修复建议
//   Future<List<String>> _generateVariableFixSuggestions(Map<String, String> errors) async {
//     final suggestions = <String>[];
    
//     for (final entry in errors.entries) {
//       final varName = entry.key;
//       final error = entry.value;
      
//       if (error.contains('空') || error.contains('empty')) {
//         suggestions.add('为 $varName 提供有效值');
//       } else if (error.contains('格式') || error.contains('format')) {
//         suggestions.add('检查 $varName 的格式要求');
//       } else {
//         suggestions.add('修复 $varName: $error');
//       }
//     }
    
//     return suggestions;
//   }

//   /// 分析高峰使用时间
//   List<int> _analyzePeakUsageHours() {
//     final hourCounts = <int, int>{};
    
//     for (final interaction in _interactionHistory) {
//       final hour = interaction.timestamp.hour;
//       hourCounts[hour] = (hourCounts[hour] ?? 0) + 1;
//     }
    
//     final sortedHours = hourCounts.entries.toList()
//       ..sort((a, b) => b.value.compareTo(a.value));
    
//     return sortedHours.take(3).map((e) => e.key).toList();
//   }

//   /// 生成用户体验建议
//   List<String> _generateUXRecommendations() {
//     final recommendations = <String>[];
    
//     if (_interactionHistory.isEmpty) {
//       recommendations.add('开始使用模板引擎以获得个性化建议');
//       return recommendations;
//     }
    
//     final recentErrors = _interactionHistory
//         .where((i) => i.result != 'success')
//         .take(5)
//         .toList();
    
//     if (recentErrors.length > 2) {
//       recommendations.add('最近错误较多，建议检查模板变量配置');
//     }
    
//     final avgDuration = _interactionHistory
//         .where((i) => i.duration != null)
//         .map((i) => i.duration!.inMilliseconds)
//         .fold(0, (a, b) => a + b) / _interactionHistory.length;
    
//     if (avgDuration > 1000) {
//       recommendations.add('生成时间较长，建议使用缓存预热功能');
//     }
    
//     return recommendations;
//   }
// }

// // ==================== 支持类和数据结构 ====================

// /// 进度更新数据结构
// class ProgressUpdate {
//   const ProgressUpdate({
//     required this.message,
//     required this.progress,
//     required this.timestamp,
//   });

//   final String message;
//   final double progress; // 0.0 到 1.0
//   final DateTime timestamp;
// }

// /// 用户交互记录
// class UserInteraction {
//   UserInteraction({
//     required this.action,
//     required this.timestamp, this.templateName,
//   });

//   final String action;
//   final String? templateName;
//   final DateTime timestamp;
//   String? result;
//   Duration? duration;
//   String? error;
// }

// /// 性能指标收集器
// class PerformanceMetricsCollector {
//   final List<GenerationMetric> _metrics = [];

//   /// 记录生成指标
//   void recordGeneration(String templateName, Duration duration) {
//     _metrics.add(GenerationMetric(
//       templateName: templateName,
//       duration: duration,
//       timestamp: DateTime.now(),
//     ),);
    
//     // 保持最近1000条记录
//     if (_metrics.length > 1000) {
//       _metrics.removeAt(0);
//     }
//   }

//   /// 生成性能报告
//   Map<String, dynamic> generateReport() {
//     if (_metrics.isEmpty) {
//       return {
//         'total_generations': 0,
//         'average_duration_ms': 0,
//         'fastest_generation_ms': 0,
//         'slowest_generation_ms': 0,
//       };
//     }

//     final durations = _metrics.map((m) => m.duration.inMilliseconds).toList();
//     durations.sort();

//     return {
//       'total_generations': _metrics.length,
//       'average_duration_ms': durations.fold(0, (a, b) => a + b) / durations.length,
//       'fastest_generation_ms': durations.first,
//       'slowest_generation_ms': durations.last,
//       'median_duration_ms': durations[durations.length ~/ 2],
//     };
//   }
// }

// /// 生成指标
// class GenerationMetric {
//   const GenerationMetric({
//     required this.templateName,
//     required this.duration,
//     required this.timestamp,
//   });

//   final String templateName;
//   final Duration duration;
//   final DateTime timestamp;
// }

// /// 模板推荐
// class TemplateRecommendation {
//   const TemplateRecommendation({
//     required this.templateName,
//     required this.score,
//     required this.reason,
//     required this.metadata,
//     required this.estimatedComplexity,
//   });

//   final String templateName;
//   final double score;
//   final String reason;
//   final Map<String, dynamic> metadata;
//   final TemplateComplexity estimatedComplexity;
// }

// /// 模板复杂度枚举
// enum TemplateComplexity { low, medium, high }

// /// 模板验证结果
// class TemplateValidationResult {
//   const TemplateValidationResult({
//     required this.success,
//     this.message,
//     this.suggestions,
//   });

//   final bool success;
//   final String? message;
//   final List<String>? suggestions;
// }

// // ==================== 集成到TemplateEngine ====================

// /// TemplateEngine的用户体验扩展
// extension TemplateEngineUXExtension on TemplateEngine {
//   /// 智能错误恢复管理器（延迟初始化）
//   static final Map<TemplateEngine, IntelligentErrorRecoveryManager> _intelligentManagers = {};
  
//   /// 用户体验管理器（延迟初始化）
//   static final Map<TemplateEngine, UserExperienceManager> _uxManagers = {};

//   /// 获取智能错误恢复管理器
//   IntelligentErrorRecoveryManager get intelligentErrorRecoveryManager {
//     return _intelligentManagers.putIfAbsent(this, () => IntelligentErrorRecoveryManager(this));
//   }

//   /// 获取用户体验管理器
//   UserExperienceManager get userExperienceManager {
//     return _uxManagers.putIfAbsent(this, () => UserExperienceManager(this));
//   }

//   /// Task 36.3: 使用智能错误恢复的增强生成方法
//   Future<GenerationResult> generateWithIntelligentRecovery({
//     required String templateName,
//     required String outputPath,
//     required Map<String, dynamic> variables,
//     bool overwrite = false,
//     List<TemplateHook>? hooks,
//     TemplateInheritance? inheritance,
//   }) async {
//     try {
//       final result = await generateWithHooks(
//         templateName: templateName,
//         outputPath: outputPath,
//         variables: variables,
//         overwrite: overwrite,
//         additionalHooks: hooks,
//         inheritance: inheritance,
//       );

//       if (!result.success) {
//         // 尝试智能错误恢复
//         final error = TemplateEngineException(
//           type: TemplateEngineErrorType.unknown,
//           message: result.message ?? '生成失败',
//         );
        
//         final recoveryResult = await intelligentErrorRecoveryManager.intelligentRecover(
//           error,
//           HookContext(
//             templateName: templateName,
//             outputPath: outputPath,
//             variables: variables,
//           ),
//         );

//         if (recoveryResult.success) {
//           cli_logger.Logger.info('智能恢复成功，重新尝试生成...');
//           // 使用恢复后的信息重试
//           return await generateWithHooks(
//             templateName: templateName,
//             outputPath: outputPath,
//             variables: variables,
//             overwrite: overwrite,
//             additionalHooks: hooks,
//             inheritance: inheritance,
//           );
//         }
//       }

//       return result;
//     } catch (e) {
//       return GenerationResult.failure(
//         '智能恢复生成失败: $e',
//         outputPath: outputPath,
//       );
//     }
//   }

//   /// Task 36.4: 使用用户体验优化的生成方法
//   Future<GenerationResult> generateWithOptimizedUX({
//     required String templateName,
//     required String outputPath,
//     required Map<String, dynamic> variables,
//     bool overwrite = false,
//     List<TemplateHook>? hooks,
//     TemplateInheritance? inheritance,
//     void Function(ProgressUpdate)? onProgress,
//   }) async {
//     if (onProgress != null) {
//       userExperienceManager.setProgressCallback(onProgress);
//     }

//     return userExperienceManager.generateWithEnhancedUX(
//       templateName: templateName,
//       outputPath: outputPath,
//       variables: variables,
//       overwrite: overwrite,
//       hooks: hooks,
//       inheritance: inheritance,
//     );
//   }

//   /// 获取智能模板推荐
//   Future<List<TemplateRecommendation>> getTemplateRecommendations({
//     String? context,
//     Map<String, dynamic>? userPreferences,
//   }) async {
//     return userExperienceManager.getIntelligentRecommendations(
//       context: context,
//       userPreferences: userPreferences,
//     );
//   }

//   /// 获取错误恢复统计
//   Map<String, dynamic> getErrorRecoveryStatistics() {
//     return intelligentErrorRecoveryManager.getRecoveryStatistics();
//   }

//   /// 获取用户体验报告
//   Map<String, dynamic> getUserExperienceReport() {
//     return userExperienceManager.getUserExperienceReport();
//   }

//   /// 清理智能管理器资源
//   void cleanupIntelligentManagers() {
//     intelligentErrorRecoveryManager.cleanupHistory();
//     _intelligentManagers.remove(this);
//     _uxManagers.remove(this);
//   }
// }

// /// Task 36完成总结方法
// extension TemplateEngineTask36Summary on TemplateEngine {
//   /// 获取Task 36完整功能报告
//   Future<Map<String, dynamic>> getTask36CompleteReport() async {
//     return {
//       'task_36_1_cache_optimization': {
//         'status': 'completed',
//         'cache_stats': cacheManager.getCacheStatistics(),
//         'features': [
//           '高级模板缓存管理',
//           '智能预编译系统',
//           '缓存预热机制',
//           '缓存过期管理',
//         ],
//       },
//       'task_36_2_async_generation': {
//         'status': 'completed',
//         'async_stats': asyncManager.getGenerationStatistics(),
//         'features': [
//           '异步模板生成',
//           '并发任务队列管理',
//           '任务优先级控制',
//           '流式生成处理',
//         ],
//       },
//       'task_36_3_error_recovery': {
//         'status': 'completed',
//         'recovery_stats': getErrorRecoveryStatistics(),
//         'features': [
//           '智能错误模式分析',
//           '自适应恢复策略',
//           '错误预防机制',
//           '恢复历史追踪',
//         ],
//       },
//       'task_36_4_ux_optimization': {
//         'status': 'completed',
//         'ux_report': getUserExperienceReport(),
//         'features': [
//           '实时进度反馈',
//           '智能模板推荐',
//           '用户交互分析',
//           '性能指标收集',
//         ],
//       },
//       'integration': {
//         'all_features_integrated': true,
//         'backward_compatible': true,
//         'performance_impact': 'minimal',
//         'total_line_count': 5800,
//         'implementation_date': DateTime.now().toIso8601String(),
//       },
//       'summary': {
//         'total_tasks': 4,
//         'completed_tasks': 4,
//         'completion_rate': 1.0,
//         'key_achievements': [
//           '实现了完整的模板缓存优化系统',
//           '建立了强大的异步生成和并发处理能力',
//           '部署了智能错误恢复机制',
//           '优化了用户体验和反馈系统',
//         ],
//       },
//     };
//   }
// }
