/*
---------------------------------------------------------------
File name:          module_package.dart
Author:             lgnorant-lu
Date created:       2025-07-24
Last modified:      2025-07-24
Dart Version:       3.2+
Description:        模块包模型
---------------------------------------------------------------
Change History:
    2025-07-24: Phase ******* - 实现模块包模型;
---------------------------------------------------------------
*/

import 'package:equatable/equatable.dart';

/// 模块包信息
class ModulePackage extends Equatable {
  /// 模块ID
  final String id;

  /// 模块名称
  final String name;

  /// 模块版本
  final String version;

  /// 模块描述
  final String description;

  /// 模块作者
  final String author;

  /// 模块类型
  final ModuleType type;

  /// 模块依赖
  final List<ModuleDependency> dependencies;

  /// 模块权限
  final List<ModulePackagePermission> permissions;

  /// 模块大小（字节）
  final int size;

  /// 安装路径
  final String? installPath;

  /// 创建时间
  final DateTime createdAt;

  /// 更新时间
  final DateTime updatedAt;

  const ModulePackage({
    required this.id,
    required this.name,
    required this.version,
    required this.description,
    required this.author,
    required this.type,
    this.dependencies = const [],
    this.permissions = const [],
    required this.size,
    this.installPath,
    required this.createdAt,
    required this.updatedAt,
  });

  /// 创建模块包副本
  ModulePackage copyWith({
    String? id,
    String? name,
    String? version,
    String? description,
    String? author,
    ModuleType? type,
    List<ModuleDependency>? dependencies,
    List<ModulePackagePermission>? permissions,
    int? size,
    String? installPath,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ModulePackage(
      id: id ?? this.id,
      name: name ?? this.name,
      version: version ?? this.version,
      description: description ?? this.description,
      author: author ?? this.author,
      type: type ?? this.type,
      dependencies: dependencies ?? this.dependencies,
      permissions: permissions ?? this.permissions,
      size: size ?? this.size,
      installPath: installPath ?? this.installPath,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// 从 JSON 创建
  factory ModulePackage.fromJson(Map<String, dynamic> json) {
    return ModulePackage(
      id: json['id'] as String,
      name: json['name'] as String,
      version: json['version'] as String,
      description: json['description'] as String,
      author: json['author'] as String,
      type: ModuleType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ModuleType.plugin,
      ),
      dependencies: (json['dependencies'] as List<dynamic>?)
              ?.map((e) => ModuleDependency.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      permissions: (json['permissions'] as List<dynamic>?)
              ?.map((e) => ModulePackagePermission.values.firstWhere(
                    (p) => p.name == e,
                    orElse: () => ModulePackagePermission.basic,
                  ))
              .toList() ??
          [],
      size: json['size'] as int,
      installPath: json['installPath'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'version': version,
      'description': description,
      'author': author,
      'type': type.name,
      'dependencies': dependencies.map((e) => e.toJson()).toList(),
      'permissions': permissions.map((e) => e.name).toList(),
      'size': size,
      'installPath': installPath,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        version,
        description,
        author,
        type,
        dependencies,
        permissions,
        size,
        installPath,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'ModulePackage(id: $id, name: $name, version: $version)';
  }
}

/// 模块依赖
class ModuleDependency extends Equatable {
  /// 依赖模块ID
  final String moduleId;

  /// 版本约束
  final String versionConstraint;

  /// 是否为可选依赖
  final bool isOptional;

  const ModuleDependency({
    required this.moduleId,
    required this.versionConstraint,
    this.isOptional = false,
  });

  /// 检查版本是否兼容
  bool isVersionCompatible(String version) {
    // 简单的版本兼容性检查
    // TODO: 实现更复杂的语义化版本检查
    return versionConstraint == version || versionConstraint == '*';
  }

  /// 从 JSON 创建
  factory ModuleDependency.fromJson(Map<String, dynamic> json) {
    return ModuleDependency(
      moduleId: json['moduleId'] as String,
      versionConstraint: json['versionConstraint'] as String,
      isOptional: json['isOptional'] as bool? ?? false,
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'moduleId': moduleId,
      'versionConstraint': versionConstraint,
      'isOptional': isOptional,
    };
  }

  @override
  List<Object?> get props => [moduleId, versionConstraint, isOptional];

  @override
  String toString() {
    return 'ModuleDependency(moduleId: $moduleId, versionConstraint: $versionConstraint)';
  }
}

/// 模块类型
enum ModuleType {
  /// 插件
  plugin,

  /// 主题
  theme,

  /// 工具
  tool,

  /// 服务
  service,

  /// 扩展
  extension,
}

/// 模块包权限
enum ModulePackagePermission {
  /// 基础权限
  basic,

  /// 文件系统访问
  fileSystem,

  /// 网络访问
  network,

  /// 系统信息访问
  systemInfo,

  /// 用户数据访问
  userData,

  /// 管理员权限
  admin,
}
