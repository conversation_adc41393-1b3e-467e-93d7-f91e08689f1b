name: communication_system
description: 跨模块通信系统 - 统一消息总线、事件路由、数据同步
version: 1.0.0

environment:
  sdk: ">=3.2.0 <4.0.0"
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter

  # 状态管理
  flutter_riverpod: ^2.6.1
  riverpod_annotation: ^2.6.1
  provider: ^6.1.2

  # 路由管理
  go_router: ^14.8.1

  # 数据序列化
  freezed_annotation: ^2.4.4
  json_annotation: ^4.8.1
  equatable: ^2.0.5

  # 网络请求
  dio: ^5.4.0

  # 本地存储
  shared_preferences: ^2.2.2

  # 工具库
  intl: ^0.20.2
  device_info_plus: ^9.1.1
  package_info_plus: ^8.3.0
  connectivity_plus: ^5.0.2
  battery_plus: ^5.0.2


dev_dependencies:
  flutter_test:
    sdk: flutter

  # 代码生成
  build_runner: ^2.4.7
  freezed: ^2.5.7
  json_serializable: ^6.9.0
  riverpod_generator: ^2.6.3

  # 资源生成
  flutter_gen_runner: ^5.4.0

  # 测试
  mockito: ^5.4.4
  build_verify: ^3.1.0

  # 代码质量
  flutter_lints: ^4.0.0
  very_good_analysis: ^6.0.0

  # 工具
  flutter_launcher_icons: ^0.14.1
  flutter_native_splash: ^2.4.1

flutter:
  uses-material-design: true
  generate: true

  # 资源文件
  assets:
    # - assets/images/
    # - assets/icons/
    # - assets/colors/
    # - assets/fonts/
    # l10n 文件由 flutter gen-l10n 自动处理，不需要在这里声明

  # 字体配置
  # fonts:
  #   - family: CustomFont
  #     fonts:
  #       - asset: assets/fonts/CustomFont-Regular.ttf
  #       - asset: assets/fonts/CustomFont-Bold.ttf
  #         weight: 700

# 国际化配置
flutter_intl:
  enabled: true
  class_name: S
  main_locale: en
  arb_dir: l10n
  output_dir: lib/generated/l10n
