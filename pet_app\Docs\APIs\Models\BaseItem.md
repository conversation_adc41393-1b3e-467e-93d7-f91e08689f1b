# BaseItem API 文档

## 概述

BaseItem是桌宠应用所有业务实体的基础抽象类，定义了统一的数据模型架构、序列化机制、验证规则和查询构建能力。它为Repository模式提供了类型安全的数据契约，支持10种业务实体类型和6种状态管理。

- **主要功能**: 统一数据模型、序列化支持、数据验证、查询构建、内容哈希
- **设计模式**: 模板方法模式 + 工厂方法模式 + 构建者模式
- **支持类型**: 笔记、待办事项、项目、提醒、打卡记录、创意想法、模板、标签、附件、未知类型
- **位置**: `lib/core/models/base_item.dart`

## 核心枚举定义

### ItemType 业务实体类型
```dart
enum ItemType {
  note('note', '笔记'),
  todo('todo', '待办事项'),
  project('project', '项目'),
  reminder('reminder', '提醒'),
  punchRecord('punch_record', '打卡记录'),
  idea('idea', '创意想法'),
  template('template', '模板'),
  tag('tag', '标签'),
  attachment('attachment', '附件'),
  unknown('unknown', '未知类型');

  const ItemType(this.value, this.displayName);
  
  final String value;
  final String displayName;
  
  /// 从字符串值获取枚举
  static ItemType fromValue(String value);
}
```

### ItemStatus 实体状态
```dart
enum ItemStatus {
  active('active', '活跃'),
  inactive('inactive', '非活跃'),
  completed('completed', '已完成'),
  archived('archived', '已归档'),
  deleted('deleted', '已删除'),
  draft('draft', '草稿');

  const ItemStatus(this.value, this.displayName);
  
  final String value;
  final String displayName;
  
  /// 从字符串值获取枚举
  static ItemStatus fromValue(String value);
}
```

## 核心抽象类

### BaseItem 基础实体类
```dart
abstract class BaseItem {
  BaseItem({
    String? id,
    required this.title,
    required this.itemType,
    this.description = '',
    this.status = ItemStatus.active,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.metadata = const <String, dynamic>{},
    this.tags = const <String>[],
  });

  final String id;                          // 唯一标识符
  final String title;                       // 标题（必填）
  final ItemType itemType;                  // 实体类型
  final String description;                 // 描述信息
  final ItemStatus status;                  // 实体状态
  final DateTime createdAt;                 // 创建时间
  final DateTime updatedAt;                 // 最后更新时间
  final Map<String, dynamic> metadata;      // 扩展元数据
  final List<String> tags;                  // 标签列表

  // 抽象方法 - 子类必须实现
  BaseItem copyWith({
    String? title,
    String? description,
    ItemStatus? status,
    Map<String, dynamic>? metadata,
    List<String>? tags,
  });

  // 具体方法实现
  String get contentHash;                   // 内容哈希值
  bool get isValid;                         // 检查实体是否有效
  bool get isActive;                        // 检查是否为活跃状态
  bool get isCompleted;                     // 检查是否已完成
  bool get isArchived;                      // 检查是否已归档
  bool get isDeleted;                       // 检查是否已删除
}
```

## 工厂方法

### ID生成
```dart
/// 生成基于时间戳和随机数的唯一ID
static String _generateId() {
  final timestamp = DateTime.now().millisecondsSinceEpoch;
  final random = (timestamp * 1000 + DateTime.now().microsecond) % 999999;
  return 'item_${timestamp}_$random';
}
```

## 序列化支持

### 数据转换
```dart
/// 转换为Map格式（用于序列化）
Map<String, dynamic> toMap() {
  return {
    'id': id,
    'title': title,
    'itemType': itemType.value,
    'description': description,
    'status': status.value,
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
    'metadata': Map<String, dynamic>.from(metadata),
    'tags': List<String>.from(tags),
    'contentHash': contentHash,
  };
}

/// 转换为JSON字符串
String toJson() => json.encode(toMap());
```

## 查询构建器

### QueryBuilder 类定义
```dart
class QueryBuilder {
  QueryBuilder();

  final Map<String, dynamic> _conditions = {};
  final List<String> _tags = [];
  ItemStatus? _status;
  ItemType? _itemType;
  DateTime? _createdAfter;
  DateTime? _createdBefore;
  DateTime? _updatedAfter;
  DateTime? _updatedBefore;

  /// 按标题搜索（支持模糊匹配）
  QueryBuilder whereTitle(String title);

  /// 按描述搜索（支持模糊匹配）
  QueryBuilder whereDescription(String description);

  /// 按状态筛选
  QueryBuilder whereStatus(ItemStatus status);

  /// 按类型筛选
  QueryBuilder whereType(ItemType itemType);

  /// 按标签筛选
  QueryBuilder whereTag(String tag);

  /// 按创建时间范围筛选
  QueryBuilder whereCreatedBetween(DateTime after, DateTime before);

  /// 按更新时间范围筛选
  QueryBuilder whereUpdatedBetween(DateTime after, DateTime before);

  /// 生成查询条件Map
  Map<String, dynamic> build();
}
```

## 内容完整性

### 哈希计算
```dart
/// 生成内容哈希，用于检测数据变更
String get contentHash {
  final content = '$title|$description|${metadata.toString()}|${tags.join(',')}';
  final bytes = utf8.encode(content);
  final digest = sha256.convert(bytes);
  return digest.toString().substring(0, 8); // 取前8位作为短哈希
}
```

### 数据验证
```dart
/// 检查实体是否有效
bool get isValid {
  return id.isNotEmpty && 
         title.trim().isNotEmpty && 
         createdAt.isBefore(DateTime.now().add(const Duration(minutes: 1))) && 
         updatedAt.isAfter(createdAt.subtract(const Duration(seconds: 1)));
}
```

## 状态检查方法

### 便捷状态检查
```dart
/// 检查是否为活跃状态
bool get isActive => status == ItemStatus.active;

/// 检查是否已完成
bool get isCompleted => status == ItemStatus.completed;

/// 检查是否已归档
bool get isArchived => status == ItemStatus.archived;

/// 检查是否已删除
bool get isDeleted => status == ItemStatus.deleted;
```

## 实现子类指南

### 子类实现要求
```dart
// 子类必须实现的方法
static BaseItem fromMap(Map<String, dynamic> map) {
  throw UnimplementedError('子类必须实现 fromMap 方法');
}

static BaseItem fromJson(String jsonStr) {
  throw UnimplementedError('子类必须实现 fromJson 方法');
}
```

## 使用示例

### 基本用法
```dart
// 创建实体
final noteItem = MyNoteItem(
  title: '重要笔记',
  itemType: ItemType.note,
  description: '这是一个重要的笔记内容',
  status: ItemStatus.active,
  tags: ['重要', '工作'],
  metadata: {'priority': 'high'},
);

// 序列化
final json = noteItem.toJson();
final map = noteItem.toMap();

// 状态检查
if (noteItem.isActive && !noteItem.isDeleted) {
  print('笔记处于活跃状态');
}

// 内容完整性检查
final hash = noteItem.contentHash;
if (noteItem.isValid) {
  print('数据有效，哈希值: $hash');
}
```

### 查询构建
```dart
// 构建复杂查询
final queryBuilder = QueryBuilder()
  .whereType(ItemType.note)
  .whereStatus(ItemStatus.active)
  .whereTag('重要')
  .whereCreatedBetween(
    DateTime.now().subtract(Duration(days: 7)),
    DateTime.now(),
  );

final conditions = queryBuilder.build();
```

## 版本历史

- **v1.0.0** (2025-06-25): Phase 1初始实现
  - 基础实体定义和枚举
  - 序列化和反序列化支持
  - 内容哈希和数据验证
  - QueryBuilder查询构建器
  - 10种业务实体类型支持 