/*
---------------------------------------------------------------
File name:          installation_result.dart
Author:             lgnorant-lu
Date created:       2025-07-24
Last modified:      2025-07-24
Dart Version:       3.2+
Description:        安装结果模型
---------------------------------------------------------------
Change History:
    2025-07-24: Phase ******* - 实现安装结果模型;
*/

import 'package:equatable/equatable.dart';

/// 安装结果
class InstallationResult extends Equatable {
  /// 模块ID
  final String moduleId;
  
  /// 是否成功
  final bool isSuccess;
  
  /// 结果消息
  final String message;
  
  /// 错误类型（失败时）
  final InstallationError? error;
  
  /// 安装时间
  final DateTime timestamp;

  const InstallationResult._({
    required this.moduleId,
    required this.isSuccess,
    required this.message,
    this.error,
    required this.timestamp,
  });

  /// 创建成功结果
  factory InstallationResult.success(String moduleId, String message) {
    return InstallationResult._(
      moduleId: moduleId,
      isSuccess: true,
      message: message,
      error: null,
      timestamp: DateTime.now(),
    );
  }

  /// 创建失败结果
  factory InstallationResult.failure(
    String moduleId,
    String message,
    InstallationError error,
  ) {
    return InstallationResult._(
      moduleId: moduleId,
      isSuccess: false,
      message: message,
      error: error,
      timestamp: DateTime.now(),
    );
  }

  /// 从 JSON 创建
  factory InstallationResult.fromJson(Map<String, dynamic> json) {
    return InstallationResult._(
      moduleId: json['moduleId'] as String,
      isSuccess: json['isSuccess'] as bool,
      message: json['message'] as String,
      error: json['error'] != null
          ? InstallationError.values.firstWhere(
              (e) => e.name == json['error'],
              orElse: () => InstallationError.unknown,
            )
          : null,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'moduleId': moduleId,
      'isSuccess': isSuccess,
      'message': message,
      'error': error?.name,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  @override
  List<Object?> get props => [moduleId, isSuccess, message, error, timestamp];

  @override
  String toString() {
    return 'InstallationResult(moduleId: $moduleId, isSuccess: $isSuccess, message: $message)';
  }
}

/// 安装错误类型
enum InstallationError {
  /// 模块已安装
  alreadyInstalled,
  
  /// 模块未找到
  moduleNotFound,
  
  /// 无效的模块包
  invalidPackage,
  
  /// 依赖冲突
  dependencyConflict,
  
  /// 权限不足
  insufficientPermissions,
  
  /// 磁盘空间不足
  insufficientSpace,
  
  /// 网络错误
  networkError,
  
  /// 安装失败
  installationFailed,
  
  /// 卸载失败
  uninstallationFailed,
  
  /// 更新失败
  updateFailed,
  
  /// 已是最新版本
  alreadyLatestVersion,
  
  /// 未知错误
  unknown,
}

/// 安装错误扩展
extension InstallationErrorExtension on InstallationError {
  /// 获取错误描述
  String get description {
    switch (this) {
      case InstallationError.alreadyInstalled:
        return '模块已安装';
      case InstallationError.moduleNotFound:
        return '模块未找到';
      case InstallationError.invalidPackage:
        return '无效的模块包';
      case InstallationError.dependencyConflict:
        return '依赖冲突';
      case InstallationError.insufficientPermissions:
        return '权限不足';
      case InstallationError.insufficientSpace:
        return '磁盘空间不足';
      case InstallationError.networkError:
        return '网络错误';
      case InstallationError.installationFailed:
        return '安装失败';
      case InstallationError.uninstallationFailed:
        return '卸载失败';
      case InstallationError.updateFailed:
        return '更新失败';
      case InstallationError.alreadyLatestVersion:
        return '已是最新版本';
      case InstallationError.unknown:
        return '未知错误';
    }
  }

  /// 是否为可恢复错误
  bool get isRecoverable {
    switch (this) {
      case InstallationError.networkError:
      case InstallationError.insufficientSpace:
        return true;
      case InstallationError.alreadyInstalled:
      case InstallationError.moduleNotFound:
      case InstallationError.invalidPackage:
      case InstallationError.dependencyConflict:
      case InstallationError.insufficientPermissions:
      case InstallationError.installationFailed:
      case InstallationError.uninstallationFailed:
      case InstallationError.updateFailed:
      case InstallationError.alreadyLatestVersion:
      case InstallationError.unknown:
        return false;
    }
  }
}
