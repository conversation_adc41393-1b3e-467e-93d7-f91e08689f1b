/// Generated file. Do not edit.
///
/// To regenerate, run: `flutter gen-l10n`
///
/// Project: core_services_v1
/// Generated: 2025-07-14

// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Korean (`ko`).
class AppLocalizationsKo extends AppLocalizations {
  AppLocalizationsKo([String locale = 'ko']) : super(locale);

  @override
  String get appTitle => '내 앱';

  @override
  String get welcome => '환영합니다';

  @override
  String hello(String name) {
    return '안녕하세요, $name님！';
  }

  @override
  String get settings => '설정';

  @override
  String get about => '정보';

  @override
  String get ok => '확인';

  @override
  String get cancel => '취소';

  @override
  String get home => '홈';

  @override
  String get profile => '프로필';

  @override
  String get notifications => '알림';

  @override
  String get search => '검색';

  @override
  String get loading => '로딩 중...';

  @override
  String get error => '오류';

  @override
  String get retry => '다시 시도';

  @override
  String get save => '저장';

  @override
  String get delete => '삭제';

  @override
  String get edit => '편집';

  @override
  String itemCount(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$countString개 항목',
      one: '1개 항목',
      zero: '항목 없음',
    );
    return '$_temp0';
  }

  @override
  String get login => '로그인';

  @override
  String get logout => '로그아웃';

  @override
  String get register => '회원가입';

  @override
  String get forgotPassword => '비밀번호 찾기';

  @override
  String get email => '이메일';

  @override
  String get password => '비밀번호';

  @override
  String get confirmPassword => '비밀번호 확인';

  @override
  String get firstName => '이름';

  @override
  String get lastName => '성';

  @override
  String get phoneNumber => '전화번호';

  @override
  String get address => '주소';

  @override
  String get city => '도시';

  @override
  String get country => '국가';

  @override
  String get language => '언어';

  @override
  String get theme => '테마';

  @override
  String get darkMode => '다크 모드';

  @override
  String get lightMode => '라이트 모드';

  @override
  String get systemMode => '시스템 설정';

  @override
  String lastUpdated(DateTime date) {
    final intl.DateFormat dateDateFormat = intl.DateFormat.yMd(localeName);
    final String dateString = dateDateFormat.format(date);

    return '마지막 업데이트: $dateString';
  }

  @override
  String get version => '버전';

  @override
  String get privacyPolicy => '개인정보 처리방침';

  @override
  String get termsOfService => '서비스 약관';

  @override
  String get contactUs => '문의하기';

  @override
  String get feedback => '피드백';

  @override
  String get rateApp => '앱 평가';

  @override
  String get shareApp => '앱 공유';

  @override
  String get appName => 'core_services_v1';

  @override
  String get appDescription => 'Flutter로 구축된 현대적인 애플리케이션';
}
