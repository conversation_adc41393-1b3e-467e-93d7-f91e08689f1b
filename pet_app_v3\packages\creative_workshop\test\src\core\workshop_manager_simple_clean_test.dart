import 'package:test/test.dart';
import 'package:creative_workshop/creative_workshop.dart';

void main() {
  group('WorkshopManager简单清理测试', () {
    test('应该能够获取WorkshopManager实例', () {
      final workshopManager = WorkshopManager.instance;
      expect(workshopManager, isNotNull);
    });

    test('应该有正确的初始状态', () {
      final workshopManager = WorkshopManager.instance;
      expect(workshopManager.state, equals(WorkshopState.uninitialized));
    });

    test('应该能够初始化', () async {
      final workshopManager = WorkshopManager.instance;
      final result = await workshopManager.initialize();
      expect(result, isTrue);
      expect(workshopManager.state, equals(WorkshopState.ready));
    });

    test('应该能够启动', () async {
      final workshopManager = WorkshopManager.instance;
      await workshopManager.initialize();
      
      final result = await workshopManager.start();
      expect(result, isTrue);
      expect(workshopManager.state, equals(WorkshopState.running));
    });

    test('验证职责边界清理', () {
      final workshopManager = WorkshopManager.instance;
      
      // 验证项目管理功能存在
      expect(workshopManager.projectManager, isNotNull);
      expect(workshopManager.projectCount, isA<int>());
      
      // 验证状态管理功能存在
      expect(workshopManager.state, isA<WorkshopState>());
      expect(workshopManager.stateChanges, isA<Stream<WorkshopState>>());
    });
  });
}
