/*
---------------------------------------------------------------
File name:          module_permission_manager.dart
Author:             lgnorant-lu
Date created:       2025-07-24
Last modified:      2025-07-24
Dart Version:       3.2+
Description:        模块权限管理器
---------------------------------------------------------------
Change History:
    2025-07-24: Phase ******* - 实现模块权限管理器;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:developer' as developer;
import '../models/module_permission.dart';

/// 模块权限管理器
///
/// 负责模块权限的授予、撤销、审计等管理
class ModulePermissionManager {
  static ModulePermissionManager? _instance;
  static ModulePermissionManager get instance =>
      _instance ??= ModulePermissionManager._();

  ModulePermissionManager._();

  final Map<String, Set<ModulePermission>> _grantedPermissions = {};
  final Map<ModulePermission, PermissionPolicy> _permissionPolicies = {};
  final List<PermissionAuditLog> _auditLogs = [];
  final StreamController<PermissionEvent> _eventController =
      StreamController.broadcast();

  /// 权限事件流
  Stream<PermissionEvent> get permissionEvents => _eventController.stream;

  /// 初始化权限管理器
  Future<void> initialize() async {
    _log('info', '初始化模块权限管理器');

    // 设置默认权限策略
    _initializeDefaultPolicies();

    _log('info', '模块权限管理器初始化完成');
  }

  /// 授予权限
  Future<PermissionOperationResult> grantPermission(
    String moduleId,
    ModulePermission permission, {
    String? reason,
    Duration? duration,
  }) async {
    try {
      _log('info', '授予权限: $moduleId -> $permission');

      // 检查权限策略
      final policy = _permissionPolicies[permission] ?? PermissionPolicy.ask;
      if (policy == PermissionPolicy.deny) {
        return PermissionOperationResult.failure(
          moduleId,
          permission,
          '权限策略禁止授予此权限',
          PermissionOperationError.policyDenied,
        );
      }

      // 授予权限
      _grantedPermissions.putIfAbsent(moduleId, () => {}).add(permission);

      // 记录审计日志
      final auditLog = PermissionAuditLog(
        moduleId: moduleId,
        permission: permission,
        action: PermissionAction.granted,
        reason: reason,
        timestamp: DateTime.now(),
        duration: duration,
      );
      _auditLogs.add(auditLog);

      // 发送事件
      _eventController.add(PermissionEvent.granted(moduleId, permission));

      _log('info', '权限授予成功: $moduleId -> $permission');
      return PermissionOperationResult.success(moduleId, permission, '权限授予成功');
    } catch (e, stackTrace) {
      _log('severe', '权限授予失败: $moduleId -> $permission', e, stackTrace);
      return PermissionOperationResult.failure(
        moduleId,
        permission,
        '授予过程中发生错误: $e',
        PermissionOperationError.operationFailed,
      );
    }
  }

  /// 撤销权限
  Future<PermissionOperationResult> revokePermission(
    String moduleId,
    ModulePermission permission, {
    String? reason,
  }) async {
    try {
      _log('info', '撤销权限: $moduleId -> $permission');

      final modulePermissions = _grantedPermissions[moduleId];
      if (modulePermissions == null ||
          !modulePermissions.contains(permission)) {
        return PermissionOperationResult.failure(
          moduleId,
          permission,
          '模块未拥有此权限',
          PermissionOperationError.permissionNotFound,
        );
      }

      // 撤销权限
      modulePermissions.remove(permission);
      if (modulePermissions.isEmpty) {
        _grantedPermissions.remove(moduleId);
      }

      // 记录审计日志
      final auditLog = PermissionAuditLog(
        moduleId: moduleId,
        permission: permission,
        action: PermissionAction.revoked,
        reason: reason,
        timestamp: DateTime.now(),
      );
      _auditLogs.add(auditLog);

      // 发送事件
      _eventController.add(PermissionEvent.revoked(moduleId, permission));

      _log('info', '权限撤销成功: $moduleId -> $permission');
      return PermissionOperationResult.success(moduleId, permission, '权限撤销成功');
    } catch (e, stackTrace) {
      _log('severe', '权限撤销失败: $moduleId -> $permission', e, stackTrace);
      return PermissionOperationResult.failure(
        moduleId,
        permission,
        '撤销过程中发生错误: $e',
        PermissionOperationError.operationFailed,
      );
    }
  }

  /// 检查权限
  bool hasPermission(String moduleId, ModulePermission permission) {
    final modulePermissions = _grantedPermissions[moduleId];
    return modulePermissions?.contains(permission) ?? false;
  }

  /// 获取模块权限列表
  Set<ModulePermission> getModulePermissions(String moduleId) {
    return Set<ModulePermission>.from(_grantedPermissions[moduleId] ?? {});
  }

  /// 获取所有模块权限
  Map<String, Set<ModulePermission>> getAllModulePermissions() {
    return Map.unmodifiable(_grantedPermissions.map(
      (moduleId, permissions) =>
          MapEntry(moduleId, Set<ModulePermission>.from(permissions)),
    ));
  }

  /// 批量授予权限
  Future<List<PermissionOperationResult>> grantPermissions(
    String moduleId,
    List<ModulePermission> permissions, {
    String? reason,
  }) async {
    final results = <PermissionOperationResult>[];

    for (final permission in permissions) {
      final result =
          await grantPermission(moduleId, permission, reason: reason);
      results.add(result);
    }

    return results;
  }

  /// 批量撤销权限
  Future<List<PermissionOperationResult>> revokePermissions(
    String moduleId,
    List<ModulePermission> permissions, {
    String? reason,
  }) async {
    final results = <PermissionOperationResult>[];

    for (final permission in permissions) {
      final result =
          await revokePermission(moduleId, permission, reason: reason);
      results.add(result);
    }

    return results;
  }

  /// 撤销模块所有权限
  Future<void> revokeAllPermissions(String moduleId, {String? reason}) async {
    final permissions = getModulePermissions(moduleId);
    await revokePermissions(moduleId, permissions.toList(), reason: reason);
  }

  /// 设置权限策略
  void setPermissionPolicy(
      ModulePermission permission, PermissionPolicy policy) {
    _permissionPolicies[permission] = policy;

    _eventController.add(PermissionEvent.policyChanged(permission, policy));
    _log('info', '权限策略已更新: $permission -> $policy');
  }

  /// 获取权限策略
  PermissionPolicy getPermissionPolicy(ModulePermission permission) {
    return _permissionPolicies[permission] ?? PermissionPolicy.ask;
  }

  /// 获取权限审计日志
  List<PermissionAuditLog> getAuditLogs({
    String? moduleId,
    ModulePermission? permission,
    DateTime? startTime,
    DateTime? endTime,
  }) {
    var logs = _auditLogs.where((log) => true);

    if (moduleId != null) {
      logs = logs.where((log) => log.moduleId == moduleId);
    }

    if (permission != null) {
      logs = logs.where((log) => log.permission == permission);
    }

    if (startTime != null) {
      logs = logs.where((log) => log.timestamp.isAfter(startTime));
    }

    if (endTime != null) {
      logs = logs.where((log) => log.timestamp.isBefore(endTime));
    }

    return logs.toList()..sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }

  /// 获取权限统计信息
  PermissionStatistics getPermissionStatistics() {
    final totalModules = _grantedPermissions.length;
    final totalPermissions = _grantedPermissions.values
        .fold<int>(0, (sum, permissions) => sum + permissions.length);

    final permissionCounts = <ModulePermission, int>{};
    for (final permissions in _grantedPermissions.values) {
      for (final permission in permissions) {
        permissionCounts[permission] = (permissionCounts[permission] ?? 0) + 1;
      }
    }

    final recentLogs = _auditLogs
        .where((log) => log.timestamp
            .isAfter(DateTime.now().subtract(const Duration(days: 7))))
        .toList();

    return PermissionStatistics(
      totalModules: totalModules,
      totalPermissions: totalPermissions,
      permissionCounts: permissionCounts,
      recentAuditLogs: recentLogs.length,
      lastActivity: _auditLogs.isNotEmpty ? _auditLogs.last.timestamp : null,
    );
  }

  /// 清理过期权限
  Future<void> cleanupExpiredPermissions() async {
    _log('info', '清理过期权限');

    final now = DateTime.now();
    final expiredLogs = _auditLogs
        .where((log) =>
            log.duration != null &&
            log.timestamp.add(log.duration!).isBefore(now) &&
            log.action == PermissionAction.granted)
        .toList();

    for (final log in expiredLogs) {
      await revokePermission(
        log.moduleId,
        log.permission,
        reason: '权限已过期',
      );
    }

    _log('info', '清理了 ${expiredLogs.length} 个过期权限');
  }

  /// 初始化默认权限策略
  void _initializeDefaultPolicies() {
    // 基础权限 - 允许
    _permissionPolicies[ModulePermission.basic] = PermissionPolicy.allow;

    // 文件系统权限 - 询问
    _permissionPolicies[ModulePermission.fileSystemRead] = PermissionPolicy.ask;
    _permissionPolicies[ModulePermission.fileSystemWrite] =
        PermissionPolicy.ask;

    // 网络权限 - 询问
    _permissionPolicies[ModulePermission.networkAccess] = PermissionPolicy.ask;

    // 系统信息权限 - 允许
    _permissionPolicies[ModulePermission.systemInfo] = PermissionPolicy.allow;

    // 用户数据权限 - 询问
    _permissionPolicies[ModulePermission.userData] = PermissionPolicy.ask;

    // 管理员权限 - 拒绝（需要手动设置）
    _permissionPolicies[ModulePermission.admin] = PermissionPolicy.deny;
  }

  /// 日志记录
  void _log(String level, String message,
      [Object? error, StackTrace? stackTrace]) {
    developer.log(
      message,
      name: 'ModulePermissionManager',
      level: _getLogLevel(level),
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// 获取日志级别
  int _getLogLevel(String level) {
    switch (level) {
      case 'info':
        return 800;
      case 'warning':
        return 900;
      case 'severe':
        return 1000;
      default:
        return 700;
    }
  }

  /// 清理资源
  void dispose() {
    _eventController.close();
  }
}
