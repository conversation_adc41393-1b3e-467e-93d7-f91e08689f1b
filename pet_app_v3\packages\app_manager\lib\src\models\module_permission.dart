/*
---------------------------------------------------------------
File name:          module_permission.dart
Author:             lgnorant-lu
Date created:       2025-07-24
Last modified:      2025-07-24
Dart Version:       3.2+
Description:        模块权限模型
---------------------------------------------------------------
Change History:
    2025-07-24: Phase ******* - 实现模块权限模型;
---------------------------------------------------------------
*/

import 'package:equatable/equatable.dart';

/// 模块权限枚举
enum ModulePermission {
  /// 基础权限（默认授予）
  basic,
  
  /// 文件系统读取权限
  fileSystemRead,
  
  /// 文件系统写入权限
  fileSystemWrite,
  
  /// 网络访问权限
  networkAccess,
  
  /// 系统信息访问权限
  systemInfo,
  
  /// 用户数据访问权限
  userData,
  
  /// 通知权限
  notification,
  
  /// 剪贴板权限
  clipboard,
  
  /// 相机权限
  camera,
  
  /// 麦克风权限
  microphone,
  
  /// 位置权限
  location,
  
  /// 存储权限
  storage,
  
  /// 管理员权限
  admin,
}

/// 模块权限扩展
extension ModulePermissionExtension on ModulePermission {
  /// 获取权限描述
  String get description {
    switch (this) {
      case ModulePermission.basic:
        return '基础权限';
      case ModulePermission.fileSystemRead:
        return '文件系统读取权限';
      case ModulePermission.fileSystemWrite:
        return '文件系统写入权限';
      case ModulePermission.networkAccess:
        return '网络访问权限';
      case ModulePermission.systemInfo:
        return '系统信息访问权限';
      case ModulePermission.userData:
        return '用户数据访问权限';
      case ModulePermission.notification:
        return '通知权限';
      case ModulePermission.clipboard:
        return '剪贴板权限';
      case ModulePermission.camera:
        return '相机权限';
      case ModulePermission.microphone:
        return '麦克风权限';
      case ModulePermission.location:
        return '位置权限';
      case ModulePermission.storage:
        return '存储权限';
      case ModulePermission.admin:
        return '管理员权限';
    }
  }

  /// 获取权限风险级别
  PermissionRiskLevel get riskLevel {
    switch (this) {
      case ModulePermission.basic:
      case ModulePermission.systemInfo:
        return PermissionRiskLevel.low;
      case ModulePermission.fileSystemRead:
      case ModulePermission.networkAccess:
      case ModulePermission.notification:
      case ModulePermission.clipboard:
        return PermissionRiskLevel.medium;
      case ModulePermission.fileSystemWrite:
      case ModulePermission.userData:
      case ModulePermission.camera:
      case ModulePermission.microphone:
      case ModulePermission.location:
      case ModulePermission.storage:
        return PermissionRiskLevel.high;
      case ModulePermission.admin:
        return PermissionRiskLevel.critical;
    }
  }

  /// 是否为敏感权限
  bool get isSensitive {
    return riskLevel == PermissionRiskLevel.high || 
           riskLevel == PermissionRiskLevel.critical;
  }
}

/// 权限风险级别
enum PermissionRiskLevel {
  /// 低风险
  low,
  
  /// 中等风险
  medium,
  
  /// 高风险
  high,
  
  /// 严重风险
  critical,
}

/// 权限策略
enum PermissionPolicy {
  /// 允许
  allow,
  
  /// 拒绝
  deny,
  
  /// 询问用户
  ask,
}

/// 权限操作结果
class PermissionOperationResult extends Equatable {
  /// 模块ID
  final String moduleId;
  
  /// 权限
  final ModulePermission permission;
  
  /// 是否成功
  final bool isSuccess;
  
  /// 结果消息
  final String message;
  
  /// 错误类型（失败时）
  final PermissionOperationError? error;
  
  /// 操作时间
  final DateTime timestamp;

  const PermissionOperationResult._({
    required this.moduleId,
    required this.permission,
    required this.isSuccess,
    required this.message,
    this.error,
    required this.timestamp,
  });

  /// 创建成功结果
  factory PermissionOperationResult.success(
    String moduleId,
    ModulePermission permission,
    String message,
  ) {
    return PermissionOperationResult._(
      moduleId: moduleId,
      permission: permission,
      isSuccess: true,
      message: message,
      timestamp: DateTime.now(),
    );
  }

  /// 创建失败结果
  factory PermissionOperationResult.failure(
    String moduleId,
    ModulePermission permission,
    String message,
    PermissionOperationError error,
  ) {
    return PermissionOperationResult._(
      moduleId: moduleId,
      permission: permission,
      isSuccess: false,
      message: message,
      error: error,
      timestamp: DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [moduleId, permission, isSuccess, message, error, timestamp];

  @override
  String toString() {
    return 'PermissionOperationResult(moduleId: $moduleId, permission: $permission, isSuccess: $isSuccess)';
  }
}

/// 权限操作错误
enum PermissionOperationError {
  /// 权限未找到
  permissionNotFound,
  
  /// 策略拒绝
  policyDenied,
  
  /// 用户拒绝
  userDenied,
  
  /// 操作失败
  operationFailed,
  
  /// 权限冲突
  permissionConflict,
}

/// 权限审计日志
class PermissionAuditLog extends Equatable {
  /// 模块ID
  final String moduleId;
  
  /// 权限
  final ModulePermission permission;
  
  /// 操作类型
  final PermissionAction action;
  
  /// 操作原因
  final String? reason;
  
  /// 操作时间
  final DateTime timestamp;
  
  /// 权限持续时间（可选）
  final Duration? duration;

  const PermissionAuditLog({
    required this.moduleId,
    required this.permission,
    required this.action,
    this.reason,
    required this.timestamp,
    this.duration,
  });

  /// 从 JSON 创建
  factory PermissionAuditLog.fromJson(Map<String, dynamic> json) {
    return PermissionAuditLog(
      moduleId: json['moduleId'] as String,
      permission: ModulePermission.values.firstWhere(
        (e) => e.name == json['permission'],
        orElse: () => ModulePermission.basic,
      ),
      action: PermissionAction.values.firstWhere(
        (e) => e.name == json['action'],
        orElse: () => PermissionAction.granted,
      ),
      reason: json['reason'] as String?,
      timestamp: DateTime.parse(json['timestamp'] as String),
      duration: json['duration'] != null
          ? Duration(milliseconds: json['duration'] as int)
          : null,
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'moduleId': moduleId,
      'permission': permission.name,
      'action': action.name,
      'reason': reason,
      'timestamp': timestamp.toIso8601String(),
      'duration': duration?.inMilliseconds,
    };
  }

  @override
  List<Object?> get props => [moduleId, permission, action, reason, timestamp, duration];

  @override
  String toString() {
    return 'PermissionAuditLog(moduleId: $moduleId, permission: $permission, action: $action)';
  }
}

/// 权限操作类型
enum PermissionAction {
  /// 授予
  granted,
  
  /// 撤销
  revoked,
  
  /// 请求
  requested,
  
  /// 拒绝
  denied,
}

/// 权限统计信息
class PermissionStatistics extends Equatable {
  /// 模块总数
  final int totalModules;
  
  /// 权限总数
  final int totalPermissions;
  
  /// 各权限的使用次数
  final Map<ModulePermission, int> permissionCounts;
  
  /// 最近审计日志数量
  final int recentAuditLogs;
  
  /// 最后活动时间
  final DateTime? lastActivity;

  const PermissionStatistics({
    required this.totalModules,
    required this.totalPermissions,
    required this.permissionCounts,
    required this.recentAuditLogs,
    this.lastActivity,
  });

  /// 获取最常用的权限
  List<MapEntry<ModulePermission, int>> get mostUsedPermissions {
    final entries = permissionCounts.entries.toList();
    entries.sort((a, b) => b.value.compareTo(a.value));
    return entries;
  }

  /// 获取平均每个模块的权限数
  double get averagePermissionsPerModule {
    return totalModules > 0 ? totalPermissions / totalModules : 0.0;
  }

  @override
  List<Object?> get props => [
    totalModules,
    totalPermissions,
    permissionCounts,
    recentAuditLogs,
    lastActivity,
  ];

  @override
  String toString() {
    return 'PermissionStatistics(totalModules: $totalModules, totalPermissions: $totalPermissions)';
  }
}

/// 权限事件
class PermissionEvent extends Equatable {
  /// 模块ID
  final String moduleId;
  
  /// 事件类型
  final PermissionEventType type;
  
  /// 权限（可选）
  final ModulePermission? permission;
  
  /// 权限策略（可选）
  final PermissionPolicy? policy;
  
  /// 事件时间
  final DateTime timestamp;

  const PermissionEvent._({
    required this.moduleId,
    required this.type,
    this.permission,
    this.policy,
    required this.timestamp,
  });

  /// 权限授予事件
  factory PermissionEvent.granted(String moduleId, ModulePermission permission) {
    return PermissionEvent._(
      moduleId: moduleId,
      type: PermissionEventType.granted,
      permission: permission,
      timestamp: DateTime.now(),
    );
  }

  /// 权限撤销事件
  factory PermissionEvent.revoked(String moduleId, ModulePermission permission) {
    return PermissionEvent._(
      moduleId: moduleId,
      type: PermissionEventType.revoked,
      permission: permission,
      timestamp: DateTime.now(),
    );
  }

  /// 策略变更事件
  factory PermissionEvent.policyChanged(ModulePermission permission, PermissionPolicy policy) {
    return PermissionEvent._(
      moduleId: '',
      type: PermissionEventType.policyChanged,
      permission: permission,
      policy: policy,
      timestamp: DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [moduleId, type, permission, policy, timestamp];

  @override
  String toString() {
    return 'PermissionEvent(moduleId: $moduleId, type: $type, permission: $permission)';
  }
}

/// 权限事件类型
enum PermissionEventType {
  /// 权限授予
  granted,
  
  /// 权限撤销
  revoked,
  
  /// 策略变更
  policyChanged,
}
