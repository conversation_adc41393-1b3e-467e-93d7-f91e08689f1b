/*
---------------------------------------------------------------
File name:          fonts.dart
Author:             Ming Status CLI
Date created:       2025-07-14
Last modified:      2025-07-14
Dart Version:       3.2+
Description:        core_services_v1应用程序字体资源
---------------------------------------------------------------
Change History:
    2025-07-14: Initial creation - core_services_v1应用程序字体资源;
---------------------------------------------------------------
*/


/// 字体资源常量
///
/// 定义应用程序中使用的所有字体
class AppFonts {
  /// 私有构造函数，防止实例化
  AppFonts._();

  /// 默认字体
  static const String defaultFont = 'Roboto';
  
  /// 标题字体
  static const String titleFont = 'RobotoSlab';
  
  /// 等宽字体
  static const String monospaceFont = 'RobotoMono';
  
  /// 装饰字体
  static const String decorativeFont = 'Pacifico';

  /// 字体权重
  static const Map<String, int> fontWeights = {
    'thin': 100,
    'light': 300,
    'regular': 400,
    'medium': 500,
    'semiBold': 600,
    'bold': 700,
    'extraBold': 800,
    'black': 900,
  };

  /// 字体大小
  static const Map<String, double> fontSizes = {
    'caption': 12.0,
    'body2': 14.0,
    'body1': 16.0,
    'subtitle2': 14.0,
    'subtitle1': 16.0,
    'headline6': 20.0,
    'headline5': 24.0,
    'headline4': 34.0,
    'headline3': 48.0,
    'headline2': 60.0,
    'headline1': 96.0,
  };
}
