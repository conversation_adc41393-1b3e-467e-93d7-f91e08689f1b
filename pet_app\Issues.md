# 问题追踪文档 (Issues Tracking)

基于**项目发展蓝图V3.0**进行问题分类和优先级管理。

**核心战略转变**: 从"按计划建设"转向"在实践中演进"，优先对齐三端UI框架，通过开发核心生态应用实战检验和迭代核心服务。

## 🎯 **已解决问题** (Resolved Issues)

### `[RESOLVED-P1.6-001]` 语言切换功能误删 `[critical]` `[closed]`
- **发现时间**: 2025-06-27 Phase 1.6 (用户报告)
- **位置**: `packages/ui_framework/lib/shell/main_shell.dart` (误删`_showLanguageDialog`)
- **问题描述**: Step3重构时误删语言切换功能，用户无法切换应用界面语言，i18n核心功能缺失
- **解决方案**: 在AdaptiveNavigationDrawer中恢复语言切换功能，添加Language按钮和双语对话框
- **解决状态**: ✅ **已完成** (2025-06-27紧急修复)
- **相关提交**: Phase 1.6 emergency language switching fix

### `[RESOLVED-P1.6-002]` NavigationDrawer本地化缺失 `[critical]` `[closed]`
- **发现时间**: 2025-06-27 Phase 1.6
- **位置**: `packages/ui_framework/lib/shell/navigation_drawer.dart` 
- **问题描述**: 7个TODO标记的硬编码中文字符串破坏i18n体系完整性
- **解决方案**: 扩展MainShellLocalizations参数传递模式，与MainShell保持架构一致性
- **解决状态**: ✅ **已完成** (2025-06-27)
- **相关提交**: Phase 1.6 finalization

### `[RESOLVED-P1.6-003]` LoggingService文件写入功能不完整 `[high]` `[closed]`
- **发现时间**: 2025-06-27 Phase 1.6  
- **位置**: `packages/core_services/lib/services/logging_service.dart`
- **问题描述**: FileLogOutput类缺少实际文件操作实现，只有占位符TODO
- **解决方案**: 实现完整的异步文件操作：懒加载初始化、IOSink管理、错误处理、文件轮转
- **解决状态**: ✅ **已完成** (2025-06-27)
- **相关提交**: Phase 1.6 finalization

---

## 🚨 **当前开放问题** (Open Issues)

### Phase 2.2C相关问题 (架构转型与模块化基础设施)

### `[OPEN-P2.2C-001]` 兼容性迁移+新架构验证策略实施 `[high]` `[architecture]`
- **发现时间**: 2025-06-29 Phase 2.2C Step 8.5架构战略决策
- **位置**: 整体项目架构层面
- **问题描述**: 
  - 当前开发模式仍为"功能添加模式"而非真正模块化开发
  - 需要在保持现有功能稳定的前提下，验证和迁移到真正的模块化架构
  - 涉及包结构重构、数据架构改变、依赖关系重设计等核心架构调整
- **影响范围**: 🟡 **整体架构演进，影响未来扩展能力和开发者生态**
- **优先级**: `[high]` - Phase 2.2C核心目标，模块化转型的关键基础
- **确认的解决方案**: 
  - **阶段1**: 模块化基础设施先行 (脚手架工具 + 验证器 + 创意工坊MVP)
  - **阶段2**: 现有包适配层 (保持当前结构，添加模块接口适配)
  - **阶段3**: 主题系统验证案例 (第一个真正模块化实现)
  - **阶段4**: 逐步迁移评估 (基于验证结果决定其他包迁移策略)
- **风险缓解**: 渐进式迁移，每阶段独立验证，避免大爆炸式重构风险
- **用户确认**: "时间压力小，选择兼容性迁移+新架构验证尝试"

### Phase 2.1相关问题 (三端UI框架与开放性设计)

**v3.0战略目标**: 搭建三端UI外壳基础骨架，设计Shell-Module交互契约，为未来开放性预留接口。

### `[OPEN-P2.0-UX-001]` 窗口边界约束优化 `[medium]` `[enhancement]`
- **发现时间**: 2025-06-27 Sprint 2.0b用户体验反馈
- **位置**: `packages/desktop_environment/lib/floating_window.dart`
- **问题描述**: 窗口拖拽时边界处理需要改进，可能存在窗口超出屏幕边界的情况
- **影响范围**: 🟡 **用户体验，窗口可能被拖拽到无法操作的位置**
- **优先级**: `[medium]` - Sprint 2.0c考虑实现
- **预期解决方案**: 实现窗口边界检测和自动约束逻辑
- **用户反馈**: "边界尚未处理好"

### `[OPEN-P2.0-UX-002]` 窗口自定义调整大小功能缺失 `[medium]` `[feature]`
- **发现时间**: 2025-06-27 Sprint 2.0b用户体验反馈
- **位置**: `packages/desktop_environment/lib/floating_window.dart`
- **问题描述**: 目前窗口大小固定，用户无法通过拖拽边角调整窗口大小
- **影响范围**: 🟡 **用户体验，窗口大小无法适应内容需要**
- **优先级**: `[medium]` - Sprint 2.0c边缘缩放功能实现
- **预期解决方案**: 实现窗口边缘和角落的调整大小手柄
- **用户反馈**: "窗口自定义大小尚无"

### `[OPEN-P2.0-UX-003]` 窗口状态记录逻辑设计决策 `[low]` `[behavior]`
- **发现时间**: 2025-06-27 Sprint 2.0b用户体验反馈
- **位置**: `packages/desktop_environment/lib/window_manager.dart`
- **问题描述**: 最大化→最小化→恢复时，记录的是默认大小而非原始大小。用户认为这可能是有用特性但需要设计规范
- **影响范围**: 🟢 **用户体验，状态恢复行为可能不符合预期，但可能是有价值的特性**
- **优先级**: `[low]` - 行为设计决策，后续UI/UX改进考虑
- **预期解决方案**: 
  - 选项A: 修复为严格恢复原始大小
  - 选项B: 设计为有意的特性，提供多级状态记录
- **用户反馈**: "有点使用价值，这个特性，但需挖掘；以及进阶的设计"

### `[OPEN-P2.0-UX-004]` 最小化行为语义优化 `[low]` `[behavior]`
- **发现时间**: 2025-06-27 Sprint 2.0b用户体验反馈
- **位置**: `packages/desktop_environment/lib/spatial_os_shell.dart`
- **问题描述**: 当前所有窗口最小化为统一行为，用户反馈点击恢复时的交互逻辑需要优化
- **影响范围**: 🟢 **用户体验，最小化/恢复的交互逻辑**
- **优先级**: `[low]` - 交互设计优化，后续UI/UX改进考虑
- **预期解决方案**: 设计更精细的最小化行为和任务栏交互
- **用户反馈**: "窗口是所有都最小化，点击一个的时候"

### `[OPEN-P2.0-UX-005]` 任务栏自动隐藏功能 `[high]` `[enhancement]`
- **发现时间**: 2025-06-27 Sprint 2.0c完成后用户建议
- **位置**: `packages/desktop_environment/lib/spatial_os_shell.dart`, `packages/desktop_environment/lib/app_dock.dart`
- **问题描述**: 参考Windows任务栏设计，添加任务栏隐藏开关功能，提供更大的窗口交互空间
- **影响范围**: 🟡 **用户体验增强，提供60px额外显示空间，沉浸式体验优化**
- **优先级**: `[high]` - 优秀的UX增强功能，用户主动提出
- **预期解决方案**: 
  - AppDock添加autoHide状态管理
  - 底部边缘鼠标检测机制(类似窗口resize检测)
  - 滑入/滑出动画过渡效果
  - 隐藏状态下窗口约束调整(可用完整屏幕高度)
  - 设置界面添加自动隐藏开关选项
- **用户反馈**: "任务栏的隐藏设计开关.隐藏的话,就多一块交互范围更简洁了"
- **技术要点**: 
  - 鼠标hover底部边缘检测
  - 动画状态管理(显示/隐藏/过渡中)
  - 窗口边界约束动态调整
  - 用户偏好设置持久化
- **实现优先级**: Phase 2.0后期或Phase 2.1前期，避免与当前大改冲突

### `[OPEN-P2.2-005]` PC端性能监控面板优化与i18n完善 `[medium]` `[enhancement]`
- **发现时间**: 2025-06-28 Phase 2.2 Sprint 2用户反馈
- **位置**: `packages/desktop_environment/lib/spatial_os_shell.dart` (性能监控区域)
- **问题描述**: 桌面环境的性能监控面板和页面模块显示面板需要优化完善，以及对应的i18n支持
- **影响范围**: 🟡 **PC端用户体验完整性，国际化体系完整性**
- **优先级**: `[medium]` - Phase 2.2优化目标
- **预期解决方案**: 
  - 完善性能监控面板的显示逻辑和样式
  - 优化页面模块显示面板的交互体验
  - 为监控面板添加完整的i18n翻译支持
  - 统一桌面环境相关UI元素的本地化
- **用户反馈**: "pc端的性能监控面板与页面模块显示面板的优化完善,和对应的i18n"

### `[OPEN-P2.2-006]` Web端侧栏交互与语言切换事件绑定 `[medium]` `[bug]`
- **发现时间**: 2025-06-28 Phase 2.2 Sprint 2用户测试
- **位置**: `packages/ui_framework/lib/shell/responsive_web_shell.dart`
- **问题描述**: Web端的展开侧栏和便捷语言切换功能点击后无响应，事件绑定存在问题
- **影响范围**: 🟡 **Web端用户无法正常使用侧栏展开和语言切换功能**
- **优先级**: `[medium]` - 基本交互功能缺失
- **预期解决方案**: 
  - 检查并修复侧栏展开按钮的事件处理逻辑
  - 修复语言切换按钮的点击响应问题
  - 确保Web端所有交互按钮的事件绑定正确
  - 添加必要的状态管理和UI更新逻辑
- **用户反馈**: "web/PC端的展开侧栏和便捷语言切换点击后无响应"

### `[OPEN-P2.2-007]` PC端边界位置Bug持续存在 `[high]` `[bug]`
- **发现时间**: 2025-06-28 Phase 2.2 Sprint 2用户测试
- **位置**: `packages/desktop_environment/lib/spatial_os_shell.dart`
- **问题描述**: PC端的窗口边界Bug依旧存在，初始状态不正常，边界处理非全屏状态有问题
- **影响范围**: 🟡 **PC端用户基本使用体验，窗口显示和交互异常**
- **优先级**: `[high]` - 影响PC端基本可用性
- **预期解决方案**: 
  - 深入分析SpatialOsShell的边界计算逻辑
  - 修复窗口初始化时的边界设置问题
  - 确保安全区域计算的正确性
  - 优化WindowManager的屏幕尺寸设置逻辑
- **用户反馈**: "pc的边界bug依旧存在"
- **历史记录**: 在Step 26中尝试修复但问题仍然存在，需要更深入的解决方案

### `[OPEN-P2.1-001]` 三端UI框架架构设计缺失 `[critical]` `[open]`
- **发现时间**: 2025-06-27 项目蓝图V3.0制定
- **位置**: 项目架构层面
- **问题描述**: 
  - 缺少SuperAppShell(移动端)、ResponsiveWebShell(Web端)、SpatialOsShell(PC端)的完整框架设计
  - 缺少DisplayModeService的三端切换实现
  - Shell-Module交互契约未定义，影响未来插件生态
  - 缺少开放性接口设计(桌面小组件、自定义UI等)
- **影响范围**: 🔴 **Phase 2.1核心目标无法启动**
- **优先级**: `[critical]` - Phase 2.1第一优先级
- **预期解决方案**: 
  - 设计并文档化Shell-Module交互契约
  - 实现可工作的三端UI骨架
  - 建立DisplayModeService手动切换基础逻辑
  - 为开放性预留架构接口
- **技术债务影响**: 阻碍v3.0"实战验证"策略的执行基础
- **当前状态**: 🟡 **已完成部分** - SpatialOsShell和StandardAppShell已实现，需要ResponsiveWebShell和契约设计

### `[OPEN-P2.0-002]` 性能基准线定义缺失 `[high]` `[open]`
- **发现时间**: 2025-06-27 Phase 2.0规划
- **位置**: 整体性能管理
- **问题描述**:
  - 缺少明确的性能基准线定义(窗口拖拽帧率、模块启动时间等)
  - 没有性能监控和测试机制
  - 缺少浮窗内存使用限制
- **影响范围**: 🟡 **影响双端UI框架的用户体验质量**
- **优先级**: `[high]` - Phase 2.0质量保障
- **预期解决方案**:
  - 定义性能基准: 窗口拖拽≥60fps，模块启动≤300ms，浮窗内存≤50MB
  - 实现性能监控工具和测试套件
  - 建立性能回归测试机制
- **技术选型建议**: 集成Flutter Performance工具链

### `[OPEN-P2.0-003]` 用户验证闭环机制缺失 `[medium]` `[open]`
- **发现时间**: 2025-06-27 Phase 2.0规划
- **位置**: 项目管理流程
- **问题描述**:
  - 每个Sprint缺少用户验证检查点的具体实施机制
  - 没有用户反馈收集和处理流程
  - 缺少体验评分和满意度测量工具
- **影响范围**: 🟡 **影响Phase 2.0的迭代质量和用户体验验证**
- **优先级**: `[medium]` - Phase 2.0流程保障
- **预期解决方案**:
  - 建立用户验证检查点标准流程
  - 设计体验评分问卷和反馈收集机制
  - 制定基于用户反馈的迭代调整策略

---

### Phase 2.2相关问题 (平台核心服务就绪)

**v3.0战略目标**: 为"内核实战炼金"准备所有必需的底层服务，包括Drift数据库、i18n服务、DX工具等。

### `[RESOLVED-P2.2-001]` 编辑功能完全缺失 `[critical]` `[resolved]` ✅
- **发现时间**: 2025-06-28 Phase 2.1技术债务审查
- **位置**: 所有UI Shell和业务模块
- **影响文件**: 
  - `packages/workshop/lib/workshop_widget.dart:115`
  - `packages/ui_framework/lib/shell/responsive_web_shell.dart:568`
  - `packages/ui_framework/lib/shell/modular_mobile_shell.dart:711`
  - `packages/ui_framework/lib/shell/app_shell.dart:373`
  - `packages/app_routing/lib/app_router.dart:412`
  - `apps/platform_app/lib/l10n/app_localizations_zh.dart:183`
- **解决时间**: ✅ **2025-06-28 Phase 2.2 Sprint 1 完成**
- **问题描述**: (已解决)
  - ✅ 所有UI Shell中的"编辑功能待实现"占位符已移除
  - ✅ 用户现可编辑所有已创建的内容，CRUD操作完整
  - ✅ 国际化文件中的占位符文本已清理
- **解决方案**: ✅ **已完整实现**
  - **UniversalEditDialog**: 创建400+行统一编辑组件，支持6种事务类型
  - **EditableItem接口**: 为所有业务模块建立一致的编辑标准
  - **Workshop模块**: 完全集成编辑功能，用户可编辑创意项目
  - **NotesHub模块**: 完全集成编辑功能，支持笔记、待办、项目等6种类型
  - **15个文件修复**: 移除所有"编辑功能待实现"占位符和相关本地化
- **技术债务分类**: TODO-CRITICAL-001 ✅ **已解决**
- **成功验证**: ✅ 用户可编辑所有创建的内容，无任何"待实现"占位符

### `[OPEN-P2.2-002]` Drift数据库集成架构缺失 `[critical]` `[open]`
- **发现时间**: 2025-06-27 v3.0策略制定
- **位置**: 所有业务模块数据层
- **问题描述**: 
  - 当前InMemoryRepository无法满足生产环境需求
  - 缺少DriftPersistenceRepository实现设计
  - 数据迁移策略和版本控制未定义
  - 备份恢复机制缺失
- **影响范围**: 🔴 **阻碍Phase 2.3实战验证，数据丢失风险极高**
- **优先级**: `[critical]` - Phase 2.2核心目标
- **预期解决方案**: 
  - 设计Drift数据库架构和模式
  - 实现IPersistenceRepository的Drift版本
  - 建立数据迁移和版本控制机制
- **技术选型**: Drift (SQLite for Flutter)

### `[OPEN-P2.2-003]` i18n服务架构重建 `[high]` `[open]`
- **发现时间**: 2025-06-27 基于DEBT-I18N-006升级
- **位置**: 整个应用国际化体系
- **问题描述**: 
  - Phase 2.0临时移除的动态本地化系统需要重建
  - 缺少模块级i18n扩展机制
  - AppRouter与i18n的集成方案未定义
- **影响范围**: 🔴 **影响设置应用MVP的语言切换功能**
- **优先级**: `[high]` - Phase 2.2必需，为Phase 2.3设置应用准备
- **预期解决方案**: 
  - 重新设计AppRouter的本地化集成
  - 建立模块i18n扩展标准
  - 恢复动态语言切换能力

### `[OPEN-P2.2-004]` 模块开发DX工具链缺失 `[medium]` `[open]`
- **发现时间**: 2025-06-27 v3.0策略制定
- **位置**: 开发者体验和生态建设
- **问题描述**: 
  - 缺少模块接口规范v1.0
  - 模块开发脚手架未实现
  - 缺少模块验证和测试工具
- **影响范围**: 🟡 **影响Phase 2.3三个应用的并行开发效率**
- **优先级**: `[medium]` - Phase 2.2增强目标
- **预期解决方案**: 
  - 制定模块接口规范文档
  - 开发模块脚手架工具
  - 建立模块质量验证机制

---

### Phase 2.3相关问题 (生态MVP与内核迭代)

**v3.0战略目标**: "在战争中学习战争" - 通过并行开发三个核心生态应用来实战检验和迭代核心服务。

### `[OPEN-P2.3-001]` 事务管理中心MVP开发 `[critical]` `[open]`
- **发现时间**: 2025-06-27 v3.0实战验证策略制定
- **位置**: `packages/notes_hub/` (核心服务第一个实战用户)
- **问题描述**: 
  - 完整CRUD功能实现，特别是编辑功能缺失
  - 需要作为Drift数据库的首个实战验证应用
  - ModuleManager、NavigationService的第一个真实验证场景
  - 数据持久化从内存到SQLite的完整迁移
- **影响范围**: 🔴 **v3.0实战验证策略的核心支柱，影响整个内核迭代过程**
- **优先级**: `[critical]` - Phase 2.3核心目标(Week 1-2)
- **预期解决方案**: 
  - 实现完整CRUD功能，移除所有"待实现"占位符
  - 集成Drift数据库，建立真实数据持久化
  - 验证核心服务设计，输出第一版**核心服务迭代日志**
- **成功标准**: 完整可用的事务管理，数据可靠持久化，为其他应用提供参考范例

### `[OPEN-P2.3-002]` 设置应用MVP开发 `[critical]` `[open]`
- **发现时间**: 2025-06-27 v3.0策略制定
- **位置**: 新建设置应用模块
- **问题描述**: 
  - 语言切换功能需要验证重建的i18n服务
  - 显示模式切换需要验证DisplayModeService设计
  - 作为配置管理和UI框架切换的实战用户
- **影响范围**: 🔴 **验证配置管理和UI框架的核心功能**
- **优先级**: `[critical]` - Phase 2.3核心目标(Week 3-4)
- **预期解决方案**: 
  - 实现语言切换，验证i18n服务完整性
  - 实现显示模式切换，验证三端UI框架
  - 建立ThemeService基础实现
  - 输出第二版**核心服务迭代日志**

### `[OPEN-P2.3-003]` 创意工坊模块管理MVP开发 `[critical]` `[open]`
- **发现时间**: 2025-06-27 v3.0策略重新定义
- **位置**: `packages/workshop/` (重新定位为模块管理器)
- **问题描述**: 
  - 从创意内容管理转变为模块管理器
  - 实现模块启用/禁用功能，验证插件生态基础
  - Shell-Module交互契约的第一个实战验证
  - 动态模块加载/卸载机制验证
- **影响范围**: 🔴 **验证插件生态基础架构和生态管理能力**
- **优先级**: `[critical]` - Phase 2.3核心目标(Week 5-6)
- **预期解决方案**: 
  - 实现模块管理器完整功能
  - 验证Shell-Module交互契约设计
  - 建立动态模块管理机制
  - 输出第三版**核心服务迭代日志**

### `[OPEN-P2.3-004]` 核心服务迭代管理机制缺失 `[high]` `[open]`
- **发现时间**: 2025-06-27 v3.0实战验证策略制定
- **位置**: 整个核心服务体系
- **问题描述**: 
  - 缺少核心服务变更管理机制
  - 没有API稳定性跟踪和迭代日志
  - 缺少服务间依赖变更的影响评估
- **影响范围**: 🟡 **影响并行开发的稳定性和质量控制**
- **优先级**: `[high]` - Phase 2.3质量保障
- **预期解决方案**: 
  - 建立核心服务迭代日志标准
  - 实现API变更影响评估机制
  - 制定服务重构的安全策略

---

### Phase 3.0+相关问题 (生态深化与体验精修)

**v3.0战略目标**: 在经过实战检验和重构的稳固内核之上，开始丰富用户体验和平台功能。

### `[OPEN-P3.0-001]` 桌宠皮肤系统开发 `[medium]` `[open]`
- **发现时间**: 2025-06-27 v3.0长期规划
- **位置**: 主题和视觉系统
- **问题描述**: 
  - 完整ThemeService实现和皮肤切换功能
  - 皮肤资源管理和动态加载机制
  - 用户自定义皮肤支持
- **影响范围**: 🟡 **平台个性化体验的核心功能**
- **优先级**: `[medium]` - Phase 3.0核心目标
- **预期解决方案**: 基于Phase 2.3中ThemeService基础实现完整皮肤系统

### `[OPEN-P3.0-002]` 文件系统模块开发 `[medium]` `[open]`
- **发现时间**: 2025-06-27 v3.0长期规划
- **位置**: 新建文件系统应用
- **问题描述**: 
  - 数据浏览和管理界面
  - 文件操作和组织功能
  - 数据导入导出能力
- **影响范围**: 🟡 **用户数据管理和透明度**
- **优先级**: `[medium]` - Phase 3.0目标
- **预期解决方案**: 基于Phase 2.3建立的数据架构开发文件系统

### `[OPEN-P3.0-003]` 业务模块高级功能增强 `[low]` `[open]`
- **发现时间**: 2025-06-27 基于v2.0问题重新评估
- **位置**: 所有业务模块
- **问题描述**: 
  - 搜索、过滤、排序功能
  - 批量操作能力
  - 富文本和多媒体支持
  - 数据统计和分析功能
- **影响范围**: 🟢 **增强用户体验，提升大量数据场景效率**
- **优先级**: `[low]` - Phase 3.0+可选目标
- **预期解决方案**: 根据Phase 2.3实战验证结果和用户反馈确定优先级

### `[OPEN-P3.0-004]` AI与智能功能集成 `[low]` `[open]`
- **发现时间**: 2025-06-27 v3.0长期愿景
- **位置**: 智能服务模块
- **问题描述**: 
  - RAG知识检索集成
  - LLM对话和辅助功能
  - 智能推荐和自动化
- **影响范围**: 🟢 **平台智能化核心竞争力**
- **优先级**: `[low]` - Phase 4+远期目标
- **预期解决方案**: 基于稳固的平台基础逐步集成AI能力

---

## 📊 **技术债务信用系统追踪** (Technical Debt Tracker)

*Phase 2.0 Sprint 2.0a引入 - 基于[设计原则文档](docs/design_principles.md#技术债务信用系统)*

### 当前债务概览 (2025-06-28 Phase 2.1完成后更新)
- **TODO项目总量**: 47个待完成项 (信用额度: ≤50个) ⚠️ **接近临界值**
- **关键业务缺陷**: 6个 TODO-CRITICAL (编辑功能完全缺失)
- **Phase分类债务**: 32个已明确规划延后项目
- **未分类债务**: 9个需要重新评估
- **传统DEBT标记**: 7个 (保持原有追踪)
- **债务风险等级**: 🟡 **中高等** (总量接近信用额度上限)

### 活跃技术债务清单

#### 🚨 **新发现关键业务缺陷** (Phase 2.1技术债务审查)

#### `[TODO-CRITICAL-001]` 编辑功能完全缺失 `[CRITICAL]` `[Phase 2.2 Week 1]`
- **位置**: 所有UI Shell和业务模块 (6个文件受影响)
- **债务类型**: 关键业务功能缺陷
- **描述**: 
  - 用户无法编辑任何已创建的内容，严重破坏基本CRUD操作
  - "编辑功能待实现"占位符遍布系统
  - 国际化文件硬编码占位符文本
- **利息影响**: 🔴 **应用基本不可用，严重影响用户体验**
- **偿还计划**: Phase 2.2 Week 1 最高优先级处理
- **关联问题**: `[OPEN-P2.2-001]`

#### `[TODO-FEATURE-002]` Phase 2.2热插拔功能集群 `[HIGH]` `[Phase 2.2]`
- **位置**: `packages/desktop_environment/` (4个TODO项目)
- **债务类型**: 计划功能延后债务
- **描述**: 窗口大小调整、模块热加载、性能监控面板等功能
- **利息影响**: 🟡 **影响桌面端用户体验完整性**
- **偿还计划**: Phase 2.2按计划实施

#### `[TODO-OPTIMIZE-003]` Phase 2.3性能优化集群 `[MED]` `[Phase 2.3]`
- **位置**: 多个模块 (18个TODO项目)
- **债务类型**: 性能和体验优化延后债务  
- **描述**: 搜索功能、过滤排序、动画效果、缓存优化等
- **利息影响**: 🟢 **不影响基本功能，但影响用户体验质量**
- **偿还计划**: Phase 2.3按优先级选择性实施

---

#### **传统DEBT标记** (保持原有追踪机制)

#### `[DEBT-ARCH-001]` WindowManager需要抽象接口 `[MED]` `[Sprint 2.0c]`
- **位置**: `packages/desktop_environment/lib/window_manager.dart`
- **债务类型**: 架构设计债务
- **描述**: 当前为具体类实现，未来需要抽象接口支持多种窗口管理策略
- **利息影响**: 影响桌面端扩展能力和测试覆盖
- **偿还计划**: Sprint 2.0c完成接口抽象

#### `[DEBT-PERF-002]` 浮窗拖拽帧率优化 `[HIGH]` `[Sprint 2.0d]`
- **位置**: `packages/desktop_environment/lib/floating_window.dart`
- **债务类型**: 性能优化债务
- **描述**: 当前拖拽实现可能导致帧率下降，需要优化渲染性能
- **利息影响**: 直接影响用户体验，违反60fps性能基准
- **偿还计划**: Sprint 2.0d立即处理

#### `[DEBT-UX-003]` 模块加载动画缺失 `[LOW]` `[Phase 2.1]`
- **位置**: `packages/ui_framework/lib/shell/app_shell.dart`
- **债务类型**: 用户体验债务
- **描述**: 模块切换时缺少平滑过渡动画，体验略显生硬
- **利息影响**: 影响整体用户体验流畅度
- **偿还计划**: Phase 2.1视情况处理

#### `[DEBT-TEST-004]` StandardAppShell缺少集成测试 `[MED]` `[Sprint 2.0d]`
- **位置**: `packages/ui_framework/test/shell/`
- **债务类型**: 测试覆盖债务
- **描述**: 新实现的StandardAppShell缺少完整的Widget测试覆盖
- **利息影响**: 影响代码质量和重构信心
- **偿还计划**: Sprint 2.0d测试套件完善

#### `[DEBT-I18N-005]` 路由模块本地化硬编码 `[LOW]` `[Phase 2.1]`
- **位置**: `packages/app_routing/lib/app_router.dart`  
- **债务类型**: 国际化债务
- **描述**: 路由模块中存在硬编码的中文字符串，未集成i18n系统
- **利息影响**: 破坏国际化体系完整性
- **偿还计划**: Phase 2.1国际化完善时处理

#### `[DEBT-I18N-006]` Phase 2.0 i18n系统临时简化 `[MED]` `[Phase 2.1]`
- **位置**: `packages/app_routing/lib/app_router.dart`, `main.dart`
- **债务类型**: 国际化债务
- **描述**: Phase 2.0为专注双端UI框架，临时移除了动态本地化系统(_LocalizedPetApp类)，改为硬编码中文字符串
- **利息影响**: 失去多语言切换能力，影响国际用户体验
- **偿还计划**: Phase 2.1重新集成完整i18n系统，恢复动态语言切换功能
- **技术方案**: 重新设计AppRouter的本地化集成方式，支持ShellRoute + i18n组合

#### `[DEBT-UX-007]` 设置页面导航功能缺失 `[LOW]` `[Phase 2.1]`
- **位置**: `packages/ui_framework/lib/shell/app_shell.dart:299`
- **债务类型**: 用户体验债务
- **描述**: StandardAppShell的NavigationDrawer中设置按钮点击事件只有TODO注释，未实现导航功能
- **利息影响**: 用户无法访问设置页面，影响应用配置和个性化体验
- **偿还计划**: Phase 2.1实现设置页面和导航功能
- **技术方案**: 创建设置页面路由，集成用户偏好设置、主题切换、语言选择等功能

### 债务趋势分析 (2025-06-28更新)
- **Phase 2.1完成时新增**: 47个TODO项目 (重大债务发现)
- **关键发现**: 6个编辑功能完全缺失，严重影响应用可用性
- **债务增长率**: 从7个增长到54个 (含TODO项目) - **671%增长** ⚠️
- **信用额度使用率**: 94% (47/50) 🔴 **临界状态**
- **风险评估**: Phase 2.2必须立即偿还关键债务，否则触发债务危机

### 债务偿还紧急优先级排序 (基于技术债务审查)
🚨 **立即处理 (Phase 2.2 Week 1)**:
1. **`[TODO-CRITICAL-001]`** - 编辑功能完全缺失，应用基本不可用
2. **`[DEBT-I18N-006]`** - i18n系统恢复，支持设置应用

🟡 **高优处理 (Phase 2.2 Week 2-3)**:
3. **`[TODO-FEATURE-002]`** - Phase 2.2热插拔功能集群
4. **`[DEBT-PERF-002]`** - 浮窗拖拽帧率优化  
5. **`[DEBT-ARCH-001]`** - WindowManager抽象接口

🟢 **计划处理 (Phase 2.3)**:
6. **`[TODO-OPTIMIZE-003]`** - Phase 2.3性能优化集群
7. **`[DEBT-TEST-004]`** - StandardAppShell集成测试
8. **`[DEBT-UX-003]`** - 模块加载动画
9. **`[DEBT-I18N-005]`** - 路由模块本地化
10. **`[DEBT-UX-007]`** - 设置页面导航

### 债务危机管理机制 (紧急启动)
- **债务信用额度**: 🔴 **已达94%使用率，临界状态**
- **紧急措施**: Phase 2.2必须偿还至少20个TODO项目，降至70%以下
- **质量门控**: 新增功能前必须先偿还等量债务
- **债务审查频率**: 从每Phase调整为每Sprint进行债务审查

---

## 📊 **问题统计摘要** (Issue Statistics - v3.0重构版)

### Phase 1.6 已解决 (历史成果)
- **总计**: 3个问题 ✅
- **关键**: 3个 (语言切换功能恢复、NavigationDrawer本地化、LoggingService文件写入)
- **解决率**: 100%

### Phase 2.1 当前开放 (三端UI框架与开放性设计)
- **总计**: 8个问题 (含5个UX优化问题)
- **关键**: 1个 (三端UI框架架构设计)
- **高优**: 1个 (任务栏自动隐藏功能)
- **中优**: 4个 (窗口边界、大小调整、性能基准线、用户验证)
- **低优**: 2个 (窗口状态记录、最小化行为)

### Phase 2.2 待解决 (平台核心服务就绪)
- **总计**: 4个问题 (新增编辑功能缺失关键问题)
- **关键**: 2个 (编辑功能缺失、Drift数据库集成架构)
- **高优**: 1个 (i18n服务架构重建)
- **中优**: 1个 (模块开发DX工具链)

### Phase 2.3 核心目标 (生态MVP与内核迭代)
- **总计**: 4个问题
- **关键**: 3个 (事务管理中心、设置应用、创意工坊模块管理MVP)
- **高优**: 1个 (核心服务迭代管理机制)

### Phase 3.0+ 远期规划 (生态深化与体验精修)
- **总计**: 4个问题
- **中优**: 2个 (桌宠皮肤系统、文件系统模块)
- **低优**: 2个 (业务模块高级功能、AI智能功能集成)

### 技术债务统计 (2025-06-28 16:15 Phase 2.2 Sprint 1更新)
- **TODO项目总量**: 35个 (⬇️ 从47个减少12个，25%债务减少率)
- **关键业务缺陷**: ✅ **0个** (TODO-CRITICAL-001已解决)
- **设置导航缺失**: ✅ **已解决** (TODO-CRITICAL-003已修复)
- **Phase 2.2处理**: 5个 (国际化+热插拔功能)
- **Phase 2.3延后**: 18个 (性能优化集群，按计划延后)
- **传统DEBT标记**: 7个 (保持原有追踪)
- **信用额度使用**: 🟢 **70% (35/50) 健康状态** (从94%临界降至良好水平)

### 整体项目评估 (基于项目发展蓝图V3.0 - 2025-06-28更新)
- **架构基础**: ✅ Excellent (100%) 
- **UI框架实现**: ✅ **已完成100%** (SpatialOsShell+ModularMobileShell+ResponsiveWebShell+DisplayModeService)
- **核心CRUD功能**: ✅ **已完成100%** (编辑功能全面恢复，创建、读取、更新、删除四大操作完整)
- **设置导航**: ✅ **已完成100%** (三端导航逻辑统一实现)
- **核心服务就绪**: 🟡 **Phase 2.2推进中** (需要Drift+i18n重建+DX工具)
- **生态MVP验证**: 🟢 **Phase 2.3准备就绪** (核心业务功能已具备)
- **实战验证策略**: 🟢 **v3.0核心创新** (从按计划建设转向实践演进)
- **技术债务管理**: 🟢 **优秀状态** (70%使用率，债务危机已化解)

---

## 🎯 **阶段性行动建议** (Recommendations by Phase - v3.0重构版)

### Phase 2.1 状态确认 (三端UI框架与开放性设计) ✅ **已完成**
✅ **已解决P2.1-001**: 三端UI框架架构完全实现 (DisplayModeAwareShell + ModularMobileShell + ResponsiveWebShell + SpatialOsShell)  
✅ **已设计Shell-Module交互契约**: 契约v1.0和模块API规范文档已完成  
✅ **已实现DisplayModeService**: 24个单元测试100%通过，三端切换逻辑完成  
✅ **已修复关键Bug**: DisplayModeService初始化问题、应用启动流程优化  
✅ **已建立测试体系**: 4个Widget测试文件覆盖三端框架  

**🔴 新发现关键问题**: 编辑功能完全缺失，必须在Phase 2.2立即解决

### Phase 2.2 紧急重构计划 (平台核心服务就绪) 🚨 **债务危机管理**

**Week 1 - 关键业务修复** (最高优先级):
1. **🔴 解决P2.2-001**: 编辑功能完全缺失 - **立即修复，应用基本不可用**
2. **解决DEBT-I18N-006**: i18n系统恢复，支持设置应用开发

**Week 2-3 - 核心服务建设**:
3. **解决P2.2-002**: Drift数据库集成架构设计和实现
4. **解决P2.2-003**: i18n服务架构重建，恢复动态语言切换
5. **解决TODO-FEATURE-002**: Phase 2.2热插拔功能集群

**Week 4 - 质量与准备**:
6. **解决P2.2-004**: 建立模块开发DX工具链和接口规范
7. **债务偿还**: 处理DEBT-PERF-002和DEBT-ARCH-001
8. **债务控制**: 总债务量降至35个以下 (70%信用额度)

### Phase 2.3 实战验证核心 (生态MVP与内核迭代)
**v3.0关键策略**: 采用优化后的"串行MVP + 并行迭代"方式
1. **Week 1-2**: 解决P2.3-001事务管理中心MVP开发
2. **Week 3-4**: 解决P2.3-002设置应用MVP开发
3. **Week 5-6**: 解决P2.3-003创意工坊模块管理MVP开发
4. **Week 7-8**: 三端UI集成与优化，输出完整的核心服务迭代日志
5. **解决P2.3-004**: 建立核心服务迭代管理机制，确保API稳定性

### Phase 3.0+ 生态深化 (基于实战验证结果)
1. **基于内核迭代结果**: 优先处理P3.0-001皮肤系统和P3.0-002文件系统
2. **用户反馈驱动**: 根据Phase 2.3用户验证结果调整P3.0-003高级功能优先级
3. **债务清理**: 确保债务总量控制在信用额度10%以内
4. **长期愿景**: 为P3.0-004 AI智能功能集成做技术准备

### v3.0成功标准 (实战验证导向)
- **Phase 2.1**: 三端UI框架完全实现，Shell-Module契约定义完成，开放性接口设计完备
- **Phase 2.2**: 核心服务架构稳定，Drift+i18n+DX工具链完全就绪，为实战验证提供基础
- **Phase 2.3**: 三个MVP应用100%可用，核心服务经过实战验证和迭代，API稳定性达标
- **整体质量**: 无"功能待实现"占位符，技术债务控制在10%以内，实战验证策略成功

### 风险控制机制 (v3.0新增)
- **API变更管理**: 每个核心服务变更都需RFC文档和影响评估
- **并行开发风险**: 如果任一应用开发周期超过计划50%，立即触发风险评估
- **质量保障**: 核心服务变更频率超过每周2次，启动API稳定性专项治理
- **用户反馈循环**: 每个Phase结束都需要用户验证和满意度评估

---

*最后更新: 2025-06-28 基于Phase 2.1技术债务审查 + 47个TODO项目发现*
*重大发现: 编辑功能完全缺失，债务信用额度达94%临界状态，启动债务危机管理*
*下次更新: Phase 2.2 Week 1完成时，重新评估债务偿还进度和Phase 2.3计划调整*
