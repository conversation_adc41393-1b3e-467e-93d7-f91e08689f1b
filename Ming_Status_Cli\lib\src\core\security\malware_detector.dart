/*
---------------------------------------------------------------
File name:          malware_detector.dart
Author:             lgnorant-lu
Date created:       2025/07/11
Last modified:      2025/07/11
Dart Version:       3.2+
Description:        恶意代码检测器 (Malware Detector)
---------------------------------------------------------------
Change History:
    2025/07/11: Initial creation - Task 2.2.2 企业级安全验证系统;
---------------------------------------------------------------
*/

import 'dart:async';

import 'dart:io';
import 'dart:typed_data';

/// 威胁级别枚举
enum ThreatLevel {
  /// 无威胁
  none,

  /// 低风险
  low,

  /// 中等风险
  medium,

  /// 高风险
  high,

  /// 严重威胁
  critical,
}

/// 检测类型枚举
enum DetectionType {
  /// 静态代码分析
  staticAnalysis,

  /// 沙箱执行
  sandboxExecution,

  /// 第三方API扫描
  thirdPartyApi,

  /// 启发式检测
  heuristicDetection,

  /// 机器学习模型
  machineLearning,
}

/// 威胁类型枚举
enum ThreatType {
  /// 恶意软件
  malware,

  /// 病毒
  virus,

  /// 木马
  trojan,

  /// 间谍软件
  spyware,

  /// 勒索软件
  ransomware,

  /// 后门
  backdoor,

  /// 可疑行为
  suspiciousBehavior,

  /// 数据泄露
  dataLeakage,

  /// 权限滥用
  privilegeEscalation,
}

/// 检测结果
class DetectionResult {
  const DetectionResult({
    required this.threatLevel,
    required this.detectionType,
    required this.threatTypes,
    required this.issues,
    required this.detectedAt,
    required this.scanDuration,
    required this.confidence,
    required this.engineVersion,
    required this.metadata,
  });

  /// 威胁级别
  final ThreatLevel threatLevel;

  /// 检测类型
  final DetectionType detectionType;

  /// 威胁类型列表
  final List<ThreatType> threatTypes;

  /// 检测到的问题
  final List<SecurityIssue> issues;

  /// 检测时间
  final DateTime detectedAt;

  /// 检测耗时
  final Duration scanDuration;

  /// 置信度 (0-100)
  final int confidence;

  /// 检测引擎版本
  final String engineVersion;

  /// 额外信息
  final Map<String, dynamic> metadata;

  /// 是否安全
  bool get isSafe => threatLevel == ThreatLevel.none && issues.isEmpty;

  /// 是否有威胁
  bool get hasThreat => threatLevel != ThreatLevel.none || issues.isNotEmpty;

  /// 是否高风险
  bool get isHighRisk =>
      threatLevel == ThreatLevel.high || threatLevel == ThreatLevel.critical;
}

/// 安全问题
class SecurityIssue {
  const SecurityIssue({
    required this.id,
    required this.title,
    required this.description,
    required this.threatType,
    required this.severity,
    required this.references,
    required this.confidence,
    this.filePath,
    this.lineNumber,
    this.codeSnippet,
    this.remediation,
  });

  /// 问题ID
  final String id;

  /// 问题标题
  final String title;

  /// 问题描述
  final String description;

  /// 威胁类型
  final ThreatType threatType;

  /// 威胁级别
  final ThreatLevel severity;

  /// 文件路径
  final String? filePath;

  /// 行号
  final int? lineNumber;

  /// 代码片段
  final String? codeSnippet;

  /// 修复建议
  final String? remediation;

  /// 参考链接
  final List<String> references;

  /// 置信度
  final int confidence;
}

/// 沙箱执行结果
class SandboxResult {
  const SandboxResult({
    required this.executionSuccessful,
    required this.executionTime,
    required this.networkConnections,
    required this.fileOperations,
    required this.processCreations,
    required this.registryOperations,
    required this.anomalies,
    required this.resourceUsage,
  });

  /// 执行是否成功
  final bool executionSuccessful;

  /// 执行时间
  final Duration executionTime;

  /// 网络连接尝试
  final List<String> networkConnections;

  /// 文件系统操作
  final List<String> fileOperations;

  /// 进程创建
  final List<String> processCreations;

  /// 注册表操作 (Windows)
  final List<String> registryOperations;

  /// 异常行为
  final List<String> anomalies;

  /// 资源使用情况
  final Map<String, dynamic> resourceUsage;

  /// 是否有可疑行为
  bool get hasSuspiciousBehavior =>
      networkConnections.isNotEmpty ||
      anomalies.isNotEmpty ||
      processCreations.length > 5;
}

/// 第三方API扫描结果
class ThirdPartyApiResult {
  const ThirdPartyApiResult({
    required this.provider,
    required this.scanId,
    required this.totalEngines,
    required this.positiveEngines,
    required this.engineResults,
    required this.scannedAt,
    required this.fileHash,
  });

  /// API提供商
  final String provider;

  /// 扫描ID
  final String scanId;

  /// 检测引擎数量
  final int totalEngines;

  /// 检测到威胁的引擎数量
  final int positiveEngines;

  /// 详细结果
  final Map<String, String> engineResults;

  /// 扫描时间
  final DateTime scannedAt;

  /// 文件哈希
  final String fileHash;

  /// 检测率
  double get detectionRate =>
      totalEngines > 0 ? positiveEngines / totalEngines : 0.0;

  /// 是否被多数引擎检测为威胁
  bool get isMalicious => detectionRate > 0.3;
}

/// 恶意代码检测器
class MalwareDetector {
  /// 构造函数
  MalwareDetector({
    Map<String, String>? apiConfigs,
  }) {
    if (apiConfigs != null) {
      _apiConfigs.addAll(apiConfigs);
    }
  }

  /// 危险函数列表
  static const List<String> _dangerousFunctions = [
    'eval',
    'exec',
    'system',
    'shell_exec',
    'passthru',
    'file_get_contents',
    'file_put_contents',
    'fopen',
    'fwrite',
    'curl_exec',
    'socket_create',
    'fsockopen',
    'base64_decode',
    'gzinflate',
    'str_rot13',
  ];

  /// 可疑模式列表
  static const List<String> _suspiciousPatterns = [
    r'password\s*=',
    r'api.*key\s*=',
    r'secret\s*=',
    r'token\s*=',
    r'eval\s*\(',
    r'exec\s*\(',
    r'system\s*\(',
    r'shell_exec\s*\(',
    r'base64_decode\s*\(',
    r'gzinflate\s*\(',
    r'file_get_contents\s*\(',
    r'curl_exec\s*\(',
    r'socket_create\s*\(',
    r'fsockopen\s*\(',
  ];

  /// 检测统计
  final Map<String, int> _detectionStats = {};

  /// 检测缓存
  final Map<String, DetectionResult> _detectionCache = {};

  /// 第三方API配置
  final Map<String, String> _apiConfigs = {};

  /// 扫描文件
  Future<DetectionResult> scanFile(String filePath) async {
    final startTime = DateTime.now();

    try {
      // 检查缓存
      final fileHash = await _calculateFileHash(filePath);
      final cached = _detectionCache[fileHash];
      if (cached != null) {
        return cached;
      }

      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('File not found: $filePath');
      }

      final fileData = await file.readAsBytes();
      final result = await scanData(fileData, filePath);

      // 缓存结果
      _detectionCache[fileHash] = result;

      return result;
    } catch (e) {
      final endTime = DateTime.now();
      return DetectionResult(
        threatLevel: ThreatLevel.none,
        detectionType: DetectionType.staticAnalysis,
        threatTypes: [],
        issues: [
          SecurityIssue(
            id: 'scan_error',
            title: 'Scan Error',
            description: 'Failed to scan file: $e',
            threatType: ThreatType.suspiciousBehavior,
            severity: ThreatLevel.low,
            filePath: filePath,
            references: [],
            confidence: 100,
          ),
        ],
        detectedAt: DateTime.now(),
        scanDuration: endTime.difference(startTime),
        confidence: 0,
        engineVersion: '1.0.0',
        metadata: {'error': e.toString()},
      );
    }
  }

  /// 扫描数据
  Future<DetectionResult> scanData(Uint8List data, [String? filePath]) async {
    final startTime = DateTime.now();
    final issues = <SecurityIssue>[];
    final threatTypes = <ThreatType>{};

    // 静态代码分析
    final staticIssues = await _performStaticAnalysis(data, filePath);
    issues.addAll(staticIssues);

    // 启发式检测
    final heuristicIssues = await _performHeuristicDetection(data, filePath);
    issues.addAll(heuristicIssues);

    // 沙箱执行 (如果是可执行文件)
    if (_isExecutableFile(filePath)) {
      final sandboxIssues = await _performSandboxExecution(data, filePath);
      issues.addAll(sandboxIssues);
    }

    // 第三方API扫描
    if (_apiConfigs.isNotEmpty) {
      final apiIssues = await _performThirdPartyApiScan(data, filePath);
      issues.addAll(apiIssues);
    }

    // 收集威胁类型
    for (final issue in issues) {
      threatTypes.add(issue.threatType);
    }

    // 计算威胁级别
    final threatLevel = _calculateThreatLevel(issues);

    // 计算置信度
    final confidence = _calculateConfidence(issues);

    final endTime = DateTime.now();

    // 更新统计
    _updateDetectionStats(threatLevel, issues.length);

    return DetectionResult(
      threatLevel: threatLevel,
      detectionType: DetectionType.staticAnalysis,
      threatTypes: threatTypes.toList(),
      issues: issues,
      detectedAt: endTime,
      scanDuration: endTime.difference(startTime),
      confidence: confidence,
      engineVersion: '1.0.0',
      metadata: {
        'fileSize': data.length,
        'filePath': filePath,
        'scanMethods': ['static', 'heuristic', 'sandbox', 'api'],
      },
    );
  }

  /// 批量扫描
  Future<List<DetectionResult>> scanBatch(List<String> filePaths) async {
    final results = <DetectionResult>[];

    for (final filePath in filePaths) {
      final result = await scanFile(filePath);
      results.add(result);
    }

    return results;
  }

  /// 获取检测统计
  Map<String, dynamic> getDetectionStats() {
    return {
      'totalScans': _detectionStats.values.fold(0, (sum, count) => sum + count),
      'cacheSize': _detectionCache.length,
      'apiConfigs': _apiConfigs.length,
      'detectionsByThreatLevel': Map<String, int>.from(_detectionStats),
      'dangerousFunctionsCount': _dangerousFunctions.length,
      'suspiciousPatternsCount': _suspiciousPatterns.length,
    };
  }

  /// 清理缓存
  void clearCache() {
    _detectionCache.clear();
  }

  /// 静态代码分析
  Future<List<SecurityIssue>> _performStaticAnalysis(
    Uint8List data,
    String? filePath,
  ) async {
    final issues = <SecurityIssue>[];
    final content = String.fromCharCodes(data);

    // 检查危险函数
    for (final func in _dangerousFunctions) {
      if (content.contains(func)) {
        issues.add(
          SecurityIssue(
            id: 'dangerous_function_$func',
            title: 'Dangerous Function Usage',
            description: 'Usage of potentially dangerous function: $func',
            threatType: ThreatType.suspiciousBehavior,
            severity: ThreatLevel.medium,
            filePath: filePath,
            codeSnippet: _extractCodeSnippet(content, func),
            remediation:
                "Review the usage of $func and ensure it's necessary and secure",
            references: ['https://owasp.org/www-project-top-ten/'],
            confidence: 80,
          ),
        );
      }
    }

    // 检查可疑模式
    for (final pattern in _suspiciousPatterns) {
      final regex = RegExp(pattern);
      final matches = regex.allMatches(content);

      for (final match in matches) {
        issues.add(
          SecurityIssue(
            id: 'suspicious_pattern_${pattern.hashCode}',
            title: 'Suspicious Pattern Detected',
            description: 'Suspicious code pattern found: ${match.group(0)}',
            threatType: ThreatType.suspiciousBehavior,
            severity: ThreatLevel.low,
            filePath: filePath,
            lineNumber: _getLineNumber(content, match.start),
            codeSnippet: match.group(0),
            remediation:
                'Review this code pattern for potential security issues',
            references: [],
            confidence: 60,
          ),
        );
      }
    }

    return issues;
  }

  /// 启发式检测
  Future<List<SecurityIssue>> _performHeuristicDetection(
    Uint8List data,
    String? filePath,
  ) async {
    final issues = <SecurityIssue>[];

    // 检查文件大小异常
    if (data.length > 100 * 1024 * 1024) {
      // 100MB
      issues.add(
        SecurityIssue(
          id: 'large_file_size',
          title: 'Unusually Large File',
          description: 'File size is unusually large: ${data.length} bytes',
          threatType: ThreatType.suspiciousBehavior,
          severity: ThreatLevel.low,
          filePath: filePath,
          remediation: 'Verify that this large file size is expected',
          references: [],
          confidence: 40,
        ),
      );
    }

    // 检查高熵内容 (可能的加密/混淆)
    final entropy = _calculateEntropy(data);
    if (entropy > 7.5) {
      issues.add(
        SecurityIssue(
          id: 'high_entropy_content',
          title: 'High Entropy Content',
          description:
              'File contains high entropy content (entropy: ${entropy.toStringAsFixed(2)})',
          threatType: ThreatType.suspiciousBehavior,
          severity: ThreatLevel.medium,
          filePath: filePath,
          remediation:
              'High entropy may indicate encrypted or obfuscated content',
          references: [],
          confidence: 70,
        ),
      );
    }

    return issues;
  }

  /// 沙箱执行
  Future<List<SecurityIssue>> _performSandboxExecution(
    Uint8List data,
    String? filePath,
  ) async {
    final issues = <SecurityIssue>[];

    // 模拟沙箱执行
    await Future<void>.delayed(const Duration(milliseconds: 500));

    const sandboxResult = SandboxResult(
      executionSuccessful: true,
      executionTime: Duration(milliseconds: 100),
      networkConnections: [],
      fileOperations: [],
      processCreations: [],
      registryOperations: [],
      anomalies: [],
      resourceUsage: {'cpu': 5.0, 'memory': 1024},
    );

    if (sandboxResult.hasSuspiciousBehavior) {
      issues.add(
        SecurityIssue(
          id: 'sandbox_suspicious_behavior',
          title: 'Suspicious Behavior in Sandbox',
          description:
              'File exhibited suspicious behavior during sandbox execution',
          threatType: ThreatType.suspiciousBehavior,
          severity: ThreatLevel.high,
          filePath: filePath,
          remediation:
              'Review sandbox execution results for malicious activity',
          references: [],
          confidence: 90,
        ),
      );
    }

    return issues;
  }

  /// 第三方API扫描
  Future<List<SecurityIssue>> _performThirdPartyApiScan(
    Uint8List data,
    String? filePath,
  ) async {
    final issues = <SecurityIssue>[];

    // 模拟第三方API扫描
    await Future<void>.delayed(const Duration(milliseconds: 1000));

    final apiResult = ThirdPartyApiResult(
      provider: 'VirusTotal',
      scanId: 'scan_${DateTime.now().millisecondsSinceEpoch}',
      totalEngines: 70,
      positiveEngines: 0,
      engineResults: {},
      scannedAt: DateTime.now(),
      fileHash: await _calculateFileHashFromData(data),
    );

    if (apiResult.isMalicious) {
      issues.add(
        SecurityIssue(
          id: 'third_party_api_detection',
          title: 'Third-party API Detection',
          description:
              'File detected as malicious by ${apiResult.positiveEngines}/${apiResult.totalEngines} engines',
          threatType: ThreatType.malware,
          severity: ThreatLevel.critical,
          filePath: filePath,
          remediation:
              'File is flagged as malicious by multiple antivirus engines',
          references: ['https://www.virustotal.com/'],
          confidence: 95,
        ),
      );
    }

    return issues;
  }

  /// 计算威胁级别
  ThreatLevel _calculateThreatLevel(List<SecurityIssue> issues) {
    if (issues.isEmpty) return ThreatLevel.none;

    final maxSeverity = issues
        .map((issue) => issue.severity)
        .reduce((a, b) => a.index > b.index ? a : b);

    return maxSeverity;
  }

  /// 计算置信度
  int _calculateConfidence(List<SecurityIssue> issues) {
    if (issues.isEmpty) return 100;

    final totalConfidence =
        issues.fold(0, (sum, issue) => sum + issue.confidence);
    return (totalConfidence / issues.length).round();
  }

  /// 计算文件哈希
  Future<String> _calculateFileHash(String filePath) async {
    final file = File(filePath);
    final data = await file.readAsBytes();
    return _calculateFileHashFromData(data);
  }

  /// 计算数据哈希
  Future<String> _calculateFileHashFromData(Uint8List data) async {
    // 简化实现
    return data.length.toString();
  }

  /// 计算熵值
  double _calculateEntropy(Uint8List data) {
    final frequency = <int, int>{};

    for (final byte in data) {
      frequency[byte] = (frequency[byte] ?? 0) + 1;
    }

    var entropy = 0.0;
    final length = data.length;

    for (final count in frequency.values) {
      final probability = count / length;
      entropy -= probability *
          (probability > 0 ? (probability * 3.321928) : 0); // log2
    }

    return entropy;
  }

  /// 检查是否为可执行文件
  bool _isExecutableFile(String? filePath) {
    if (filePath == null) return false;

    final extensions = [
      '.exe',
      '.dll',
      '.so',
      '.dylib',
      '.app',
      '.deb',
      '.rpm',
    ];
    return extensions.any((ext) => filePath.toLowerCase().endsWith(ext));
  }

  /// 提取代码片段
  String _extractCodeSnippet(String content, String searchTerm) {
    final index = content.indexOf(searchTerm);
    if (index == -1) return searchTerm;

    final start = (index - 50).clamp(0, content.length);
    final end = (index + searchTerm.length + 50).clamp(0, content.length);

    return content.substring(start, end);
  }

  /// 获取行号
  int _getLineNumber(String content, int position) {
    return content.substring(0, position).split('\n').length;
  }

  /// 更新检测统计
  void _updateDetectionStats(ThreatLevel level, int issueCount) {
    final key = level.name;
    _detectionStats[key] = (_detectionStats[key] ?? 0) + 1;

    final issueKey = 'issues_$issueCount';
    _detectionStats[issueKey] = (_detectionStats[issueKey] ?? 0) + 1;
  }
}
