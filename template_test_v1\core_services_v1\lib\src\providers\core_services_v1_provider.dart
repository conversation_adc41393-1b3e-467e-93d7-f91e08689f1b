/*
---------------------------------------------------------------
File name:          core_services_v1_provider.dart
Author:             Ming Status CLI
Date created:       2025-07-14
Last modified:      2025-07-14
Dart Version:       3.2+
Description:        core_services_v1状态管理Provider
---------------------------------------------------------------
Change History:
    2025-07-14: Initial creation - core_services_v1状态管理Provider;
---------------------------------------------------------------
*/

import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'package:provider/provider.dart';

import '../services/core_services_v1_service.dart';
import '../models/core_services_v1_model.dart';

/// core_services_v1应用状态管理Provider
///
/// ## 使用示例
///
/// ```dart
/// final provider = CoreServicesV1Provider();
/// ```
///
/// ```dart
/// provider.initialize();
/// ```
///
/// ```dart
/// provider.updateState(newData);
/// ```
///
/// ## 相关类
///
/// * [ChangeNotifier]
/// * [Provider]
///
class CoreServicesV1Provider extends ChangeNotifier {
  /// 是否已初始化
  bool _isInitialized = false;

  /// 是否正在加载
  bool _isLoading = false;

  /// 错误信息
  String? _error;

  /// 数据列表
  List<Map<String, dynamic>> _data = [];

  /// 当前选中项
  Map<String, dynamic>? _selectedItem;

  /// 获取初始化状态
  bool get isInitialized => _isInitialized;

  /// 获取加载状态
  bool get isLoading => _isLoading;

  /// 获取错误信息
  String? get error => _error;

  /// 获取数据列表
  List<Map<String, dynamic>> get data => List.unmodifiable(_data);

  /// 获取当前选中项
  Map<String, dynamic>? get selectedItem => _selectedItem;

  /// 创建CoreServicesV1Provider实例
  CoreServicesV1Provider();

  /// 初始化Provider
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _setLoading(true);
      _clearError();

      // TODO: 添加初始化逻辑
      await loadData();

      _isInitialized = true;
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// 设置错误信息
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// 清除错误信息
  void _clearError() {
    if (_error != null) {
      _error = null;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    // TODO: 清理资源
    super.dispose();
  }

}

/// Provider扩展方法
extension CoreServicesV1ProviderExtensions on CoreServicesV1Provider {
  /// 重置状态
  void reset() {
    _isInitialized = false;
    _isLoading = false;
    _error = null;
    _data.clear();
    _selectedItem = null;
    notifyListeners();
  }
}
