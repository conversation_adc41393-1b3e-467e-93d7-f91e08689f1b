^D:\狗PY\PYTHONPROJECT\CRUD\TASKS_ORGANIZING\PET_APP\APPS\PLATFORM_APP\BUILD\WINDOWS\X64\CMAKEFILES\F0E2CC5D01F3C911387F5BB59DA376CD\GENERATE.STAMP.RULE
setlocal
D:\VS\VisualStudio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe -SD:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/windows -BD:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64/pet_app.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
