/// Generated file. Do not edit.
///
/// To regenerate, run: `flutter gen-l10n`
///
/// Project: core_services_v1
/// Generated: 2025-07-14

// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Japanese (`ja`).
class AppLocalizationsJa extends AppLocalizations {
  AppLocalizationsJa([String locale = 'ja']) : super(locale);

  @override
  String get appTitle => 'マイアプリ';

  @override
  String get welcome => 'ようこそ';

  @override
  String hello(String name) {
    return 'こんにちは、$nameさん！';
  }

  @override
  String get settings => '設定';

  @override
  String get about => 'について';

  @override
  String get ok => 'OK';

  @override
  String get cancel => 'キャンセル';

  @override
  String get home => 'ホーム';

  @override
  String get profile => 'プロフィール';

  @override
  String get notifications => '通知';

  @override
  String get search => '検索';

  @override
  String get loading => '読み込み中...';

  @override
  String get error => 'エラー';

  @override
  String get retry => '再試行';

  @override
  String get save => '保存';

  @override
  String get delete => '削除';

  @override
  String get edit => '編集';

  @override
  String itemCount(int count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$countStringつのアイテム',
      one: '1つのアイテム',
      zero: 'アイテムなし',
    );
    return '$_temp0';
  }

  @override
  String get login => 'ログイン';

  @override
  String get logout => 'ログアウト';

  @override
  String get register => '登録';

  @override
  String get forgotPassword => 'パスワードを忘れた';

  @override
  String get email => 'メール';

  @override
  String get password => 'パスワード';

  @override
  String get confirmPassword => 'パスワード確認';

  @override
  String get firstName => '名前';

  @override
  String get lastName => '姓';

  @override
  String get phoneNumber => '電話番号';

  @override
  String get address => '住所';

  @override
  String get city => '市';

  @override
  String get country => '国';

  @override
  String get language => '言語';

  @override
  String get theme => 'テーマ';

  @override
  String get darkMode => 'ダークモード';

  @override
  String get lightMode => 'ライトモード';

  @override
  String get systemMode => 'システム設定';

  @override
  String lastUpdated(DateTime date) {
    final intl.DateFormat dateDateFormat = intl.DateFormat.yMd(localeName);
    final String dateString = dateDateFormat.format(date);

    return '最終更新：$dateString';
  }

  @override
  String get version => 'バージョン';

  @override
  String get privacyPolicy => 'プライバシーポリシー';

  @override
  String get termsOfService => '利用規約';

  @override
  String get contactUs => 'お問い合わせ';

  @override
  String get feedback => 'フィードバック';

  @override
  String get rateApp => 'アプリを評価';

  @override
  String get shareApp => 'アプリを共有';

  @override
  String get appName => 'core_services_v1';

  @override
  String get appDescription => 'Flutterで構築されたモダンなアプリケーション';
}
