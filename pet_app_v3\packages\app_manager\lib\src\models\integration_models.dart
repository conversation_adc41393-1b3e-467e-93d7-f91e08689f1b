/*
---------------------------------------------------------------
File name:          integration_models.dart
Author:             lgnorant-lu
Date created:       2025-07-24
Last modified:      2025-07-24
Dart Version:       3.2+
Description:        App Manager 集成模型
---------------------------------------------------------------
Change History:
    2025-07-24: Phase 5.0.9 - 实现App Manager集成模型;
---------------------------------------------------------------
*/

import 'package:equatable/equatable.dart';
import 'module_permission.dart';
import 'module_resource.dart';
import 'monitoring_dashboard.dart';

/// 集成结果
class IntegrationResult extends Equatable {
  /// 是否成功
  final bool isSuccess;
  
  /// 结果消息
  final String message;
  
  /// 操作时间
  final DateTime timestamp;

  const IntegrationResult._({
    required this.isSuccess,
    required this.message,
    required this.timestamp,
  });

  /// 创建成功结果
  factory IntegrationResult.success(String message) {
    return IntegrationResult._(
      isSuccess: true,
      message: message,
      timestamp: DateTime.now(),
    );
  }

  /// 创建失败结果
  factory IntegrationResult.failure(String message) {
    return IntegrationResult._(
      isSuccess: false,
      message: message,
      timestamp: DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [isSuccess, message, timestamp];

  @override
  String toString() {
    return 'IntegrationResult(isSuccess: $isSuccess, message: $message)';
  }
}

/// 模块注册结果
class ModuleRegistrationResult extends Equatable {
  /// 模块ID
  final String moduleId;
  
  /// 是否成功
  final bool isSuccess;
  
  /// 结果消息
  final String message;
  
  /// 失败的权限列表
  final List<ModulePermission> failedPermissions;
  
  /// 操作时间
  final DateTime timestamp;

  const ModuleRegistrationResult._({
    required this.moduleId,
    required this.isSuccess,
    required this.message,
    required this.failedPermissions,
    required this.timestamp,
  });

  /// 创建成功结果
  factory ModuleRegistrationResult.success(
    String moduleId,
    String message,
    List<ModulePermission> failedPermissions,
  ) {
    return ModuleRegistrationResult._(
      moduleId: moduleId,
      isSuccess: true,
      message: message,
      failedPermissions: failedPermissions,
      timestamp: DateTime.now(),
    );
  }

  /// 创建失败结果
  factory ModuleRegistrationResult.failure(String moduleId, String message) {
    return ModuleRegistrationResult._(
      moduleId: moduleId,
      isSuccess: false,
      message: message,
      failedPermissions: [],
      timestamp: DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [moduleId, isSuccess, message, failedPermissions, timestamp];

  @override
  String toString() {
    return 'ModuleRegistrationResult(moduleId: $moduleId, isSuccess: $isSuccess)';
  }
}

/// 模块注销结果
class ModuleUnregistrationResult extends Equatable {
  /// 模块ID
  final String moduleId;
  
  /// 是否成功
  final bool isSuccess;
  
  /// 结果消息
  final String message;
  
  /// 操作时间
  final DateTime timestamp;

  const ModuleUnregistrationResult._({
    required this.moduleId,
    required this.isSuccess,
    required this.message,
    required this.timestamp,
  });

  /// 创建成功结果
  factory ModuleUnregistrationResult.success(String moduleId, String message) {
    return ModuleUnregistrationResult._(
      moduleId: moduleId,
      isSuccess: true,
      message: message,
      timestamp: DateTime.now(),
    );
  }

  /// 创建失败结果
  factory ModuleUnregistrationResult.failure(String moduleId, String message) {
    return ModuleUnregistrationResult._(
      moduleId: moduleId,
      isSuccess: false,
      message: message,
      timestamp: DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [moduleId, isSuccess, message, timestamp];

  @override
  String toString() {
    return 'ModuleUnregistrationResult(moduleId: $moduleId, isSuccess: $isSuccess)';
  }
}

/// 集成状态
class IntegrationStatus extends Equatable {
  /// 是否已集成
  final bool isIntegrated;
  
  /// 模块总数
  final int totalModules;
  
  /// 运行中的模块数
  final int runningModules;
  
  /// 系统资源概览
  final SystemResourceOverview systemResourceOverview;
  
  /// 权限统计信息
  final PermissionStatistics permissionStatistics;
  
  /// 最后更新时间
  final DateTime lastUpdated;

  const IntegrationStatus({
    required this.isIntegrated,
    required this.totalModules,
    required this.runningModules,
    required this.systemResourceOverview,
    required this.permissionStatistics,
    required this.lastUpdated,
  });

  @override
  List<Object?> get props => [
    isIntegrated,
    totalModules,
    runningModules,
    systemResourceOverview,
    permissionStatistics,
    lastUpdated,
  ];

  @override
  String toString() {
    return 'IntegrationStatus(isIntegrated: $isIntegrated, totalModules: $totalModules)';
  }
}

/// 系统健康报告
class SystemHealthReport extends Equatable {
  /// 总体健康状态
  final HealthStatus overallHealth;
  
  /// 健康检查项目
  final List<HealthCheckItem> healthChecks;
  
  /// 系统警报
  final List<SystemAlert> systemAlerts;
  
  /// 集成状态
  final IntegrationStatus integrationStatus;
  
  /// 报告时间
  final DateTime timestamp;

  const SystemHealthReport({
    required this.overallHealth,
    required this.healthChecks,
    required this.systemAlerts,
    required this.integrationStatus,
    required this.timestamp,
  });

  /// 创建失败报告
  factory SystemHealthReport.failed() {
    return SystemHealthReport(
      overallHealth: HealthStatus.critical,
      healthChecks: [
        const HealthCheckItem(
          name: '系统检查',
          status: HealthStatus.critical,
          message: '健康检查执行失败',
          details: {},
        ),
      ],
      systemAlerts: [],
      integrationStatus: IntegrationStatus(
        isIntegrated: false,
        totalModules: 0,
        runningModules: 0,
        systemResourceOverview: SystemResourceOverview.empty(),
        permissionStatistics: const PermissionStatistics(
          totalModules: 0,
          totalPermissions: 0,
          permissionCounts: {},
          recentAuditLogs: 0,
        ),
        lastUpdated: DateTime.now(),
      ),
      timestamp: DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [
    overallHealth,
    healthChecks,
    systemAlerts,
    integrationStatus,
    timestamp,
  ];

  @override
  String toString() {
    return 'SystemHealthReport(overallHealth: $overallHealth, alerts: ${systemAlerts.length})';
  }
}

/// 集成事件
class IntegrationEvent extends Equatable {
  /// 事件类型
  final IntegrationEventType type;
  
  /// 模块ID（可选）
  final String? moduleId;
  
  /// 相关数据
  final dynamic data;
  
  /// 事件时间
  final DateTime timestamp;

  const IntegrationEvent._({
    required this.type,
    this.moduleId,
    this.data,
    required this.timestamp,
  });

  /// 初始化完成事件
  factory IntegrationEvent.initialized() {
    return IntegrationEvent._(
      type: IntegrationEventType.initialized,
      timestamp: DateTime.now(),
    );
  }

  /// 初始化失败事件
  factory IntegrationEvent.failed(String error) {
    return IntegrationEvent._(
      type: IntegrationEventType.failed,
      data: error,
      timestamp: DateTime.now(),
    );
  }

  /// 模块注册事件
  factory IntegrationEvent.moduleRegistered(String moduleId) {
    return IntegrationEvent._(
      type: IntegrationEventType.moduleRegistered,
      moduleId: moduleId,
      timestamp: DateTime.now(),
    );
  }

  /// 模块注销事件
  factory IntegrationEvent.moduleUnregistered(String moduleId) {
    return IntegrationEvent._(
      type: IntegrationEventType.moduleUnregistered,
      moduleId: moduleId,
      timestamp: DateTime.now(),
    );
  }

  /// 健康检查完成事件
  factory IntegrationEvent.healthCheckCompleted(SystemHealthReport report) {
    return IntegrationEvent._(
      type: IntegrationEventType.healthCheckCompleted,
      data: report,
      timestamp: DateTime.now(),
    );
  }

  /// 清理完成事件
  factory IntegrationEvent.cleaned() {
    return IntegrationEvent._(
      type: IntegrationEventType.cleaned,
      timestamp: DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [type, moduleId, data, timestamp];

  @override
  String toString() {
    return 'IntegrationEvent(type: $type, moduleId: $moduleId)';
  }
}

/// 集成事件类型
enum IntegrationEventType {
  /// 初始化完成
  initialized,
  
  /// 初始化失败
  failed,
  
  /// 模块注册
  moduleRegistered,
  
  /// 模块注销
  moduleUnregistered,
  
  /// 健康检查完成
  healthCheckCompleted,
  
  /// 清理完成
  cleaned,
}
