/*
---------------------------------------------------------------
File name:          developer_plugin_management_page.dart
Author:             lgnorant-lu
Date created:       2025-07-31
Last modified:      2025-07-31
Dart Version:       3.2+
Description:        开发者插件管理页面 - 调用Plugin System接口
---------------------------------------------------------------
职责说明：
    Creative Workshop → 插件开发工作台
    - 专注于开发者视角的插件管理
    - 调用Plugin System的标准接口
    - 与App Manager的用户插件管理功能区分
---------------------------------------------------------------
Change History:
    2025-07-31: 重构为Plugin System接口调用，明确职责边界;
---------------------------------------------------------------
*/

import 'package:flutter/material.dart';
import 'package:plugin_system/plugin_system.dart';

/// 开发者插件管理页面
/// 
/// 专注于开发者视角的插件管理，调用Plugin System的标准接口
class DeveloperPluginManagementPage extends StatefulWidget {
  const DeveloperPluginManagementPage({super.key});

  @override
  State<DeveloperPluginManagementPage> createState() => _DeveloperPluginManagementPageState();
}

class _DeveloperPluginManagementPageState extends State<DeveloperPluginManagementPage> {
  late final PluginRegistry _pluginRegistry;
  List<Plugin> _registeredPlugins = [];
  
  // 统计数据
  int _totalRegistered = 0;
  int _totalLoaded = 0;
  int _totalRunning = 0;

  @override
  void initState() {
    super.initState();
    _pluginRegistry = PluginRegistry.instance;
    _loadPluginData();
  }

  /// 加载插件数据
  void _loadPluginData() {
    setState(() {
      _registeredPlugins = _pluginRegistry.getAllPlugins();
      _totalRegistered = _registeredPlugins.length;
      _totalLoaded = _registeredPlugins.where((p) => 
        _pluginRegistry.getState(p.id) == PluginState.loaded ||
        _pluginRegistry.getState(p.id) == PluginState.initialized ||
        _pluginRegistry.getState(p.id) == PluginState.running
      ).length;
      _totalRunning = _registeredPlugins.where((p) => 
        _pluginRegistry.getState(p.id) == PluginState.running
      ).length;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('开发者插件管理'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadPluginData,
            tooltip: '刷新',
          ),
        ],
      ),
      body: Column(
        children: [
          // 统计面板
          _buildStatsPanel(),
          
          // 插件列表
          Expanded(
            child: _buildPluginList(),
          ),
        ],
      ),
    );
  }

  /// 构建统计面板
  Widget _buildStatsPanel() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('已注册', _totalRegistered, Icons.app_registration),
          _buildStatItem('已加载', _totalLoaded, Icons.check_circle),
          _buildStatItem('运行中', _totalRunning, Icons.play_circle),
        ],
      ),
    );
  }

  /// 构建统计项
  Widget _buildStatItem(String label, int count, IconData icon) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 32, color: Theme.of(context).colorScheme.primary),
        const SizedBox(height: 8),
        Text(
          count.toString(),
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  /// 构建插件列表
  Widget _buildPluginList() {
    if (_registeredPlugins.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.extension_off, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              '暂无已注册的插件',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              '插件将在开发时自动注册',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _registeredPlugins.length,
      itemBuilder: (context, index) {
        final plugin = _registeredPlugins[index];
        return _buildPluginCard(plugin);
      },
    );
  }

  /// 构建插件卡片
  Widget _buildPluginCard(Plugin plugin) {
    final state = _pluginRegistry.getState(plugin.id);
    final metadata = _pluginRegistry.getMetadata(plugin.id);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getStateColor(state),
          child: Icon(
            _getStateIcon(state),
            color: Colors.white,
          ),
        ),
        title: Text(plugin.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(plugin.description),
            const SizedBox(height: 4),
            Row(
              children: [
                Chip(
                  label: Text(state.toString().split('.').last),
                  backgroundColor: _getStateColor(state).withOpacity(0.2),
                  labelStyle: TextStyle(color: _getStateColor(state)),
                ),
                const SizedBox(width: 8),
                Text(
                  'v${plugin.version}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (action) => _handlePluginAction(action, plugin),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'details',
              child: ListTile(
                leading: Icon(Icons.info),
                title: Text('详情'),
                dense: true,
              ),
            ),
            if (state == PluginState.loaded)
              const PopupMenuItem(
                value: 'start',
                child: ListTile(
                  leading: Icon(Icons.play_arrow),
                  title: Text('启动'),
                  dense: true,
                ),
              ),
            if (state == PluginState.running)
              const PopupMenuItem(
                value: 'stop',
                child: ListTile(
                  leading: Icon(Icons.stop),
                  title: Text('停止'),
                  dense: true,
                ),
              ),
          ],
        ),
        isThreeLine: true,
      ),
    );
  }

  /// 获取状态颜色
  Color _getStateColor(PluginState state) {
    switch (state) {
      case PluginState.loaded:
        return Colors.blue;
      case PluginState.initialized:
        return Colors.orange;
      case PluginState.running:
        return Colors.green;
      case PluginState.paused:
        return Colors.amber;
      case PluginState.stopped:
        return Colors.grey;
      case PluginState.error:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  /// 获取状态图标
  IconData _getStateIcon(PluginState state) {
    switch (state) {
      case PluginState.loaded:
        return Icons.check;
      case PluginState.initialized:
        return Icons.settings;
      case PluginState.running:
        return Icons.play_arrow;
      case PluginState.paused:
        return Icons.pause;
      case PluginState.stopped:
        return Icons.stop;
      case PluginState.error:
        return Icons.error;
      default:
        return Icons.help;
    }
  }

  /// 处理插件操作
  void _handlePluginAction(String action, Plugin plugin) {
    switch (action) {
      case 'details':
        _showPluginDetails(plugin);
        break;
      case 'start':
        // TODO(Phase 5.0.7): 实现插件启动功能
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('启动插件: ${plugin.name}')),
        );
        break;
      case 'stop':
        // TODO(Phase 5.0.7): 实现插件停止功能
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('停止插件: ${plugin.name}')),
        );
        break;
    }
  }

  /// 显示插件详情
  void _showPluginDetails(Plugin plugin) {
    final metadata = _pluginRegistry.getMetadata(plugin.id);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(plugin.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('ID: ${plugin.id}'),
            Text('版本: ${plugin.version}'),
            Text('作者: ${plugin.author}'),
            Text('描述: ${plugin.description}'),
            Text('类型: ${plugin.category}'),
            Text('状态: ${_pluginRegistry.getState(plugin.id)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}
