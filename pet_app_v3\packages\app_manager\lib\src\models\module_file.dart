/*
---------------------------------------------------------------
File name:          module_file.dart
Author:             lgnorant-lu
Date created:       2025-07-24
Last modified:      2025-07-24
Dart Version:       3.2+
Description:        模块文件模型
---------------------------------------------------------------
Change History:
    2025-07-24: Phase ******* - 实现模块文件模型;
---------------------------------------------------------------
*/

import 'package:equatable/equatable.dart';

/// 模块文件
class ModuleFile extends Equatable {
  /// 所属模块ID
  final String moduleId;
  
  /// 文件路径
  final String path;
  
  /// 文件名
  final String name;
  
  /// 文件类型
  final FileType type;
  
  /// 文件大小（字节）
  final int size;
  
  /// 文件内容
  final String? content;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 修改时间
  final DateTime modifiedAt;
  
  /// 是否为目录
  final bool isDirectory;
  
  /// 文件权限
  final FilePermissions permissions;

  const ModuleFile({
    required this.moduleId,
    required this.path,
    required this.name,
    required this.type,
    required this.size,
    this.content,
    required this.createdAt,
    required this.modifiedAt,
    this.isDirectory = false,
    this.permissions = const FilePermissions(),
  });

  /// 创建文件副本
  ModuleFile copyWith({
    String? moduleId,
    String? path,
    String? name,
    FileType? type,
    int? size,
    String? content,
    DateTime? createdAt,
    DateTime? modifiedAt,
    bool? isDirectory,
    FilePermissions? permissions,
  }) {
    return ModuleFile(
      moduleId: moduleId ?? this.moduleId,
      path: path ?? this.path,
      name: name ?? this.name,
      type: type ?? this.type,
      size: size ?? this.size,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      modifiedAt: modifiedAt ?? this.modifiedAt,
      isDirectory: isDirectory ?? this.isDirectory,
      permissions: permissions ?? this.permissions,
    );
  }

  /// 从 JSON 创建
  factory ModuleFile.fromJson(Map<String, dynamic> json) {
    return ModuleFile(
      moduleId: json['moduleId'] as String,
      path: json['path'] as String,
      name: json['name'] as String,
      type: FileType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => FileType.unknown,
      ),
      size: json['size'] as int,
      content: json['content'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      modifiedAt: DateTime.parse(json['modifiedAt'] as String),
      isDirectory: json['isDirectory'] as bool? ?? false,
      permissions: json['permissions'] != null
          ? FilePermissions.fromJson(json['permissions'] as Map<String, dynamic>)
          : const FilePermissions(),
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'moduleId': moduleId,
      'path': path,
      'name': name,
      'type': type.name,
      'size': size,
      'content': content,
      'createdAt': createdAt.toIso8601String(),
      'modifiedAt': modifiedAt.toIso8601String(),
      'isDirectory': isDirectory,
      'permissions': permissions.toJson(),
    };
  }

  /// 获取文件扩展名
  String get extension {
    final parts = name.split('.');
    return parts.length > 1 ? parts.last.toLowerCase() : '';
  }

  /// 获取格式化的文件大小
  String get formattedSize {
    if (size < 1024) {
      return '${size}B';
    } else if (size < 1024 * 1024) {
      return '${(size / 1024).toStringAsFixed(1)}KB';
    } else if (size < 1024 * 1024 * 1024) {
      return '${(size / (1024 * 1024)).toStringAsFixed(1)}MB';
    } else {
      return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
    }
  }

  /// 是否为文本文件
  bool get isTextFile {
    return type == FileType.text ||
           type == FileType.markdown ||
           type == FileType.json ||
           type == FileType.yaml ||
           type == FileType.dart ||
           type == FileType.javascript ||
           type == FileType.css ||
           type == FileType.html;
  }

  /// 是否可编辑
  bool get isEditable {
    return isTextFile && permissions.canWrite;
  }

  @override
  List<Object?> get props => [
    moduleId,
    path,
    name,
    type,
    size,
    content,
    createdAt,
    modifiedAt,
    isDirectory,
    permissions,
  ];

  @override
  String toString() {
    return 'ModuleFile(moduleId: $moduleId, path: $path, name: $name)';
  }
}

/// 文件类型
enum FileType {
  /// 文本文件
  text,
  
  /// Markdown 文件
  markdown,
  
  /// JSON 文件
  json,
  
  /// YAML 文件
  yaml,
  
  /// Dart 文件
  dart,
  
  /// JavaScript 文件
  javascript,
  
  /// CSS 文件
  css,
  
  /// HTML 文件
  html,
  
  /// 图片文件
  image,
  
  /// 音频文件
  audio,
  
  /// 视频文件
  video,
  
  /// 压缩文件
  archive,
  
  /// 二进制文件
  binary,
  
  /// 未知类型
  unknown,
}

/// 文件类型扩展
extension FileTypeExtension on FileType {
  /// 获取类型描述
  String get description {
    switch (this) {
      case FileType.text:
        return '文本文件';
      case FileType.markdown:
        return 'Markdown 文件';
      case FileType.json:
        return 'JSON 文件';
      case FileType.yaml:
        return 'YAML 文件';
      case FileType.dart:
        return 'Dart 文件';
      case FileType.javascript:
        return 'JavaScript 文件';
      case FileType.css:
        return 'CSS 文件';
      case FileType.html:
        return 'HTML 文件';
      case FileType.image:
        return '图片文件';
      case FileType.audio:
        return '音频文件';
      case FileType.video:
        return '视频文件';
      case FileType.archive:
        return '压缩文件';
      case FileType.binary:
        return '二进制文件';
      case FileType.unknown:
        return '未知类型';
    }
  }

  /// 获取常见扩展名
  List<String> get extensions {
    switch (this) {
      case FileType.text:
        return ['txt', 'log'];
      case FileType.markdown:
        return ['md', 'markdown'];
      case FileType.json:
        return ['json'];
      case FileType.yaml:
        return ['yaml', 'yml'];
      case FileType.dart:
        return ['dart'];
      case FileType.javascript:
        return ['js', 'mjs'];
      case FileType.css:
        return ['css'];
      case FileType.html:
        return ['html', 'htm'];
      case FileType.image:
        return ['png', 'jpg', 'jpeg', 'gif', 'svg', 'webp'];
      case FileType.audio:
        return ['mp3', 'wav', 'ogg', 'aac'];
      case FileType.video:
        return ['mp4', 'avi', 'mov', 'webm'];
      case FileType.archive:
        return ['zip', 'tar', 'gz', 'rar'];
      case FileType.binary:
        return ['exe', 'dll', 'so'];
      case FileType.unknown:
        return [];
    }
  }

  /// 根据扩展名获取文件类型
  static FileType fromExtension(String extension) {
    final ext = extension.toLowerCase();
    
    for (final type in FileType.values) {
      if (type.extensions.contains(ext)) {
        return type;
      }
    }
    
    return FileType.unknown;
  }
}

/// 文件权限
class FilePermissions extends Equatable {
  /// 可读
  final bool canRead;
  
  /// 可写
  final bool canWrite;
  
  /// 可执行
  final bool canExecute;

  const FilePermissions({
    this.canRead = true,
    this.canWrite = true,
    this.canExecute = false,
  });

  /// 从 JSON 创建
  factory FilePermissions.fromJson(Map<String, dynamic> json) {
    return FilePermissions(
      canRead: json['canRead'] as bool? ?? true,
      canWrite: json['canWrite'] as bool? ?? true,
      canExecute: json['canExecute'] as bool? ?? false,
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'canRead': canRead,
      'canWrite': canWrite,
      'canExecute': canExecute,
    };
  }

  /// 获取权限字符串表示
  String get permissionString {
    return '${canRead ? 'r' : '-'}${canWrite ? 'w' : '-'}${canExecute ? 'x' : '-'}';
  }

  @override
  List<Object?> get props => [canRead, canWrite, canExecute];

  @override
  String toString() {
    return 'FilePermissions($permissionString)';
  }
}
