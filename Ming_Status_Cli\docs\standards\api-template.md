# API 文档模板

> **文档类型**: 模板文档  
> **目标受众**: API文档编写者  
> **维护者**: Ming Status CLI 团队  
> **最后更新**: 2025-07-13  
> **版本**: 1.0.0

## 📋 概述

本文档提供了 Ming Status CLI 项目中 API 文档的标准模板和编写规范。

## 🎯 API 文档结构

### 1. 文档头部

```markdown
# [模块名称] API 文档

> **文档类型**: API文档  
> **目标受众**: 开发者、集成者  
> **维护者**: [负责人姓名]  
> **最后更新**: [YYYY-MM-DD]  
> **API版本**: [版本号]

## 📋 概述

[API概述，1-2段描述API的目的、主要功能和使用场景]

## 🔗 快速导航

- [核心类](#核心类)
- [数据模型](#数据模型)
- [工具函数](#工具函数)
- [异常处理](#异常处理)
- [示例代码](#示例代码)
```

### 2. 类文档模板

```markdown
## 🔧 核心类

### [类名]

> **类型**: [抽象类/具体类/接口/混入]  
> **继承**: [父类名称]  
> **实现**: [接口名称]  
> **混入**: [混入名称]

#### 概述

[类的简要描述，说明类的职责和主要功能]

#### 构造函数

##### 默认构造函数

```dart
/// [类描述]
/// 
/// 创建[类名]的实例。
/// 
/// 参数:
/// - [参数1]: [参数描述，包括类型、是否必需、默认值]
/// - [参数2]: [参数描述]
/// 
/// 异常:
/// - [异常类型]: [抛出条件]
/// 
/// 示例:
/// ```dart
/// final instance = [类名](
///   [参数1]: [示例值],
///   [参数2]: [示例值],
/// );
/// ```
[类名]({
  required [类型] [参数1],
  [类型]? [参数2] = [默认值],
});
```

##### 命名构造函数

```dart
/// [构造函数描述]
/// 
/// 从[数据源]创建[类名]实例。
/// 
/// 参数:
/// - [参数]: [参数描述]
/// 
/// 返回值:
/// - [类名]实例
/// 
/// 异常:
/// - [异常类型]: [抛出条件]
/// 
/// 示例:
/// ```dart
/// final instance = [类名].[构造函数名]([参数]);
/// ```
factory [类名].[构造函数名]([参数类型] [参数名]);
```

#### 属性

##### [属性名]

```dart
/// [属性描述]
/// 
/// 类型: [属性类型]
/// 访问: [只读/读写]
/// 默认值: [默认值]
/// 
/// 示例:
/// ```dart
/// print(instance.[属性名]); // 输出: [示例输出]
/// ```
[类型] get [属性名];
```

#### 方法

##### [方法名]

```dart
/// [方法描述]
/// 
/// [详细说明，包括方法的作用、使用场景、注意事项等]
/// 
/// 参数:
/// - [参数1]: [参数描述，包括类型、是否必需、取值范围等]
/// - [参数2]: [参数描述]
/// 
/// 返回值:
/// - [返回值类型]: [返回值描述]
/// 
/// 异常:
/// - [异常类型]: [抛出条件和处理建议]
/// 
/// 复杂度:
/// - 时间复杂度: O([复杂度])
/// - 空间复杂度: O([复杂度])
/// 
/// 示例:
/// ```dart
/// // 基本用法
/// final result = await instance.[方法名]([参数]);
/// print(result); // 输出: [示例输出]
/// 
/// // 高级用法
/// final advancedResult = await instance.[方法名](
///   [参数1]: [高级参数值],
///   [参数2]: [高级参数值],
/// );
/// ```
/// 
/// 另请参阅:
/// - [相关方法1]
/// - [相关方法2]
Future<[返回类型]> [方法名]([参数列表]);
```
```

### 3. 数据模型模板

```markdown
## 📊 数据模型

### [模型名]

> **类型**: [数据类/值对象/实体]  
> **不可变**: [是/否]  
> **序列化**: [支持/不支持]

#### 概述

[模型的简要描述，说明模型代表的数据和用途]

#### 字段

| 字段名 | 类型 | 必需 | 默认值 | 描述 |
|--------|------|------|--------|------|
| [字段1] | [类型] | ✅ | - | [字段描述] |
| [字段2] | [类型] | ❌ | [默认值] | [字段描述] |

#### 构造函数

```dart
/// 创建[模型名]实例
/// 
/// 参数:
/// - [字段1]: [字段描述]
/// - [字段2]: [字段描述]
/// 
/// 示例:
/// ```dart
/// final model = [模型名](
///   [字段1]: [示例值],
///   [字段2]: [示例值],
/// );
/// ```
const [模型名]({
  required this.[字段1],
  this.[字段2] = [默认值],
});
```

#### 工厂构造函数

```dart
/// 从JSON创建[模型名]实例
/// 
/// 参数:
/// - json: JSON对象
/// 
/// 返回值:
/// - [模型名]实例
/// 
/// 异常:
/// - FormatException: JSON格式错误
/// 
/// 示例:
/// ```dart
/// final json = {'[字段1]': '[值]', '[字段2]': '[值]'};
/// final model = [模型名].fromJson(json);
/// ```
factory [模型名].fromJson(Map<String, dynamic> json);
```

#### 方法

```dart
/// 转换为JSON对象
/// 
/// 返回值:
/// - Map<String, dynamic>: JSON对象
/// 
/// 示例:
/// ```dart
/// final json = model.toJson();
/// print(json); // 输出: {[字段1]: [值], [字段2]: [值]}
/// ```
Map<String, dynamic> toJson();

/// 创建副本
/// 
/// 参数:
/// - [字段1]: [可选]新的[字段1]值
/// - [字段2]: [可选]新的[字段2]值
/// 
/// 返回值:
/// - [模型名]: 新的实例
/// 
/// 示例:
/// ```dart
/// final newModel = model.copyWith([字段1]: [新值]);
/// ```
[模型名] copyWith({
  [类型]? [字段1],
  [类型]? [字段2],
});
```
```

### 4. 枚举模板

```markdown
### [枚举名]

> **类型**: 枚举  
> **值类型**: [String/int/自定义]

#### 概述

[枚举的简要描述，说明枚举代表的概念和用途]

#### 枚举值

| 值 | 描述 | 使用场景 |
|----|------|----------|
| [值1] | [描述] | [使用场景] |
| [值2] | [描述] | [使用场景] |

#### 定义

```dart
/// [枚举描述]
enum [枚举名] {
  /// [值1描述]
  [值1],
  
  /// [值2描述]
  [值2],
}
```

#### 扩展方法

```dart
/// [枚举名]的扩展方法
extension [枚举名]Extension on [枚举名] {
  /// 获取显示名称
  String get displayName {
    switch (this) {
      case [枚举名].[值1]:
        return '[显示名称1]';
      case [枚举名].[值2]:
        return '[显示名称2]';
    }
  }
}
```
```

### 5. 异常文档模板

```markdown
## ⚠️ 异常处理

### [异常类名]

> **继承**: [父异常类]  
> **类型**: [检查异常/运行时异常]

#### 概述

[异常的简要描述，说明异常的触发条件和含义]

#### 构造函数

```dart
/// 创建[异常类名]实例
/// 
/// 参数:
/// - message: 错误消息
/// - cause: [可选]原因异常
/// 
/// 示例:
/// ```dart
/// throw [异常类名]('错误消息');
/// ```
const [异常类名](this.message, [this.cause]);
```

#### 处理示例

```dart
try {
  // 可能抛出异常的代码
  await someOperation();
} on [异常类名] catch (e) {
  // 处理特定异常
  print('发生[异常类名]: ${e.message}');
  // 恢复策略
} catch (e) {
  // 处理其他异常
  print('未知错误: $e');
  rethrow;
}
```
```

## 📝 编写规范

### 1. 文档注释规范

```dart
/// 简要描述（一句话概括功能）
/// 
/// 详细描述（可选，多段落说明）
/// 
/// 参数:
/// - [参数名]: [参数描述]
/// 
/// 返回值:
/// - [返回值描述]
/// 
/// 异常:
/// - [异常类型]: [抛出条件]
/// 
/// 示例:
/// ```dart
/// // 代码示例
/// ```
/// 
/// 另请参阅:
/// - [相关API]
```

### 2. 示例代码规范

- 提供完整可运行的示例
- 包含基本用法和高级用法
- 添加注释说明关键步骤
- 展示错误处理方式

### 3. 参数文档规范

- 说明参数类型和是否必需
- 描述参数的作用和取值范围
- 提供默认值信息
- 说明参数之间的关系

### 4. 返回值文档规范

- 明确返回值类型
- 描述返回值的含义
- 说明可能的返回值范围
- 提供返回值示例

---

> **维护者**: Ming Status CLI 团队  
> **最后更新**: 2025-07-13  
> **版本**: 1.0.0
