/*
---------------------------------------------------------------
File name:          core_services_v1_page.dart
Author:             Ming Status CLI
Date created:       2025-07-14
Last modified:      2025-07-14
Dart Version:       3.2+
Description:        core_services_v1 页面组件
---------------------------------------------------------------
Change History:
    2025-07-14: Initial creation - core_services_v1 页面组件;
---------------------------------------------------------------
*/

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:provider/provider.dart';

import '../providers/core_services_v1_provider.dart';
import '../constants/core_services_v1_constants.dart';
import '../utils/core_services_v1_utils.dart';

/// core_services_v1页面组件
///
/// ## 使用示例
///
/// ```dart
/// Navigator.push(
/// ```
///
/// ```dart
///   context,
/// ```
///
/// ```dart
///   MaterialPageRoute(builder: (context) => CoreServicesV1Page()),
/// ```
///
/// ```dart
/// )
/// ```
///
class CoreServicesV1Page extends StatefulWidget {
  /// 创建CoreServicesV1Page实例
  const CoreServicesV1Page({super.key});

  @override
  State<CoreServicesV1Page> createState() => _CoreServicesV1PageState();
}

class _CoreServicesV1PageState extends State<CoreServicesV1Page> {
  late CoreServicesV1Provider _provider;

  @override
  void initState() {
    super.initState();
    _provider = context.read<CoreServicesV1Provider>();
    _initializeData();
  }

  Future<void> _initializeData() async {
    await _provider.initialize();
    if (mounted) {
      await _provider.loadData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CoreServicesV1Provider>(
      builder: (context, provider, child) {
        return Scaffold(
        appBar: AppBar(
          title: Text('CoreServicesV1'),
        ),
        body: provider.isLoading
            ? const Center(child: CircularProgressIndicator())
            : provider.error != null
                ? _buildErrorWidget(provider.error!)
                : _buildContent(),
        floatingActionButton: FloatingActionButton(
          onPressed: () => _provider.loadData(),
          child: const Icon(Icons.refresh),
        ),
        );
      },
    );
  }

  Widget _buildContent() {
    final data = _provider.data;
    
    if (data.isEmpty) {
      return const Center(
        child: Text('暂无数据'),
      );
    }
    
    return ListView.builder(
      itemCount: data.length,
      itemBuilder: (context, index) {
        final item = data[index];
        return ListTile(
          title: Text(item['name'] ?? 'Unknown'),
          subtitle: Text(item['description'] ?? ''),
          onTap: () => _provider.selectItem(item),
        );
      },
    );
  }

  Widget _buildErrorWidget(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(error),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => _provider.loadData(),
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }
}
