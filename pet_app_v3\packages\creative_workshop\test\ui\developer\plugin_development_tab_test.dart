import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:creative_workshop/src/ui/developer/plugin_development_tab.dart';

void main() {
  group('PluginDevelopmentTab Tests', () {
    testWidgets('应该显示开发工具选择器', (WidgetTester tester) async {
      // 设置更大的测试屏幕尺寸
      await tester.binding.setSurfaceSize(const Size(1200, 800));

      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: PluginDevelopmentTab(),
          ),
        ),
      );

      // 验证基础UI元素
      expect(find.text('选择项目开始开发'), findsOneWidget);
      expect(find.text('创建新项目'), findsOneWidget);
      expect(find.byIcon(Icons.code), findsOneWidget);
    });

    testWidgets('应该显示项目选择下拉菜单', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: PluginDevelopmentTab(),
          ),
        ),
      );

      // 查找项目选择下拉菜单
      expect(find.byType(DropdownButton<String?>), findsOneWidget);
    });

    testWidgets('选择项目后应该显示开发工具', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: PluginDevelopmentTab(),
          ),
        ),
      );

      // 点击项目选择下拉菜单
      await tester.tap(find.byType(DropdownButton<String?>));
      await tester.pumpAndSettle();

      // 选择第一个项目
      await tester.tap(find.text('高级画笔工具').last);
      await tester.pumpAndSettle();

      // 验证开发工具选择器出现
      expect(find.text('代码编辑器'), findsOneWidget);
      expect(find.text('调试器'), findsOneWidget);
      expect(find.text('测试工具'), findsOneWidget);
      expect(find.text('性能分析'), findsOneWidget);
    });

    testWidgets('应该能够切换开发工具', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: PluginDevelopmentTab(),
          ),
        ),
      );

      // 选择项目
      await tester.tap(find.byType(DropdownButton<String?>));
      await tester.pumpAndSettle();
      await tester.tap(find.text('高级画笔工具').last);
      await tester.pumpAndSettle();

      // 点击调试器工具
      await tester.tap(find.text('调试器'));
      await tester.pumpAndSettle();

      // 验证调试器界面显示
      expect(find.text('开始调试'), findsOneWidget);
      expect(find.text('停止调试'), findsOneWidget);
    });

    testWidgets('VS Code在线版按钮应该可以点击', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: PluginDevelopmentTab(),
          ),
        ),
      );

      // 选择项目
      await tester.tap(find.byType(DropdownButton<String?>));
      await tester.pumpAndSettle();
      await tester.tap(find.text('高级画笔工具').last);
      await tester.pumpAndSettle();

      // 确保在代码编辑器工具
      await tester.tap(find.text('代码编辑器'));
      await tester.pumpAndSettle();

      // 点击VS Code在线版按钮
      await tester.tap(find.text('VS Code 在线版'));
      await tester.pumpAndSettle();

      // 验证SnackBar显示
      expect(find.text('正在启动 VS Code 在线版...'), findsOneWidget);
    });

    testWidgets('测试工具应该显示测试文件列表', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: PluginDevelopmentTab(),
          ),
        ),
      );

      // 选择项目并切换到测试工具
      await tester.tap(find.byType(DropdownButton<String?>));
      await tester.pumpAndSettle();
      await tester.tap(find.text('高级画笔工具').last);
      await tester.pumpAndSettle();

      await tester.tap(find.text('测试工具'));
      await tester.pumpAndSettle();

      // 验证测试工具界面
      expect(find.text('运行所有测试'), findsOneWidget);
      expect(find.text('测试覆盖率'), findsOneWidget);
      expect(find.text('测试文件'), findsOneWidget);
      expect(find.text('测试输出'), findsOneWidget);
    });

    testWidgets('调试器应该显示断点管理', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: PluginDevelopmentTab(),
          ),
        ),
      );

      // 选择项目并切换到调试器
      await tester.tap(find.byType(DropdownButton<String?>));
      await tester.pumpAndSettle();
      await tester.tap(find.text('高级画笔工具').last);
      await tester.pumpAndSettle();

      await tester.tap(find.text('调试器'));
      await tester.pumpAndSettle();

      // 验证调试器界面
      expect(find.text('断点管理'), findsOneWidget);
      expect(find.text('调试操作'), findsOneWidget);
      expect(find.text('调试信息'), findsOneWidget);
      expect(find.text('变量'), findsOneWidget);
      expect(find.text('调用堆栈'), findsOneWidget);
    });

    testWidgets('性能分析器应该显示占位符', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: PluginDevelopmentTab(),
          ),
        ),
      );

      // 选择项目并切换到性能分析器
      await tester.tap(find.byType(DropdownButton<String?>));
      await tester.pumpAndSettle();
      await tester.tap(find.text('高级画笔工具').last);
      await tester.pumpAndSettle();

      await tester.tap(find.text('性能分析'));
      await tester.pumpAndSettle();

      // 验证性能分析器占位符
      expect(find.text('性能分析和优化'), findsOneWidget);
      expect(find.text('CPU 使用率'), findsOneWidget);
      expect(find.text('内存分析'), findsOneWidget);
    });

    testWidgets('创建新项目按钮应该可以点击', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: PluginDevelopmentTab(),
          ),
        ),
      );

      // 点击创建新项目按钮
      await tester.tap(find.text('创建新项目'));
      await tester.pumpAndSettle();

      // 验证SnackBar显示
      expect(find.text('新建项目功能即将推出...'), findsOneWidget);
    });

    testWidgets('开发工具类型枚举应该有正确的显示名称', (WidgetTester tester) async {
      // 测试枚举值
      expect(DevelopmentTool.codeEditor.displayName, equals('代码编辑器'));
      expect(DevelopmentTool.debugger.displayName, equals('调试器'));
      expect(DevelopmentTool.tester.displayName, equals('测试工具'));
      expect(DevelopmentTool.profiler.displayName, equals('性能分析'));
      expect(DevelopmentTool.documentation.displayName, equals('文档生成'));
      expect(DevelopmentTool.packaging.displayName, equals('打包工具'));
    });

    testWidgets('VS Code在线版界面应该显示正确的元素', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: PluginDevelopmentTab(),
          ),
        ),
      );

      // 选择项目
      await tester.tap(find.byType(DropdownButton<String?>));
      await tester.pumpAndSettle();
      await tester.tap(find.text('高级画笔工具').last);
      await tester.pumpAndSettle();

      // 验证VS Code界面元素
      expect(find.text('VS Code - 插件开发'), findsOneWidget);
      expect(find.text('资源管理器'), findsOneWidget);
      expect(find.text('main.dart'), findsOneWidget);
      expect(find.text('plugin_core.dart'), findsOneWidget);
    });

    testWidgets('测试运行按钮应该可以点击', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: PluginDevelopmentTab(),
          ),
        ),
      );

      // 选择项目并切换到测试工具
      await tester.tap(find.byType(DropdownButton<String?>));
      await tester.pumpAndSettle();
      await tester.tap(find.text('高级画笔工具').last);
      await tester.pumpAndSettle();

      await tester.tap(find.text('测试工具'));
      await tester.pumpAndSettle();

      // 点击运行所有测试按钮
      await tester.tap(find.text('运行所有测试'));
      await tester.pumpAndSettle();

      // 验证SnackBar显示
      expect(find.text('正在运行所有测试...'), findsOneWidget);
    });

    testWidgets('调试控制按钮应该可以点击', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: PluginDevelopmentTab(),
          ),
        ),
      );

      // 选择项目并切换到调试器
      await tester.tap(find.byType(DropdownButton<String?>));
      await tester.pumpAndSettle();
      await tester.tap(find.text('高级画笔工具').last);
      await tester.pumpAndSettle();

      await tester.tap(find.text('调试器'));
      await tester.pumpAndSettle();

      // 点击开始调试按钮
      await tester.tap(find.text('开始调试'));
      await tester.pumpAndSettle();

      // 验证SnackBar显示
      expect(find.text('正在启动调试器...'), findsOneWidget);
    });
  });
}
