# Flutter Gen资源代码生成配置
# 更多信息: https://pub.dev/packages/flutter_gen

# 输出配置
output: lib/generated/

# 代码生成配置
flutter_gen:
  # 输出目录
  output: lib/generated/assets.gen.dart
  
  # 代码格式
  line_length: 80
  
  # Null Safety
  null_safety: true
  
  # 生成文档注释
  generate_docs: true

  # === 资源配置 ===
  
  # 图片资源
  assets:
    enabled: true
    package_parameter_enabled: true
    style: dot-delimiter
    
    # 输出配置
    outputs:
      # 生成类名
      class_name: Assets
      # 生成文件名
      file_name: assets.gen.dart
      # 生成目录
      directory_name: generated
    
    # 排除文件
    exclude:
      - assets/images/.gitkeep
      - assets/icons/.gitkeep
      - assets/fonts/.gitkeep

  # 字体资源
  fonts:
    enabled: true
    
    # 输出配置
    outputs:
      class_name: FontFamily
      file_name: fonts.gen.dart

  # 颜色资源
  colors:
    enabled: true
    
    # 输入文件
    inputs:
      - assets/colors/colors.xml
      - assets/colors/material_colors.xml
      - assets/colors/brand_colors.json
    
    # 输出配置
    outputs:
      class_name: ColorName
      file_name: colors.gen.dart

# === 第三方库集成 ===

# Flutter SVG集成
flutter_svg:
  enabled: true
  # 生成SvgPicture.asset()方法
  generate_for_assets: true

# Flare集成
flare_flutter:
  enabled: false
  # 生成FlareActor()方法
  generate_for_assets: false

# Rive集成
rive:
  enabled: false
  # 生成RiveAnimation.asset()方法
  generate_for_assets: false

# Lottie集成
lottie:
  enabled: false
  # 生成Lottie.asset()方法
  generate_for_assets: false

# Cached Network Image集成
cached_network_image:
  enabled: true
  # 生成CachedNetworkImage()方法
  generate_for_network: true

# === 高级配置 ===

# 性能优化
performance:
  # 启用懒加载
  lazy_loading: true
  
  # 缓存策略
  cache:
    enabled: true
    max_size: 100
    ttl: 3600
  
  # 预加载配置
  preload:
    enabled: false
    critical_assets:
      - assets/images/logo.png
      - assets/images/splash.png

# 代码生成选项
code_generation:
  # 生成常量
  generate_constants: true
  
  # 生成扩展方法
  generate_extensions: true
  
  # 生成工厂方法
  generate_factories: true
  
  # 生成类型安全的访问器
  type_safe_accessors: true
  
  # 生成文档注释
  generate_documentation: true
  
  # 生成示例代码
  generate_examples: true

# 验证配置
validation:
  # 验证资源文件存在
  validate_assets: true
  
  # 验证文件格式
  validate_formats: true
  
  # 验证命名规范
  validate_naming: true
  
  # 严格模式
  strict_mode: false

# 调试配置
debug:
  # 详细输出
  verbose: false
  
  # 输出生成统计
  show_stats: true
  
  # 输出时间信息
  show_timing: false

# 自定义配置
custom:
  # 自定义资源处理器
  processors: []
  
  # 自定义模板
  templates: []
  
  # 自定义钩子
  hooks:
    pre_generate: []
    post_generate: []
