/*
---------------------------------------------------------------
File name:          core_services_v1_model_test.dart
Author:             Ming Status CLI
Date created:       2025-07-14
Last modified:      2025-07-14
Dart Version:       3.2+
Description:        core_services_v1 Model 单元测试
---------------------------------------------------------------
Change History:
    2025-07-14: Initial creation - core_services_v1 Model 单元测试;
---------------------------------------------------------------
*/

import 'package:test/test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:core_services_v1/lib/src/models/core_services_v1_model.dart';

void main() {
  group('CoreServicesV1Model 单元测试', () {
    late CoreServicesV1Model model;

    setUp(() {
      model = CoreServicesV1Model();
    });

    tearDown(() async {
    });

    test('创建模型实例应该成功', () {
      final model = CoreServicesV1Model(
        id: 1,
        name: '测试模型',
        description: '测试描述',
      );
      
      expect(model.id, 1);
      expect(model.name, '测试模型');
      expect(model.description, '测试描述');
    });

    test('JSON序列化应该正确', () {
      final model = CoreServicesV1Model(
        id: 1,
        name: '测试模型',
        description: '测试描述',
      );
      
      final json = model.toJson();
      expect(json['id'], 1);
      expect(json['name'], '测试模型');
      
      final fromJson = CoreServicesV1Model.fromJson(json);
      expect(fromJson.id, model.id);
      expect(fromJson.name, model.name);
    });

    test('copyWith应该创建新实例', () {
      final original = CoreServicesV1Model(
        id: 1,
        name: '原始名称',
      );
      
      final updated = original.copyWith(name: '更新名称');
      expect(updated.id, original.id);
      expect(updated.name, '更新名称');
      expect(original.name, '原始名称'); // 原实例不变
    });

  });
}
