import 'dart:io';
import 'package:test/test.dart';
import 'package:path/path.dart' as path;

void main() {
  group('真实插件编译验证测试', () {
    late String testPluginPath;

    setUpAll(() async {
      // 设置测试插件路径
      final currentDir = Directory.current.path;
      testPluginPath = path.join(currentDir, '..', '..', 'plugins', 'testing');
    });

    group('插件依赖验证', () {
      test('应该包含Plugin System依赖', () {
        final pubspecFile = File(path.join(testPluginPath, 'pubspec.yaml'));
        expect(pubspecFile.existsSync(), isTrue, reason: 'pubspec.yaml应该存在');

        final content = pubspecFile.readAsStringSync();
        expect(content.contains('plugin_system:'), isTrue, 
               reason: '应该包含plugin_system依赖');
        expect(content.contains('path: ../../packages/plugin_system'), isTrue,
               reason: '应该正确引用plugin_system路径');
      });

      test('应该能够获取依赖', () async {
        // 验证dart pub get是否成功
        final result = await Process.run(
          'dart',
          ['pub', 'get'],
          workingDirectory: testPluginPath,
        );

        expect(result.exitCode, equals(0), 
               reason: 'dart pub get应该成功执行');
      });
    });

    group('插件代码结构验证', () {
      test('应该正确导入Plugin System', () {
        final coreFile = File(path.join(testPluginPath, 'lib', 'src', 'plugin_core.dart'));
        expect(coreFile.existsSync(), isTrue, reason: '插件核心文件应该存在');

        final content = coreFile.readAsStringSync();
        
        // 验证正确的导入语句
        expect(content.contains('import \'package:plugin_system/plugin_system.dart\' as plugin_sys;'), 
               isTrue, reason: '应该正确导入Plugin System');
        
        // 验证正确的基类继承
        expect(content.contains('extends plugin_sys.Plugin'), 
               isTrue, reason: '应该继承Plugin System基类');
        
        // 验证使用Plugin System类型
        expect(content.contains('plugin_sys.PluginState'), 
               isTrue, reason: '应该使用Plugin System状态类型');
        expect(content.contains('plugin_sys.PluginType'), 
               isTrue, reason: '应该使用Plugin System类型枚举');
      });

      test('应该包含必需的插件方法', () {
        final coreFile = File(path.join(testPluginPath, 'lib', 'src', 'plugin_core.dart'));
        final content = coreFile.readAsStringSync();
        
        // 验证必需的生命周期方法
        final requiredMethods = [
          'Future<void> initialize()',
          'Future<void> start()',
          'Future<void> pause()',
          'Future<void> resume()',
          'Future<void> stop()',
          'Future<void> dispose()',
        ];
        
        for (final method in requiredMethods) {
          expect(content.contains(method), isTrue, 
                 reason: '应该包含方法: $method');
        }
        
        // 验证必需的属性
        final requiredProperties = [
          'String get id',
          'String get name',
          'String get version',
          'String get description',
          'String get author',
        ];
        
        for (final property in requiredProperties) {
          expect(content.contains(property), isTrue, 
                 reason: '应该包含属性: $property');
        }
      });
    });

    group('插件编译验证', () {
      test('应该能够通过静态分析（忽略风格警告）', () async {
        // 运行dart analyze，只检查错误，忽略风格警告
        final result = await Process.run(
          'dart',
          ['analyze', '--no-fatal-warnings'],
          workingDirectory: testPluginPath,
        );

        // 检查是否有编译错误（不是风格警告）
        final output = result.stderr.toString() + result.stdout.toString();
        
        // 不应该有error级别的问题
        expect(output.contains('error •'), isFalse, 
               reason: '不应该有编译错误');
        
        // 可以有info级别的风格问题，但不应该有warning级别的问题
        if (output.contains('warning •')) {
          fail('发现警告级别的问题，需要修复:\n$output');
        }
      });

      test('应该能够编译插件库', () async {
        // 尝试编译插件库
        final result = await Process.run(
          'dart',
          ['compile', 'kernel', 'lib/test_plugin_fixed.dart'],
          workingDirectory: testPluginPath,
        );

        expect(result.exitCode, equals(0), 
               reason: '插件库应该能够编译成功');
      });
    });

    group('插件清单验证', () {
      test('应该包含完整的Pet App V3兼容性配置', () {
        final manifestFile = File(path.join(testPluginPath, 'plugin.yaml'));
        expect(manifestFile.existsSync(), isTrue, reason: '插件清单应该存在');

        final content = manifestFile.readAsStringSync();
        
        // 验证基本信息
        expect(content.contains('id: test_plugin_fixed'), isTrue);
        expect(content.contains('name: Test Plugin Fixed'), isTrue);
        expect(content.contains('version: 1.0.0'), isTrue);
        
        // 验证Pet App V3兼容性
        expect(content.contains('min_pet_app_version: "3.0.0"'), isTrue);
        expect(content.contains('plugin_system_version: "^1.0.0"'), isTrue);
        
        // 验证多平台支持
        final platforms = ['android', 'ios', 'web', 'windows', 'macos', 'linux'];
        for (final platform in platforms) {
          expect(content.contains('- $platform'), isTrue, 
                 reason: '应该支持平台: $platform');
        }
        
        // 验证生命周期钩子
        expect(content.contains('lifecycle:'), isTrue);
        expect(content.contains('on_initialize: "initialize"'), isTrue);
        expect(content.contains('on_start: "start"'), isTrue);
        expect(content.contains('on_stop: "stop"'), isTrue);
        expect(content.contains('on_dispose: "dispose"'), isTrue);
      });
    });

    group('插件测试验证', () {
      test('应该包含插件测试文件', () {
        final testFile = File(path.join(testPluginPath, 'test', 'plugin_core_test.dart'));
        expect(testFile.existsSync(), isTrue, reason: '插件测试文件应该存在');

        final content = testFile.readAsStringSync();
        expect(content.contains('import \'package:test/test.dart\''), isTrue);
        expect(content.contains('import \'package:test_plugin_fixed/test_plugin_fixed.dart\''), isTrue);
      });

      test('应该能够运行插件测试', () async {
        // 运行插件测试
        final result = await Process.run(
          'dart',
          ['test'],
          workingDirectory: testPluginPath,
        );

        expect(result.exitCode, equals(0), 
               reason: '插件测试应该能够运行成功');
      });
    });

    group('真实集成验证', () {
      test('验证插件模板修正的完整性', () {
        // 这个测试验证我们的修正是否完整
        final coreFile = File(path.join(testPluginPath, 'lib', 'src', 'plugin_core.dart'));
        final content = coreFile.readAsStringSync();
        
        // 验证没有模拟实现的痕迹
        expect(content.contains('abstract class Plugin'), isFalse, 
               reason: '不应该包含模拟的Plugin基类定义');
        expect(content.contains('// TODO: 实现'), isFalse, 
               reason: '不应该包含TODO标记的模拟实现');
        
        // 验证使用真实的Plugin System
        expect(content.contains('package:plugin_system/plugin_system.dart'), isTrue, 
               reason: '应该导入真实的Plugin System');
        expect(content.contains('plugin_sys.Plugin'), isTrue, 
               reason: '应该使用真实的Plugin基类');
      });

      test('验证生成的插件符合Pet App V3规范', () {
        final manifestFile = File(path.join(testPluginPath, 'plugin.yaml'));
        final content = manifestFile.readAsStringSync();
        
        // 验证符合Pet App V3插件规范
        expect(content.contains('plugin:'), isTrue);
        expect(content.contains('compatibility:'), isTrue);
        expect(content.contains('platforms:'), isTrue);
        expect(content.contains('dependencies:'), isTrue);
        expect(content.contains('entry_points:'), isTrue);
        expect(content.contains('lifecycle:'), isTrue);
        expect(content.contains('security:'), isTrue);
        expect(content.contains('performance:'), isTrue);
      });
    });
  });
}
