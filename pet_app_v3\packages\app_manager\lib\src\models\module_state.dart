/*
---------------------------------------------------------------
File name:          module_state.dart
Author:             lgnorant-lu
Date created:       2025-07-24
Last modified:      2025-07-24
Dart Version:       3.2+
Description:        模块状态模型
---------------------------------------------------------------
Change History:
    2025-07-24: Phase ******* - 实现模块状态模型;
*/

import 'package:equatable/equatable.dart';

/// 模块状态
class ModuleState extends Equatable {
  /// 模块ID
  final String moduleId;
  
  /// 模块状态
  final ModuleStatus status;
  
  /// 最后更新时间
  final DateTime lastUpdated;
  
  /// 启动时间
  final DateTime? startedAt;
  
  /// 错误信息
  final String? error;
  
  /// 性能指标
  final ModulePerformanceMetrics? performanceMetrics;

  const ModuleState({
    required this.moduleId,
    required this.status,
    required this.lastUpdated,
    this.startedAt,
    this.error,
    this.performanceMetrics,
  });

  /// 创建状态副本
  ModuleState copyWith({
    String? moduleId,
    ModuleStatus? status,
    DateTime? lastUpdated,
    DateTime? startedAt,
    String? error,
    ModulePerformanceMetrics? performanceMetrics,
  }) {
    return ModuleState(
      moduleId: moduleId ?? this.moduleId,
      status: status ?? this.status,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      startedAt: startedAt ?? this.startedAt,
      error: error ?? this.error,
      performanceMetrics: performanceMetrics ?? this.performanceMetrics,
    );
  }

  /// 从 JSON 创建
  factory ModuleState.fromJson(Map<String, dynamic> json) {
    return ModuleState(
      moduleId: json['moduleId'] as String,
      status: ModuleStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ModuleStatus.stopped,
      ),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      startedAt: json['startedAt'] != null 
          ? DateTime.parse(json['startedAt'] as String)
          : null,
      error: json['error'] as String?,
      performanceMetrics: json['performanceMetrics'] != null
          ? ModulePerformanceMetrics.fromJson(
              json['performanceMetrics'] as Map<String, dynamic>)
          : null,
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'moduleId': moduleId,
      'status': status.name,
      'lastUpdated': lastUpdated.toIso8601String(),
      'startedAt': startedAt?.toIso8601String(),
      'error': error,
      'performanceMetrics': performanceMetrics?.toJson(),
    };
  }

  /// 获取运行时长
  Duration? get uptime {
    if (startedAt == null || status != ModuleStatus.running) {
      return null;
    }
    return DateTime.now().difference(startedAt!);
  }

  /// 是否健康
  bool get isHealthy {
    return status == ModuleStatus.running && error == null;
  }

  @override
  List<Object?> get props => [
    moduleId,
    status,
    lastUpdated,
    startedAt,
    error,
    performanceMetrics,
  ];

  @override
  String toString() {
    return 'ModuleState(moduleId: $moduleId, status: $status)';
  }
}

/// 模块状态枚举
enum ModuleStatus {
  /// 已停止
  stopped,
  
  /// 启动中
  starting,
  
  /// 运行中
  running,
  
  /// 暂停中
  paused,
  
  /// 停止中
  stopping,
  
  /// 错误状态
  error,
  
  /// 未知状态
  unknown,
}

/// 模块状态扩展
extension ModuleStatusExtension on ModuleStatus {
  /// 获取状态描述
  String get description {
    switch (this) {
      case ModuleStatus.stopped:
        return '已停止';
      case ModuleStatus.starting:
        return '启动中';
      case ModuleStatus.running:
        return '运行中';
      case ModuleStatus.paused:
        return '已暂停';
      case ModuleStatus.stopping:
        return '停止中';
      case ModuleStatus.error:
        return '错误';
      case ModuleStatus.unknown:
        return '未知';
    }
  }

  /// 是否为活跃状态
  bool get isActive {
    switch (this) {
      case ModuleStatus.running:
      case ModuleStatus.starting:
      case ModuleStatus.stopping:
        return true;
      case ModuleStatus.stopped:
      case ModuleStatus.paused:
      case ModuleStatus.error:
      case ModuleStatus.unknown:
        return false;
    }
  }

  /// 是否可以启动
  bool get canStart {
    switch (this) {
      case ModuleStatus.stopped:
      case ModuleStatus.error:
        return true;
      case ModuleStatus.starting:
      case ModuleStatus.running:
      case ModuleStatus.paused:
      case ModuleStatus.stopping:
      case ModuleStatus.unknown:
        return false;
    }
  }

  /// 是否可以停止
  bool get canStop {
    switch (this) {
      case ModuleStatus.running:
      case ModuleStatus.paused:
      case ModuleStatus.error:
        return true;
      case ModuleStatus.stopped:
      case ModuleStatus.starting:
      case ModuleStatus.stopping:
      case ModuleStatus.unknown:
        return false;
    }
  }

  /// 是否可以暂停
  bool get canPause {
    return this == ModuleStatus.running;
  }

  /// 是否可以恢复
  bool get canResume {
    return this == ModuleStatus.paused;
  }
}

/// 模块性能指标
class ModulePerformanceMetrics extends Equatable {
  /// CPU 使用率 (0.0 - 1.0)
  final double cpuUsage;
  
  /// 内存使用量 (字节)
  final int memoryUsage;
  
  /// 网络发送字节数
  final int networkSent;
  
  /// 网络接收字节数
  final int networkReceived;
  
  /// 错误计数
  final int errorCount;
  
  /// 最后更新时间
  final DateTime lastUpdated;

  const ModulePerformanceMetrics({
    required this.cpuUsage,
    required this.memoryUsage,
    required this.networkSent,
    required this.networkReceived,
    required this.errorCount,
    required this.lastUpdated,
  });

  /// 从 JSON 创建
  factory ModulePerformanceMetrics.fromJson(Map<String, dynamic> json) {
    return ModulePerformanceMetrics(
      cpuUsage: (json['cpuUsage'] as num).toDouble(),
      memoryUsage: json['memoryUsage'] as int,
      networkSent: json['networkSent'] as int,
      networkReceived: json['networkReceived'] as int,
      errorCount: json['errorCount'] as int,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'cpuUsage': cpuUsage,
      'memoryUsage': memoryUsage,
      'networkSent': networkSent,
      'networkReceived': networkReceived,
      'errorCount': errorCount,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  /// 格式化内存使用量
  String get formattedMemoryUsage {
    if (memoryUsage < 1024) {
      return '${memoryUsage}B';
    } else if (memoryUsage < 1024 * 1024) {
      return '${(memoryUsage / 1024).toStringAsFixed(1)}KB';
    } else if (memoryUsage < 1024 * 1024 * 1024) {
      return '${(memoryUsage / (1024 * 1024)).toStringAsFixed(1)}MB';
    } else {
      return '${(memoryUsage / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
    }
  }

  /// 格式化 CPU 使用率
  String get formattedCpuUsage {
    return '${(cpuUsage * 100).toStringAsFixed(1)}%';
  }

  @override
  List<Object?> get props => [
    cpuUsage,
    memoryUsage,
    networkSent,
    networkReceived,
    errorCount,
    lastUpdated,
  ];

  @override
  String toString() {
    return 'ModulePerformanceMetrics(cpu: $formattedCpuUsage, memory: $formattedMemoryUsage)';
  }
}
