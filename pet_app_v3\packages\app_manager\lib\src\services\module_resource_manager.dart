/*
---------------------------------------------------------------
File name:          module_resource_manager.dart
Author:             lgnorant-lu
Date created:       2025-07-24
Last modified:      2025-07-24
Dart Version:       3.2+
Description:        模块资源管理器
---------------------------------------------------------------
Change History:
    2025-07-24: Phase ******* - 实现模块资源管理器;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:developer' as developer;
import '../models/module_resource.dart';

/// 模块资源管理器
///
/// 负责模块资源的监控、管理、缓存清理等操作
class ModuleResourceManager {
  static ModuleResourceManager? _instance;
  static ModuleResourceManager get instance =>
      _instance ??= ModuleResourceManager._();

  ModuleResourceManager._();

  final Map<String, ModuleResourceUsage> _resourceUsage = {};
  final Map<String, ModuleCache> _moduleCache = {};
  final StreamController<ResourceEvent> _eventController =
      StreamController.broadcast();

  /// 资源事件流
  Stream<ResourceEvent> get resourceEvents => _eventController.stream;

  /// 获取模块资源使用情况
  ModuleResourceUsage? getResourceUsage(String moduleId) {
    return _resourceUsage[moduleId];
  }

  /// 获取所有模块资源使用情况
  Map<String, ModuleResourceUsage> getAllResourceUsage() {
    return Map.unmodifiable(_resourceUsage);
  }

  /// 更新模块资源使用情况
  void updateResourceUsage(String moduleId, ModuleResourceUsage usage) {
    final oldUsage = _resourceUsage[moduleId];
    _resourceUsage[moduleId] = usage;

    // 检查资源使用是否超过阈值
    _checkResourceThresholds(moduleId, usage, oldUsage);

    _eventController.add(ResourceEvent.usageUpdated(moduleId, usage));
  }

  /// 监控模块资源使用
  Stream<ModuleResourceUsage> monitorResourceUsage(String moduleId) async* {
    while (true) {
      // 模拟资源监控
      final usage = await _collectResourceUsage(moduleId);
      updateResourceUsage(moduleId, usage);
      yield usage;

      await Future<void>.delayed(const Duration(seconds: 5));
    }
  }

  /// 获取模块缓存信息
  ModuleCache? getModuleCache(String moduleId) {
    return _moduleCache[moduleId];
  }

  /// 清理模块缓存
  Future<CacheOperationResult> clearModuleCache(String moduleId) async {
    try {
      _log('info', '清理模块缓存: $moduleId');

      final cache = _moduleCache[moduleId];
      if (cache == null) {
        return CacheOperationResult.failure(
          moduleId,
          '模块缓存不存在',
          CacheOperationError.cacheNotFound,
        );
      }

      // 执行缓存清理
      await _performCacheClear(moduleId);

      // 更新缓存信息
      _moduleCache[moduleId] = cache.copyWith(
        size: 0,
        fileCount: 0,
        lastCleared: DateTime.now(),
      );

      _eventController.add(ResourceEvent.cacheCleared(moduleId));

      _log('info', '模块缓存清理成功: $moduleId');
      return CacheOperationResult.success(moduleId, '缓存清理成功');
    } catch (e, stackTrace) {
      _log('severe', '模块缓存清理失败: $moduleId', e, stackTrace);
      return CacheOperationResult.failure(
        moduleId,
        '清理过程中发生错误: $e',
        CacheOperationError.operationFailed,
      );
    }
  }

  /// 清理所有模块缓存
  Future<List<CacheOperationResult>> clearAllCache() async {
    final results = <CacheOperationResult>[];

    for (final moduleId in _moduleCache.keys) {
      final result = await clearModuleCache(moduleId);
      results.add(result);
    }

    return results;
  }

  /// 获取系统资源概览
  SystemResourceOverview getSystemResourceOverview() {
    final allUsage = _resourceUsage.values.toList();

    if (allUsage.isEmpty) {
      return SystemResourceOverview.empty();
    }

    final totalCpuUsage =
        allUsage.fold<double>(0.0, (sum, usage) => sum + usage.cpuUsage);
    final totalMemoryUsage =
        allUsage.fold<int>(0, (sum, usage) => sum + usage.memoryUsage);
    final totalNetworkSent =
        allUsage.fold<int>(0, (sum, usage) => sum + usage.networkSent);
    final totalNetworkReceived =
        allUsage.fold<int>(0, (sum, usage) => sum + usage.networkReceived);

    final totalCacheSize =
        _moduleCache.values.fold<int>(0, (sum, cache) => sum + cache.size);
    final totalCacheFiles =
        _moduleCache.values.fold<int>(0, (sum, cache) => sum + cache.fileCount);

    return SystemResourceOverview(
      totalModules: _resourceUsage.length,
      totalCpuUsage: totalCpuUsage,
      totalMemoryUsage: totalMemoryUsage,
      totalNetworkSent: totalNetworkSent,
      totalNetworkReceived: totalNetworkReceived,
      totalCacheSize: totalCacheSize,
      totalCacheFiles: totalCacheFiles,
      lastUpdated: DateTime.now(),
    );
  }

  /// 优化模块资源
  Future<ResourceOptimizationResult> optimizeModuleResources(
      String moduleId) async {
    try {
      _log('info', '优化模块资源: $moduleId');

      final usage = _resourceUsage[moduleId];
      if (usage == null) {
        return ResourceOptimizationResult.failure(
          moduleId,
          '模块资源信息不存在',
        );
      }

      final optimizations = <String>[];

      // 内存优化
      if (usage.memoryUsage > 100 * 1024 * 1024) {
        // 100MB
        await _optimizeMemory(moduleId);
        optimizations.add('内存优化');
      }

      // 缓存优化
      final cache = _moduleCache[moduleId];
      if (cache != null && cache.size > 50 * 1024 * 1024) {
        // 50MB
        await clearModuleCache(moduleId);
        optimizations.add('缓存清理');
      }

      // CPU 优化
      if (usage.cpuUsage > 0.8) {
        // 80%
        await _optimizeCpu(moduleId);
        optimizations.add('CPU 优化');
      }

      _eventController.add(ResourceEvent.optimized(moduleId, optimizations));

      _log('info', '模块资源优化完成: $moduleId');
      return ResourceOptimizationResult.success(
        moduleId,
        '资源优化完成',
        optimizations,
      );
    } catch (e, stackTrace) {
      _log('severe', '模块资源优化失败: $moduleId', e, stackTrace);
      return ResourceOptimizationResult.failure(
        moduleId,
        '优化过程中发生错误: $e',
      );
    }
  }

  /// 初始化模块资源监控
  void initializeResourceMonitoring(String moduleId) {
    _log('info', '初始化模块资源监控: $moduleId');

    // 初始化资源使用情况
    _resourceUsage[moduleId] = ModuleResourceUsage.initial(moduleId);

    // 初始化缓存信息
    _moduleCache[moduleId] = ModuleCache.initial(moduleId);

    _eventController.add(ResourceEvent.monitoringStarted(moduleId));
  }

  /// 停止模块资源监控
  void stopResourceMonitoring(String moduleId) {
    _log('info', '停止模块资源监控: $moduleId');

    _resourceUsage.remove(moduleId);
    _moduleCache.remove(moduleId);

    _eventController.add(ResourceEvent.monitoringStopped(moduleId));
  }

  /// 收集模块资源使用情况
  Future<ModuleResourceUsage> _collectResourceUsage(String moduleId) async {
    // 模拟资源收集
    await Future<void>.delayed(const Duration(milliseconds: 100));

    // 生成模拟数据
    final random = DateTime.now().millisecondsSinceEpoch % 100;

    return ModuleResourceUsage(
      moduleId: moduleId,
      cpuUsage: (random % 50) / 100.0, // 0-50%
      memoryUsage: (random % 100) * 1024 * 1024, // 0-100MB
      networkSent: random * 1024, // KB
      networkReceived: random * 2 * 1024, // KB
      diskUsage: random * 10 * 1024 * 1024, // MB
      lastUpdated: DateTime.now(),
    );
  }

  /// 检查资源使用阈值
  void _checkResourceThresholds(
    String moduleId,
    ModuleResourceUsage current,
    ModuleResourceUsage? previous,
  ) {
    // CPU 使用率阈值检查
    if (current.cpuUsage > 0.9) {
      // 90%
      _eventController.add(ResourceEvent.thresholdExceeded(
        moduleId,
        ResourceThresholdType.cpu,
        current.cpuUsage,
      ));
    }

    // 内存使用阈值检查
    if (current.memoryUsage > 500 * 1024 * 1024) {
      // 500MB
      _eventController.add(ResourceEvent.thresholdExceeded(
        moduleId,
        ResourceThresholdType.memory,
        current.memoryUsage.toDouble(),
      ));
    }
  }

  /// 执行缓存清理
  Future<void> _performCacheClear(String moduleId) async {
    // 模拟缓存清理
    await Future<void>.delayed(const Duration(milliseconds: 200));
  }

  /// 优化内存使用
  Future<void> _optimizeMemory(String moduleId) async {
    // 模拟内存优化
    await Future<void>.delayed(const Duration(milliseconds: 300));
  }

  /// 优化 CPU 使用
  Future<void> _optimizeCpu(String moduleId) async {
    // 模拟 CPU 优化
    await Future<void>.delayed(const Duration(milliseconds: 250));
  }

  /// 日志记录
  void _log(String level, String message,
      [Object? error, StackTrace? stackTrace]) {
    developer.log(
      message,
      name: 'ModuleResourceManager',
      level: _getLogLevel(level),
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// 获取日志级别
  int _getLogLevel(String level) {
    switch (level) {
      case 'info':
        return 800;
      case 'warning':
        return 900;
      case 'severe':
        return 1000;
      default:
        return 700;
    }
  }

  /// 清理资源
  void dispose() {
    _eventController.close();
  }
}
