# 代码审查规范

## 概述

本文档定义了Ming Status CLI项目的代码审查规范，确保代码质量、一致性和团队协作效率。

## 审查原则

### 1. 四眼原则
- 所有代码变更必须经过至少一人审查
- 审查者不能是代码作者本人
- 关键功能变更需要两人以上审查

### 2. 及时响应
- 审查请求应在24小时内响应
- 简单变更应在4小时内完成审查
- 复杂变更可延长至48小时

### 3. 建设性反馈
- 提供具体、可操作的建议
- 解释问题的原因和影响
- 提供替代解决方案

## 审查检查清单

### 📋 功能性检查

- [ ] **功能完整性**: 代码是否实现了预期功能
- [ ] **边界条件**: 是否处理了异常情况和边界值
- [ ] **错误处理**: 是否有适当的错误处理和恢复机制
- [ ] **性能影响**: 是否存在性能瓶颈或资源泄漏
- [ ] **安全性**: 是否存在安全漏洞或敏感信息泄露

### 🏗️ 架构设计检查

- [ ] **设计模式**: 是否遵循项目的架构模式
- [ ] **职责分离**: 类和方法是否职责单一
- [ ] **依赖关系**: 是否存在循环依赖或过度耦合
- [ ] **接口设计**: API设计是否清晰、一致
- [ ] **扩展性**: 代码是否易于扩展和维护

### 🎨 代码质量检查

- [ ] **命名规范**: 变量、方法、类名是否清晰、一致
- [ ] **代码风格**: 是否遵循Dart官方代码风格
- [ ] **注释质量**: 是否有必要的注释和文档
- [ ] **代码复杂度**: 方法是否过于复杂，需要拆分
- [ ] **重复代码**: 是否存在可以提取的重复逻辑

### 🧪 测试覆盖检查

- [ ] **单元测试**: 新功能是否有对应的单元测试
- [ ] **测试覆盖率**: 测试是否覆盖主要代码路径
- [ ] **测试质量**: 测试用例是否有意义、可维护
- [ ] **集成测试**: 是否需要集成测试或端到端测试
- [ ] **性能测试**: 性能关键功能是否有性能测试

### 📚 文档检查

- [ ] **API文档**: 公共接口是否有文档注释
- [ ] **README更新**: 是否需要更新README或其他文档
- [ ] **变更日志**: 是否需要更新CHANGELOG.md
- [ ] **使用示例**: 新功能是否有使用示例

## 审查流程

### 1. 创建Pull Request

```bash
# 1. 确保分支是最新的
git checkout main
git pull origin main
git checkout feature/your-feature
git rebase main

# 2. 运行质量检查
dart analyze
dart test
dart format --set-exit-if-changed .

# 3. 创建PR
git push origin feature/your-feature
# 在GitHub/GitLab上创建Pull Request
```

### 2. PR描述模板

```markdown
## 变更描述
简要描述本次变更的内容和目的。

## 变更类型
- [ ] 新功能 (feat)
- [ ] Bug修复 (fix)
- [ ] 文档更新 (docs)
- [ ] 代码重构 (refactor)
- [ ] 性能优化 (perf)
- [ ] 测试相关 (test)
- [ ] 其他 (请说明)

## 测试
- [ ] 单元测试已通过
- [ ] 集成测试已通过
- [ ] 手动测试已完成
- [ ] 性能测试已完成 (如适用)

## 检查清单
- [ ] 代码遵循项目规范
- [ ] 提交信息符合约定式提交规范
- [ ] 文档已更新 (如需要)
- [ ] 变更日志已更新 (如需要)

## 相关问题
Closes #issue_number (如适用)

## 截图/演示
(如适用，添加截图或GIF演示)

## 额外说明
(任何需要审查者注意的特殊情况)
```

### 3. 审查过程

#### 审查者职责
1. **及时响应**: 在规定时间内开始审查
2. **全面检查**: 按照检查清单进行系统性审查
3. **建设性反馈**: 提供具体、有帮助的建议
4. **测试验证**: 必要时本地测试变更

#### 作者职责
1. **响应反馈**: 及时回应审查意见
2. **修复问题**: 根据反馈修复代码问题
3. **解释决策**: 对争议点提供合理解释
4. **更新文档**: 根据需要更新相关文档

### 4. 审查标准

#### ✅ 批准条件
- 所有检查清单项目通过
- 没有阻塞性问题
- 代码质量达到项目标准
- 测试覆盖率满足要求

#### ❌ 拒绝条件
- 存在功能性错误
- 代码质量不达标
- 缺少必要的测试
- 不符合项目规范

#### 🔄 需要修改
- 存在改进空间但不阻塞合并
- 代码风格问题
- 文档需要完善
- 测试可以加强

## 审查工具

### 自动化检查
```bash
# 代码质量检查
dart analyze

# 测试运行
dart test --coverage

# 代码格式检查
dart format --set-exit-if-changed .

# 提交信息检查
./scripts/check-commit-msg.sh "feat(core): 添加新功能"
```

### 手动检查工具
- **IDE插件**: 使用Dart/Flutter插件进行代码检查
- **差异对比**: 仔细查看文件差异
- **本地测试**: 在本地环境测试变更

## 常见问题和解决方案

### 1. 审查冲突处理
- **技术分歧**: 通过技术讨论达成共识
- **风格偏好**: 遵循项目既定规范
- **性能争议**: 通过基准测试验证

### 2. 大型变更审查
- **分阶段审查**: 将大型变更拆分为多个小PR
- **架构讨论**: 先讨论架构设计再审查实现
- **原型验证**: 通过原型验证设计可行性

### 3. 紧急修复
- **快速通道**: 关键Bug修复可以简化审查流程
- **事后审查**: 紧急合并后进行补充审查
- **风险评估**: 评估紧急修复的风险和影响

## 相关文档

- [Git工作流规范](./Git-Workflow.md)
- [项目贡献指南](../README.md#贡献指南)
- [代码风格指南](./developer_guide.md#代码规范)
- [测试规范](./developer_guide.md#测试指南)
