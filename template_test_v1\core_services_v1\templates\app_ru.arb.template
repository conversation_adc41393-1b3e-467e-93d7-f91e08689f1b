{"@@locale": "ru", "@@last_modified": "2025-07-14T11:45:22.051075", "@@author": "Ming Status CLI", "appTitle": "My App", "welcome": "Welcome", "hello": "Hello, {name}!", "settings": "Settings", "about": "About", "ok": "OK", "cancel": "Cancel", "home": "Home", "profile": "Profile", "notifications": "Notifications", "search": "Search", "loading": "Loading...", "error": "Error", "retry": "Retry", "save": "Save", "delete": "Delete", "edit": "Edit", "itemCount": "{count, plural, =0{No items} =1{1 item} other{{count} items}}", "login": "<PERSON><PERSON>", "logout": "Logout", "register": "Register", "forgotPassword": "Forgot Password", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "firstName": "First Name", "lastName": "Last Name", "phoneNumber": "Phone Number", "address": "Address", "city": "City", "country": "Country", "language": "Language", "theme": "Theme", "darkMode": "Dark Mode", "lightMode": "Light Mode", "systemMode": "System Default", "lastUpdated": "Last updated: {date}", "version": "Version", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "contactUs": "Contact Us", "feedback": "<PERSON><PERSON><PERSON>", "rateApp": "Rate App", "shareApp": "Share App", "@appTitle": {"description": "The title of the application displayed in the app bar", "context": "app_bar"}, "@welcome": {"description": "Welcome message displayed on the home screen", "context": "home_screen"}, "@hello": {"description": "Personalized greeting message with user name", "context": "greeting", "placeholders": {"name": {"type": "String", "example": "<PERSON>", "description": "Full name of the user"}}}, "@itemCount": {"description": "Number of items with plural support", "context": "item_list", "placeholders": {"count": {"type": "int", "format": "compact", "description": "Total number of items"}}}, "@lastUpdated": {"description": "Last updated timestamp", "context": "status", "placeholders": {"date": {"type": "DateTime", "format": "yMd", "description": "Date when content was last updated"}}}, "appName": "core_services_v1", "appDescription": "企业级核心服务模板", "@appName": {"description": "Application name", "context": "app_info"}, "@appDescription": {"description": "Application description", "context": "app_info"}}