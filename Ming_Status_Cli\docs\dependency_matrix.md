# 依赖版本兼容性矩阵

## 🎯 版本策略

### **保守策略 (Stable)**
- 使用经过验证的稳定版本
- 适用于生产环境
- 兼容性优先

### **平衡策略 (Balanced)**  
- 使用较新但稳定的版本
- 适用于大多数项目
- 功能与稳定性平衡

### **激进策略 (Latest)**
- 使用最新版本
- 适用于新项目或实验性项目
- 功能优先

## 📦 核心依赖版本矩阵

| 包名 | 保守版本 | 平衡版本 | 激进版本 | 当前使用 | 建议 |
|------|----------|----------|----------|----------|------|
| **状态管理** |
| flutter_riverpod | ^2.4.0 | ^2.5.0 | ^2.6.1 | ^2.4.9 | 平衡 |
| riverpod_annotation | ^2.3.0 | ^2.5.0 | ^2.6.1 | ^2.3.3 | 平衡 |
| riverpod_generator | ^2.4.0 | ^2.6.0 | ^2.6.5 | ^2.6.3 | 保持 |
| **路由管理** |
| go_router | ^12.0.0 | ^14.0.0 | ^16.0.0 | ^14.0.0 | 保持 |
| **数据序列化** |
| freezed_annotation | ^2.4.0 | ^2.4.4 | ^3.1.0 | ^2.4.4 | 保持 |
| freezed | ^2.4.0 | ^2.5.7 | ^3.1.0 | ^2.5.7 | 保持 |
| json_annotation | ^4.8.0 | ^4.8.1 | ^4.9.0 | ^4.8.1 | 保持 |
| json_serializable | ^6.7.0 | ^6.9.0 | ^6.9.5 | ^6.9.0 | 保持 |
| **网络请求** |
| dio | ^5.3.0 | ^5.4.0 | ^5.8.0 | ^5.4.0 | 保持 |
| retrofit | ^4.0.0 | ^4.0.3 | ^4.6.0 | ^4.0.3 | 保持 |
| retrofit_generator | ^8.0.0 | ^8.2.1 | ^9.7.0 | ^8.2.1 | 保持 |
| **代码质量** |
| very_good_analysis | ^5.1.0 | ^6.0.0 | ^9.0.0 | ^5.1.0 | ⚠️ 需更新 |
| flutter_lints | ^3.0.0 | ^4.0.0 | ^6.0.0 | ^3.0.1 | 平衡 |
| **工具链** |
| build_runner | ^2.4.0 | ^2.4.7 | ^2.4.8 | ^2.4.7 | 保持 |
| melos | ^6.0.0 | ^6.3.3 | ^6.3.3 | ^6.3.3 | 保持 |

## 🚨 已知问题和解决方案

### **Very Good Analysis 规则过时**
```yaml
# 问题: 部分lint规则在新版本Dart中已移除
# 解决方案: 更新到compatible版本并移除过时规则

# 过时规则列表:
- iterable_contains_unrelated_type  # Dart 3.3.0+ 已移除
- list_remove_unrelated_type       # Dart 3.3.0+ 已移除  
- package_api_docs                 # Dart 3.7.0+ 已移除
```

### **Firebase 依赖问题**
```yaml
# 问题: Web平台兼容性问题
# 解决方案: 
firebase_core: ^3.0.0      # 最新版本
firebase_auth: ^5.0.0      # 最新版本
cloud_firestore: ^5.0.0    # 最新版本

# 或者使用稳定版本:
firebase_core: ^2.24.0
firebase_auth: ^4.15.0
cloud_firestore: ^4.13.0
```

### **JS包废弃问题**
```yaml
# 问题: js包已废弃
# 解决方案: 移除js依赖，使用dart:js_interop

# 移除:
# js: ^0.6.7

# 在代码中使用:
import 'dart:js_interop';
```

## 🎯 模板配置优化建议

### **基础模板 (Basic)**
- 使用保守版本策略
- 最小依赖集合
- 高兼容性

### **标准模板 (Standard)**  
- 使用平衡版本策略
- 常用依赖集合
- 功能与稳定性平衡

### **企业模板 (Enterprise)**
- 使用平衡版本策略
- 完整依赖集合
- 包含所有企业级工具

### **实验模板 (Experimental)**
- 使用激进版本策略
- 最新功能
- 适用于新技术验证

## 🔄 版本更新策略

### **定期更新计划**
1. **每月检查**: 安全更新和重要bug修复
2. **每季度评估**: 功能更新和版本升级
3. **每年重构**: 架构升级和技术栈更新

### **更新优先级**
1. **安全更新**: 立即更新
2. **Bug修复**: 1周内更新
3. **功能更新**: 1个月内评估
4. **主版本升级**: 1个季度内规划
