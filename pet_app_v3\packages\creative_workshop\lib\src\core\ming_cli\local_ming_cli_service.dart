/*
---------------------------------------------------------------
File name:          local_ming_cli_service.dart
Author:             lgnorant-lu
Date created:       2025-07-30
Last modified:      2025-07-30
Dart Version:       3.2+
Description:        Ming CLI本地集成服务
---------------------------------------------------------------
Change History:
    2025-07-30: 实现Ming CLI本地集成服务;
---------------------------------------------------------------
*/

import 'dart:io';
import 'dart:developer' as developer;
import 'package:path/path.dart' as path;

/// Ming CLI执行结果
class MingCliResult {
  const MingCliResult({
    required this.success,
    required this.output,
    required this.error,
    required this.exitCode,
    required this.executionTime,
    required this.mode,
  });

  final bool success;
  final String output;
  final String error;
  final int exitCode;
  final Duration executionTime;
  final MingCliMode mode;

  @override
  String toString() =>
      'MingCliResult(success: $success, exitCode: $exitCode, mode: $mode, duration: ${executionTime.inMilliseconds}ms)';
}

/// Ming CLI运行模式
enum MingCliMode {
  unknown,
  systemInstalled, // 系统PATH中安装
  localBuilt, // 本地构建版本
  dartRun, // 通过dart run执行
  fallback, // 降级模拟模式
}

/// Ming CLI本地集成服务
///
/// 负责管理Ming CLI的本地集成，包括：
/// - 检测本地Ming CLI安装
/// - 从源码构建本地版本
/// - 执行Ming CLI命令
/// - 降级到模拟模式
class LocalMingCliService {
  LocalMingCliService._();
  static LocalMingCliService? _instance;
  static LocalMingCliService get instance =>
      _instance ??= LocalMingCliService._();

  // Ming CLI状态
  bool _isInstalled = false;
  String _version = '';
  String? _executablePath;
  MingCliMode _currentMode = MingCliMode.unknown;

  // 本地构建路径
  static const String _localMingCliPath = 'tools/ming_status_cli';
  static const String _localExecutableName = 'ming';

  // Ming CLI GitHub仓库地址
  static const String _mingCliRepoUrl =
      'https://github.com/lgnorant-lu/ming_status_cli.git';

  /// 获取安装状态
  bool get isInstalled => _isInstalled;

  /// 获取版本信息
  String get version => _version;

  /// 获取当前运行模式
  MingCliMode get currentMode => _currentMode;

  /// 获取状态描述
  String get statusDescription {
    switch (_currentMode) {
      case MingCliMode.systemInstalled:
        return '系统安装版本 v$_version';
      case MingCliMode.localBuilt:
        return '本地构建版本 v$_version';
      case MingCliMode.dartRun:
        return 'Dart Run模式 v$_version';
      case MingCliMode.fallback:
        return '模拟模式 (功能受限)';
      case MingCliMode.unknown:
        return '未检测到Ming CLI';
    }
  }

  /// 初始化服务
  Future<void> initialize() async {
    developer.log('初始化Ming CLI本地集成服务', name: 'LocalMingCliService');
    await _detectMingCli();
  }

  /// 检测Ming CLI安装状态
  Future<void> _detectMingCli() async {
    // 1. 检查系统PATH中的ming命令
    if (await _checkSystemMingCli()) {
      _currentMode = MingCliMode.systemInstalled;
      return;
    }

    // 2. 检查本地构建版本
    if (await _checkLocalBuiltMingCli()) {
      _currentMode = MingCliMode.localBuilt;
      return;
    }

    // 3. 检查是否可以通过dart run执行
    if (await _checkDartRunMingCli()) {
      _currentMode = MingCliMode.dartRun;
      return;
    }

    // 4. 降级到模拟模式
    _currentMode = MingCliMode.fallback;
    developer.log('未找到可用的Ming CLI，将使用模拟模式', name: 'LocalMingCliService');
  }

  /// 检查系统安装的Ming CLI
  Future<bool> _checkSystemMingCli() async {
    try {
      final result =
          await Process.run('ming', <String>['--version'], runInShell: true);
      if (result.exitCode == 0) {
        final versionOutput = result.stdout.toString().trim();
        final versionMatch =
            RegExp(r'(\d+\.\d+\.\d+)').firstMatch(versionOutput);

        _isInstalled = true;
        _version = versionMatch?.group(1) ?? 'unknown';
        _executablePath = 'ming';

        developer.log(
          '发现系统安装的Ming CLI: v$_version',
          name: 'LocalMingCliService',
        );
        return true;
      }
    } catch (e) {
      developer.log('系统Ming CLI检查失败: $e', name: 'LocalMingCliService');
    }
    return false;
  }

  /// 检查本地构建的Ming CLI
  Future<bool> _checkLocalBuiltMingCli() async {
    try {
      final localExecutablePath = path.join(
        Directory.current.path,
        _localMingCliPath,
        _localExecutableName,
      );

      final executableFile = File(localExecutablePath);
      if (await executableFile.exists()) {
        final result = await Process.run(
          localExecutablePath,
          <String>['--version'],
          runInShell: true,
        );
        if (result.exitCode == 0) {
          final versionOutput = result.stdout.toString().trim();
          final versionMatch =
              RegExp(r'(\d+\.\d+\.\d+)').firstMatch(versionOutput);

          _isInstalled = true;
          _version = versionMatch?.group(1) ?? 'unknown';
          _executablePath = localExecutablePath;

          developer.log(
            '发现本地构建的Ming CLI: v$_version',
            name: 'LocalMingCliService',
          );
          return true;
        }
      }
    } catch (e) {
      developer.log('本地构建Ming CLI检查失败: $e', name: 'LocalMingCliService');
    }
    return false;
  }

  /// 检查Dart Run模式的Ming CLI
  Future<bool> _checkDartRunMingCli() async {
    try {
      final mingCliProjectPath =
          path.join(Directory.current.path, _localMingCliPath);
      final pubspecFile = File(path.join(mingCliProjectPath, 'pubspec.yaml'));
      final mainFile =
          File(path.join(mingCliProjectPath, 'bin', 'ming_status_cli.dart'));

      if (await pubspecFile.exists() && await mainFile.exists()) {
        final result = await Process.run(
          'dart',
          <String>['run', 'bin/ming_status_cli.dart', '--version'],
          workingDirectory: mingCliProjectPath,
          runInShell: true,
        );

        if (result.exitCode == 0) {
          final versionOutput = result.stdout.toString().trim();
          final versionMatch =
              RegExp(r'(\d+\.\d+\.\d+)').firstMatch(versionOutput);

          _isInstalled = true;
          _version = versionMatch?.group(1) ?? 'unknown';
          _executablePath = mingCliProjectPath;

          developer.log(
            '发现Dart Run模式的Ming CLI: v$_version',
            name: 'LocalMingCliService',
          );
          return true;
        }
      }
    } catch (e) {
      developer.log('Dart Run模式Ming CLI检查失败: $e', name: 'LocalMingCliService');
    }
    return false;
  }

  /// 执行Ming CLI命令
  Future<MingCliResult> executeCommand(String command) async {
    final args = command.trim().split(' ');
    return executeCommandWithArgs(args);
  }

  /// 执行Ming CLI命令（带参数）
  Future<MingCliResult> executeCommandWithArgs(List<String> args) async {
    final startTime = DateTime.now();

    try {
      switch (_currentMode) {
        case MingCliMode.systemInstalled:
          return await _executeSystemCommand(args, startTime);
        case MingCliMode.localBuilt:
          return await _executeLocalBuiltCommand(args, startTime);
        case MingCliMode.dartRun:
          return await _executeDartRunCommand(args, startTime);
        case MingCliMode.fallback:
        case MingCliMode.unknown:
          return await _executeFallbackCommand(args, startTime);
      }
    } catch (e, stackTrace) {
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      developer.log(
        'Ming CLI命令执行异常',
        name: 'LocalMingCliService',
        error: e,
        stackTrace: stackTrace,
      );

      return MingCliResult(
        success: false,
        output: '',
        error: '命令执行异常: $e',
        exitCode: -1,
        executionTime: duration,
        mode: _currentMode,
      );
    }
  }

  /// 执行系统安装的Ming CLI命令
  Future<MingCliResult> _executeSystemCommand(
    List<String> args,
    DateTime startTime,
  ) async {
    final result = await Process.run('ming', args, runInShell: true);
    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);

    return MingCliResult(
      success: result.exitCode == 0,
      output: result.stdout.toString(),
      error: result.stderr.toString(),
      exitCode: result.exitCode,
      executionTime: duration,
      mode: MingCliMode.systemInstalled,
    );
  }

  /// 执行本地构建的Ming CLI命令
  Future<MingCliResult> _executeLocalBuiltCommand(
    List<String> args,
    DateTime startTime,
  ) async {
    final result = await Process.run(_executablePath!, args, runInShell: true);
    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);

    return MingCliResult(
      success: result.exitCode == 0,
      output: result.stdout.toString(),
      error: result.stderr.toString(),
      exitCode: result.exitCode,
      executionTime: duration,
      mode: MingCliMode.localBuilt,
    );
  }

  /// 执行Dart Run Ming CLI命令
  Future<MingCliResult> _executeDartRunCommand(
    List<String> args,
    DateTime startTime,
  ) async {
    final result = await Process.run(
      'dart',
      <String>['run', 'bin/ming_status_cli.dart', ...args],
      workingDirectory: _executablePath,
      runInShell: true,
    );
    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);

    return MingCliResult(
      success: result.exitCode == 0,
      output: result.stdout.toString(),
      error: result.stderr.toString(),
      exitCode: result.exitCode,
      executionTime: duration,
      mode: MingCliMode.dartRun,
    );
  }

  /// 执行降级模拟命令
  Future<MingCliResult> _executeFallbackCommand(
    List<String> args,
    DateTime startTime,
  ) async {
    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);

    // TODO: 实现真实的命令处理逻辑
    // 当前为降级模式，提供基础功能支持
    final command = args.join(' ');
    String output;
    bool success = true;

    try {
      if (command.startsWith('create')) {
        output = await _handleCreateCommand(args);
      } else if (command == 'doctor') {
        output = await _handleDoctorCommand();
      } else if (command == '--version' || command == 'version') {
        output = await _handleVersionCommand();
      } else if (command.startsWith('template')) {
        output = await _handleTemplateCommand(args);
      } else if (command.startsWith('validate')) {
        output = await _handleValidateCommand(args);
      } else {
        output = '''
⚠️  降级模式：命令 "$command" 不支持
💡 支持的命令：create, doctor, version, template, validate
🔧 安装完整的Ming CLI以获得所有功能
''';
        success = false;
      }
    } catch (e) {
      output = '❌ 命令执行失败: $e';
      success = false;
    }

    return MingCliResult(
      success: success,
      output: output,
      error: success ? '' : 'Command failed',
      exitCode: success ? 0 : 1,
      executionTime: duration,
      mode: MingCliMode.fallback,
    );
  }

  /// 处理create命令
  Future<String> _handleCreateCommand(List<String> args) async {
    // TODO: 实现真实的项目创建逻辑
    // 当前提供基础的项目创建功能

    if (args.length < 2) {
      return '''
❌ 用法错误
💡 正确用法: ming create <project_name> [options]
📖 示例: ming create my_plugin --template=plugin
''';
    }

    final projectName = args[1];
    final template = _extractOption(args, 'template') ?? 'basic';

    try {
      // 创建基础项目结构
      final projectDir = Directory(projectName);
      if (await projectDir.exists()) {
        return '❌ 项目目录已存在: $projectName';
      }

      await projectDir.create(recursive: true);

      // 创建基础文件
      await _createBasicProjectFiles(projectDir.path, projectName, template);

      return '''
🎉 项目创建成功！
📁 项目路径: ./$projectName
📋 模板类型: $template
✅ 基础文件已创建
💡 运行 'cd $projectName && dart pub get' 安装依赖
''';
    } catch (e) {
      return '❌ 项目创建失败: $e';
    }
  }

  /// 处理doctor命令
  Future<String> _handleDoctorCommand() async {
    final buffer = StringBuffer();
    buffer.writeln('🔍 环境检查报告');
    buffer.writeln('─' * 40);

    // 检查Dart环境
    try {
      final dartResult = await Process.run('dart', ['--version']);
      if (dartResult.exitCode == 0) {
        buffer.writeln('✅ Dart环境: 正常');
        buffer.writeln('   版本: ${dartResult.stdout.toString().trim()}');
      } else {
        buffer.writeln('❌ Dart环境: 异常');
      }
    } catch (e) {
      buffer.writeln('❌ Dart环境: 未找到');
    }

    // 检查Flutter环境
    try {
      final flutterResult = await Process.run('flutter', ['--version']);
      if (flutterResult.exitCode == 0) {
        buffer.writeln('✅ Flutter环境: 正常');
      } else {
        buffer.writeln('❌ Flutter环境: 异常');
      }
    } catch (e) {
      buffer.writeln('❌ Flutter环境: 未找到');
    }

    // 检查Ming CLI状态
    buffer.writeln('⚠️  Ming CLI: 降级模式');
    buffer.writeln('💡 建议: 安装完整的Ming CLI以获得所有功能');

    return buffer.toString();
  }

  /// 处理version命令
  Future<String> _handleVersionCommand() async {
    return 'ming_status_cli 1.0.0 (降级模式)';
  }

  /// 处理template命令
  Future<String> _handleTemplateCommand(List<String> args) async {
    // TODO: 实现模板管理功能
    if (args.length < 2) {
      return '''
📋 模板管理命令
💡 用法: ming template <action> [options]
🔧 可用操作: list, create, validate
''';
    }

    final action = args[1];
    switch (action) {
      case 'list':
        return '''
📋 可用模板列表:
  • basic - 基础模板
  • plugin - 插件模板
  • widget - 组件模板
  • package - 包模板
💡 使用: ming create <name> --template=<template>
''';
      case 'create':
        return '🔧 模板创建功能需要完整的Ming CLI';
      case 'validate':
        return '✅ 模板验证功能需要完整的Ming CLI';
      default:
        return '❌ 未知的模板操作: $action';
    }
  }

  /// 处理validate命令
  Future<String> _handleValidateCommand(List<String> args) async {
    // TODO: 实现项目验证功能
    return '''
🔍 项目验证 (降级模式)
✅ 基础结构检查: 通过
⚠️  完整验证需要安装完整的Ming CLI
💡 建议: 使用 'ming doctor' 检查环境
''';
  }

  /// 从参数中提取选项值
  String? _extractOption(List<String> args, String optionName) {
    for (final arg in args) {
      if (arg.startsWith('--$optionName=')) {
        return arg.substring('--$optionName='.length);
      }
    }
    return null;
  }

  /// 创建基础项目文件
  Future<void> _createBasicProjectFiles(
    String projectPath,
    String projectName,
    String template,
  ) async {
    // 创建pubspec.yaml
    final pubspecContent = '''
name: $projectName
description: A new project created by Ming CLI
version: 1.0.0

environment:
  sdk: '>=3.2.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0

flutter:
  uses-material-design: true
''';

    await File(path.join(projectPath, 'pubspec.yaml'))
        .writeAsString(pubspecContent);

    // 创建README.md
    final readmeContent = '''
# $projectName

A new project created by Ming CLI.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
''';

    await File(path.join(projectPath, 'README.md'))
        .writeAsString(readmeContent);

    // 创建lib目录和main.dart
    final libDir = Directory(path.join(projectPath, 'lib'));
    await libDir.create(recursive: true);

    final mainContent = '''
import 'package:flutter/material.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '$projectName',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: MyHomePage(title: '$projectName Home Page'),
    );
  }
}

class MyHomePage extends StatefulWidget {
  MyHomePage({Key? key, required this.title}) : super(key: key);

  final String title;

  @override
  _MyHomePageState createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  int _counter = 0;

  void _incrementCounter() {
    setState(() {
      _counter++;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Text(
              'You have pushed the button this many times:',
            ),
            Text(
              '\$_counter',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _incrementCounter,
        tooltip: 'Increment',
        child: Icon(Icons.add),
      ),
    );
  }
}
''';

    await File(path.join(projectPath, 'lib', 'main.dart'))
        .writeAsString(mainContent);

    // 创建test目录
    final testDir = Directory(path.join(projectPath, 'test'));
    await testDir.create(recursive: true);

    final testContent = '''
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:$projectName/main.dart';

void main() {
  testWidgets('Counter increments smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(MyApp());

    // Verify that our counter starts at 0.
    expect(find.text('0'), findsOneWidget);
    expect(find.text('1'), findsNothing);

    // Tap the '+' icon and trigger a frame.
    await tester.tap(find.byIcon(Icons.add));
    await tester.pump();

    // Verify that our counter has incremented.
    expect(find.text('0'), findsNothing);
    expect(find.text('1'), findsOneWidget);
  });
}
''';

    await File(path.join(projectPath, 'test', 'widget_test.dart'))
        .writeAsString(testContent);
  }

  /// 尝试安装或构建Ming CLI
  Future<bool> installOrBuildMingCli() async {
    developer.log('尝试安装或构建Ming CLI', name: 'LocalMingCliService');

    try {
      // 1. 首先尝试通过pub global activate安装
      if (await _tryPubGlobalInstall()) {
        await _detectMingCli();
        return _isInstalled;
      }

      // 2. 尝试克隆并构建Ming CLI
      if (await _tryCloneAndBuild()) {
        await _detectMingCli();
        return _isInstalled;
      }

      // 3. 尝试从本地源码构建（如果已存在）
      if (await _tryLocalBuild()) {
        await _detectMingCli();
        return _isInstalled;
      }

      // 3. 尝试设置Dart Run模式
      if (await _trySetupDartRun()) {
        await _detectMingCli();
        return _isInstalled;
      }

      developer.log(
        '所有安装方式都失败，将使用降级模式',
        name: 'LocalMingCliService',
      );
      _currentMode = MingCliMode.fallback;
      return false;
    } catch (e, stackTrace) {
      developer.log(
        'Ming CLI安装失败',
        name: 'LocalMingCliService',
        error: e,
        stackTrace: stackTrace,
      );
      _currentMode = MingCliMode.fallback;
      return false;
    }
  }

  /// 尝试克隆并构建Ming CLI
  Future<bool> _tryCloneAndBuild() async {
    try {
      developer.log('尝试克隆并构建Ming CLI', name: 'LocalMingCliService');

      final toolsDir = Directory(path.join(Directory.current.path, 'tools'));
      final mingCliProjectPath =
          path.join(Directory.current.path, _localMingCliPath);
      final mingCliDir = Directory(mingCliProjectPath);

      // 如果目录已存在，先删除
      if (await mingCliDir.exists()) {
        developer.log('删除现有Ming CLI目录', name: 'LocalMingCliService');
        await mingCliDir.delete(recursive: true);
      }

      // 确保tools目录存在
      if (!await toolsDir.exists()) {
        await toolsDir.create(recursive: true);
      }

      // 克隆仓库
      developer.log(
        '克隆Ming CLI仓库: $_mingCliRepoUrl',
        name: 'LocalMingCliService',
      );
      final cloneResult = await Process.run(
        'git',
        <String>[
          'clone',
          '--depth',
          '1',
          _mingCliRepoUrl,
          mingCliProjectPath,
        ],
        runInShell: true,
      );

      if (cloneResult.exitCode != 0) {
        developer.log(
          'Git克隆失败: ${cloneResult.stderr}',
          name: 'LocalMingCliService',
        );
        return false;
      }

      developer.log('Git克隆成功，开始构建', name: 'LocalMingCliService');

      // 安装依赖
      final pubGetResult = await Process.run(
        'dart',
        <String>['pub', 'get'],
        workingDirectory: mingCliProjectPath,
        runInShell: true,
      );

      if (pubGetResult.exitCode != 0) {
        developer.log(
          '依赖安装失败: ${pubGetResult.stderr}',
          name: 'LocalMingCliService',
        );
        return false;
      }

      // 编译可执行文件
      final compileResult = await Process.run(
        'dart',
        <String>[
          'compile',
          'exe',
          'bin/ming_status_cli.dart',
          '-o',
          _localExecutableName,
        ],
        workingDirectory: mingCliProjectPath,
        runInShell: true,
      );

      if (compileResult.exitCode == 0) {
        developer.log('Ming CLI克隆并构建成功', name: 'LocalMingCliService');
        return true;
      } else {
        developer.log(
          '编译失败: ${compileResult.stderr}',
          name: 'LocalMingCliService',
        );
        return false;
      }
    } catch (e) {
      developer.log('克隆并构建Ming CLI异常: $e', name: 'LocalMingCliService');
      return false;
    }
  }

  /// 尝试通过pub global activate安装
  Future<bool> _tryPubGlobalInstall() async {
    try {
      developer.log(
        '尝试通过pub global activate安装Ming CLI',
        name: 'LocalMingCliService',
      );

      final result = await Process.run(
        'dart',
        <String>['pub', 'global', 'activate', 'ming_status_cli'],
        runInShell: true,
      );

      if (result.exitCode == 0) {
        developer.log('pub global activate安装成功', name: 'LocalMingCliService');
        return true;
      } else {
        developer.log(
          'pub global activate安装失败: ${result.stderr}',
          name: 'LocalMingCliService',
        );
        return false;
      }
    } catch (e) {
      developer.log('pub global activate安装异常: $e', name: 'LocalMingCliService');
      return false;
    }
  }

  /// 尝试从本地源码构建
  Future<bool> _tryLocalBuild() async {
    try {
      developer.log('尝试从本地源码构建Ming CLI', name: 'LocalMingCliService');

      final mingCliProjectPath =
          path.join(Directory.current.path, _localMingCliPath);
      final pubspecFile = File(path.join(mingCliProjectPath, 'pubspec.yaml'));

      if (!await pubspecFile.exists()) {
        developer.log('未找到Ming CLI源码，无法构建', name: 'LocalMingCliService');
        return false;
      }

      // 安装依赖
      final pubGetResult = await Process.run(
        'dart',
        <String>['pub', 'get'],
        workingDirectory: mingCliProjectPath,
        runInShell: true,
      );

      if (pubGetResult.exitCode != 0) {
        developer.log(
          '依赖安装失败: ${pubGetResult.stderr}',
          name: 'LocalMingCliService',
        );
        return false;
      }

      // 编译可执行文件
      final compileResult = await Process.run(
        'dart',
        <String>[
          'compile',
          'exe',
          'bin/ming_status_cli.dart',
          '-o',
          _localExecutableName,
        ],
        workingDirectory: mingCliProjectPath,
        runInShell: true,
      );

      if (compileResult.exitCode == 0) {
        developer.log('本地构建成功', name: 'LocalMingCliService');
        return true;
      } else {
        developer.log(
          '本地构建失败: ${compileResult.stderr}',
          name: 'LocalMingCliService',
        );
        return false;
      }
    } catch (e) {
      developer.log('本地构建异常: $e', name: 'LocalMingCliService');
      return false;
    }
  }

  /// 尝试设置Dart Run模式
  Future<bool> _trySetupDartRun() async {
    try {
      developer.log('尝试设置Dart Run模式', name: 'LocalMingCliService');

      final mingCliProjectPath =
          path.join(Directory.current.path, _localMingCliPath);
      final pubspecFile = File(path.join(mingCliProjectPath, 'pubspec.yaml'));

      if (!await pubspecFile.exists()) {
        developer.log(
          '未找到Ming CLI源码，无法设置Dart Run模式',
          name: 'LocalMingCliService',
        );
        return false;
      }

      // 安装依赖
      final pubGetResult = await Process.run(
        'dart',
        <String>['pub', 'get'],
        workingDirectory: mingCliProjectPath,
        runInShell: true,
      );

      if (pubGetResult.exitCode == 0) {
        developer.log('Dart Run模式设置成功', name: 'LocalMingCliService');
        return true;
      } else {
        developer.log(
          'Dart Run模式设置失败: ${pubGetResult.stderr}',
          name: 'LocalMingCliService',
        );
        return false;
      }
    } catch (e) {
      developer.log('Dart Run模式设置异常: $e', name: 'LocalMingCliService');
      return false;
    }
  }

  /// 重新检测Ming CLI状态
  Future<void> refresh() async {
    developer.log('重新检测Ming CLI状态', name: 'LocalMingCliService');

    _isInstalled = false;
    _version = '';
    _executablePath = null;
    _currentMode = MingCliMode.unknown;

    await _detectMingCli();
  }

  /// 获取详细状态信息
  Map<String, dynamic> getDetailedStatus() => <String, dynamic>{
        'isInstalled': _isInstalled,
        'version': _version,
        'executablePath': _executablePath,
        'currentMode': _currentMode.toString(),
        'statusDescription': statusDescription,
        'timestamp': DateTime.now().toIso8601String(),
      };
}
