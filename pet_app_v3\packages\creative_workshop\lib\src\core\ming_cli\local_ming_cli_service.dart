/*
---------------------------------------------------------------
File name:          local_ming_cli_service.dart
Author:             lgnorant-lu
Date created:       2025-07-31
Last modified:      2025-07-31
Dart Version:       3.2+
Description:        本地Ming CLI服务
---------------------------------------------------------------
Change History:
    2025-07-31: Initial creation - 本地Ming CLI服务实现;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:convert';
import 'dart:developer' as developer;
import 'dart:io';

import 'package:path/path.dart' as path;

import 'ming_cli_result.dart';

/// 本地Ming CLI服务
///
/// 负责检测、安装和执行Ming CLI命令
/// 支持多种运行模式，优先使用真实的Ming CLI实现
class LocalMingCliService {
  static LocalMingCliService? _instance;

  /// 获取单例实例
  static LocalMingCliService get instance {
    return _instance ??= LocalMingCliService._();
  }

  LocalMingCliService._();

  // 状态变量
  bool _isInstalled = false;
  String _version = '';
  String _executablePath = '';
  MingCliMode _currentMode = MingCliMode.fallback;

  // 路径配置
  static const String _localMingCliPath = 'tools/ming_status_cli';
  static const String _mingCliExecutable = 'ming_status_cli';

  /// 是否已安装
  bool get isInstalled => _isInstalled;

  /// 版本信息
  String get version => _version;

  /// 可执行文件路径
  String get executablePath => _executablePath;

  /// 当前运行模式
  MingCliMode get currentMode => _currentMode;

  /// 状态描述
  String get status => _getStatusDescription();

  /// 状态描述
  String get statusDescription => _getStatusDescription();

  /// 初始化服务
  Future<void> initialize() async {
    developer.log('初始化LocalMingCliService', name: 'LocalMingCliService');
    await detectMingCli();
  }

  /// 检测Ming CLI
  Future<void> detectMingCli() async {
    await _detectMingCli();
  }

  /// 刷新状态
  Future<void> refresh() async {
    await detectMingCli();
  }

  /// 获取详细状态信息
  Map<String, dynamic> getDetailedStatus() => <String, dynamic>{
        'isInstalled': _isInstalled,
        'version': _version,
        'executablePath': _executablePath,
        'currentMode': _currentMode.toString(),
        'statusDescription': statusDescription,
        'timestamp': DateTime.now().toIso8601String(),
      };

  /// 执行Ming CLI命令
  Future<MingCliResult> executeCommand(List<String> args) async {
    final startTime = DateTime.now();

    developer.log(
      '执行Ming CLI命令: ${args.join(' ')}',
      name: 'LocalMingCliService',
    );

    try {
      switch (_currentMode) {
        case MingCliMode.systemInstalled:
          return await _executeSystemCommand(args, startTime);
        case MingCliMode.localBuilt:
          return await _executeLocalBuiltCommand(args, startTime);
        case MingCliMode.dartRun:
          return await _executeDartRunCommand(args, startTime);
        case MingCliMode.fallback:
          return await _executeFallbackCommand(args, startTime);
      }
    } catch (e, stackTrace) {
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      developer.log(
        'Ming CLI命令执行失败',
        name: 'LocalMingCliService',
        error: e,
        stackTrace: stackTrace,
      );

      return MingCliResult(
        success: false,
        output: '',
        error: '命令执行失败: $e',
        exitCode: -1,
        executionTime: duration,
        mode: _currentMode,
      );
    }
  }

  /// 检测Ming CLI安装状态
  Future<void> _detectMingCli() async {
    developer.log('检测Ming CLI安装状态', name: 'LocalMingCliService');

    // 1. 检查系统全局安装
    if (await _checkSystemInstalled()) {
      _currentMode = MingCliMode.systemInstalled;
      return;
    }

    // 2. 检查本地构建版本
    if (await _checkLocalBuilt()) {
      _currentMode = MingCliMode.localBuilt;
      return;
    }

    // 3. 检查Dart Run模式
    if (await _checkDartRun()) {
      _currentMode = MingCliMode.dartRun;
      return;
    }

    // 4. 降级模式
    _currentMode = MingCliMode.fallback;
    _isInstalled = false;
    _version = '降级模式';
    _executablePath = '';

    developer.log('Ming CLI未找到，使用降级模式', name: 'LocalMingCliService');
  }

  /// 检查系统全局安装
  Future<bool> _checkSystemInstalled() async {
    try {
      final result = await Process.run(_mingCliExecutable, ['--version']);
      if (result.exitCode == 0) {
        _isInstalled = true;
        _version = result.stdout.toString().trim();
        _executablePath = _mingCliExecutable;
        developer.log('发现系统安装的Ming CLI: $_version',
            name: 'LocalMingCliService');
        return true;
      }
    } catch (e) {
      // 系统未安装，继续检查其他方式
    }
    return false;
  }

  /// 检查本地构建版本
  Future<bool> _checkLocalBuilt() async {
    try {
      // 检查tools/ming_status_cli目录下的可执行文件
      final mingCliProjectPath =
          path.join(Directory.current.path, _localMingCliPath);

      // 检查预编译的可执行文件
      final possibleExecutables = [
        'ming_status_cli.exe', // Windows可执行文件
        'ming_status_cli', // Unix/Linux可执行文件
        'ming_status_cli_v1.0.0.exe', // 版本化可执行文件
      ];

      for (final executableName in possibleExecutables) {
        final executableFile =
            File(path.join(mingCliProjectPath, executableName));
        if (await executableFile.exists()) {
          // 尝试运行以验证
          final result = await Process.run(
            executableFile.path,
            ['--version'],
            runInShell: true,
          );

          if (result.exitCode == 0) {
            _isInstalled = true;
            _version = result.stdout.toString().trim();
            _executablePath = executableFile.path;
            developer.log('发现本地构建的Ming CLI: $_version',
                name: 'LocalMingCliService');
            return true;
          }
        }
      }

      // 检查是否可以通过dart compile exe构建
      final pubspecFile = File(path.join(mingCliProjectPath, 'pubspec.yaml'));
      final mainFile =
          File(path.join(mingCliProjectPath, 'bin', 'ming_status_cli.dart'));

      if (await pubspecFile.exists() && await mainFile.exists()) {
        // 尝试构建可执行文件
        developer.log('尝试构建本地Ming CLI可执行文件', name: 'LocalMingCliService');

        final buildResult = await Process.run(
          'dart',
          [
            'compile',
            'exe',
            'bin/ming_status_cli.dart',
            '-o',
            'ming_status_cli${Platform.isWindows ? '.exe' : ''}'
          ],
          workingDirectory: mingCliProjectPath,
          runInShell: true,
        );

        if (buildResult.exitCode == 0) {
          final builtExecutable = File(path.join(mingCliProjectPath,
              'ming_status_cli${Platform.isWindows ? '.exe' : ''}'));

          if (await builtExecutable.exists()) {
            // 验证构建的可执行文件
            final testResult = await Process.run(
              builtExecutable.path,
              ['--version'],
              runInShell: true,
            );

            if (testResult.exitCode == 0) {
              _isInstalled = true;
              _version = testResult.stdout.toString().trim();
              _executablePath = builtExecutable.path;
              developer.log('成功构建本地Ming CLI: $_version',
                  name: 'LocalMingCliService');
              return true;
            }
          }
        } else {
          developer.log('构建本地Ming CLI失败: ${buildResult.stderr}',
              name: 'LocalMingCliService');
        }
      }

      return false;
    } catch (e) {
      developer.log('检查本地构建版本失败: $e', name: 'LocalMingCliService');
      return false;
    }
  }

  /// 检查Dart Run模式
  Future<bool> _checkDartRun() async {
    try {
      final mingCliProjectPath =
          path.join(Directory.current.path, _localMingCliPath);
      final pubspecFile = File(path.join(mingCliProjectPath, 'pubspec.yaml'));
      final mainFile =
          File(path.join(mingCliProjectPath, 'bin', 'ming_status_cli.dart'));

      if (await pubspecFile.exists() && await mainFile.exists()) {
        // 尝试运行以验证
        final result = await Process.run(
          'dart',
          ['run', 'bin/ming_status_cli.dart', '--version'],
          workingDirectory: mingCliProjectPath,
          runInShell: true,
        );

        if (result.exitCode == 0) {
          _isInstalled = true;
          _version = result.stdout.toString().trim();
          _executablePath =
              path.join(mingCliProjectPath, 'bin', 'ming_status_cli.dart');
          developer.log('发现Dart Run模式的Ming CLI: $_version',
              name: 'LocalMingCliService');
          return true;
        }
      }
    } catch (e) {
      developer.log('Dart Run模式检测失败: $e', name: 'LocalMingCliService');
    }
    return false;
  }

  /// 执行系统安装的命令
  Future<MingCliResult> _executeSystemCommand(
    List<String> args,
    DateTime startTime,
  ) async {
    final result =
        await Process.run(_mingCliExecutable, args, runInShell: true);
    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);

    return MingCliResult(
      success: result.exitCode == 0,
      output: result.stdout.toString(),
      error: result.stderr.toString(),
      exitCode: result.exitCode,
      executionTime: duration,
      mode: MingCliMode.systemInstalled,
    );
  }

  /// 执行本地构建的命令
  Future<MingCliResult> _executeLocalBuiltCommand(
    List<String> args,
    DateTime startTime,
  ) async {
    if (_executablePath.isEmpty) {
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      return MingCliResult(
        success: false,
        output: '',
        error: '本地构建的可执行文件路径未设置',
        exitCode: -1,
        executionTime: duration,
        mode: MingCliMode.localBuilt,
      );
    }

    try {
      final result = await Process.run(
        _executablePath,
        args,
        runInShell: true,
      );

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      return MingCliResult(
        success: result.exitCode == 0,
        output: result.stdout.toString(),
        error: result.stderr.toString(),
        exitCode: result.exitCode,
        executionTime: duration,
        mode: MingCliMode.localBuilt,
      );
    } catch (e) {
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      return MingCliResult(
        success: false,
        output: '',
        error: '执行本地构建版本失败: $e',
        exitCode: -1,
        executionTime: duration,
        mode: MingCliMode.localBuilt,
      );
    }
  }

  /// 执行Dart Run命令
  Future<MingCliResult> _executeDartRunCommand(
    List<String> args,
    DateTime startTime,
  ) async {
    final mingCliProjectPath =
        path.join(Directory.current.path, _localMingCliPath);

    final result = await Process.run(
      'dart',
      ['run', 'bin/ming_status_cli.dart', ...args],
      workingDirectory: mingCliProjectPath,
      runInShell: true,
    );

    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);

    return MingCliResult(
      success: result.exitCode == 0,
      output: result.stdout.toString(),
      error: result.stderr.toString(),
      exitCode: result.exitCode,
      executionTime: duration,
      mode: MingCliMode.dartRun,
    );
  }

  /// 执行降级模式命令
  Future<MingCliResult> _executeFallbackCommand(
    List<String> args,
    DateTime startTime,
  ) async {
    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);
    final command = args.join(' ');

    String output;
    bool success = true;

    if (command == '--version' || command == 'version' || args.isEmpty) {
      output = 'ming_status_cli 1.0.0 (降级模式)';
    } else if (command == 'doctor') {
      output = '''
🔍 环境检查 (降级模式)
✅ Dart环境: 可用
⚠️  Ming CLI: 降级模式
💡 建议: 运行 'dart pub get' 在 tools/ming_status_cli 目录
''';
    } else {
      output = '''
⚠️  降级模式：命令 "$command" 需要完整的Ming CLI
💡 可用命令：--version, doctor
🔧 请确保 tools/ming_status_cli 目录存在并运行 'dart pub get'
''';
      success = false;
    }

    return MingCliResult(
      success: success,
      output: output,
      error: success ? '' : 'Command not supported in fallback mode',
      exitCode: success ? 0 : 1,
      executionTime: duration,
      mode: MingCliMode.fallback,
    );
  }

  /// 尝试安装或构建Ming CLI
  Future<bool> installOrBuildMingCli() async {
    developer.log('尝试安装或构建Ming CLI', name: 'LocalMingCliService');

    try {
      // 1. 首先尝试通过pub global activate安装
      if (await _tryPubGlobalInstall()) {
        await detectMingCli();
        return _isInstalled;
      }

      // 2. 尝试克隆并构建Ming CLI
      if (await _tryCloneAndBuild()) {
        await detectMingCli();
        return _isInstalled;
      }

      // 3. 尝试从本地源码构建（如果已存在）
      if (await _tryLocalBuild()) {
        await detectMingCli();
        return _isInstalled;
      }

      // 4. 尝试设置Dart Run模式
      if (await _trySetupDartRun()) {
        await detectMingCli();
        return _isInstalled;
      }

      developer.log(
        '所有安装方式都失败，将使用降级模式',
        name: 'LocalMingCliService',
      );
      _currentMode = MingCliMode.fallback;
      return false;
    } catch (e, stackTrace) {
      developer.log(
        'Ming CLI安装失败',
        name: 'LocalMingCliService',
        error: e,
        stackTrace: stackTrace,
      );
      _currentMode = MingCliMode.fallback;
      return false;
    }
  }

  /// 尝试通过pub global activate安装
  Future<bool> _tryPubGlobalInstall() async {
    try {
      developer.log(
        '尝试通过pub global activate安装Ming CLI',
        name: 'LocalMingCliService',
      );

      final result = await Process.run(
        'dart',
        <String>['pub', 'global', 'activate', 'ming_status_cli'],
        runInShell: true,
      );

      if (result.exitCode == 0) {
        developer.log('pub global activate安装成功', name: 'LocalMingCliService');
        return true;
      } else {
        developer.log(
          'pub global activate安装失败: ${result.stderr}',
          name: 'LocalMingCliService',
        );
        return false;
      }
    } catch (e) {
      developer.log('pub global activate安装异常: $e', name: 'LocalMingCliService');
      return false;
    }
  }

  /// 尝试克隆并构建Ming CLI
  Future<bool> _tryCloneAndBuild() async {
    try {
      developer.log('尝试从GitHub克隆并构建Ming CLI', name: 'LocalMingCliService');

      final mingCliPath = path.join(Directory.current.path, _localMingCliPath);
      final mingCliDir = Directory(mingCliPath);

      // 如果目录已存在，先删除
      if (await mingCliDir.exists()) {
        developer.log('删除现有的Ming CLI目录', name: 'LocalMingCliService');
        await mingCliDir.delete(recursive: true);
      }

      // 克隆仓库
      final cloneResult = await Process.run(
        'git',
        [
          'clone',
          'https://github.com/lgnorant-lu/ming_status_cli.git',
          mingCliPath,
        ],
        runInShell: true,
      );

      if (cloneResult.exitCode != 0) {
        developer.log(
          'Git克隆失败: ${cloneResult.stderr}',
          name: 'LocalMingCliService',
        );
        return false;
      }

      developer.log('Git克隆成功', name: 'LocalMingCliService');

      // 安装依赖
      final pubGetResult = await Process.run(
        'dart',
        ['pub', 'get'],
        workingDirectory: mingCliPath,
        runInShell: true,
      );

      if (pubGetResult.exitCode != 0) {
        developer.log(
          'pub get失败: ${pubGetResult.stderr}',
          name: 'LocalMingCliService',
        );
        return false;
      }

      developer.log('依赖安装成功', name: 'LocalMingCliService');

      // 构建可执行文件
      return await _buildExecutable(mingCliPath);
    } catch (e) {
      developer.log('克隆并构建失败: $e', name: 'LocalMingCliService');
      return false;
    }
  }

  /// 尝试从本地源码构建
  Future<bool> _tryLocalBuild() async {
    try {
      developer.log('尝试从本地源码构建Ming CLI', name: 'LocalMingCliService');

      final mingCliPath = path.join(Directory.current.path, _localMingCliPath);
      final mingCliDir = Directory(mingCliPath);
      final pubspecFile = File(path.join(mingCliPath, 'pubspec.yaml'));

      if (!await mingCliDir.exists() || !await pubspecFile.exists()) {
        developer.log('本地源码不存在', name: 'LocalMingCliService');
        return false;
      }

      // 更新依赖
      final pubGetResult = await Process.run(
        'dart',
        ['pub', 'get'],
        workingDirectory: mingCliPath,
        runInShell: true,
      );

      if (pubGetResult.exitCode != 0) {
        developer.log(
          'pub get失败: ${pubGetResult.stderr}',
          name: 'LocalMingCliService',
        );
        return false;
      }

      // 构建可执行文件
      return await _buildExecutable(mingCliPath);
    } catch (e) {
      developer.log('本地构建失败: $e', name: 'LocalMingCliService');
      return false;
    }
  }

  /// 尝试设置Dart Run模式
  Future<bool> _trySetupDartRun() async {
    try {
      developer.log('尝试设置Dart Run模式', name: 'LocalMingCliService');

      final mingCliPath = path.join(Directory.current.path, _localMingCliPath);
      final pubspecFile = File(path.join(mingCliPath, 'pubspec.yaml'));
      final mainFile =
          File(path.join(mingCliPath, 'bin', 'ming_status_cli.dart'));

      if (!await pubspecFile.exists() || !await mainFile.exists()) {
        developer.log('Dart Run模式所需文件不存在', name: 'LocalMingCliService');
        return false;
      }

      // 确保依赖已安装
      final pubGetResult = await Process.run(
        'dart',
        ['pub', 'get'],
        workingDirectory: mingCliPath,
        runInShell: true,
      );

      if (pubGetResult.exitCode != 0) {
        developer.log(
          'pub get失败: ${pubGetResult.stderr}',
          name: 'LocalMingCliService',
        );
        return false;
      }

      // 测试Dart Run是否可用
      final testResult = await Process.run(
        'dart',
        ['run', 'bin/ming_status_cli.dart', '--version'],
        workingDirectory: mingCliPath,
        runInShell: true,
      );

      if (testResult.exitCode == 0) {
        developer.log('Dart Run模式设置成功', name: 'LocalMingCliService');
        return true;
      } else {
        developer.log(
          'Dart Run模式测试失败: ${testResult.stderr}',
          name: 'LocalMingCliService',
        );
        return false;
      }
    } catch (e) {
      developer.log('Dart Run模式设置失败: $e', name: 'LocalMingCliService');
      return false;
    }
  }

  /// 构建可执行文件
  Future<bool> _buildExecutable(String mingCliPath) async {
    try {
      developer.log('构建可执行文件', name: 'LocalMingCliService');

      final buildResult = await Process.run(
        'dart',
        [
          'compile',
          'exe',
          'bin/ming_status_cli.dart',
          '-o',
          'ming_status_cli${Platform.isWindows ? '.exe' : ''}'
        ],
        workingDirectory: mingCliPath,
        runInShell: true,
      );

      if (buildResult.exitCode == 0) {
        final executableFile = File(path.join(
            mingCliPath, 'ming_status_cli${Platform.isWindows ? '.exe' : ''}'));

        if (await executableFile.exists()) {
          developer.log('可执行文件构建成功', name: 'LocalMingCliService');
          return true;
        }
      }

      developer.log(
        '可执行文件构建失败: ${buildResult.stderr}',
        name: 'LocalMingCliService',
      );
      return false;
    } catch (e) {
      developer.log('构建可执行文件异常: $e', name: 'LocalMingCliService');
      return false;
    }
  }

  /// 获取状态描述
  String _getStatusDescription() {
    switch (_currentMode) {
      case MingCliMode.systemInstalled:
        return '系统安装 - $_version';
      case MingCliMode.localBuilt:
        return '本地构建 - $_version';
      case MingCliMode.dartRun:
        return 'Dart Run - $_version';
      case MingCliMode.fallback:
        return '降级模式 - 基础功能';
    }
  }
}
