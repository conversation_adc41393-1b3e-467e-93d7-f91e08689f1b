{"@@locale": "zh_CN", "@@last_modified": "2025-07-21T09:01:07.690820", "@@author": "Pet App V3 Team", "appTitle": "我的应用", "welcome": "欢迎", "hello": "你好，{name}！", "settings": "设置", "about": "关于", "ok": "确定", "cancel": "取消", "home": "首页", "profile": "个人资料", "notifications": "通知", "search": "搜索", "loading": "加载中...", "error": "错误", "retry": "重试", "save": "保存", "delete": "删除", "edit": "编辑", "itemCount": "{count, plural, =0{没有项目} =1{1个项目} other{{count}个项目}}", "@appTitle": {"description": "The title of the application"}, "@welcome": {"description": "Welcome message displayed on the home screen"}, "@hello": {"description": "Personalized greeting message", "placeholders": {"name": {"type": "String", "example": "<PERSON>", "description": "User name"}}}, "@itemCount": {"description": "Number of items with plural support", "placeholders": {"count": {"type": "int", "format": "compact"}}}, "appName": "communication_system", "appDescription": "一个使用Flutter构建的现代化应用程序", "@appName": {"description": "Application name", "context": "app_info"}, "@appDescription": {"description": "Application description", "context": "app_info"}}