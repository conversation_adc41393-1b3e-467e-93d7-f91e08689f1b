{"version": 2, "entries": [{"package": "app_config", "rootUri": "../../../packages/app_config/", "packageUri": "lib/"}, {"package": "app_routing", "rootUri": "../../../packages/app_routing/", "packageUri": "lib/"}, {"package": "core_services", "rootUri": "../../../packages/core_services/", "packageUri": "lib/"}, {"package": "desktop_environment", "rootUri": "../../../packages/desktop_environment/", "packageUri": "lib/"}, {"package": "drift", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/drift-2.27.0/", "packageUri": "lib/", "config": {"name": "drift", "issue_tracker": "https://github.com/simolus3/drift/issues", "version": "0.0.1", "material_icon_code_point": "0xf41e", "issueTracker": "https://github.com/simolus3/drift/issues", "materialIconCodePoint": "0xf41e"}}, {"package": "notes_hub", "rootUri": "../../../packages/notes_hub/", "packageUri": "lib/"}, {"package": "punch_in", "rootUri": "../../../packages/punch_in/", "packageUri": "lib/"}, {"package": "shared_preferences", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences-2.5.3/", "packageUri": "lib/", "config": {"name": "shared_preferences", "issueTracker": "https://github.com/flutter/flutter/issues?q=is%3Aissue+is%3Aopen+label%3A%22p%3A+shared_preferences%22", "version": "1.0.0", "materialIconCodePoint": "0xe683"}}, {"package": "ui_framework", "rootUri": "../../../packages/ui_framework/", "packageUri": "lib/"}, {"package": "workshop", "rootUri": "../../../packages/workshop/", "packageUri": "lib/"}, {"package": "pet_app", "rootUri": "../", "packageUri": "lib/"}]}