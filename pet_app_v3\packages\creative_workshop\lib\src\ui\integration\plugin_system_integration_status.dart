/*
---------------------------------------------------------------
File name:          plugin_system_integration_status.dart
Author:             lgnorant-lu
Date created:       2025-07-31
Last modified:      2025-07-31
Dart Version:       3.2+
Description:        Plugin System集成状态显示组件
---------------------------------------------------------------
职责说明：
    Creative Workshop → 插件开发工作台
    - 显示与Plugin System的集成状态
    - 监控内置插件注册情况
    - 提供集成诊断信息
---------------------------------------------------------------
Change History:
    2025-07-31: 创建Plugin System集成状态组件;
---------------------------------------------------------------
*/

import 'package:flutter/material.dart';
import 'package:plugin_system/plugin_system.dart';
import 'package:creative_workshop/src/core/workshop_manager.dart';

/// Plugin System集成状态显示组件
class PluginSystemIntegrationStatus extends StatefulWidget {
  const PluginSystemIntegrationStatus({super.key});

  @override
  State<PluginSystemIntegrationStatus> createState() => 
      _PluginSystemIntegrationStatusState();
}

class _PluginSystemIntegrationStatusState 
    extends State<PluginSystemIntegrationStatus> {
  late final PluginRegistry _pluginRegistry;
  late final WorkshopManager _workshopManager;
  
  // 集成状态
  bool _isPluginSystemAvailable = false;
  bool _isWorkshopManagerInitialized = false;
  List<Plugin> _builtinPlugins = [];
  Map<String, PluginState> _pluginStates = {};
  
  // 预期的内置插件
  static const List<String> _expectedBuiltinPlugins = [
    'builtin_brush_tool',
    'builtin_text_editor',
    'builtin_image_viewer',
  ];

  @override
  void initState() {
    super.initState();
    _pluginRegistry = PluginRegistry.instance;
    _workshopManager = WorkshopManager.instance;
    _checkIntegrationStatus();
  }

  /// 检查集成状态
  Future<void> _checkIntegrationStatus() async {
    try {
      // 检查Plugin System可用性
      _isPluginSystemAvailable = true;
      
      // 检查WorkshopManager状态
      _isWorkshopManagerInitialized = 
          _workshopManager.state != WorkshopState.uninitialized;
      
      // 获取内置插件信息
      _builtinPlugins = _pluginRegistry.getAllPlugins()
          .where((plugin) => _expectedBuiltinPlugins.contains(plugin.id))
          .toList();
      
      // 获取插件状态
      _pluginStates = {};
      for (final pluginId in _expectedBuiltinPlugins) {
        if (_pluginRegistry.contains(pluginId)) {
          _pluginStates[pluginId] = _pluginRegistry.getState(pluginId);
        }
      }
      
      setState(() {});
    } catch (e) {
      _isPluginSystemAvailable = false;
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Row(
              children: [
                Icon(
                  Icons.integration_instructions,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Plugin System 集成状态',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: _checkIntegrationStatus,
                  tooltip: '刷新状态',
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // 集成状态概览
            _buildIntegrationOverview(),
            
            const SizedBox(height: 16),
            
            // 内置插件状态
            _buildBuiltinPluginsStatus(),
            
            const SizedBox(height: 16),
            
            // 集成诊断
            _buildIntegrationDiagnostics(),
          ],
        ),
      ),
    );
  }

  /// 构建集成状态概览
  Widget _buildIntegrationOverview() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '集成概览',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          
          _buildStatusItem(
            'Plugin System 可用性',
            _isPluginSystemAvailable,
            _isPluginSystemAvailable ? '正常' : '不可用',
          ),
          
          _buildStatusItem(
            'WorkshopManager 初始化',
            _isWorkshopManagerInitialized,
            _isWorkshopManagerInitialized ? '已初始化' : '未初始化',
          ),
          
          _buildStatusItem(
            '内置插件注册',
            _builtinPlugins.length == _expectedBuiltinPlugins.length,
            '${_builtinPlugins.length}/${_expectedBuiltinPlugins.length}',
          ),
        ],
      ),
    );
  }

  /// 构建内置插件状态
  Widget _buildBuiltinPluginsStatus() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '内置插件状态',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          
          if (_builtinPlugins.isEmpty)
            const Text(
              '暂无内置插件注册',
              style: TextStyle(color: Colors.grey),
            )
          else
            ..._builtinPlugins.map((plugin) => _buildPluginStatusItem(plugin)),
        ],
      ),
    );
  }

  /// 构建集成诊断
  Widget _buildIntegrationDiagnostics() {
    final diagnostics = _generateDiagnostics();
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '集成诊断',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          
          ...diagnostics.map((diagnostic) => Padding(
            padding: const EdgeInsets.symmetric(vertical: 2),
            child: Row(
              children: [
                Icon(
                  diagnostic.isWarning ? Icons.warning : Icons.info,
                  size: 16,
                  color: diagnostic.isWarning ? Colors.orange : Colors.blue,
                ),
                const SizedBox(width: 8),
                Expanded(child: Text(diagnostic.message)),
              ],
            ),
          )),
        ],
      ),
    );
  }

  /// 构建状态项
  Widget _buildStatusItem(String label, bool isOk, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            isOk ? Icons.check_circle : Icons.error,
            size: 16,
            color: isOk ? Colors.green : Colors.red,
          ),
          const SizedBox(width: 8),
          Expanded(child: Text(label)),
          Text(
            value,
            style: TextStyle(
              color: isOk ? Colors.green : Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建插件状态项
  Widget _buildPluginStatusItem(Plugin plugin) {
    final state = _pluginStates[plugin.id] ?? PluginState.loaded;
    final isOk = state == PluginState.loaded || 
                 state == PluginState.initialized ||
                 state == PluginState.running;
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            isOk ? Icons.extension : Icons.extension_off,
            size: 16,
            color: isOk ? Colors.green : Colors.red,
          ),
          const SizedBox(width: 8),
          Expanded(child: Text(plugin.name)),
          Chip(
            label: Text(state.toString().split('.').last),
            backgroundColor: _getStateColor(state).withValues(alpha: 0.2),
            labelStyle: TextStyle(color: _getStateColor(state)),
          ),
        ],
      ),
    );
  }

  /// 获取状态颜色
  Color _getStateColor(PluginState state) {
    switch (state) {
      case PluginState.loaded:
      case PluginState.initialized:
      case PluginState.running:
        return Colors.green;
      case PluginState.paused:
        return Colors.orange;
      case PluginState.stopped:
        return Colors.grey;
      case PluginState.error:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  /// 生成诊断信息
  List<DiagnosticMessage> _generateDiagnostics() {
    final diagnostics = <DiagnosticMessage>[];
    
    if (!_isPluginSystemAvailable) {
      diagnostics.add(DiagnosticMessage(
        'Plugin System 不可用，请检查依赖配置',
        isWarning: true,
      ));
    }
    
    if (!_isWorkshopManagerInitialized) {
      diagnostics.add(DiagnosticMessage(
        'WorkshopManager 未初始化，请调用 initialize() 方法',
        isWarning: true,
      ));
    }
    
    final missingPlugins = _expectedBuiltinPlugins
        .where((id) => !_pluginStates.containsKey(id))
        .toList();
    
    if (missingPlugins.isNotEmpty) {
      diagnostics.add(DiagnosticMessage(
        '缺少内置插件: ${missingPlugins.join(', ')}',
        isWarning: true,
      ));
    }
    
    if (diagnostics.isEmpty) {
      diagnostics.add(DiagnosticMessage(
        'Plugin System 集成正常，所有检查通过',
        isWarning: false,
      ));
    }
    
    return diagnostics;
  }
}

/// 诊断消息
class DiagnosticMessage {
  const DiagnosticMessage(this.message, {required this.isWarning});
  
  final String message;
  final bool isWarning;
}
