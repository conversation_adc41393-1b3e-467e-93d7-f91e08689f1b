/*
---------------------------------------------------------------
File name:          main_app_integration_example.dart
Author:             lgnorant-lu
Date created:       2025-07-24
Last modified:      2025-07-24
Dart Version:       3.2+
Description:        App Manager 主应用集成示例
---------------------------------------------------------------
Change History:
    2025-07-24: Phase 5.0.9 - 创建App Manager主应用集成示例;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:developer' as developer;
import 'package:app_manager/app_manager.dart';

/// 主应用集成示例
///
/// 演示如何在主应用中集成和使用 App Manager
class MainAppIntegrationExample {
  late AppManagerIntegration _appManagerIntegration;
  late AppManagerModule _appManagerModule;
  late StreamSubscription<IntegrationEvent> _integrationSubscription;

  /// 初始化主应用
  Future<void> initializeMainApp() async {
    try {
      developer.log('开始初始化主应用', name: 'MainApp');

      // 1. 获取 App Manager 集成实例
      _appManagerIntegration = AppManagerIntegration.instance;

      // 2. 监听集成事件
      _integrationSubscription =
          _appManagerIntegration.integrationEvents.listen(
        _handleIntegrationEvent,
      );

      // 3. 初始化 App Manager 集成
      final integrationResult =
          await _appManagerIntegration.initializeIntegration(
        config: {
          'monitoring_interval': 3, // 3秒监控间隔
          'enable_auto_cleanup': true,
          'max_modules': 50,
        },
      );

      if (!integrationResult.isSuccess) {
        throw Exception('App Manager 集成初始化失败: ${integrationResult.message}');
      }

      // 4. 初始化 App Manager 模块
      _appManagerModule = AppManagerModule.instance;
      await _appManagerModule.initialize();

      // 5. 注册路由
      final routes = _appManagerModule.registerRoutes();
      _registerAppRoutes(routes);

      developer.log('主应用初始化完成', name: 'MainApp');
    } catch (e, stackTrace) {
      developer.log('主应用初始化失败',
          error: e, stackTrace: stackTrace, name: 'MainApp');
      rethrow;
    }
  }

  /// 注册示例模块
  Future<void> registerExampleModules() async {
    try {
      developer.log('注册示例模块', name: 'MainApp');

      // 注册用户管理模块
      final userModuleResult = await _appManagerIntegration.registerAppModule(
        moduleId: 'user_manager',
        moduleName: '用户管理模块',
        requiredPermissions: [
          ModulePermission.basic,
          ModulePermission.userData,
          ModulePermission.fileSystemRead,
        ],
        metadata: {
          'version': '1.0.0',
          'description': '用户账户管理和认证',
          'author': 'Pet App Team',
        },
      );

      if (userModuleResult.isSuccess) {
        developer.log('用户管理模块注册成功', name: 'MainApp');
      } else {
        developer.log('用户管理模块注册失败: ${userModuleResult.message}',
            name: 'MainApp');
      }

      // 注册数据同步模块
      final syncModuleResult = await _appManagerIntegration.registerAppModule(
        moduleId: 'data_sync',
        moduleName: '数据同步模块',
        requiredPermissions: [
          ModulePermission.basic,
          ModulePermission.networkAccess,
          ModulePermission.fileSystemWrite,
        ],
        metadata: {
          'version': '1.2.0',
          'description': '数据备份和同步服务',
          'author': 'Pet App Team',
        },
      );

      if (syncModuleResult.isSuccess) {
        developer.log('数据同步模块注册成功', name: 'MainApp');
      } else {
        developer.log('数据同步模块注册失败: ${syncModuleResult.message}',
            name: 'MainApp');
      }

      // 注册通知模块
      final notificationModuleResult =
          await _appManagerIntegration.registerAppModule(
        moduleId: 'notification_service',
        moduleName: '通知服务模块',
        requiredPermissions: [
          ModulePermission.basic,
          ModulePermission.notification,
          ModulePermission.systemInfo,
        ],
        metadata: {
          'version': '2.0.0',
          'description': '推送通知和消息服务',
          'author': 'Pet App Team',
        },
      );

      if (notificationModuleResult.isSuccess) {
        developer.log('通知服务模块注册成功', name: 'MainApp');
      } else {
        developer.log('通知服务模块注册失败: ${notificationModuleResult.message}',
            name: 'MainApp');
      }
    } catch (e, stackTrace) {
      developer.log('示例模块注册失败',
          error: e, stackTrace: stackTrace, name: 'MainApp');
    }
  }

  /// 演示系统监控功能
  Future<void> demonstrateSystemMonitoring() async {
    try {
      developer.log('演示系统监控功能', name: 'MainApp');

      // 1. 获取集成状态
      final integrationStatus = _appManagerIntegration.getIntegrationStatus();
      developer.log(
          '集成状态: 已集成=${integrationStatus.isIntegrated}, '
          '模块总数=${integrationStatus.totalModules}, '
          '运行中模块=${integrationStatus.runningModules}',
          name: 'MainApp');

      // 2. 执行系统健康检查
      final healthReport =
          await _appManagerIntegration.performSystemHealthCheck();
      developer.log(
          '系统健康状态: ${healthReport.overallHealth}, '
          '检查项目=${healthReport.healthChecks.length}, '
          '系统警报=${healthReport.systemAlerts.length}',
          name: 'MainApp');

      // 3. 显示系统警报
      if (healthReport.systemAlerts.isNotEmpty) {
        developer.log('系统警报:', name: 'MainApp');
        for (final alert in healthReport.systemAlerts) {
          developer.log(
              '  - ${alert.title}: ${alert.message} (${alert.severity})',
              name: 'MainApp');
        }
      }

      // 4. 显示健康检查结果
      developer.log('健康检查结果:', name: 'MainApp');
      for (final check in healthReport.healthChecks) {
        developer.log('  - ${check.name}: ${check.status} - ${check.message}',
            name: 'MainApp');
      }
    } catch (e, stackTrace) {
      developer.log('系统监控演示失败',
          error: e, stackTrace: stackTrace, name: 'MainApp');
    }
  }

  /// 演示模块管理功能
  Future<void> demonstrateModuleManagement() async {
    try {
      developer.log('演示模块管理功能', name: 'MainApp');

      // 1. 启动模块
      final stateController = ModuleStateController.instance;
      await stateController.startModule('user_manager');
      await stateController.startModule('data_sync');

      // 2. 获取模块状态
      final userModuleState = stateController.getModuleState('user_manager');
      if (userModuleState != null) {
        developer.log('用户管理模块状态: ${userModuleState.status}', name: 'MainApp');
      }

      // 3. 暂停模块
      await stateController.pauseModule('data_sync');
      developer.log('数据同步模块已暂停', name: 'MainApp');

      // 4. 恢复模块
      await Future<void>.delayed(const Duration(seconds: 2));
      await stateController.resumeModule('data_sync');
      developer.log('数据同步模块已恢复', name: 'MainApp');
    } catch (e, stackTrace) {
      developer.log('模块管理演示失败',
          error: e, stackTrace: stackTrace, name: 'MainApp');
    }
  }

  /// 处理集成事件
  void _handleIntegrationEvent(IntegrationEvent event) {
    switch (event.type) {
      case IntegrationEventType.initialized:
        developer.log('App Manager 集成初始化完成', name: 'MainApp');
        break;
      case IntegrationEventType.failed:
        developer.log('App Manager 集成失败: ${event.data}', name: 'MainApp');
        break;
      case IntegrationEventType.moduleRegistered:
        developer.log('模块注册: ${event.moduleId}', name: 'MainApp');
        break;
      case IntegrationEventType.moduleUnregistered:
        developer.log('模块注销: ${event.moduleId}', name: 'MainApp');
        break;
      case IntegrationEventType.healthCheckCompleted:
        final report = event.data as SystemHealthReport;
        developer.log('健康检查完成: ${report.overallHealth}', name: 'MainApp');
        break;
      case IntegrationEventType.cleaned:
        developer.log('App Manager 集成已清理', name: 'MainApp');
        break;
    }
  }

  /// 注册应用路由
  void _registerAppRoutes(Map<String, Function> routes) {
    developer.log('注册应用路由: ${routes.keys.join(', ')}', name: 'MainApp');

    // 在实际应用中，这里会将路由注册到路由系统
    // 例如：GoRouter, Navigator 2.0, 或其他路由管理器
    for (final entry in routes.entries) {
      final route = entry.key;
      // final handler = entry.value; // 暂时不使用

      // 模拟路由注册
      developer.log('注册路由: $route', name: 'MainApp');

      // 在实际应用中可能是这样：
      // router.addRoute(route, handler);
    }
  }

  /// 清理主应用
  Future<void> cleanupMainApp() async {
    try {
      developer.log('开始清理主应用', name: 'MainApp');

      // 1. 取消事件订阅
      await _integrationSubscription.cancel();

      // 2. 清理 App Manager 模块
      await _appManagerModule.dispose();

      // 3. 清理 App Manager 集成
      await _appManagerIntegration.cleanupIntegration();

      developer.log('主应用清理完成', name: 'MainApp');
    } catch (e, stackTrace) {
      developer.log('主应用清理失败',
          error: e, stackTrace: stackTrace, name: 'MainApp');
    }
  }
}

/// 主函数 - 演示完整的集成流程
Future<void> main() async {
  final mainApp = MainAppIntegrationExample();

  try {
    // 1. 初始化主应用
    await mainApp.initializeMainApp();

    // 2. 注册示例模块
    await mainApp.registerExampleModules();

    // 3. 演示系统监控
    await mainApp.demonstrateSystemMonitoring();

    // 4. 演示模块管理
    await mainApp.demonstrateModuleManagement();

    // 5. 等待一段时间观察系统运行
    developer.log('系统运行中，等待 10 秒...', name: 'MainApp');
    await Future<void>.delayed(const Duration(seconds: 10));

    // 6. 再次检查系统状态
    await mainApp.demonstrateSystemMonitoring();
  } finally {
    // 7. 清理资源
    await mainApp.cleanupMainApp();
  }
}
