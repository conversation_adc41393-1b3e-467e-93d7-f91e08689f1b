/*
---------------------------------------------------------------
File name:          app_router.dart
Author:             Ming Status CLI
Date created:       2025-07-14
Last modified:      2025-07-14
Dart Version:       3.2+
Description:        core_services_v1应用程序路由配置
---------------------------------------------------------------
Change History:
    2025-07-14: Initial creation - core_services_v1应用程序路由配置;
---------------------------------------------------------------
*/


import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../screens/home_screen.dart';
import '../screens/settings_screen.dart';
import '../screens/profile_screen.dart';
import '../screens/about_screen.dart';
import '../screens/error_screen.dart';
import '../providers/auth_provider.dart';

/// {className}应用程序路由配置
///
/// 使用GoRouter进行声明式路由管理
class AppRouter {
  /// 私有构造函数，防止实例化
  AppRouter._();

  /// 路由路径常量
  static const String home = '/';
  static const String settings = '/settings';
  static const String profile = '/profile';
  static const String about = '/about';
  static const String login = '/login';
  static const String error = '/error';

  /// 全局导航键
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  /// 路由提供者
  static final routerProvider = Provider<GoRouter>((ref) {
    final authState = ref.watch(authStateProvider);
    
    return GoRouter(
      navigatorKey: navigatorKey,
      initialLocation: home,
      debugLogDiagnostics: true,
      redirect: (context, state) {
        final isLoggedIn = authState.when(
          data: (user) => user != null,
          loading: () => false,
          error: (_, __) => false,
        );

        final isLoginRoute = state.location == login;

        // 如果未登录且不在登录页面，重定向到登录页面
        if (!isLoggedIn && !isLoginRoute) {
          return login;
        }

        // 如果已登录且在登录页面，重定向到首页
        if (isLoggedIn && isLoginRoute) {
          return home;
        }

        return null;
      },
      routes: [
        GoRoute(
          path: home,
          name: 'home',
          builder: (context, state) => const HomeScreen(),
          routes: [
            GoRoute(
              path: 'details/:id',
              name: 'details',
              builder: (context, state) {
                final id = state.pathParameters['id']!;
                return DetailsScreen(id: id);
              },
            ),
          ],
        ),
        GoRoute(
          path: settings,
          name: 'settings',
          builder: (context, state) => const SettingsScreen(),
        ),
        GoRoute(
          path: profile,
          name: 'profile',
          builder: (context, state) => const ProfileScreen(),
        ),
        GoRoute(
          path: about,
          name: 'about',
          builder: (context, state) => const AboutScreen(),
        ),
        GoRoute(
          path: login,
          name: 'login',
          builder: (context, state) => const LoginScreen(),
        ),
        GoRoute(
          path: error,
          name: 'error',
          builder: (context, state) {
            final error = state.extra as String?;
            return ErrorScreen(error: error);
          },
        ),
      ],
      errorBuilder: (context, state) => ErrorScreen(
        error: '页面未找到: ${state.location}',
      ),
    );
  });

  /// GoRouter实例（用于非Riverpod环境）
  static final GoRouter router = GoRouter(
    navigatorKey: navigatorKey,
    initialLocation: home,
    debugLogDiagnostics: true,
    routes: [
      GoRoute(
        path: home,
        name: 'home',
        builder: (context, state) => const HomeScreen(),
      ),
      GoRoute(
        path: settings,
        name: 'settings',
        builder: (context, state) => const SettingsScreen(),
      ),
      GoRoute(
        path: profile,
        name: 'profile',
        builder: (context, state) => const ProfileScreen(),
      ),
      GoRoute(
        path: about,
        name: 'about',
        builder: (context, state) => const AboutScreen(),
      ),
    ],
    errorBuilder: (context, state) => ErrorScreen(
      error: '页面未找到: ${state.location}',
    ),
  );

  /// 导航辅助方法
  static void goToHome() => router.goNamed('home');
  static void goToSettings() => router.goNamed('settings');
  static void goToProfile() => router.goNamed('profile');
  static void goToAbout() => router.goNamed('about');
  
  /// 返回上一页
  static void goBack() {
    if (router.canPop()) {
      router.pop();
    }
  }
  
  /// 获取当前路由
  static String get currentLocation => router.location;
}
