import 'package:test/test.dart';
import 'package:creative_workshop/src/core/plugins/plugin_manager.dart';

void main() {
  group('PluginManager代理测试', () {
    late PluginManager pluginManager;

    setUp(() {
      pluginManager = PluginManager.instance;
    });

    test('应该能够获取单例实例', () {
      final instance1 = PluginManager.instance;
      final instance2 = PluginManager.instance;
      expect(instance1, same(instance2));
    });

    test('应该能够初始化插件管理器代理', () async {
      await pluginManager.initialize();
      // 验证初始化不会抛出异常
      expect(pluginManager, isNotNull);
    });

    test('应该能够获取已安装插件列表', () {
      final plugins = pluginManager.installedPlugins;
      expect(plugins, isNotNull);
      expect(plugins, isA<List<PluginInstallInfo>>());
      
      // 验证模拟数据
      expect(plugins.length, greaterThan(0));
      print('已安装插件数量: ${plugins.length}');
      
      for (final plugin in plugins) {
        expect(plugin.id, isNotEmpty);
        expect(plugin.name, isNotEmpty);
        expect(plugin.version, isNotEmpty);
        print('插件: ${plugin.name} (${plugin.id}) v${plugin.version}');
      }
    });

    test('应该能够获取已启用插件', () {
      final enabledPlugins = pluginManager.enabledPlugins;
      expect(enabledPlugins, isNotNull);
      expect(enabledPlugins, isA<List<PluginInstallInfo>>());
      
      for (final plugin in enabledPlugins) {
        expect(plugin.state, equals(PluginState.enabled));
        print('已启用插件: ${plugin.name}');
      }
    });

    test('应该能够获取需要更新的插件', () {
      final updatablePlugins = pluginManager.updatablePlugins;
      expect(updatablePlugins, isNotNull);
      expect(updatablePlugins, isA<List<PluginInstallInfo>>());
      
      for (final plugin in updatablePlugins) {
        expect(plugin.state, equals(PluginState.updateAvailable));
        print('需要更新的插件: ${plugin.name}');
      }
    });

    test('应该能够获取插件信息', () {
      final plugins = pluginManager.installedPlugins;
      if (plugins.isNotEmpty) {
        final firstPlugin = plugins.first;
        final pluginInfo = pluginManager.getPluginInfo(firstPlugin.id);
        
        expect(pluginInfo, isNotNull);
        expect(pluginInfo!.id, equals(firstPlugin.id));
        expect(pluginInfo.name, equals(firstPlugin.name));
        print('获取插件信息: ${pluginInfo.name}');
      }
    });

    test('应该能够处理不存在的插件', () {
      final pluginInfo = pluginManager.getPluginInfo('non_existent_plugin');
      expect(pluginInfo, isNull);
    });

    test('应该能够获取插件统计信息', () {
      final stats = pluginManager.getPluginStats();
      expect(stats, isNotNull);
      expect(stats, isA<Map<String, dynamic>>());
      
      expect(stats.containsKey('totalInstalled'), isTrue);
      expect(stats.containsKey('enabled'), isTrue);
      expect(stats.containsKey('disabled'), isTrue);
      expect(stats.containsKey('updateAvailable'), isTrue);
      
      expect(stats['totalInstalled'], isA<int>());
      expect(stats['enabled'], isA<int>());
      expect(stats['disabled'], isA<int>());
      expect(stats['updateAvailable'], isA<int>());
      
      print('插件统计: $stats');
    });

    test('应该能够测试安装插件（模拟）', () async {
      final result = await pluginManager.installPlugin('test_plugin_proxy');
      expect(result, isNotNull);
      expect(result.success, isA<bool>());
      expect(result.message, isNotEmpty);
      
      print('安装结果: ${result.success ? "成功" : "失败"} - ${result.message}');
    });

    test('应该能够测试卸载插件（模拟）', () async {
      final result = await pluginManager.uninstallPlugin('test_plugin_proxy');
      expect(result, isNotNull);
      expect(result.success, isA<bool>());
      expect(result.message, isNotEmpty);
      
      print('卸载结果: ${result.success ? "成功" : "失败"} - ${result.message}');
    });

    test('应该能够验证权限枚举', () {
      // 验证所有权限类型都有displayName
      for (final permission in PluginPermission.values) {
        expect(permission.displayName, isNotEmpty);
        print('权限: ${permission.name} -> ${permission.displayName}');
      }
    });

    test('应该能够验证插件状态枚举', () {
      // 验证所有插件状态
      for (final state in PluginState.values) {
        expect(state.name, isNotEmpty);
        print('状态: ${state.name}');
      }
    });

    test('应该能够创建插件依赖', () {
      final dependency = PluginDependency(
        pluginId: 'test_dependency',
        version: '1.0.0',
        isRequired: true,
      );
      
      expect(dependency.pluginId, equals('test_dependency'));
      expect(dependency.version, equals('1.0.0'));
      expect(dependency.isRequired, isTrue);
    });

    test('应该能够创建插件安装信息', () {
      final now = DateTime.now();
      final installInfo = PluginInstallInfo(
        id: 'test_plugin',
        name: '测试插件',
        version: '1.0.0',
        state: PluginState.enabled,
        installedAt: now,
        lastUsedAt: now,
        permissions: [PluginPermission.fileSystem],
        dependencies: [],
        size: 1024000,
        autoUpdate: true,
      );
      
      expect(installInfo.id, equals('test_plugin'));
      expect(installInfo.name, equals('测试插件'));
      expect(installInfo.version, equals('1.0.0'));
      expect(installInfo.state, equals(PluginState.enabled));
      expect(installInfo.permissions.length, equals(1));
      expect(installInfo.permissions.first, equals(PluginPermission.fileSystem));
    });

    test('应该能够复制插件安装信息', () {
      final now = DateTime.now();
      final original = PluginInstallInfo(
        id: 'test_plugin',
        name: '测试插件',
        version: '1.0.0',
        state: PluginState.disabled,
        installedAt: now,
        lastUsedAt: now,
        permissions: [],
        dependencies: [],
        size: 1024000,
        autoUpdate: true,
      );
      
      final updated = original.copyWith(state: PluginState.enabled);
      
      expect(updated.id, equals(original.id));
      expect(updated.name, equals(original.name));
      expect(updated.state, equals(PluginState.enabled));
      expect(original.state, equals(PluginState.disabled)); // 原对象不变
    });

    test('应该能够创建操作结果', () {
      final successResult = PluginOperationResult.success('操作成功');
      expect(successResult.success, isTrue);
      expect(successResult.message, equals('操作成功'));
      
      final failureResult = PluginOperationResult.failure('操作失败');
      expect(failureResult.success, isFalse);
      expect(failureResult.message, equals('操作失败'));
    });
  });
}
