/*
---------------------------------------------------------------
File name:          real_plugin_data_source.dart
Author:             lgnorant-lu
Date created:       2025-07-24
Last modified:      2025-07-24
Dart Version:       3.2+
Description:        真实插件数据源 - Phase 5.0.11
---------------------------------------------------------------
*/

import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import '../core/plugins/plugin_manifest.dart';
import '../models/plugin_store_entry.dart';
import '../models/plugin_search_query.dart';
import '../models/plugin_search_result.dart';

/// 真实插件数据源
class RealPluginDataSource {
  static const String _pluginDataFile = 'plugin_store_data.json';
  static const String _installedPluginsFile = 'installed_plugins.json';

  /// 单例实例
  static final RealPluginDataSource _instance =
      RealPluginDataSource._internal();
  factory RealPluginDataSource() => _instance;
  RealPluginDataSource._internal();

  /// 缓存的插件数据
  List<PluginStoreEntry>? _cachedPlugins;

  /// 已安装插件数据
  Map<String, Map<String, dynamic>>? _installedPlugins;

  /// 初始化数据源
  Future<void> initialize() async {
    await _loadPluginData();
    await _loadInstalledPlugins();
  }

  /// 搜索插件
  Future<PluginSearchResult> searchPlugins(PluginSearchQuery query) async {
    await _ensureDataLoaded();

    List<PluginStoreEntry> results = _cachedPlugins ?? [];

    // 应用关键词过滤
    if (query.keyword.isNotEmpty) {
      final keyword = query.keyword.toLowerCase();
      results = results.where((plugin) {
        return plugin.name.toLowerCase().contains(keyword) ||
            plugin.description.toLowerCase().contains(keyword) ||
            plugin.tags.any((tag) => tag.toLowerCase().contains(keyword));
      }).toList();
    }

    // 应用分类过滤
    if (query.category != null && query.category!.isNotEmpty) {
      results =
          results.where((plugin) => plugin.category == query.category).toList();
    }

    // 应用排序
    switch (query.sortBy) {
      case PluginSortBy.name:
        results.sort((a, b) => a.name.compareTo(b.name));
        break;
      case PluginSortBy.rating:
        results.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      case PluginSortBy.downloads:
        results.sort((a, b) => b.downloadCount.compareTo(a.downloadCount));
        break;
      case PluginSortBy.updated:
        results.sort((a, b) => b.lastUpdated.compareTo(a.lastUpdated));
        break;
      default:
        // 默认按评分排序
        results.sort((a, b) => b.rating.compareTo(a.rating));
    }

    // 应用分页
    final totalCount = results.length;
    final startIndex = query.offset;
    final endIndex = (startIndex + query.limit).clamp(0, totalCount);

    if (startIndex >= totalCount) {
      results = [];
    } else {
      results = results.sublist(startIndex, endIndex);
    }

    return PluginSearchResult(
      plugins: results,
      totalCount: totalCount,
      hasMore: endIndex < totalCount,
    );
  }

  /// 获取插件详情
  Future<PluginStoreEntry?> getPluginDetails(String pluginId) async {
    await _ensureDataLoaded();

    try {
      return _cachedPlugins?.firstWhere((plugin) => plugin.id == pluginId);
    } catch (e) {
      return null;
    }
  }

  /// 获取已安装插件列表
  Future<Map<String, Map<String, dynamic>>> getInstalledPlugins() async {
    await _ensureDataLoaded();
    return _installedPlugins ?? {};
  }

  /// 标记插件为已安装
  Future<void> markPluginAsInstalled(
      String pluginId, Map<String, dynamic> installInfo) async {
    _installedPlugins ??= {};
    _installedPlugins![pluginId] = {
      ...installInfo,
      'installedAt': DateTime.now().toIso8601String(),
    };
    await _saveInstalledPlugins();
  }

  /// 移除已安装插件记录
  Future<void> removeInstalledPlugin(String pluginId) async {
    _installedPlugins?.remove(pluginId);
    await _saveInstalledPlugins();
  }

  /// 确保数据已加载
  Future<void> _ensureDataLoaded() async {
    if (_cachedPlugins == null || _installedPlugins == null) {
      await initialize();
    }
  }

  /// 加载插件数据
  Future<void> _loadPluginData() async {
    try {
      final file = await _getPluginDataFile();

      if (await file.exists()) {
        final jsonString = await file.readAsString();
        final jsonData = json.decode(jsonString) as Map<String, dynamic>;
        final pluginList = jsonData['plugins'] as List<dynamic>;

        _cachedPlugins = pluginList
            .map((data) =>
                PluginStoreEntry.fromJson(data as Map<String, dynamic>))
            .toList();
      } else {
        // 如果文件不存在，创建默认数据
        await _createDefaultPluginData();
      }
    } catch (e) {
      debugPrint('加载插件数据失败: $e');
      await _createDefaultPluginData();
    }
  }

  /// 加载已安装插件数据
  Future<void> _loadInstalledPlugins() async {
    try {
      final file = await _getInstalledPluginsFile();

      if (await file.exists()) {
        final jsonString = await file.readAsString();
        _installedPlugins = Map<String, Map<String, dynamic>>.from(
          json.decode(jsonString) as Map<String, dynamic>,
        );
      } else {
        _installedPlugins = {};
      }
    } catch (e) {
      debugPrint('加载已安装插件数据失败: $e');
      _installedPlugins = {};
    }
  }

  /// 保存已安装插件数据
  Future<void> _saveInstalledPlugins() async {
    try {
      final file = await _getInstalledPluginsFile();
      final jsonString = json.encode(_installedPlugins);
      await file.writeAsString(jsonString);
    } catch (e) {
      debugPrint('保存已安装插件数据失败: $e');
    }
  }

  /// 创建默认插件数据TODO[lgnorant-lu]
  Future<void> _createDefaultPluginData() async {
    _cachedPlugins = [
      PluginStoreEntry(
        id: 'advanced_brush',
        name: '高级画笔工具',
        description: '提供多种高级画笔效果，包括水彩、油画、素描等多种风格',
        version: '1.2.0',
        author: 'Creative Team',
        category: 'tools',
        tags: ['画笔', '绘画', '工具'],
        rating: 4.8,
        downloadCount: 15420,
        size: 2048000,
        lastUpdated: DateTime.now().subtract(const Duration(days: 7)),
        screenshots: [
          'https://example.com/screenshots/brush1.png',
          'https://example.com/screenshots/brush2.png',
        ],
        permissions: ['fileSystem', 'clipboard'],
        dependencies: [],
        minAppVersion: '1.0.0',
        price: 0.0,
        currency: 'CNY',
        downloadUrl: 'https://example.com/plugins/advanced_brush.zip',
        changeLog: '修复了画笔延迟问题，新增水彩效果',
        supportUrl: 'https://example.com/support/advanced_brush',
      ),
      PluginStoreEntry(
        id: 'color_palette',
        name: '调色板专家',
        description: '智能调色板工具，支持颜色提取、配色方案生成等功能',
        version: '1.5.2',
        author: 'Color Studio',
        category: 'tools',
        tags: ['调色板', '颜色', '设计'],
        rating: 4.6,
        downloadCount: 8930,
        size: 1024000,
        lastUpdated: DateTime.now().subtract(const Duration(days: 3)),
        screenshots: [
          'https://example.com/screenshots/palette1.png',
        ],
        permissions: ['clipboard', 'camera'],
        dependencies: [],
        minAppVersion: '1.0.0',
        price: 9.99,
        currency: 'CNY',
        downloadUrl: 'https://example.com/plugins/color_palette.zip',
        changeLog: '新增AI智能配色功能',
        supportUrl: 'https://example.com/support/color_palette',
      ),
      PluginStoreEntry(
        id: 'animation_studio',
        name: '动画工作室',
        description: '专业的2D动画制作工具，支持关键帧动画、骨骼动画等',
        version: '2.1.0',
        author: 'Animation Pro',
        category: 'tools',
        tags: ['动画', '2D', '关键帧'],
        rating: 4.9,
        downloadCount: 12650,
        size: 5120000,
        lastUpdated: DateTime.now().subtract(const Duration(days: 1)),
        screenshots: [
          'https://example.com/screenshots/animation1.png',
          'https://example.com/screenshots/animation2.png',
          'https://example.com/screenshots/animation3.png',
        ],
        permissions: ['fileSystem', 'network'],
        dependencies: [],
        minAppVersion: '1.1.0',
        price: 29.99,
        currency: 'CNY',
        downloadUrl: 'https://example.com/plugins/animation_studio.zip',
        changeLog: '新增骨骼动画系统，优化渲染性能',
        supportUrl: 'https://example.com/support/animation_studio',
      ),
      PluginStoreEntry(
        id: 'puzzle_game',
        name: '益智拼图游戏',
        description: '经典的拼图游戏，包含多种难度和主题',
        version: '1.0.5',
        author: 'Game Studio',
        category: 'games',
        tags: ['拼图', '益智', '游戏'],
        rating: 4.3,
        downloadCount: 25680,
        size: 3072000,
        lastUpdated: DateTime.now().subtract(const Duration(days: 14)),
        screenshots: [
          'https://example.com/screenshots/puzzle1.png',
        ],
        permissions: ['storage'],
        dependencies: [],
        minAppVersion: '1.0.0',
        price: 0.0,
        currency: 'CNY',
        downloadUrl: 'https://example.com/plugins/puzzle_game.zip',
        changeLog: '新增每日挑战模式',
        supportUrl: 'https://example.com/support/puzzle_game',
      ),
    ];

    // 保存到文件
    await _savePluginDataToFile();
  }

  /// 保存插件数据到文件
  Future<void> _savePluginDataToFile() async {
    try {
      final file = await _getPluginDataFile();
      final Map<String, dynamic> jsonData = <String, dynamic>{
        'plugins': _cachedPlugins
                ?.map((plugin) => plugin as Map<String, dynamic>)
                .toList() ??
            <Map<String, dynamic>>[],
        'lastUpdated': DateTime.now().toIso8601String(),
      };
      final jsonString = json.encode(jsonData);
      await file.writeAsString(jsonString);
    } catch (e) {
      debugPrint('保存插件数据失败: $e');
    }
  }

  /// 获取插件数据文件
  Future<File> _getPluginDataFile() async {
    final Directory directory = await getApplicationDocumentsDirectory();
    return File('${directory.path}/$_pluginDataFile');
  }

  /// 获取已安装插件文件
  Future<File> _getInstalledPluginsFile() async {
    final Directory directory = await getApplicationDocumentsDirectory();
    return File('${directory.path}/$_installedPluginsFile');
  }
}
