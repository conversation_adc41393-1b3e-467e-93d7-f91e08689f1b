/*
---------------------------------------------------------------
File name:          core_services_v1.dart
Author:             Ming Status CLI
Date created:       2025-07-14
Last modified:      2025-07-14
Dart Version:       3.2+
Description:        core_services_v1模块公共API导出文件
---------------------------------------------------------------
Change History:
    2025-07-14: Initial creation - core_services_v1模块公共API导出文件;
---------------------------------------------------------------
*/

/// core_services_v1模块
/// 
/// 企业级核心服务模板
/// 
/// ## 功能特性
/// 
/// - 提供业务服务和API接口
/// - 支持异步操作和错误处理
/// - 数据持久化和缓存
/// 
/// ## 使用示例
/// 
/// ```dart
/// import 'package:core_services_v1/core_services_v1.dart';
/// 
/// // 在Flutter应用中使用
/// class MyApp extends StatelessWidget {
///   @override
///   Widget build(BuildContext context) {
///     return MaterialApp(
///       home: CoreServicesV1Widget(),
///     );
///   }
/// }
/// ```
/// 
/// <AUTHOR> Status CLI
/// @version 1.0.0
library core_services_v1;

// 核心模块导出
export 'core_services_v1_module.dart';

// 服务接口导出
export 'src/services/index.dart';
export 'src/repositories/index.dart';
export 'src/providers/index.dart';

// API接口导出
export 'src/api/index.dart';
export 'src/models/index.dart';

// 企业级功能导出
export 'src/security/index.dart';
export 'src/monitoring/index.dart';
export 'src/logging/index.dart';
export 'src/configuration/index.dart';

// 条件导出（根据平台和环境）
export 'src/cross_platform/index.dart';

// 开发工具导出（仅在开发环境）
// export 'src/dev_tools/index.dart';

// 测试工具导出（仅在测试环境）
// export 'src/test_utils/index.dart';
