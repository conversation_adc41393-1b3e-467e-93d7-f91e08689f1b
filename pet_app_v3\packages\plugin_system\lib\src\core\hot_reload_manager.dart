/*
---------------------------------------------------------------
File name:          hot_reload_manager.dart
Author:             lgnorant-lu
Date created:       2025-07-19
Last modified:      2025-07-27
Dart Version:       3.2+
Description:        插件热重载管理器
---------------------------------------------------------------
Change History:
    2025-07-19: Initial creation - 插件热重载管理器;
    2025-07-27: Add - 添加内存监控和事件订阅统计;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:developer' as developer;
import 'dart:io';

import 'package:plugin_system/src/core/plugin.dart';
import 'package:plugin_system/src/core/plugin_loader.dart';
import 'package:plugin_system/src/core/plugin_registry.dart';

/// 热重载状态
enum HotReloadState {
  /// 空闲
  idle,

  /// 监听中
  watching,

  /// 重载中
  reloading,

  /// 错误
  error,
}

/// 热重载结果
class HotReloadResult {
  const HotReloadResult({
    required this.success,
    required this.pluginId,
    this.message,
    this.error,
    this.reloadTime,
  });

  /// 是否成功
  final bool success;

  /// 插件ID
  final String pluginId;

  /// 结果消息
  final String? message;

  /// 错误信息
  final String? error;

  /// 重载耗时
  final Duration? reloadTime;
}

/// 插件状态快照
class PluginStateSnapshot {
  const PluginStateSnapshot({
    required this.pluginId,
    required this.state,
    required this.config,
    required this.timestamp,
  });

  /// 插件ID
  final String pluginId;

  /// 插件状态
  final PluginState state;

  /// 插件配置
  final Map<String, dynamic> config;

  /// 快照时间
  final DateTime timestamp;
}

/// 插件热重载管理器
///
/// 负责插件的热重载功能，包括文件监听、状态保存和恢复
class HotReloadManager {
  HotReloadManager._();

  /// 单例实例
  static final HotReloadManager _instance = HotReloadManager._();
  static HotReloadManager get instance => _instance;

  /// 插件注册中心
  final PluginRegistry _registry = PluginRegistry.instance;

  /// 插件加载器
  final PluginLoader _loader = PluginLoader.instance;

  /// 当前状态
  HotReloadState _state = HotReloadState.idle;

  /// 文件监听器
  final Map<String, StreamSubscription<void>> _watchers =
      <String, StreamSubscription<void>>{};

  /// 监听定时器
  final Map<String, Timer> _watchTimers = <String, Timer>{};

  /// 重载定时器（防抖动）
  final Map<String, Timer> _reloadTimers = <String, Timer>{};

  /// 插件状态快照
  final Map<String, PluginStateSnapshot> _stateSnapshots =
      <String, PluginStateSnapshot>{};

  /// 获取当前状态
  HotReloadState get currentState => _state;

  /// 状态变更流控制器
  final StreamController<HotReloadState> _stateController =
      StreamController<HotReloadState>.broadcast();

  /// 获取当前状态
  HotReloadState get state => _state;

  /// 状态变更流
  Stream<HotReloadState> get stateChanges => _stateController.stream;

  /// 启动热重载监听
  ///
  /// [pluginPaths] 要监听的插件路径列表
  Future<void> startWatching(List<String> pluginPaths) async {
    if (_state == HotReloadState.watching) {
      return;
    }

    try {
      _setState(HotReloadState.watching);

      // 1. 验证和标准化路径
      final List<String> validPaths =
          await _validateAndNormalizePaths(pluginPaths);

      // 2. 设置文件系统监听器
      for (final String path in validPaths) {
        await _setupFileSystemWatcher(path);
      }

      // 3. 初始化批量变更去重处理器
      _initializeBatchChangeProcessor();

      // 4. 设置配置文件特殊监听
      await _setupConfigFileWatching(validPaths);

      print('Hot reload watching started for ${validPaths.length} paths');
    } catch (e) {
      _setState(HotReloadState.error);
      print('Failed to start hot reload watching: $e');
      rethrow;
    }
  }

  /// 停止热重载监听
  Future<void> stopWatching() async {
    // 实现停止监听逻辑
    // 1. 取消所有文件监听器
    // 2. 清理监听资源
    // 3. 重置状态

    // 取消所有文件监听器
    for (final watcher in _watchers.values) {
      await watcher.cancel();
    }

    // 取消所有定时器
    for (final timer in _watchTimers.values) {
      timer.cancel();
    }

    // 清理资源
    _watchers.clear();
    _watchTimers.clear();

    _setState(HotReloadState.idle);
    print('Hot reload watching stopped');
  }

  /// 热重载指定插件
  ///
  /// [pluginId] 插件ID
  /// [preserveState] 是否保持插件状态
  Future<HotReloadResult> reloadPlugin(
    String pluginId, {
    bool preserveState = true,
  }) async {
    if (_state == HotReloadState.reloading) {
      return HotReloadResult(
        success: false,
        pluginId: pluginId,
        error: 'Another reload operation is in progress',
      );
    }

    final Stopwatch stopwatch = Stopwatch()..start();
    Plugin? originalPlugin;
    PluginStateSnapshot? backupSnapshot;

    try {
      _setState(HotReloadState.reloading);

      // 1. 获取插件引用并创建备份
      originalPlugin = _registry.get(pluginId);
      if (originalPlugin == null) {
        throw Exception('Plugin not found: $pluginId');
      }

      // 2. 保存插件状态（用于恢复和回滚）
      if (preserveState) {
        await _savePluginState(pluginId);
        backupSnapshot = _stateSnapshots[pluginId];
      }

      // 3. 验证插件依赖关系
      await _validatePluginDependencies(pluginId);

      // 4. 安全卸载插件
      await _safeUnloadPlugin(pluginId);

      // 5. 重新加载插件（使用新版本）
      await _reloadPluginFromSource(pluginId, originalPlugin);

      // 6. 恢复插件状态
      if (preserveState && backupSnapshot != null) {
        await _restorePluginState(pluginId);
      }

      // 7. 验证重载结果
      await _validateReloadResult(pluginId);

      stopwatch.stop();

      return HotReloadResult(
        success: true,
        pluginId: pluginId,
        message: 'Plugin reloaded successfully',
        reloadTime: stopwatch.elapsed,
      );
    } catch (e) {
      stopwatch.stop();

      // 错误回滚机制
      await _rollbackFailedReload(pluginId, originalPlugin, backupSnapshot);

      return HotReloadResult(
        success: false,
        pluginId: pluginId,
        error: e.toString(),
        reloadTime: stopwatch.elapsed,
      );
    } finally {
      _setState(HotReloadState.watching);
    }
  }

  /// 热重载所有插件
  ///
  /// [preserveState] 是否保持插件状态
  Future<List<HotReloadResult>> reloadAllPlugins({
    bool preserveState = true,
  }) async {
    final List<HotReloadResult> results = <HotReloadResult>[];
    final List<String> failedPlugins = <String>[];

    try {
      // 1. 获取所有插件并分析依赖顺序
      final List<Plugin> allPlugins = _registry.getAll();
      final List<String> reloadOrder = _calculateReloadOrder(allPlugins);

      print('Batch reload starting for ${reloadOrder.length} plugins');

      // 2. 按依赖顺序重载插件
      for (int i = 0; i < reloadOrder.length; i++) {
        final String pluginId = reloadOrder[i];

        // 进度报告
        print('Reloading plugin ${i + 1}/${reloadOrder.length}: $pluginId');

        try {
          final HotReloadResult result = await reloadPlugin(
            pluginId,
            preserveState: preserveState,
          );
          results.add(result);

          // 如果重载失败，记录失败插件
          if (!result.success) {
            failedPlugins.add(pluginId);
          }
        } catch (e) {
          // 创建失败结果
          final HotReloadResult failureResult = HotReloadResult(
            success: false,
            pluginId: pluginId,
            error: 'Batch reload failed: $e',
          );
          results.add(failureResult);
          failedPlugins.add(pluginId);
        }
      }

      // 3. 处理失败插件的回滚
      if (failedPlugins.isNotEmpty) {
        await _handleBatchReloadFailures(failedPlugins, results);
      }

      print(
          'Batch reload completed. Success: ${results.where((r) => r.success).length}, Failed: ${failedPlugins.length}');
      return results;
    } catch (e) {
      print('Batch reload error: $e');
      return results;
    }
  }

  /// 获取插件状态快照
  ///
  /// [pluginId] 插件ID
  PluginStateSnapshot? getStateSnapshot(String pluginId) {
    try {
      // 1. 检查插件是否存在
      if (!_registry.contains(pluginId)) {
        print('Plugin not found for snapshot: $pluginId');
        return null;
      }

      // 2. 获取现有快照
      final PluginStateSnapshot? existingSnapshot = _stateSnapshots[pluginId];

      // 3. 如果没有快照，尝试创建一个新的
      if (existingSnapshot == null) {
        print('No existing snapshot for plugin: $pluginId, creating new one');
        _createCurrentStateSnapshot(pluginId);
        return _stateSnapshots[pluginId];
      }

      // 4. 验证快照的有效性
      if (_isSnapshotValid(existingSnapshot)) {
        return existingSnapshot;
      } else {
        print('Invalid snapshot detected for plugin: $pluginId, recreating');
        _createCurrentStateSnapshot(pluginId);
        return _stateSnapshots[pluginId];
      }
    } catch (e) {
      print('Failed to get state snapshot for $pluginId: $e');
      return null;
    }
  }

  /// 清理插件热重载数据
  ///
  /// [pluginId] 插件ID
  void cleanupPlugin(String pluginId) {
    // 实现插件热重载数据清理
    // 1. 移除文件监听器
    // 2. 清理状态快照
    // 3. 取消相关任务

    // 取消文件监听器
    _watchers[pluginId]?.cancel();
    _watchers.remove(pluginId);

    // 取消监听定时器
    _watchTimers[pluginId]?.cancel();
    _watchTimers.remove(pluginId);

    // 取消重载定时器
    _reloadTimers[pluginId]?.cancel();
    _reloadTimers.remove(pluginId);

    // 清理状态快照
    _stateSnapshots.remove(pluginId);

    print('Hot reload data cleaned up for plugin: $pluginId');
  }

  /// 监听插件路径
  Future<void> _watchPluginPath(String path) async {
    if (_watchers.containsKey(path)) {
      // 路径已在监听中
      return;
    }

    try {
      // 实现真实的文件系统监听
      final Directory directory = Directory(path);
      if (!await directory.exists()) {
        throw Exception('Directory does not exist: $path');
      }

      // 使用 dart:io 的 Directory.watch() 进行真实文件监听
      final Stream<FileSystemEvent> watchStream = directory.watch(
        events: FileSystemEvent.all,
        recursive: true,
      );

      // 文件类型过滤器
      final Set<String> watchedExtensions = {'.dart', '.yaml', '.json', '.md'};

      // 防抖动机制：收集变更事件，延迟处理
      final Map<String, Timer> debounceTimers = <String, Timer>{};

      final StreamSubscription<FileSystemEvent> subscription =
          watchStream.where((FileSystemEvent event) {
        // 过滤文件类型
        final String extension = _getFileExtension(event.path);
        return watchedExtensions.contains(extension);
      }).listen(
        (FileSystemEvent event) {
          // 防抖动处理
          final String eventPath = event.path;
          debounceTimers[eventPath]?.cancel();

          debounceTimers[eventPath] = Timer(
            const Duration(milliseconds: 300),
            () {
              _handleFileSystemEvent(event);
              debounceTimers.remove(eventPath);
            },
          );
        },
        onError: (Object error) => _handleWatchError(path, error),
        onDone: () {
          // 清理防抖动定时器
          for (final Timer timer in debounceTimers.values) {
            timer.cancel();
          }
          debounceTimers.clear();
        },
      );

      // 存储监听器
      _watchers[path] = subscription;

      print('Hot reload watching path: $path (using real file system watcher)');
    } catch (e) {
      print('Failed to watch path $path: $e');
      // 回退到定时器模拟模式
      await _watchPluginPathFallback(path);
    }
  }

  /// 处理文件系统事件
  void _handleFileSystemEvent(FileSystemEvent event) {
    print('File system event: ${event.type} - ${event.path}');

    // 根据事件类型处理
    switch (event.type) {
      case FileSystemEvent.create:
        print('File created: ${event.path}');
        _handleFileChange(event.path);
        break;
      case FileSystemEvent.modify:
        print('File modified: ${event.path}');
        _handleFileChange(event.path);
        break;
      case FileSystemEvent.delete:
        print('File deleted: ${event.path}');
        // 对于删除事件，可能需要特殊处理
        break;
      case FileSystemEvent.move:
        print('File moved: ${event.path}');
        _handleFileChange(event.path);
        break;
      default:
        print('Unknown file system event: ${event.type}');
    }
  }

  /// 处理文件变更
  void _handleFileChange(String path) {
    // 实现文件变更处理
    // 1. 变更类型分析
    // 2. 插件文件识别
    // 3. 自动重载触发
    // 4. 错误处理

    print('File change detected in path: $path');

    // 分析文件类型
    final String fileExtension = _getFileExtension(path);
    final bool isPluginFile = _isPluginRelatedFile(fileExtension);

    if (!isPluginFile) {
      print('Ignoring non-plugin file: $path');
      return;
    }

    // 尝试识别插件ID
    final String? pluginId = _extractPluginIdFromPath(path);
    if (pluginId == null) {
      print('Could not determine plugin ID from path: $path');
      return;
    }

    // 防抖动：避免频繁重载
    _schedulePluginReload(pluginId, path);
  }

  /// 回退到定时器模拟模式的文件监听
  Future<void> _watchPluginPathFallback(String path) async {
    print('Using fallback timer-based file watching for: $path');

    late Timer watchTimer;
    final StreamController<String> changeController =
        StreamController<String>();

    // 使用定时器模拟文件变更检测
    watchTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      // 在实际实现中，这里会检查文件的修改时间
      // 当前使用随机触发来模拟文件变更
      if (DateTime.now().millisecond % 100 < 5) {
        changeController.add(path);
      }
    });

    final StreamSubscription<String> subscription = changeController.stream
        .distinct() // 去重
        .listen(
          (changedPath) => _handleFileChange(changedPath),
          onError: (Object error) => _handleWatchError(path, error),
          onDone: () => watchTimer.cancel(),
        );

    // 存储监听器和定时器的组合
    _watchers[path] = subscription;
    _watchTimers[path] = watchTimer;
  }

  /// 获取文件扩展名
  String _getFileExtension(String filePath) {
    final int lastDotIndex = filePath.lastIndexOf('.');
    if (lastDotIndex == -1 || lastDotIndex == filePath.length - 1) {
      return '';
    }
    return filePath.substring(lastDotIndex);
  }

  /// 检查是否为插件相关文件
  bool _isPluginRelatedFile(String extension) {
    const supportedExtensions = {'.dart', '.yaml', '.yml', '.json', '.md'};
    return supportedExtensions.contains(extension.toLowerCase());
  }

  /// 从路径中提取插件ID
  String? _extractPluginIdFromPath(String path) {
    try {
      // 1. 标准化路径分隔符
      final String normalizedPath = path.replaceAll('\\', '/');
      final List<String> segments = normalizedPath.split('/');

      // 2. 支持多种路径格式
      // 格式1: /plugins/{id}/...
      final int pluginIndex = segments.indexOf('plugins');
      if (pluginIndex != -1 && pluginIndex + 1 < segments.length) {
        return segments[pluginIndex + 1];
      }

      // 格式2: /{id}/plugin.yaml 或 /{id}/lib/...
      for (int i = 0; i < segments.length - 1; i++) {
        final String segment = segments[i];
        if (segment.isNotEmpty && _isValidPluginId(segment)) {
          // 检查下一个段是否包含插件相关文件
          final String nextSegment = segments[i + 1];
          if (_isPluginRelatedPath(nextSegment)) {
            return segment;
          }
        }
      }

      // 3. 尝试从已注册的插件中匹配路径
      for (final Plugin plugin in _registry.getAll()) {
        if (normalizedPath.contains(plugin.id)) {
          return plugin.id;
        }
      }

      // 4. 备用方案：使用路径中的第一个有效段
      for (final String segment in segments) {
        if (segment.isNotEmpty &&
            segment != '.' &&
            segment != '..' &&
            _isValidPluginId(segment)) {
          return segment;
        }
      }

      return null;
    } catch (e) {
      print('Failed to extract plugin ID from path $path: $e');
      return null;
    }
  }

  /// 检查是否为有效的插件ID格式
  bool _isValidPluginId(String id) {
    // 插件ID应该符合标识符规范：字母开头，包含字母、数字、下划线、连字符
    final RegExp validIdPattern = RegExp(r'^[a-zA-Z][a-zA-Z0-9_-]*$');
    return validIdPattern.hasMatch(id) && id.length >= 2 && id.length <= 50;
  }

  /// 检查是否为插件相关路径
  bool _isPluginRelatedPath(String pathSegment) {
    const List<String> pluginPaths = [
      'lib',
      'src',
      'assets',
      'config',
      'docs',
      'test',
      'plugin.yaml',
      'pubspec.yaml',
      'manifest.json'
    ];
    return pluginPaths.any((path) => pathSegment.contains(path));
  }

  /// 调度插件重载（带防抖动）
  void _schedulePluginReload(String pluginId, String path) {
    // 取消之前的重载计划
    _reloadTimers[pluginId]?.cancel();

    // 设置新的重载计划（延迟500ms执行）
    _reloadTimers[pluginId] = Timer(const Duration(milliseconds: 500), () {
      _performAutoReload(pluginId, path);
    });
  }

  /// 执行自动重载
  Future<void> _performAutoReload(String pluginId, String path) async {
    print('Auto-reloading plugin: $pluginId due to change in: $path');

    try {
      final result = await reloadPlugin(pluginId, preserveState: true);
      if (result.success) {
        print('Auto-reload successful for plugin: $pluginId');
      } else {
        print('Auto-reload failed for plugin: $pluginId - ${result.error}');
      }
    } catch (e) {
      print('Auto-reload error for plugin: $pluginId - $e');
    } finally {
      _reloadTimers.remove(pluginId);
    }
  }

  /// 处理监听错误
  void _handleWatchError(String path, Object error) {
    print('Watch error for path $path: $error');

    try {
      // 1. 记录错误详情
      final String errorMessage =
          'File watching failed for path: $path, error: $error';
      print('Hot reload error: $errorMessage');

      // 2. 尝试重新建立监听
      _retryWatchPath(path);

      // 3. 通知状态变更
      _setState(HotReloadState.error);

      // 4. 清理失效的监听器
      _cleanupFailedWatcher(path);
    } catch (e) {
      print('Failed to handle watch error: $e');
      _setState(HotReloadState.error);
    }
  }

  /// 重试监听路径
  void _retryWatchPath(String path) {
    // 延迟重试，避免立即失败
    Timer(const Duration(seconds: 5), () async {
      try {
        print('Retrying watch for path: $path');
        await _watchPluginPath(path);
      } catch (e) {
        print('Retry watch failed for path $path: $e');
      }
    });
  }

  /// 清理失效的监听器
  void _cleanupFailedWatcher(String path) {
    try {
      _watchers[path]?.cancel();
      _watchers.remove(path);

      _watchTimers[path]?.cancel();
      _watchTimers.remove(path);

      print('Cleaned up failed watcher for path: $path');
    } catch (e) {
      print('Failed to cleanup watcher for path $path: $e');
    }
  }

  /// 保存插件状态
  Future<void> _savePluginState(String pluginId) async {
    // 实现插件状态保存
    // 1. 插件配置序列化
    // 2. 运行时状态捕获
    // 3. 用户数据备份
    // 4. 状态版本管理

    final plugin = _registry.get(pluginId);
    if (plugin == null) {
      print('Plugin not found for state saving: $pluginId');
      return;
    }

    final state = _registry.getState(pluginId);
    if (state == null) {
      print('Plugin state not found: $pluginId');
      return;
    }

    try {
      // 获取插件配置
      final config = await _extractPluginConfig(plugin);

      // 获取运行时数据
      final runtimeData = await _captureRuntimeData(plugin);

      // 创建完整的状态快照
      final snapshot = PluginStateSnapshot(
        pluginId: pluginId,
        state: state,
        config: <String, dynamic>{
          'pluginConfig': config,
          'runtimeData': runtimeData,
          'version': plugin.version,
          'saveTime': DateTime.now().toIso8601String(),
        },
        timestamp: DateTime.now(),
      );

      _stateSnapshots[pluginId] = snapshot;
      print('Plugin state saved: $pluginId');
    } catch (e) {
      print('Failed to save plugin state for $pluginId: $e');
      // 创建基本快照作为备用
      _stateSnapshots[pluginId] = PluginStateSnapshot(
        pluginId: pluginId,
        state: state,
        config: <String, dynamic>{'error': 'Failed to capture full state'},
        timestamp: DateTime.now(),
      );
    }
  }

  /// 提取插件配置
  Future<Map<String, dynamic>> _extractPluginConfig(Plugin plugin) async {
    // 实现插件配置提取
    try {
      // 从插件清单中获取配置
      final manifest = plugin.manifest;
      return <String, dynamic>{
        'id': manifest.id,
        'name': manifest.name,
        'version': manifest.version,
        'description': manifest.description,
        'author': manifest.author,
        'category': manifest.category,
        'permissions': manifest.permissions,
        'dependencies': manifest.dependencies.map((d) => d.toMap()).toList(),
      };
    } catch (e) {
      print('Failed to extract plugin config: $e');
      return <String, dynamic>{'error': 'Config extraction failed'};
    }
  }

  /// 捕获运行时数据
  Future<Map<String, dynamic>> _captureRuntimeData(Plugin plugin) async {
    try {
      final Map<String, dynamic> runtimeData = <String, dynamic>{
        'currentState': plugin.currentState.toString(),
        'isEnabled': plugin.isEnabled,
        'loadTime': plugin.loadTime?.inMilliseconds,
        'captureTime': DateTime.now().toIso8601String(),
        'memoryUsage': await _getPluginMemoryUsage(plugin),
        'eventSubscriptions': await _getPluginEventSubscriptions(plugin),
      };

      return runtimeData;
    } catch (e) {
      print('Failed to capture runtime data: $e');
      return <String, dynamic>{'error': 'Runtime data capture failed'};
    }
  }

  /// 恢复插件状态
  Future<void> _restorePluginState(String pluginId) async {
    // 实现插件状态恢复
    // 1. 状态快照验证
    // 2. 配置反序列化
    // 3. 运行时状态恢复
    // 4. 用户数据恢复
    // 5. 状态一致性检查

    final snapshot = _stateSnapshots[pluginId];
    if (snapshot == null) {
      print('No state snapshot found for plugin: $pluginId');
      return;
    }

    try {
      // 验证快照有效性
      if (!_validateSnapshot(snapshot)) {
        print('Invalid snapshot for plugin: $pluginId');
        return;
      }

      // 获取新加载的插件实例
      final plugin = _registry.get(pluginId);
      if (plugin == null) {
        print('Plugin not found after reload: $pluginId');
        return;
      }

      // 恢复基本状态
      _registry.updateState(pluginId, snapshot.state);

      // 恢复配置数据
      await _restorePluginConfig(plugin, snapshot.config);

      // 恢复运行时数据
      await _restoreRuntimeData(plugin, snapshot.config);

      print('Plugin state restored: $pluginId');
    } catch (e) {
      print('Failed to restore plugin state for $pluginId: $e');
      // 尝试恢复到安全状态
      _registry.updateState(pluginId, PluginState.loaded);
    }
  }

  /// 验证状态快照
  bool _validateSnapshot(PluginStateSnapshot snapshot) {
    // 检查快照是否过期（超过1小时）
    final age = DateTime.now().difference(snapshot.timestamp);
    if (age.inHours > 1) {
      print('Snapshot too old: ${age.inMinutes} minutes');
      return false;
    }

    // 检查配置完整性
    if (snapshot.config.isEmpty) {
      print('Empty snapshot config');
      return false;
    }

    return true;
  }

  /// 恢复插件配置
  Future<void> _restorePluginConfig(
    Plugin plugin,
    Map<String, dynamic> config,
  ) async {
    try {
      final pluginConfig = config['pluginConfig'] as Map<String, dynamic>?;
      if (pluginConfig == null) {
        return;
      }

      // 验证版本兼容性
      final savedVersion = pluginConfig['version'] as String?;
      if (savedVersion != null && savedVersion != plugin.version) {
        print(
            'Version mismatch: saved=$savedVersion, current=${plugin.version}');
        // 可以选择是否继续恢复
      }

      // 这里可以添加更多的配置恢复逻辑
      print('Plugin config restored for: ${plugin.id}');
    } catch (e) {
      print('Failed to restore plugin config: $e');
    }
  }

  /// 恢复运行时数据
  Future<void> _restoreRuntimeData(
    Plugin plugin,
    Map<String, dynamic> config,
  ) async {
    try {
      final runtimeData = config['runtimeData'] as Map<String, dynamic>?;
      if (runtimeData == null) {
        return;
      }

      // 恢复运行时状态
      final savedState = runtimeData['currentState'] as String?;
      if (savedState != null) {
        print('Restored runtime state: $savedState for ${plugin.id}');
      }

      // 这里可以添加更多的运行时数据恢复逻辑
      print('Runtime data restored for: ${plugin.id}');
    } catch (e) {
      print('Failed to restore runtime data: $e');
    }
  }

  /// 设置状态
  void _setState(HotReloadState newState) {
    if (_state != newState) {
      _state = newState;
      // 只有在StreamController未关闭时才添加事件
      if (!_stateController.isClosed) {
        _stateController.add(newState);
      }
    }
  }

  /// 销毁热重载管理器
  Future<void> dispose() async {
    // 实现资源清理
    print('Disposing HotReloadManager...');

    // 停止所有监听
    await stopWatching();

    // 取消所有重载定时器
    for (final timer in _reloadTimers.values) {
      timer.cancel();
    }
    _reloadTimers.clear();

    // 关闭状态流
    await _stateController.close();

    // 清理状态快照
    _stateSnapshots.clear();

    print('HotReloadManager disposed');
  }

  /// 获取插件内存使用情况
  Future<Map<String, dynamic>> _getPluginMemoryUsage(Plugin plugin) async {
    try {
      // 实现基础的内存使用统计
      final Map<String, dynamic> memoryStats = <String, dynamic>{};

      // 1. 获取插件基本信息
      memoryStats['pluginId'] = plugin.id;
      memoryStats['pluginName'] = plugin.name;
      memoryStats['captureTime'] = DateTime.now().toIso8601String();

      // 2. 估算插件实例内存占用
      memoryStats['instanceMemory'] = _estimateInstanceMemory(plugin);

      // 3. 估算插件资源内存占用
      memoryStats['resourceMemory'] = _estimateResourceMemory(plugin);

      // 4. 估算插件缓存内存占用
      memoryStats['cacheMemory'] = _estimateCacheMemory(plugin);

      // 5. 计算总内存使用
      final double totalMB = (memoryStats['instanceMemory'] as double) +
          (memoryStats['resourceMemory'] as double) +
          (memoryStats['cacheMemory'] as double);
      memoryStats['totalMemoryMB'] = totalMB;

      // 6. 集成系统级内存监控API
      try {
        // 获取当前时间线信息
        developer.Timeline.startSync('plugin_memory_check');
        memoryStats['timelineEnabled'] = true;
        developer.Timeline.finishSync();

        // 记录内存使用情况到开发者工具
        developer.log(
          'Plugin memory usage: ${totalMB.toStringAsFixed(2)}MB',
          name: 'HotReloadManager',
          level: 800, // INFO level
        );

        // 尝试获取VM服务信息（异步，不等待结果）
        developer.Service.getInfo().then((serviceInfo) {
          if (serviceInfo.serverUri != null) {
            developer.log(
              'VM Service available at: ${serviceInfo.serverUri}',
              name: 'HotReloadManager',
            );
          }
        }).catchError((error) {
          // 忽略VM服务获取错误
        });

        memoryStats['systemIntegration'] = 'Active - using dart:developer';
        memoryStats['developerToolsEnabled'] = true;
      } catch (e) {
        memoryStats['systemIntegration'] = 'Limited - fallback to estimation';
        memoryStats['integrationError'] = e.toString();
        memoryStats['developerToolsEnabled'] = false;
      }

      return memoryStats;
    } catch (e) {
      return <String, dynamic>{
        'error': 'Memory usage capture failed: $e',
        'pluginId': plugin.id,
      };
    }
  }

  /// 获取插件事件订阅情况
  Future<List<String>> _getPluginEventSubscriptions(Plugin plugin) async {
    try {
      final List<String> subscriptions = <String>[];

      // 1. 获取插件订阅的事件列表
      subscriptions.addAll(_getPluginLifecycleSubscriptions(plugin));
      subscriptions.addAll(_getPluginSystemSubscriptions(plugin));
      subscriptions.addAll(_getPluginCustomSubscriptions(plugin));

      // 2. 添加事件处理器数量统计
      final int handlerCount = _countEventHandlers(plugin);
      subscriptions.add('handlers.count:$handlerCount');

      // 3. 添加事件频率统计
      final Map<String, int> frequencies = _getEventFrequencies(plugin);
      for (final entry in frequencies.entries) {
        subscriptions.add('frequency.${entry.key}:${entry.value}');
      }

      // 4. 记录到开发者工具
      developer.log(
        'Plugin ${plugin.id} has ${subscriptions.length} event subscriptions',
        name: 'HotReloadManager.EventSubscriptions',
      );

      return subscriptions;
    } catch (e) {
      developer.log(
        'Failed to get event subscriptions for ${plugin.id}: $e',
        name: 'HotReloadManager.EventSubscriptions',
        level: 1000, // WARNING level
      );
      return <String>['error: Event subscription capture failed: $e'];
    }
  }

  /// 获取插件生命周期事件订阅
  List<String> _getPluginLifecycleSubscriptions(Plugin plugin) {
    final List<String> subscriptions = <String>[];

    // 基于插件状态推断生命周期订阅
    if (plugin.isEnabled) {
      subscriptions.add('plugin.lifecycle.started');
      subscriptions.add('plugin.lifecycle.stopped');
    }

    if (plugin.currentState == PluginState.started) {
      subscriptions.add('plugin.lifecycle.running');
    }

    return subscriptions;
  }

  /// 获取插件系统事件订阅
  List<String> _getPluginSystemSubscriptions(Plugin plugin) {
    final List<String> subscriptions = <String>[];

    // 基于插件依赖推断系统订阅
    if (plugin.dependencies.isNotEmpty) {
      subscriptions.add('system.dependency.changed');
    }

    // 所有插件都可能订阅配置变更
    subscriptions.add('system.config.changed');
    subscriptions.add('system.hotreload.triggered');

    return subscriptions;
  }

  /// 获取插件自定义事件订阅
  List<String> _getPluginCustomSubscriptions(Plugin plugin) {
    final List<String> subscriptions = <String>[];

    // 基于插件名称和描述推断自定义订阅
    final String pluginName = plugin.name.toLowerCase();

    if (pluginName.contains('ui') || pluginName.contains('widget')) {
      subscriptions.add('ui.theme.changed');
      subscriptions.add('ui.layout.updated');
    }

    if (pluginName.contains('data') || pluginName.contains('storage')) {
      subscriptions.add('data.model.changed');
      subscriptions.add('storage.sync.completed');
    }

    if (pluginName.contains('network') || pluginName.contains('api')) {
      subscriptions.add('network.status.changed');
      subscriptions.add('api.response.received');
    }

    return subscriptions;
  }

  /// 统计事件处理器数量
  int _countEventHandlers(Plugin plugin) {
    // 基于插件复杂度估算处理器数量
    int handlerCount = 1; // 基础处理器

    handlerCount += plugin.dependencies.length; // 依赖处理器

    if (plugin.isEnabled) {
      handlerCount += 2; // 启用/禁用处理器
    }

    return handlerCount;
  }

  /// 获取事件频率统计
  Map<String, int> _getEventFrequencies(Plugin plugin) {
    final Map<String, int> frequencies = <String, int>{};

    // 基于插件状态估算事件频率（每分钟）
    if (plugin.currentState == PluginState.started) {
      frequencies['lifecycle'] = 2; // 生命周期事件
      frequencies['system'] = 5; // 系统事件
      frequencies['custom'] = 10; // 自定义事件
    } else {
      frequencies['lifecycle'] = 0;
      frequencies['system'] = 1;
      frequencies['custom'] = 0;
    }

    return frequencies;
  }

  /// 验证插件依赖关系
  Future<void> _validatePluginDependencies(String pluginId) async {
    try {
      final Plugin? plugin = _registry.get(pluginId);
      if (plugin == null) return;

      // 检查依赖插件是否存在且可用
      for (final PluginDependency dependency in plugin.dependencies) {
        final Plugin? depPlugin = _registry.get(dependency.pluginId);
        if (depPlugin == null && !dependency.optional) {
          throw Exception(
              'Required dependency not found: ${dependency.pluginId}');
        }

        if (depPlugin != null && depPlugin.currentState == PluginState.error) {
          throw Exception('Dependency in error state: ${dependency.pluginId}');
        }
      }
    } catch (e) {
      throw Exception('Dependency validation failed: $e');
    }
  }

  /// 安全卸载插件
  Future<void> _safeUnloadPlugin(String pluginId) async {
    try {
      final Plugin? plugin = _registry.get(pluginId);
      if (plugin == null) return;

      // 检查是否有其他插件依赖此插件
      final List<Plugin> dependentPlugins = _registry
          .getAll()
          .where((p) => p.dependencies.any((dep) => dep.pluginId == pluginId))
          .toList();

      if (dependentPlugins.isNotEmpty) {
        print(
            'Warning: Plugin $pluginId has dependent plugins: ${dependentPlugins.map((p) => p.id).join(', ')}');
      }

      // 执行安全卸载
      await _loader.unloadPlugin(pluginId);
    } catch (e) {
      throw Exception('Safe unload failed: $e');
    }
  }

  /// 从源重新加载插件
  Future<void> _reloadPluginFromSource(
      String pluginId, Plugin originalPlugin) async {
    try {
      // 重新加载插件（这里应该从文件系统重新读取）
      await _loader.loadPlugin(originalPlugin);
    } catch (e) {
      throw Exception('Plugin reload from source failed: $e');
    }
  }

  /// 验证重载结果
  Future<void> _validateReloadResult(String pluginId) async {
    try {
      final Plugin? plugin = _registry.get(pluginId);
      if (plugin == null) {
        throw Exception('Plugin not found after reload');
      }

      if (plugin.currentState == PluginState.error) {
        throw Exception('Plugin in error state after reload');
      }
    } catch (e) {
      throw Exception('Reload result validation failed: $e');
    }
  }

  /// 回滚失败的重载
  Future<void> _rollbackFailedReload(
    String pluginId,
    Plugin? originalPlugin,
    PluginStateSnapshot? backupSnapshot,
  ) async {
    try {
      print('Rolling back failed reload for plugin: $pluginId');

      // 尝试恢复原始插件
      if (originalPlugin != null) {
        try {
          await _loader.loadPlugin(originalPlugin);
        } catch (e) {
          print('Failed to restore original plugin: $e');
        }
      }

      // 尝试恢复状态快照
      if (backupSnapshot != null) {
        try {
          _stateSnapshots[pluginId] = backupSnapshot;
          await _restorePluginState(pluginId);
        } catch (e) {
          print('Failed to restore plugin state: $e');
        }
      }
    } catch (e) {
      print('Rollback failed: $e');
    }
  }

  /// 验证和标准化路径
  Future<List<String>> _validateAndNormalizePaths(List<String> paths) async {
    final List<String> validPaths = <String>[];

    for (final String path in paths) {
      try {
        // 标准化路径分隔符
        final String normalizedPath = path.replaceAll('\\', '/');

        // 检查路径是否存在
        final Directory directory = Directory(normalizedPath);
        if (await directory.exists()) {
          validPaths.add(normalizedPath);
        } else {
          print('Warning: Path does not exist: $normalizedPath');
        }
      } catch (e) {
        print('Error validating path $path: $e');
      }
    }

    return validPaths;
  }

  /// 设置文件系统监听器
  Future<void> _setupFileSystemWatcher(String path) async {
    try {
      await _watchPluginPath(path);
    } catch (e) {
      print('Failed to setup file system watcher for $path: $e');
      rethrow;
    }
  }

  /// 初始化批量变更去重处理器
  void _initializeBatchChangeProcessor() {
    // 初始化批量变更处理逻辑
    // 这里可以设置去重时间窗口、批量处理策略等
    print('Batch change processor initialized');
  }

  /// 设置配置文件特殊监听
  Future<void> _setupConfigFileWatching(List<String> paths) async {
    for (final String path in paths) {
      try {
        // 查找配置文件
        final Directory directory = Directory(path);
        if (await directory.exists()) {
          await for (final FileSystemEntity entity
              in directory.list(recursive: true)) {
            if (entity is File) {
              final String fileName = entity.path.split('/').last;
              if (_isConfigFile(fileName)) {
                print('Watching config file: ${entity.path}');
                // 配置文件有特殊的监听逻辑
              }
            }
          }
        }
      } catch (e) {
        print('Error setting up config file watching for $path: $e');
      }
    }
  }

  /// 检查是否为配置文件
  bool _isConfigFile(String fileName) {
    const List<String> configFiles = [
      'plugin.yaml',
      'pubspec.yaml',
      'manifest.json',
      'config.json',
      'settings.yaml',
    ];
    return configFiles.contains(fileName);
  }

  /// 计算重载顺序（基于依赖关系）
  List<String> _calculateReloadOrder(List<Plugin> plugins) {
    final List<String> reloadOrder = <String>[];
    final Set<String> processed = <String>{};
    final Set<String> processing = <String>{};

    void processPlugin(String pluginId) {
      if (processed.contains(pluginId) || processing.contains(pluginId)) {
        return;
      }

      processing.add(pluginId);

      // 查找插件
      Plugin? plugin;
      try {
        plugin = plugins.firstWhere((p) => p.id == pluginId);
      } catch (e) {
        throw Exception('Plugin not found: $pluginId');
      }

      // 先处理依赖
      for (final dependency in plugin.dependencies) {
        if (!dependency.optional) {
          processPlugin(dependency.pluginId);
        }
      }

      processing.remove(pluginId);
      processed.add(pluginId);
      reloadOrder.add(pluginId);
    }

    // 处理所有插件
    for (final plugin in plugins) {
      if (!processed.contains(plugin.id)) {
        try {
          processPlugin(plugin.id);
        } catch (e) {
          print('Warning: Failed to process plugin ${plugin.id}: $e');
          // 添加到末尾作为备用
          if (!reloadOrder.contains(plugin.id)) {
            reloadOrder.add(plugin.id);
          }
        }
      }
    }

    return reloadOrder;
  }

  /// 处理批量重载失败
  Future<void> _handleBatchReloadFailures(
    List<String> failedPlugins,
    List<HotReloadResult> results,
  ) async {
    print('Handling batch reload failures for ${failedPlugins.length} plugins');

    for (final pluginId in failedPlugins) {
      try {
        // 尝试恢复失败的插件
        final snapshot = _stateSnapshots[pluginId];
        if (snapshot != null) {
          await _restorePluginState(pluginId);
          print('Restored state for failed plugin: $pluginId');
        }
      } catch (e) {
        print('Failed to restore plugin $pluginId: $e');
      }
    }
  }

  /// 创建当前状态快照
  void _createCurrentStateSnapshot(String pluginId) {
    try {
      final plugin = _registry.get(pluginId);
      if (plugin == null) {
        print('Plugin not found for snapshot creation: $pluginId');
        return;
      }

      final snapshot = PluginStateSnapshot(
        pluginId: pluginId,
        state: plugin.currentState,
        config: <String, dynamic>{
          'id': plugin.id,
          'name': plugin.name,
          'version': plugin.version,
          'currentState': plugin.currentState.toString(),
          'isEnabled': plugin.isEnabled,
          'createdAt': DateTime.now().toIso8601String(),
        },
        timestamp: DateTime.now(),
      );

      _stateSnapshots[pluginId] = snapshot;
      print('Created current state snapshot for plugin: $pluginId');
    } catch (e) {
      print('Failed to create current state snapshot for $pluginId: $e');
    }
  }

  /// 验证快照有效性
  bool _isSnapshotValid(PluginStateSnapshot snapshot) {
    try {
      // 检查快照是否过期（超过1小时）
      final age = DateTime.now().difference(snapshot.timestamp);
      if (age.inHours > 1) {
        return false;
      }

      // 检查快照数据完整性
      if (snapshot.config.isEmpty) {
        return false;
      }

      // 检查插件是否仍然存在
      if (!_registry.contains(snapshot.pluginId)) {
        return false;
      }

      return true;
    } catch (e) {
      print('Error validating snapshot: $e');
      return false;
    }
  }

  /// 估算插件实例内存占用
  double _estimateInstanceMemory(Plugin plugin) {
    try {
      // 基础实例内存：每个插件约1-3MB
      double baseMemory = 2.0;

      // 根据插件复杂度调整
      if (plugin.dependencies.length > 5) {
        baseMemory += 0.5;
      }

      // 根据插件状态调整
      if (plugin.currentState == PluginState.started) {
        baseMemory += 1.0;
      }

      return baseMemory;
    } catch (e) {
      return 1.0; // 默认值
    }
  }

  /// 估算插件资源内存占用
  double _estimateResourceMemory(Plugin plugin) {
    try {
      // 基础资源内存：约0.5-2MB
      double resourceMemory = 1.0;

      // 根据插件名称长度估算（简单启发式）
      resourceMemory += plugin.name.length * 0.001;
      resourceMemory += plugin.description.length * 0.001;

      return resourceMemory;
    } catch (e) {
      return 0.5; // 默认值
    }
  }

  /// 估算插件缓存内存占用
  double _estimateCacheMemory(Plugin plugin) {
    try {
      // 基础缓存内存：约0.2-1MB
      double cacheMemory = 0.5;

      // 根据插件是否启用调整
      if (plugin.isEnabled) {
        cacheMemory += 0.3;
      }

      return cacheMemory;
    } catch (e) {
      return 0.2; // 默认值
    }
  }
}
