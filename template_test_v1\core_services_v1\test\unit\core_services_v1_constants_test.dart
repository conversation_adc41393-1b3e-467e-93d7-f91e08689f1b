/*
---------------------------------------------------------------
File name:          core_services_v1_constants_test.dart
Author:             Ming Status CLI
Date created:       2025-07-14
Last modified:      2025-07-14
Dart Version:       3.2+
Description:        core_services_v1 Constants 单元测试
---------------------------------------------------------------
Change History:
    2025-07-14: Initial creation - core_services_v1 Constants 单元测试;
---------------------------------------------------------------
*/

import 'package:test/test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:core_services_v1/lib/src/constants/core_services_v1_constants.dart';

void main() {
  group('CoreServicesV1Constants 单元测试', () {
    late CoreServicesV1Constants constants;

    setUp(() {
      constants = CoreServicesV1Constants();
    });

    tearDown(() async {
    });

    test('应用常量应该正确定义', () {
      expect(CoreServicesV1AppConstants.appName, isNotEmpty);
      expect(CoreServicesV1AppConstants.version, isNotEmpty);
      expect(CoreServicesV1AppConstants.buildNumber, greaterThan(0));
    });

    test('API常量应该正确定义', () {
      expect(CoreServicesV1ApiConstants.baseUrl, isNotEmpty);
      expect(CoreServicesV1ApiConstants.endpoints.users, isNotEmpty);
      expect(CoreServicesV1ApiConstants.headers.contentType, isNotEmpty);
    });

  });
}
