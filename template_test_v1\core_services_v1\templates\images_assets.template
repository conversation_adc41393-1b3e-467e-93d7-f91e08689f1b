/*
---------------------------------------------------------------
File name:          images.dart
Author:             Ming Status CLI
Date created:       2025-07-14
Last modified:      2025-07-14
Dart Version:       3.2+
Description:        core_services_v1应用程序图片资源
---------------------------------------------------------------
Change History:
    2025-07-14: Initial creation - core_services_v1应用程序图片资源;
---------------------------------------------------------------
*/


/// 图片资源路径常量
///
/// 定义应用程序中使用的所有图片资源路径
class ImageAssets {
  /// 私有构造函数，防止实例化
  ImageAssets._();

  /// 资源根路径
  static const String _basePath = 'assets/images';

  /// === 品牌资源 ===
  
  /// 应用图标
  static const String logo = '$_basePath/logo.png';
  
  /// 应用图标（暗色主题）
  static const String logoDark = '$_basePath/logo_dark.png';
  
  /// 应用图标（小尺寸）
  static const String logoSmall = '$_basePath/logo_small.png';
  
  /// 启动画面
  static const String splash = '$_basePath/splash.png';
  
  /// 品牌横幅
  static const String banner = '$_basePath/banner.png';

  /// === 界面资源 ===
  
  /// 占位符图片
  static const String placeholder = '$_basePath/placeholder.png';
  
  /// 空状态图片
  static const String emptyState = '$_basePath/empty_state.png';
  
  /// 错误状态图片
  static const String errorState = '$_basePath/error_state.png';
  
  /// 加载状态图片
  static const String loadingState = '$_basePath/loading_state.png';
  
  /// 成功状态图片
  static const String successState = '$_basePath/success_state.png';

  /// === 用户相关 ===
  
  /// 默认头像
  static const String defaultAvatar = '$_basePath/default_avatar.png';
  
  /// 用户背景
  static const String userBackground = '$_basePath/user_background.png';

  /// === 功能图标 ===
  
  /// 设置图标
  static const String settingsIcon = '$_basePath/settings_icon.png';
  
  /// 通知图标
  static const String notificationIcon = '$_basePath/notification_icon.png';
  
  /// 搜索图标
  static const String searchIcon = '$_basePath/search_icon.png';

  /// === 背景图片 ===
  
  /// 主背景
  static const String mainBackground = '$_basePath/main_background.png';
  
  /// 登录背景
  static const String loginBackground = '$_basePath/login_background.png';

  /// 获取完整的图片路径
  ///
  /// [imageName] 图片文件名
  /// 返回完整的资源路径
  static String getImagePath(String imageName) {
    return '$_basePath/$imageName';
  }

  /// 检查图片是否存在
  ///
  /// [imagePath] 图片路径
  /// 返回图片是否存在
  static bool imageExists(String imagePath) {
    // 在实际应用中，这里可以检查资源是否存在
    return true;
  }
}
