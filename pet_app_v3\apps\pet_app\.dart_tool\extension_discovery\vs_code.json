{"version": 2, "entries": [{"package": "app_manager", "rootUri": "../../../packages/app_manager/", "packageUri": "lib/"}, {"package": "communication_system", "rootUri": "../../../packages/communication_system/", "packageUri": "lib/"}, {"package": "creative_workshop", "rootUri": "../../../packages/creative_workshop/", "packageUri": "lib/"}, {"package": "desktop_pet", "rootUri": "../../../packages/desktop_pet/", "packageUri": "lib/"}, {"package": "home_dashboard", "rootUri": "../../../packages/home_dashboard/", "packageUri": "lib/"}, {"package": "plugin_system", "rootUri": "../../../packages/plugin_system/", "packageUri": "lib/"}, {"package": "settings_system", "rootUri": "../../../packages/settings_system/", "packageUri": "lib/"}, {"package": "pet_app_v3", "rootUri": "../", "packageUri": "lib/"}]}