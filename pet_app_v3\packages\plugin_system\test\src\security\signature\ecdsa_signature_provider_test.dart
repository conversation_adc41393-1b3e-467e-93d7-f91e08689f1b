/*
---------------------------------------------------------------
File name:          ecdsa_signature_provider_test.dart
Author:             lgnorant-lu
Date created:       2025/07/28
Last modified:      2025/07/28
Dart Version:       3.2+
Description:        ECDSA数字签名提供者测试
---------------------------------------------------------------
Change History:
    2025/07/28: Initial creation - ECDSA数字签名提供者测试;
---------------------------------------------------------------
*/

import 'dart:typed_data';

import 'package:test/test.dart';
import 'package:plugin_system/src/security/signature/ecdsa_signature_provider.dart';
import 'package:plugin_system/src/security/signature/plugin_signature_core.dart';

void main() {
  group('ECDSASignatureProvider', () {
    late ECDSASignatureProvider provider;

    setUp(() {
      provider = ECDSASignatureProvider();
    });

    tearDown(() {
      provider.clearCache();
    });

    group('基本功能测试', () {
      test('应该返回正确的算法类型', () {
        expect(provider.algorithm, equals(PluginSignatureAlgorithm.ecdsaP256));
      });

      test('应该能生成密钥对', () async {
        await provider.generateKeyPair('test_key');

        final publicKey = await provider.getPublicKey('test_key');
        final privateKey = await provider.getPrivateKey('test_key');

        expect(publicKey, isNotNull);
        expect(privateKey, isNotNull);
      });

      test('应该能缓存密钥对', () async {
        await provider.generateKeyPair('cached_key');

        final publicKey1 = await provider.getPublicKey('cached_key');
        final publicKey2 = await provider.getPublicKey('cached_key');

        expect(identical(publicKey1, publicKey2), isTrue);
      });

      test('应该能清理缓存', () async {
        await provider.generateKeyPair('clear_test_key');
        expect(await provider.getPublicKey('clear_test_key'), isNotNull);

        provider.clearCache();

        // 清理后应该重新生成密钥
        final newPublicKey = await provider.getPublicKey('clear_test_key');
        expect(newPublicKey, isNotNull);
      });
    });

    group('签名生成测试', () {
      test('应该能生成签名', () async {
        final testData = Uint8List.fromList('Hello, World!'.codeUnits);

        final signature =
            await provider.generateSignature(testData, 'sign_key');

        expect(signature, isNotNull);
        expect(signature.length, greaterThan(0));
      });

      test('相同数据应该生成一致的签名', () async {
        final testData = Uint8List.fromList('Consistent test'.codeUnits);

        final signature1 =
            await provider.generateSignature(testData, 'consistent_key');
        final signature2 =
            await provider.generateSignature(testData, 'consistent_key');

        // 注意：真实的ECDSA签名可能包含随机性，所以这个测试可能需要调整
        // 但我们的简化实现应该是确定性的
        expect(signature1.length, equals(signature2.length));
      });

      test('不同数据应该生成不同的签名', () async {
        final testData1 = Uint8List.fromList('Data 1'.codeUnits);
        final testData2 = Uint8List.fromList('Data 2'.codeUnits);

        final signature1 =
            await provider.generateSignature(testData1, 'diff_key');
        final signature2 =
            await provider.generateSignature(testData2, 'diff_key');

        expect(signature1, isNot(equals(signature2)));
      });
    });

    group('签名验证测试', () {
      test('应该能验证有效签名', () async {
        final testData = Uint8List.fromList('Valid signature test'.codeUnits);

        final signature =
            await provider.generateSignature(testData, 'verify_key');
        final isValid =
            await provider.verifySignature(signature, testData, 'verify_key');

        expect(isValid, isTrue);
      });

      test('应该拒绝无效签名', () async {
        final testData = Uint8List.fromList('Invalid signature test'.codeUnits);
        final invalidSignature = Uint8List.fromList([1, 2, 3, 4, 5]);

        final isValid = await provider.verifySignature(
            invalidSignature, testData, 'invalid_key');

        expect(isValid, isFalse);
      });

      test('应该拒绝被篡改的数据', () async {
        final originalData = Uint8List.fromList('Original data'.codeUnits);
        final tamperedData = Uint8List.fromList('Tampered data'.codeUnits);

        final signature =
            await provider.generateSignature(originalData, 'tamper_key');
        final isValid = await provider.verifySignature(
            signature, tamperedData, 'tamper_key');

        expect(isValid, isFalse);
      });

      test('应该拒绝错误长度的签名', () async {
        final testData = Uint8List.fromList('Length test'.codeUnits);
        final shortSignature = Uint8List.fromList([1, 2, 3]); // 太短

        final isValid = await provider.verifySignature(
            shortSignature, testData, 'length_key');

        expect(isValid, isFalse);
      });
    });

    group('密钥对匹配测试', () {
      test('应该验证密钥对匹配', () async {
        await provider.generateKeyPair('match_key');

        final isMatch = await provider.verifyKeyPairMatch('match_key');

        expect(isMatch, isTrue);
      });

      test('应该处理不存在的密钥', () async {
        final publicKey = await provider.getPublicKey('nonexistent_key');
        final privateKey = await provider.getPrivateKey('nonexistent_key');

        expect(publicKey, isNotNull); // 应该自动生成
        expect(privateKey, isNotNull); // 应该自动生成
      });
    });

    group('密钥信息测试', () {
      test('应该返回密钥信息', () async {
        await provider.generateKeyPair('info_key');

        final keyInfo = await provider.getKeyInfo('info_key');

        expect(keyInfo, isNotNull);
        expect(keyInfo['algorithm'], equals('ECDSA'));
        expect(keyInfo['curve'], equals('P-256'));
        expect(keyInfo['keySize'], equals(256));
        expect(keyInfo['created'], isNotNull);
      });

      test('应该处理密钥信息获取错误', () async {
        // 这个测试可能需要根据实际实现调整
        final keyInfo = await provider.getKeyInfo('error_key');

        expect(keyInfo, isNotNull);
        // 即使出错，也应该返回一些信息
      });
    });

    group('错误处理测试', () {
      test('应该处理签名生成错误', () async {
        // 使用空数据测试错误处理
        final emptyData = Uint8List(0);

        try {
          await provider.generateSignature(emptyData, 'error_key');
          // 如果没有抛出异常，签名应该仍然有效
        } catch (e) {
          expect(e, isA<Exception>());
        }
      });

      test('应该处理签名验证错误', () async {
        final testData = Uint8List.fromList('Error test'.codeUnits);
        final nullSignature = Uint8List(0);

        final isValid = await provider.verifySignature(
            nullSignature, testData, 'error_verify_key');

        expect(isValid, isFalse);
      });
    });

    group('性能测试', () {
      test('应该能处理大量数据', () async {
        // 创建1MB的测试数据
        final largeData = Uint8List(1024 * 1024);
        for (int i = 0; i < largeData.length; i++) {
          largeData[i] = i % 256;
        }

        final signature =
            await provider.generateSignature(largeData, 'large_key');
        final isValid =
            await provider.verifySignature(signature, largeData, 'large_key');

        expect(signature, isNotNull);
        expect(isValid, isTrue);
      });

      test('应该能快速生成多个密钥对', () async {
        final stopwatch = Stopwatch()..start();

        for (int i = 0; i < 5; i++) {
          await provider.generateKeyPair('perf_key_$i');
        }

        stopwatch.stop();

        // 5个密钥对应该在合理时间内生成完成（比如10秒）
        expect(stopwatch.elapsedMilliseconds, lessThan(10000));
      });
    });
  });
}
