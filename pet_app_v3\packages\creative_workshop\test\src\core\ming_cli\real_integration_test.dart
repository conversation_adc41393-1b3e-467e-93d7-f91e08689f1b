import 'dart:io';
import 'package:test/test.dart';
import 'package:path/path.dart' as path;
import 'package:creative_workshop/src/core/ming_cli/local_ming_cli_service.dart';
import 'package:creative_workshop/src/core/ming_cli/ming_cli_result.dart';

void main() {
  group('Ming CLI真实集成测试', () {
    late LocalMingCliService service;

    setUpAll(() async {
      service = LocalMingCliService.instance;
      await service.initialize();
    });

    group('服务初始化和检测', () {
      test('应该能够初始化服务', () {
        expect(service, isNotNull);
        expect(service.currentMode, isA<MingCliMode>());
      });

      test('应该能够检测Ming CLI状态', () async {
        await service.detectMingCli();
        
        expect(service.currentMode, isA<MingCliMode>());
        expect(service.status, isNotEmpty);
        expect(service.statusDescription, isNotEmpty);
      });

      test('应该能够获取详细状态', () {
        final status = service.getDetailedStatus();
        
        expect(status, isA<Map<String, dynamic>>());
        expect(status['currentMode'], isA<String>());
        expect(status['isInstalled'], isA<bool>());
        expect(status['version'], isA<String>());
        expect(status['timestamp'], isA<String>());
      });

      test('应该能够刷新状态', () async {
        final oldMode = service.currentMode;
        await service.refresh();
        
        // 模式可能相同，但刷新操作应该成功
        expect(service.currentMode, isA<MingCliMode>());
      });
    });

    group('命令执行测试', () {
      test('应该能够执行version命令', () async {
        final result = await service.executeCommand(['--version']);
        
        expect(result, isA<MingCliResult>());
        expect(result.mode, equals(service.currentMode));
        expect(result.executionTime, isA<Duration>());
        expect(result.output, isNotEmpty);
        
        // 在任何模式下，version命令都应该成功
        expect(result.success, isTrue);
        expect(result.exitCode, equals(0));
      });

      test('应该能够执行doctor命令', () async {
        final result = await service.executeCommand(['doctor']);
        
        expect(result, isA<MingCliResult>());
        expect(result.mode, equals(service.currentMode));
        expect(result.output, isNotEmpty);
        expect(result.output.contains('环境检查'), isTrue);
      });

      test('应该能够处理无效命令', () async {
        final result = await service.executeCommand(['invalid_command_xyz']);
        
        expect(result, isA<MingCliResult>());
        expect(result.mode, equals(service.currentMode));
        
        // 根据模式不同，结果可能不同
        if (service.currentMode == MingCliMode.fallback) {
          expect(result.success, isFalse);
          expect(result.output.contains('降级模式'), isTrue);
        }
      });

      test('应该能够处理空命令', () async {
        final result = await service.executeCommand([]);
        
        expect(result, isA<MingCliResult>());
        expect(result.mode, equals(service.currentMode));
        
        // 空命令通常返回版本信息或帮助
        expect(result.output, isNotEmpty);
      });
    });

    group('模式特定测试', () {
      test('Dart Run模式应该能够调用真实的Ming CLI', () async {
        // 检查是否存在tools/ming_status_cli目录
        final mingCliPath = path.join(Directory.current.path, 'tools', 'ming_status_cli');
        final mingCliDir = Directory(mingCliPath);
        
        if (await mingCliDir.exists()) {
          final pubspecFile = File(path.join(mingCliPath, 'pubspec.yaml'));
          final mainFile = File(path.join(mingCliPath, 'bin', 'ming_status_cli.dart'));
          
          if (await pubspecFile.exists() && await mainFile.exists()) {
            // 如果文件存在，应该能够检测到Dart Run模式
            await service.detectMingCli();
            
            if (service.currentMode == MingCliMode.dartRun) {
              final result = await service.executeCommand(['--version']);
              expect(result.success, isTrue);
              expect(result.mode, equals(MingCliMode.dartRun));
            }
          }
        }
      });

      test('降级模式应该提供基础功能', () async {
        // 强制设置为降级模式进行测试
        final originalMode = service.currentMode;
        
        // 测试降级模式的基础命令
        final versionResult = await service.executeCommand(['--version']);
        expect(versionResult.output, contains('ming_status_cli'));
        
        final doctorResult = await service.executeCommand(['doctor']);
        expect(doctorResult.output, contains('环境检查'));
      });
    });

    group('错误处理测试', () {
      test('应该能够处理命令执行异常', () async {
        // 测试异常情况下的错误处理
        final result = await service.executeCommand(['--invalid-flag-xyz']);
        
        expect(result, isA<MingCliResult>());
        expect(result.executionTime, isA<Duration>());
        
        // 根据模式不同，错误处理方式可能不同
        if (service.currentMode == MingCliMode.fallback) {
          expect(result.success, isFalse);
        }
      });

      test('应该记录执行时间', () async {
        final result = await service.executeCommand(['--version']);
        
        expect(result.executionTime, isA<Duration>());
        expect(result.executionTime.inMilliseconds, greaterThan(0));
      });

      test('应该正确设置退出码', () async {
        final successResult = await service.executeCommand(['--version']);
        if (successResult.success) {
          expect(successResult.exitCode, equals(0));
        }
        
        final failResult = await service.executeCommand(['invalid_command']);
        if (!failResult.success) {
          expect(failResult.exitCode, isNot(equals(0)));
        }
      });
    });

    group('真实Ming CLI功能测试', () {
      test('如果可用，应该能够执行plugin命令', () async {
        if (service.currentMode == MingCliMode.dartRun || 
            service.currentMode == MingCliMode.systemInstalled) {
          final result = await service.executeCommand(['plugin', '--help']);
          
          // 如果Ming CLI支持plugin命令，应该返回帮助信息
          expect(result, isA<MingCliResult>());
          expect(result.output, isNotEmpty);
        }
      });

      test('如果可用，应该能够执行template命令', () async {
        if (service.currentMode == MingCliMode.dartRun || 
            service.currentMode == MingCliMode.systemInstalled) {
          final result = await service.executeCommand(['template', '--help']);
          
          // 如果Ming CLI支持template命令，应该返回帮助信息
          expect(result, isA<MingCliResult>());
          expect(result.output, isNotEmpty);
        }
      });

      test('如果可用，应该能够执行create命令', () async {
        if (service.currentMode == MingCliMode.dartRun || 
            service.currentMode == MingCliMode.systemInstalled) {
          final result = await service.executeCommand(['create', '--help']);
          
          // 如果Ming CLI支持create命令，应该返回帮助信息
          expect(result, isA<MingCliResult>());
          expect(result.output, isNotEmpty);
        }
      });
    });

    group('集成完整性测试', () {
      test('应该能够连续执行多个命令', () async {
        final commands = [
          ['--version'],
          ['doctor'],
        ];
        
        for (final command in commands) {
          final result = await service.executeCommand(command);
          expect(result, isA<MingCliResult>());
          expect(result.executionTime, isA<Duration>());
        }
      });

      test('应该保持状态一致性', () async {
        final initialMode = service.currentMode;
        final initialStatus = service.status;
        
        // 执行一些命令
        await service.executeCommand(['--version']);
        await service.executeCommand(['doctor']);
        
        // 状态应该保持一致
        expect(service.currentMode, equals(initialMode));
        expect(service.status, equals(initialStatus));
      });

      test('应该能够处理并发命令执行', () async {
        final futures = <Future<MingCliResult>>[];
        
        // 并发执行多个命令
        for (int i = 0; i < 3; i++) {
          futures.add(service.executeCommand(['--version']));
        }
        
        final results = await Future.wait(futures);
        
        for (final result in results) {
          expect(result, isA<MingCliResult>());
          expect(result.success, isTrue);
        }
      });
    });
  });
}
