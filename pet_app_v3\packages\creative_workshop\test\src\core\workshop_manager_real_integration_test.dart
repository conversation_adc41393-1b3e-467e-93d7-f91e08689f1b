import 'dart:async';
import 'package:test/test.dart';
import 'package:creative_workshop/creative_workshop.dart';
import 'package:plugin_system/plugin_system.dart' as plugin_sys;

void main() {
  group('WorkshopManager真实集成测试', () {
    late WorkshopManager workshopManager;
    late plugin_sys.PluginRegistry pluginSystemRegistry;

    setUpAll(() async {
      workshopManager = WorkshopManager.instance;
      pluginSystemRegistry = plugin_sys.PluginRegistry.instance;
    });

    tearDown(() async {
      // 清理测试状态
      try {
        await workshopManager.dispose();
      } catch (e) {
        // 忽略清理错误
      }
    });

    group('WorkshopManager生命周期测试', () {
      test('应该能够完整初始化和启动', () async {
        // 1. 验证初始状态
        expect(workshopManager.state, equals(WorkshopState.uninitialized));

        // 2. 执行初始化
        final initResult = await workshopManager.initialize();
        expect(initResult, isTrue, reason: '初始化应该成功');
        expect(workshopManager.state, equals(WorkshopState.ready));

        // 3. 执行启动
        final startResult = await workshopManager.start();
        expect(startResult, isTrue, reason: '启动应该成功');
        expect(workshopManager.state, equals(WorkshopState.running));
      });

      test('应该能够处理重复初始化', () async {
        // 第一次初始化
        final firstInit = await workshopManager.initialize();
        expect(firstInit, isTrue);

        // 第二次初始化应该跳过
        final secondInit = await workshopManager.initialize();
        expect(secondInit, isTrue);
        expect(workshopManager.state, equals(WorkshopState.ready));
      });
    });

    group('内置插件注册测试', () {
      test('应该能够注册内置工具插件到Plugin System', () async {
        // 记录初始插件数量
        final initialPluginIds = <String>[];
        
        // 初始化WorkshopManager（这会触发内置插件注册）
        await workshopManager.initialize();

        // 验证内置插件是否已注册到Plugin System
        final expectedBuiltinPlugins = [
          'builtin_brush_tool',
          'builtin_text_editor',
          'builtin_image_viewer',
        ];

        for (final pluginId in expectedBuiltinPlugins) {
          expect(pluginSystemRegistry.contains(pluginId), isTrue,
                 reason: '内置插件应该已注册到Plugin System: $pluginId');
          
          final metadata = pluginSystemRegistry.getMetadata(pluginId);
          expect(metadata, isNotNull, reason: '插件元数据应该存在: $pluginId');
          expect(metadata!.name.isNotEmpty, isTrue, reason: '插件名称不应为空');
          expect(metadata.version.isNotEmpty, isTrue, reason: '插件版本不应为空');
        }
      });

      test('应该能够获取内置插件状态', () async {
        await workshopManager.initialize();

        final pluginId = 'builtin_brush_tool';
        expect(pluginSystemRegistry.contains(pluginId), isTrue);

        final state = pluginSystemRegistry.getState(pluginId);
        expect(state, isNotNull, reason: '插件状态应该存在');
        expect(state, equals(plugin_sys.PluginState.initialized),
               reason: '插件应该已初始化');
      });

      test('应该能够与内置插件通信', () async {
        await workshopManager.initialize();

        final pluginId = 'builtin_brush_tool';
        final plugin = pluginSystemRegistry.get(pluginId);
        expect(plugin, isNotNull, reason: '插件实例应该存在');

        // 测试ping消息
        final pingResponse = await plugin!.handleMessage('ping', <String, dynamic>{});
        expect(pingResponse, isA<Map<String, dynamic>>());
        expect(pingResponse['status'], equals('pong'));
        expect(pingResponse['toolType'], equals('brush'));

        // 测试getInfo消息
        final infoResponse = await plugin.handleMessage('getInfo', <String, dynamic>{});
        expect(infoResponse, isA<Map<String, dynamic>>());
        expect(infoResponse['id'], equals(pluginId));
        expect(infoResponse['name'], equals('内置画笔工具'));
        expect(infoResponse['toolType'], equals('brush'));
      });
    });

    group('插件生命周期管理测试', () {
      test('应该能够启动和停止内置插件', () async {
        await workshopManager.initialize();

        final pluginId = 'builtin_text_editor';
        final plugin = pluginSystemRegistry.get(pluginId);
        expect(plugin, isNotNull);

        // 验证初始状态
        expect(plugin!.currentState, equals(plugin_sys.PluginState.initialized));

        // 启动插件
        await plugin.start();
        expect(plugin.currentState, equals(plugin_sys.PluginState.started));

        // 暂停插件
        await plugin.pause();
        expect(plugin.currentState, equals(plugin_sys.PluginState.paused));

        // 恢复插件
        await plugin.resume();
        expect(plugin.currentState, equals(plugin_sys.PluginState.started));

        // 停止插件
        await plugin.stop();
        expect(plugin.currentState, equals(plugin_sys.PluginState.stopped));
      });

      test('应该能够监听插件状态变化', () async {
        await workshopManager.initialize();

        final pluginId = 'builtin_image_viewer';
        final plugin = pluginSystemRegistry.get(pluginId);
        expect(plugin, isNotNull);

        final stateChanges = <plugin_sys.PluginState>[];
        final subscription = plugin!.stateChanges.listen((state) {
          stateChanges.add(state);
        });

        // 触发状态变化
        await plugin.start();
        await plugin.pause();
        await plugin.resume();

        // 等待状态变化传播
        await Future<void>.delayed(const Duration(milliseconds: 100));

        subscription.cancel();

        // 验证状态变化序列
        expect(stateChanges.length, greaterThanOrEqualTo(3));
        expect(stateChanges.contains(plugin_sys.PluginState.started), isTrue);
        expect(stateChanges.contains(plugin_sys.PluginState.paused), isTrue);
      });
    });

    group('错误处理测试', () {
      test('应该能够处理插件初始化失败', () async {
        // 这个测试验证错误处理机制
        // 由于我们的内置插件实现比较简单，这里主要验证异常不会导致系统崩溃
        
        expect(() async => await workshopManager.initialize(), returnsNormally);
      });

      test('应该能够处理无效的插件消息', () async {
        await workshopManager.initialize();

        final pluginId = 'builtin_brush_tool';
        final plugin = pluginSystemRegistry.get(pluginId);
        expect(plugin, isNotNull);

        // 发送无效消息
        expect(() async => await plugin!.handleMessage('invalid_action', <String, dynamic>{}),
               throwsA(isA<UnsupportedError>()));
      });
    });

    group('Plugin System集成验证', () {
      test('应该能够通过Plugin System访问所有内置插件', () async {
        await workshopManager.initialize();

        // 获取所有已注册的插件
        final allPluginIds = <String>[];
        final expectedBuiltinPlugins = [
          'builtin_brush_tool',
          'builtin_text_editor', 
          'builtin_image_viewer',
        ];

        // 验证所有内置插件都可以通过Plugin System访问
        for (final pluginId in expectedBuiltinPlugins) {
          expect(pluginSystemRegistry.contains(pluginId), isTrue);
          
          final plugin = pluginSystemRegistry.get(pluginId);
          expect(plugin, isNotNull);
          expect(plugin!.id, equals(pluginId));
          
          final metadata = pluginSystemRegistry.getMetadata(pluginId);
          expect(metadata, isNotNull);
          expect(metadata!.id, equals(pluginId));
        }
      });

      test('应该能够验证插件类型和属性', () async {
        await workshopManager.initialize();

        final pluginId = 'builtin_brush_tool';
        final plugin = pluginSystemRegistry.get(pluginId);
        expect(plugin, isNotNull);

        // 验证插件属性
        expect(plugin!.category, equals(plugin_sys.PluginType.tool));
        expect(plugin.supportedPlatforms.isNotEmpty, isTrue);
        expect(plugin.supportedPlatforms.contains(plugin_sys.SupportedPlatform.android), isTrue);
        expect(plugin.supportedPlatforms.contains(plugin_sys.SupportedPlatform.ios), isTrue);
        expect(plugin.supportedPlatforms.contains(plugin_sys.SupportedPlatform.web), isTrue);

        // 验证插件清单
        final manifest = plugin.manifest;
        expect(manifest.id, equals(pluginId));
        expect(manifest.name, equals('内置画笔工具'));
        expect(manifest.category, equals('tool'));
        expect(manifest.platforms.isNotEmpty, isTrue);
      });

      test('应该能够验证插件加载时间', () async {
        await workshopManager.initialize();

        final pluginId = 'builtin_text_editor';
        final plugin = pluginSystemRegistry.get(pluginId);
        expect(plugin, isNotNull);

        // 验证加载时间已记录
        expect(plugin!.loadTime, isNotNull);
        expect(plugin.loadTime!.inMilliseconds, greaterThan(0));
        expect(plugin.loadTime!.inSeconds, lessThan(5)); // 应该在5秒内完成加载
      });
    });

    group('端到端集成测试', () {
      test('验证完整的插件发现和注册流程', () async {
        // 1. 初始化WorkshopManager
        final initResult = await workshopManager.initialize();
        expect(initResult, isTrue);

        // 2. 启动WorkshopManager
        final startResult = await workshopManager.start();
        expect(startResult, isTrue);

        // 3. 验证所有内置插件都已正确注册和初始化
        final expectedPlugins = [
          'builtin_brush_tool',
          'builtin_text_editor',
          'builtin_image_viewer',
        ];

        for (final pluginId in expectedPlugins) {
          // 验证插件在Plugin System中存在
          expect(pluginSystemRegistry.contains(pluginId), isTrue);
          
          // 验证插件状态正确
          final state = pluginSystemRegistry.getState(pluginId);
          expect(state, equals(plugin_sys.PluginState.initialized));
          
          // 验证插件可以响应消息
          final plugin = pluginSystemRegistry.get(pluginId);
          final response = await plugin!.handleMessage('ping', <String, dynamic>{});
          expect(response['status'], equals('pong'));
        }

        // 4. 验证WorkshopManager状态正确
        expect(workshopManager.state, equals(WorkshopState.running));
      });
    });
  });
}
