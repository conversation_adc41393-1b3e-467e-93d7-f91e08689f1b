/*
---------------------------------------------------------------
File name:          plugin_module_manager.dart
Author:             lgnorant-lu
Date created:       2025-07-26
Last modified:      2025-07-26
Dart Version:       3.2+
Description:        插件模块管理器实现 - Phase 4.2 架构重构和模块解耦
---------------------------------------------------------------
Change History:
    2025-07-26: Phase 4.2 - 模块管理器实现;
---------------------------------------------------------------
*/

import 'dart:async';

import 'package:plugin_system/src/architecture/dependency_injection_container.dart';
import 'package:plugin_system/src/architecture/plugin_module_interface.dart';

/// 模块管理器配置
class ModuleManagerConfig {
  const ModuleManagerConfig({
    this.enableParallelInitialization = true,
    this.initializationTimeout = const Duration(minutes: 5),
    this.enableHealthCheck = true,
    this.healthCheckInterval = const Duration(minutes: 1),
    this.enableDependencyValidation = true,
    this.maxRetryCount = 3,
    this.retryDelay = const Duration(seconds: 1),
  });

  /// 启用并行初始化
  final bool enableParallelInitialization;

  /// 初始化超时
  final Duration initializationTimeout;

  /// 启用健康检查
  final bool enableHealthCheck;

  /// 健康检查间隔
  final Duration healthCheckInterval;

  /// 启用依赖验证
  final bool enableDependencyValidation;

  /// 最大重试次数
  final int maxRetryCount;

  /// 重试延迟
  final Duration retryDelay;
}

/// 模块启动结果
class ModuleStartupResult {
  const ModuleStartupResult({
    required this.moduleName,
    required this.success,
    this.error,
    this.duration,
  });

  /// 模块名称
  final String moduleName;

  /// 是否成功
  final bool success;

  /// 错误信息
  final String? error;

  /// 启动时长
  final Duration? duration;
}

/// 插件模块管理器实现
///
/// 提供模块注册、依赖解析、生命周期管理等功能
class PluginModuleManagerImpl implements PluginModuleManager {
  PluginModuleManagerImpl({
    required this.container,
    ModuleManagerConfig? config,
  }) : _config = config ?? const ModuleManagerConfig();

  /// 依赖注入容器
  final DIContainer container;

  /// 配置
  final ModuleManagerConfig _config;

  /// 注册的模块
  final Map<String, IPluginModule> _modules = <String, IPluginModule>{};

  /// 模块启动顺序
  List<String>? _startupOrder;

  /// 健康检查定时器
  Timer? _healthCheckTimer;

  /// 是否已初始化
  bool _initialized = false;

  /// 是否已启动
  bool _started = false;

  @override
  void registerModule(IPluginModule module) {
    final moduleName = module.metadata.name;
    
    if (_modules.containsKey(moduleName)) {
      throw StateError('Module $moduleName is already registered');
    }

    _modules[moduleName] = module;
    
    // 注册模块的依赖
    module.registerDependencies(container);
    
    // 清除缓存的启动顺序
    _startupOrder = null;
  }

  @override
  void unregisterModule(String moduleName) {
    final module = _modules.remove(moduleName);
    if (module != null) {
      // 异步销毁模块
      module.dispose().catchError((e) {
        // 记录错误但不抛出
      });
      
      // 清除缓存的启动顺序
      _startupOrder = null;
    }
  }

  @override
  IPluginModule? getModule(String moduleName) => _modules[moduleName];

  @override
  List<IPluginModule> getAllModules() => _modules.values.toList();

  @override
  Future<void> initializeModules() async {
    if (_initialized) {
      throw StateError('Modules already initialized');
    }

    // 验证依赖
    if (_config.enableDependencyValidation) {
      _validateDependencies();
    }

    // 解析启动顺序
    final startupOrder = resolveStartupOrder();

    // 初始化模块
    if (_config.enableParallelInitialization) {
      await _initializeModulesParallel(startupOrder);
    } else {
      await _initializeModulesSequential(startupOrder);
    }

    _initialized = true;
  }

  @override
  Future<void> startModules() async {
    if (!_initialized) {
      throw StateError('Modules not initialized');
    }

    if (_started) {
      throw StateError('Modules already started');
    }

    final startupOrder = resolveStartupOrder();
    final results = <ModuleStartupResult>[];

    for (final moduleName in startupOrder) {
      final module = _modules[moduleName]!;
      final startTime = DateTime.now();

      try {
        await module.start().timeout(_config.initializationTimeout);
        
        final duration = DateTime.now().difference(startTime);
        results.add(ModuleStartupResult(
          moduleName: moduleName,
          success: true,
          duration: duration,
        ),);
      } catch (e) {
        final duration = DateTime.now().difference(startTime);
        results.add(ModuleStartupResult(
          moduleName: moduleName,
          success: false,
          error: e.toString(),
          duration: duration,
        ),);

        // 如果是关键模块启动失败，停止整个启动过程
        if (module.metadata.priority == ModulePriority.critical) {
          throw StateError('Critical module $moduleName failed to start: $e');
        }
      }
    }

    // 启动健康检查
    if (_config.enableHealthCheck) {
      _startHealthCheck();
    }

    _started = true;
  }

  @override
  Future<void> stopModules() async {
    if (!_started) {
      return;
    }

    // 停止健康检查
    _healthCheckTimer?.cancel();

    // 按相反顺序停止模块
    final stopOrder = resolveStartupOrder().reversed.toList();

    for (final moduleName in stopOrder) {
      final module = _modules[moduleName];
      if (module != null && module.state == ModuleState.started) {
        try {
          await module.stop().timeout(_config.initializationTimeout);
        } catch (e) {
          // 记录错误但继续停止其他模块
        }
      }
    }

    _started = false;
  }

  @override
  Map<String, List<String>> getDependencyGraph() {
    final graph = <String, List<String>>{};

    for (final module in _modules.values) {
      graph[module.metadata.name] = List<String>.from(module.metadata.dependencies);
    }

    return graph;
  }

  @override
  List<String> resolveStartupOrder() {
    if (_startupOrder != null) {
      return _startupOrder!;
    }

    final graph = getDependencyGraph();
    final visited = <String>{};
    final visiting = <String>{};
    final order = <String>[];

    void visit(String moduleName) {
      if (visiting.contains(moduleName)) {
        throw StateError('Circular dependency detected involving module: $moduleName');
      }

      if (visited.contains(moduleName)) {
        return;
      }

      visiting.add(moduleName);

      final dependencies = graph[moduleName] ?? <String>[];
      for (final dependency in dependencies) {
        if (!graph.containsKey(dependency)) {
          throw StateError('Module $moduleName depends on unregistered module: $dependency');
        }
        visit(dependency);
      }

      visiting.remove(moduleName);
      visited.add(moduleName);
      order.add(moduleName);
    }

    // 按优先级排序模块
    final sortedModules = _modules.values.toList()
      ..sort((IPluginModule a, IPluginModule b) => b.metadata.priority.index.compareTo(a.metadata.priority.index));

    for (final module in sortedModules) {
      if (!visited.contains(module.metadata.name)) {
        visit(module.metadata.name);
      }
    }

    _startupOrder = order;
    return order;
  }

  /// 验证依赖
  void _validateDependencies() {
    final errors = <String>[];

    for (final module in _modules.values) {
      // 检查必需依赖
      for (final dependency in module.metadata.dependencies) {
        if (!_modules.containsKey(dependency)) {
          errors.add('Module ${module.metadata.name} depends on unregistered module: $dependency');
        }
      }

      // 检查可选依赖（只警告）
      for (final dependency in module.metadata.optionalDependencies) {
        if (!_modules.containsKey(dependency)) {
          // 这里可以记录警告日志
        }
      }
    }

    if (errors.isNotEmpty) {
      throw StateError('Dependency validation failed:\n${errors.join('\n')}');
    }
  }

  /// 并行初始化模块
  Future<void> _initializeModulesParallel(List<String> startupOrder) async {
    final futures = <Future<void>>[];
    final context = ModuleContext(
      container: container,
      moduleManager: this,
    );

    for (final moduleName in startupOrder) {
      final module = _modules[moduleName]!;
      final future = module.initialize(context).timeout(_config.initializationTimeout);
      futures.add(future);
    }

    await Future.wait(futures);
  }

  /// 顺序初始化模块
  Future<void> _initializeModulesSequential(List<String> startupOrder) async {
    final context = ModuleContext(
      container: container,
      moduleManager: this,
    );

    for (final moduleName in startupOrder) {
      final module = _modules[moduleName]!;
      await module.initialize(context).timeout(_config.initializationTimeout);
    }
  }

  /// 启动健康检查
  void _startHealthCheck() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = Timer.periodic(_config.healthCheckInterval, (_) async {
      await _performHealthCheck();
    });
  }

  /// 执行健康检查
  Future<void> _performHealthCheck() async {
    for (final module in _modules.values) {
      if (module.state == ModuleState.started) {
        try {
          final isHealthy = await module.healthCheck();
          if (!isHealthy) {
            // 记录健康检查失败
            // 这里可以触发告警或自动重启
          }
        } catch (e) {
          // 记录健康检查异常
        }
      }
    }
  }

  /// 获取管理器统计信息
  Map<String, dynamic> getStats() {
    final moduleStats = <String, dynamic>{};
    
    for (final module in _modules.values) {
      moduleStats[module.metadata.name] = <String, Object>{
        'state': module.state.name,
        'priority': module.metadata.priority.name,
        'dependencies': module.metadata.dependencies,
        'provides': module.metadata.provides,
      };
    }

    return <String, dynamic>{
      'totalModules': _modules.length,
      'initialized': _initialized,
      'started': _started,
      'startupOrder': _startupOrder,
      'modules': moduleStats,
    };
  }

  /// 清理资源
  Future<void> dispose() async {
    await stopModules();
    
    // 销毁所有模块
    final disposeFutures = _modules.values.map((IPluginModule module) => module.dispose());
    await Future.wait(disposeFutures);
    
    _modules.clear();
    _startupOrder = null;
    _initialized = false;
    _started = false;
  }
}
