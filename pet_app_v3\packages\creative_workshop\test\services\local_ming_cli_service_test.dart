import 'package:test/test.dart';
import 'package:creative_workshop/src/services/local_ming_cli_service.dart';

void main() {
  group('LocalMingCliService Tests', () {
    late LocalMingCliService service;

    setUp(() {
      service = LocalMingCliService.instance;
    });

    test('应该能够初始化服务', () async {
      await service.initialize();
      
      expect(service.isCliAvailable, isTrue);
      expect(service.cliVersion, isNotNull);
    });

    test('MingCliResult应该正确创建', () {
      const result = MingCliResult(
        success: true,
        command: 'ming create test_plugin',
        output: '插件创建成功',
        executionTime: Duration(seconds: 2),
        generatedFiles: ['lib/main.dart', 'pubspec.yaml'],
        projectPath: './test_plugin',
      );

      expect(result.success, isTrue);
      expect(result.command, equals('ming create test_plugin'));
      expect(result.output, equals('插件创建成功'));
      expect(result.executionTime, equals(const Duration(seconds: 2)));
      expect(result.generatedFiles, hasLength(2));
      expect(result.projectPath, equals('./test_plugin'));
      expect(result.exitCode, equals(0));
    });

    test('PluginGenerationNotification应该正确创建', () {
      final now = DateTime.now();
      final notification = PluginGenerationNotification(
        pluginId: 'test_plugin',
        pluginName: '测试插件',
        projectPath: './test_plugin',
        templateType: 'tool',
        generatedAt: now,
        version: '1.0.0',
        description: '测试插件描述',
        author: '测试作者',
        generatedFiles: ['lib/main.dart', 'pubspec.yaml'],
      );

      expect(notification.pluginId, equals('test_plugin'));
      expect(notification.pluginName, equals('测试插件'));
      expect(notification.projectPath, equals('./test_plugin'));
      expect(notification.templateType, equals('tool'));
      expect(notification.generatedAt, equals(now));
      expect(notification.version, equals('1.0.0'));
      expect(notification.description, equals('测试插件描述'));
      expect(notification.author, equals('测试作者'));
      expect(notification.generatedFiles, hasLength(2));
    });

    test('应该能够执行命令', () async {
      await service.initialize();
      
      final result = await service.executeCommand('create test_plugin');
      
      expect(result, isNotNull);
      expect(result.command, contains('create test_plugin'));
      expect(result.success, isA<bool>());
    });

    test('应该能够执行创建命令并生成正确结果', () async {
      await service.initialize();
      
      final result = await service.executeCommand('create my_awesome_plugin');
      
      expect(result.success, isTrue);
      expect(result.command, contains('create my_awesome_plugin'));
      expect(result.output, contains('插件项目创建成功'));
      expect(result.generatedFiles, isNotEmpty);
      expect(result.projectPath, contains('my_awesome_plugin'));
    });

    test('应该能够执行构建命令', () async {
      await service.initialize();
      
      final result = await service.executeCommand('build');
      
      expect(result.success, isTrue);
      expect(result.command, contains('build'));
      expect(result.output, contains('构建完成'));
    });

    test('测试命令应该模拟失败', () async {
      await service.initialize();
      
      final result = await service.executeCommand('test');
      
      expect(result.success, isFalse);
      expect(result.command, contains('test'));
      expect(result.error, contains('测试失败'));
    });

    test('应该维护命令历史', () async {
      await service.initialize();
      
      // 执行几个命令
      await service.executeCommand('create plugin1');
      await service.executeCommand('build');
      await service.executeCommand('test');
      
      final history = service.commandHistory;
      expect(history, hasLength(3));
      expect(history[0].command, contains('test')); // 最新的在前面
      expect(history[1].command, contains('build'));
      expect(history[2].command, contains('create plugin1'));
    });

    test('应该能够监听插件生成通知', () async {
      await service.initialize();
      
      // 监听通知流
      final notifications = <PluginGenerationNotification>[];
      final subscription = service.pluginGenerationNotifications.listen(
        (notification) => notifications.add(notification),
      );
      
      // 执行创建命令
      await service.executeCommand('create test_notification_plugin');
      
      // 等待通知处理
      await Future.delayed(const Duration(milliseconds: 100));
      
      expect(notifications, hasLength(1));
      expect(notifications[0].pluginName, equals('test_notification_plugin'));
      
      await subscription.cancel();
    });

    test('应该正确提取插件名称', () async {
      await service.initialize();
      
      final result1 = await service.executeCommand('create my_plugin');
      expect(result1.projectPath, contains('my_plugin'));
      
      final result2 = await service.executeCommand('create another_plugin --type tool');
      expect(result2.projectPath, contains('another_plugin'));
    });

    test('应该处理无效命令', () async {
      await service.initialize();
      
      final result = await service.executeCommand('invalid_command');
      
      expect(result, isNotNull);
      expect(result.success, isTrue); // 模拟执行会成功，但输出通用信息
      expect(result.output, contains('命令执行成功'));
    });

    test('CLI不可用时应该返回失败结果', () async {
      // 创建一个新的服务实例来测试CLI不可用的情况
      final testService = LocalMingCliService.instance;
      
      // 不初始化服务，直接执行命令
      final result = await testService.executeCommand('create test');
      
      expect(result.success, isFalse);
      expect(result.error, contains('Ming CLI不可用'));
    });

    test('应该正确识别插件生成命令', () async {
      await service.initialize();
      
      // 测试插件生成命令
      final result1 = await service.executeCommand(
        'create my_plugin',
        notifyOnPluginGeneration: true,
      );
      expect(result1.success, isTrue);
      
      // 测试非插件生成命令
      final result2 = await service.executeCommand(
        'doctor',
        notifyOnPluginGeneration: true,
      );
      expect(result2.success, isTrue);
    });

    test('命令历史应该限制在100条以内', () async {
      await service.initialize();
      
      // 执行超过100个命令
      for (int i = 0; i < 105; i++) {
        await service.executeCommand('test_command_$i');
      }
      
      final history = service.commandHistory;
      expect(history.length, lessThanOrEqualTo(100));
      expect(history[0].command, contains('test_command_104')); // 最新的命令
    });

    test('应该正确处理执行时间', () async {
      await service.initialize();
      
      final result = await service.executeCommand('create timing_test');
      
      expect(result.executionTime, isNotNull);
      expect(result.executionTime!.inMilliseconds, greaterThan(0));
    });

    test('应该正确生成插件ID', () async {
      await service.initialize();
      
      final result = await service.executeCommand('create My-Awesome_Plugin123');
      
      // 插件ID应该是小写，只包含字母数字和下划线
      expect(result.projectPath, contains('My-Awesome_Plugin123'));
    });
  });
}
