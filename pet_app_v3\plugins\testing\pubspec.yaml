name: test_plugin_fixed
description: A new Flutter project created with Ming Status CLI
version: 1.0.0
homepage: "https:&#x2F;&#x2F;example.com"

environment:
  sdk: '^3.2.0'
  flutter: ">=3.0.0"

dependencies:
  flutter:
    sdk: flutter

  # Pet App V3 插件系统
  plugin_system:
    path: ../../packages/plugin_system

  # 核心依赖
  meta: ^1.9.0

  # 状态管理（可选）
  

  # HTTP网络（可选）
  

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  test: ^1.24.0

# Flutter 配置
flutter:
  # 插件配置
  plugin:
    platforms:
      android:
        package: com.example.test_plugin_fixed
        pluginClass: TestPluginFixedPlugin
      ios:
        pluginClass: TestPluginFixedPlugin
      web:
        pluginClass: TestPluginFixedPlugin
        fileName: test_plugin_fixed_web.dart
      windows:
        pluginClass: TestPluginFixedPlugin
      macos:
        pluginClass: TestPluginFixedPlugin
      linux:
        pluginClass: TestPluginFixedPlugin


  # UI 资源（请创建对应目录）
  # assets:
  #   - assets/images/
  #   - assets/icons/



