import 'package:test/test.dart';

// 直接定义测试用的数据模型，避免导入有问题的文件
class MingCliResult {
  const MingCliResult({
    required this.success,
    required this.command,
    this.output,
    this.error,
    this.exitCode = 0,
    this.executionTime,
    this.generatedFiles = const <String>[],
    this.projectPath,
  });

  final bool success;
  final String command;
  final String? output;
  final String? error;
  final int exitCode;
  final Duration? executionTime;
  final List<String> generatedFiles;
  final String? projectPath;
}

class PluginGenerationNotification {
  const PluginGenerationNotification({
    required this.pluginId,
    required this.pluginName,
    required this.projectPath,
    required this.templateType,
    required this.generatedAt,
    this.version = '1.0.0',
    this.description,
    this.author,
    this.generatedFiles = const <String>[],
  });

  final String pluginId;
  final String pluginName;
  final String projectPath;
  final String templateType;
  final DateTime generatedAt;
  final String version;
  final String? description;
  final String? author;
  final List<String> generatedFiles;
}

void main() {
  group('Ming CLI 数据模型测试', () {
    test('MingCliResult 成功结果测试', () {
      const result = MingCliResult(
        success: true,
        command: 'ming create test_plugin',
        output: '插件创建成功',
        executionTime: Duration(seconds: 2),
        generatedFiles: ['lib/main.dart', 'pubspec.yaml'],
        projectPath: './test_plugin',
      );

      expect(result.success, isTrue);
      expect(result.command, equals('ming create test_plugin'));
      expect(result.output, equals('插件创建成功'));
      expect(result.executionTime, equals(const Duration(seconds: 2)));
      expect(result.generatedFiles, hasLength(2));
      expect(result.generatedFiles, contains('lib/main.dart'));
      expect(result.generatedFiles, contains('pubspec.yaml'));
      expect(result.projectPath, equals('./test_plugin'));
      expect(result.exitCode, equals(0));
      expect(result.error, isNull);
    });

    test('MingCliResult 失败结果测试', () {
      const result = MingCliResult(
        success: false,
        command: 'ming test',
        error: '测试失败',
        exitCode: 1,
      );

      expect(result.success, isFalse);
      expect(result.command, equals('ming test'));
      expect(result.error, equals('测试失败'));
      expect(result.exitCode, equals(1));
      expect(result.output, isNull);
      expect(result.projectPath, isNull);
      expect(result.generatedFiles, isEmpty);
    });

    test('PluginGenerationNotification 完整数据测试', () {
      final now = DateTime.now();
      final notification = PluginGenerationNotification(
        pluginId: 'test_plugin',
        pluginName: '测试插件',
        projectPath: './test_plugin',
        templateType: 'tool',
        generatedAt: now,
        version: '1.0.0',
        description: '测试插件描述',
        author: '测试作者',
        generatedFiles: ['lib/main.dart', 'pubspec.yaml', 'README.md'],
      );

      expect(notification.pluginId, equals('test_plugin'));
      expect(notification.pluginName, equals('测试插件'));
      expect(notification.projectPath, equals('./test_plugin'));
      expect(notification.templateType, equals('tool'));
      expect(notification.generatedAt, equals(now));
      expect(notification.version, equals('1.0.0'));
      expect(notification.description, equals('测试插件描述'));
      expect(notification.author, equals('测试作者'));
      expect(notification.generatedFiles, hasLength(3));
      expect(notification.generatedFiles, contains('lib/main.dart'));
      expect(notification.generatedFiles, contains('pubspec.yaml'));
      expect(notification.generatedFiles, contains('README.md'));
    });

    test('PluginGenerationNotification 默认值测试', () {
      final now = DateTime.now();
      final notification = PluginGenerationNotification(
        pluginId: 'simple_plugin',
        pluginName: '简单插件',
        projectPath: './simple_plugin',
        templateType: 'basic',
        generatedAt: now,
      );

      expect(notification.pluginId, equals('simple_plugin'));
      expect(notification.pluginName, equals('简单插件'));
      expect(notification.projectPath, equals('./simple_plugin'));
      expect(notification.templateType, equals('basic'));
      expect(notification.version, equals('1.0.0')); // 默认版本
      expect(notification.description, isNull);
      expect(notification.author, isNull);
      expect(notification.generatedFiles, isEmpty); // 默认空列表
    });

    test('MingCliResult 各种组合测试', () {
      // 测试成功结果的各种组合
      const result1 = MingCliResult(
        success: true,
        command: 'test command',
      );
      expect(result1.success, isTrue);
      expect(result1.exitCode, equals(0));
      expect(result1.generatedFiles, isEmpty);

      const result2 = MingCliResult(
        success: false,
        command: 'failed command',
        exitCode: 1,
      );
      expect(result2.success, isFalse);
      expect(result2.exitCode, equals(1));
    });

    test('数据模型不变性测试', () {
      const result = MingCliResult(
        success: true,
        command: 'test',
        generatedFiles: ['file1.dart', 'file2.dart'],
      );

      // 测试列表是不可变的
      expect(() => result.generatedFiles.add('file3.dart'),
          throwsUnsupportedError);
    });

    test('PluginGenerationNotification 时间处理测试', () {
      final now = DateTime.now();
      final notification = PluginGenerationNotification(
        pluginId: 'time_test',
        pluginName: '时间测试',
        projectPath: './time_test',
        templateType: 'test',
        generatedAt: now,
      );

      expect(notification.generatedAt, equals(now));
      expect(
          notification.generatedAt
              .isBefore(DateTime.now().add(const Duration(seconds: 1))),
          isTrue);
    });

    test('MingCliResult 执行时间测试', () {
      const result = MingCliResult(
        success: true,
        command: 'timing test',
        executionTime: Duration(milliseconds: 1500),
      );

      expect(result.executionTime, isNotNull);
      expect(result.executionTime!.inMilliseconds, equals(1500));
      expect(result.executionTime!.inSeconds, equals(1));
    });

    test('PluginGenerationNotification 文件列表测试', () {
      final notification = PluginGenerationNotification(
        pluginId: 'file_test',
        pluginName: '文件测试',
        projectPath: './file_test',
        templateType: 'test',
        generatedAt: DateTime.now(),
        generatedFiles: [
          'lib/main.dart',
          'lib/src/plugin.dart',
          'test/plugin_test.dart',
          'pubspec.yaml',
          'README.md',
        ],
      );

      expect(notification.generatedFiles, hasLength(5));
      expect(notification.generatedFiles, contains('lib/main.dart'));
      expect(notification.generatedFiles, contains('lib/src/plugin.dart'));
      expect(notification.generatedFiles, contains('test/plugin_test.dart'));
      expect(notification.generatedFiles, contains('pubspec.yaml'));
      expect(notification.generatedFiles, contains('README.md'));
    });

    test('MingCliResult 错误信息测试', () {
      const result = MingCliResult(
        success: false,
        command: 'error test',
        error: '详细错误信息：文件不存在',
        exitCode: 2,
      );

      expect(result.success, isFalse);
      expect(result.error, contains('详细错误信息'));
      expect(result.error, contains('文件不存在'));
      expect(result.exitCode, equals(2));
      expect(result.output, isNull);
    });
  });
}
