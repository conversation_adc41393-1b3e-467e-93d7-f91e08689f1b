/*
---------------------------------------------------------------
File name:          app_store_service.dart
Author:             Pet App Team
Date created:       2025-08-02
Last modified:      2025-08-02
Dart Version:       3.2+
Description:        应用商店服务 - 集成插件安装
---------------------------------------------------------------
Change History:
    2025-08-02: Initial creation - 应用商店服务集成插件安装;
---------------------------------------------------------------
*/

import 'dart:async';
import 'package:plugin_system/plugin_system.dart';
import 'plugin_installation_service.dart';

/// 应用商店项目类型
enum AppStoreItemType {
  /// 插件
  plugin,

  /// 模块
  module,

  /// 主题
  theme,

  /// 模板
  template,
}

/// 应用商店项目
class AppStoreItem {
  const AppStoreItem({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.version,
    required this.author,
    this.iconUrl,
    this.screenshots = const <String>[],
    this.tags = const <String>[],
    this.rating = 0.0,
    this.downloadCount = 0,
    this.price = 0.0,
    this.isFree = true,
    this.isOfficial = false,
    this.metadata = const <String, dynamic>{},
  });

  /// 项目ID
  final String id;

  /// 项目名称
  final String name;

  /// 项目描述
  final String description;

  /// 项目类型
  final AppStoreItemType type;

  /// 版本号
  final String version;

  /// 作者
  final String author;

  /// 图标URL
  final String? iconUrl;

  /// 截图列表
  final List<String> screenshots;

  /// 标签列表
  final List<String> tags;

  /// 评分 (0.0-5.0)
  final double rating;

  /// 下载次数
  final int downloadCount;

  /// 价格
  final double price;

  /// 是否免费
  final bool isFree;

  /// 是否官方
  final bool isOfficial;

  /// 元数据
  final Map<String, dynamic> metadata;
}

/// 应用商店搜索结果
class AppStoreSearchResult {
  const AppStoreSearchResult({
    required this.items,
    required this.totalCount,
    required this.page,
    required this.pageSize,
  });

  /// 搜索结果项目
  final List<AppStoreItem> items;

  /// 总数量
  final int totalCount;

  /// 当前页码
  final int page;

  /// 页面大小
  final int pageSize;

  /// 是否有更多页面
  bool get hasMore => (page * pageSize) < totalCount;
}

/// 应用商店服务
///
/// 负责应用商店的搜索、浏览和安装功能，集成PluginInstallationService
class AppStoreService {
  AppStoreService._();

  /// 单例实例
  static final AppStoreService _instance = AppStoreService._();
  static AppStoreService get instance => _instance;

  /// 插件安装服务
  final PluginInstallationService _installationService =
      PluginInstallationService.instance;

  /// Plugin System商店管理器
  final PluginStoreManager _storeManager = PluginStoreManager.instance;

  /// 是否已初始化
  bool _isInitialized = false;

  /// 初始化服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // 初始化插件安装服务
      await _installationService.initialize();

      _isInitialized = true;
      _log('info', '应用商店服务初始化完成');
    } catch (e) {
      _log('error', '应用商店服务初始化失败: $e');
      rethrow;
    }
  }

  /// 搜索应用商店项目
  Future<AppStoreSearchResult> searchItems({
    String? query,
    AppStoreItemType? type,
    List<String>? tags,
    int page = 1,
    int pageSize = 20,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      _log('info', '搜索应用商店项目: query=$query, type=$type, page=$page');

      // 如果是插件类型，使用Plugin System搜索
      if (type == AppStoreItemType.plugin || type == null) {
        final pluginQuery = PluginSearchQuery(
          keyword: query,
          tags: tags ?? <String>[],
          offset: (page - 1) * pageSize,
          limit: pageSize,
        );

        final pluginResult = await _storeManager.searchPlugins(pluginQuery);

        // 转换为AppStoreItem
        final items = pluginResult.plugins
            .map((PluginStoreEntry entry) => AppStoreItem(
                  id: entry.id,
                  name: entry.name,
                  description: entry.description,
                  type: AppStoreItemType.plugin,
                  version: entry.version,
                  author: entry.author,
                  iconUrl: null, // PluginStoreEntry没有iconUrl字段
                  screenshots: entry.screenshots,
                  tags: entry.tags,
                  rating: entry.rating,
                  downloadCount: entry.downloadCount,
                  price: 0.0, // PluginStoreEntry没有price字段，默认免费
                  isFree: true, // 默认免费
                  isOfficial: entry.isVerified, // 使用isVerified作为isOfficial
                  metadata: entry.metadata,
                ))
            .toList();

        return AppStoreSearchResult(
          items: items,
          totalCount: pluginResult.totalCount,
          page: page,
          pageSize: pageSize,
        );
      }

      // TODO: 实现其他类型的搜索（模块、主题、模板）
      _log('info', '其他类型搜索暂未实现: $type');
      return const AppStoreSearchResult(
        items: <AppStoreItem>[],
        totalCount: 0,
        page: 1,
        pageSize: 20,
      );
    } catch (e) {
      _log('error', '搜索应用商店项目失败: $e');
      return const AppStoreSearchResult(
        items: <AppStoreItem>[],
        totalCount: 0,
        page: 1,
        pageSize: 20,
      );
    }
  }

  /// 安装应用商店项目
  Future<PluginInstallationResult> installItem(AppStoreItem item) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      _log('info', '开始安装应用商店项目: ${item.name} (${item.id})');

      // 根据项目类型选择安装方式
      switch (item.type) {
        case AppStoreItemType.plugin:
          // 使用PluginInstallationService安装插件
          return await _installationService.installPlugin(
            pluginId: item.id,
            version: item.version,
            config: item.metadata,
          );

        case AppStoreItemType.module:
        case AppStoreItemType.theme:
        case AppStoreItemType.template:
          // TODO: 实现其他类型的安装
          _log('warning', '${item.type.name}类型安装暂未实现');
          return PluginInstallationResult.failure(
            pluginId: item.id,
            message: '${item.type.name}类型安装暂未实现',
            error: PluginInstallationError.unknown,
          );
      }
    } catch (e) {
      _log('error', '安装应用商店项目失败: ${item.id}, 错误: $e');
      return PluginInstallationResult.failure(
        pluginId: item.id,
        message: '安装过程中发生异常: $e',
        error: PluginInstallationError.unknown,
      );
    }
  }

  /// 卸载应用商店项目
  Future<PluginInstallationResult> uninstallItem(String itemId) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      _log('info', '开始卸载应用商店项目: $itemId');

      // 目前只支持插件卸载
      return await _installationService.uninstallPlugin(itemId);
    } catch (e) {
      _log('error', '卸载应用商店项目失败: $itemId, 错误: $e');
      return PluginInstallationResult.failure(
        pluginId: itemId,
        message: '卸载过程中发生异常: $e',
        error: PluginInstallationError.unknown,
      );
    }
  }

  /// 获取安装进度
  Stream<PluginInstallationProgress>? getInstallationProgress(String itemId) {
    return _installationService.getInstallationProgress(itemId);
  }

  /// 获取热门项目
  Future<List<AppStoreItem>> getPopularItems({
    AppStoreItemType? type,
    int limit = 10,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      // 如果是插件类型，使用Plugin System获取热门插件
      if (type == AppStoreItemType.plugin || type == null) {
        final popularPlugins =
            await _storeManager.getFeaturedPlugins(limit: limit);

        return popularPlugins
            .map((PluginStoreEntry entry) => AppStoreItem(
                  id: entry.id,
                  name: entry.name,
                  description: entry.description,
                  type: AppStoreItemType.plugin,
                  version: entry.version,
                  author: entry.author,
                  iconUrl: null, // PluginStoreEntry没有iconUrl字段
                  screenshots: entry.screenshots,
                  tags: entry.tags,
                  rating: entry.rating,
                  downloadCount: entry.downloadCount,
                  price: 0.0, // PluginStoreEntry没有price字段，默认免费
                  isFree: true, // 默认免费
                  isOfficial: entry.isVerified, // 使用isVerified作为isOfficial
                  metadata: entry.metadata,
                ))
            .toList();
      }

      // TODO: 实现其他类型的热门项目
      return <AppStoreItem>[];
    } catch (e) {
      _log('error', '获取热门项目失败: $e');
      return <AppStoreItem>[];
    }
  }

  /// 日志记录
  void _log(String level, String message) {
    print('[$level] [AppStoreService] $message');
  }
}
