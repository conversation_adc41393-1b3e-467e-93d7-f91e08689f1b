/*
---------------------------------------------------------------
File name:          plugin_installation_manager_test.dart
Author:             lgnorant-lu
Date created:       2025-07-28
Last modified:      2025-07-28
Dart Version:       3.2+
Description:        插件安装管理器测试 - 简化版本
---------------------------------------------------------------
Change History:
    2025-07-28:     创建插件安装管理器简化测试;
---------------------------------------------------------------
*/

import 'dart:typed_data';

import 'package:plugin_system/src/api/plugin_installation_manager.dart';
import 'package:plugin_system/src/core/plugin_registry.dart';
import 'package:test/test.dart';

void main() {
  group('PluginInstallationManager Tests', () {
    late PluginInstallationManager manager;
    late PluginRegistry registry;

    setUp(() {
      registry = PluginRegistry.instance;
      manager = PluginInstallationManager(registry: registry);
      // 清理注册表
      registry.clear();
    });

    tearDown(() {
      // 清理注册表
      registry.clear();
    });

    group('插件安装测试', () {
      test('应该能成功安装插件', () async {
        final result = await manager.installPlugin('test_plugin');

        if (!result.isSuccess) {
          print('安装失败: ${result.message}');
          print('状态码: ${result.statusCode}');
        }

        expect(result.isSuccess, isTrue);
        expect(result.data, isNotNull);
        expect(result.data!['version'], equals('1.0.0'));
        expect(result.data!['installLocation'], contains('test_plugin'));
      });

      test('应该能安装指定版本的插件', () async {
        final result = await manager.installPlugin(
          'test_plugin_v2',
          version: '2.1.0',
        );

        expect(result.isSuccess, isTrue);
        expect(result.data!['version'], equals('2.1.0'));
      });

      test('应该能安装带配置的插件', () async {
        final config = <String, dynamic>{
          'theme': 'dark',
          'autoStart': true,
        };

        final result = await manager.installPlugin(
          'test_plugin_config',
          config: config,
        );

        expect(result.isSuccess, isTrue);
        expect(result.data, isNotNull);
      });

      test('应该拒绝空插件ID', () async {
        final result = await manager.installPlugin('');

        expect(result.isSuccess, isFalse);
        expect(result.statusCode, equals(400));
        expect(result.message, contains('插件ID不能为空'));
      });

      test('应该拒绝重复安装', () async {
        // 先安装一次
        await manager.installPlugin('duplicate_plugin');

        // 再次安装应该失败
        final result = await manager.installPlugin('duplicate_plugin');

        expect(result.isSuccess, isFalse);
        expect(result.statusCode, equals(409));
        expect(result.message, contains('插件已安装'));
      });
    });

    group('插件卸载测试', () {
      test('应该能成功卸载插件', () async {
        // 先安装插件
        await manager.installPlugin('uninstall_test');

        // 然后卸载
        final result = await manager.uninstallPlugin('uninstall_test');

        expect(result.isSuccess, isTrue);
        expect(result.data, isNotNull);
        expect(result.data!['cleanupResult'], isNotNull);
      });

      test('应该能强制卸载插件', () async {
        // 先安装插件
        await manager.installPlugin('force_uninstall_test');

        // 强制卸载
        final result = await manager.uninstallPlugin(
          'force_uninstall_test',
          force: true,
        );

        expect(result.isSuccess, isTrue);
        expect(result.data!['cleanupResult'], isNotNull);
      });

      test('应该拒绝空插件ID', () async {
        final result = await manager.uninstallPlugin('');

        expect(result.isSuccess, isFalse);
        expect(result.statusCode, equals(400));
        expect(result.message, contains('插件ID不能为空'));
      });

      test('应该拒绝卸载不存在的插件', () async {
        final result = await manager.uninstallPlugin('non_existent_plugin');

        expect(result.isSuccess, isFalse);
        expect(result.statusCode, equals(404));
        expect(result.message, contains('插件未安装'));
      });
    });

    group('插件包验证测试', () {
      test('应该能验证有效的ZIP插件包', () async {
        final validPackage = Uint8List.fromList([
          0x50, 0x4B, 0x03, 0x04, // ZIP文件头
          ...List.generate(100, (i) => i % 256), // 模拟ZIP内容
        ]);

        final result = await manager.validatePluginPackage(validPackage);

        expect(result.isSuccess, isTrue);
        expect(result.data!['valid'], isTrue);
        expect(result.data!['format'], isNotNull);
        expect(result.data!['security'], isNotNull);
        expect(result.data!['dependencies'], isNotNull);
      });

      test('应该能验证TAR格式的插件包', () async {
        final tarPackage = Uint8List(262);
        // 设置TAR魔数
        final tarMagic = 'ustar'.codeUnits;
        for (int i = 0; i < tarMagic.length; i++) {
          tarPackage[257 + i] = tarMagic[i];
        }

        final result = await manager.validatePluginPackage(tarPackage);

        expect(result.isSuccess, isTrue);
        expect(result.data!['valid'], isTrue);
        expect(result.data!['format']['format'], equals('tar'));
      });

      test('应该能验证GZIP格式的插件包', () async {
        final gzipPackage = Uint8List.fromList([
          0x1F, 0x8B, 0x08, // GZIP文件头
          ...List.generate(100, (i) => i % 256),
        ]);

        final result = await manager.validatePluginPackage(gzipPackage);

        expect(result.isSuccess, isTrue);
        expect(result.data!['valid'], isTrue);
        expect(result.data!['format']['format'], equals('gzip'));
      });

      test('应该拒绝太小的文件', () async {
        final tooSmallPackage = Uint8List.fromList([0x01, 0x02]);

        final result = await manager.validatePluginPackage(tooSmallPackage);

        expect(result.isSuccess, isFalse);
        expect(result.statusCode, equals(400));
        expect(result.message, contains('文件太小'));
      });

      test('应该拒绝不支持的文件格式', () async {
        final unsupportedPackage = Uint8List.fromList([
          0xFF, 0xFE, 0xFD, 0xFC, // 不支持的文件头
          ...List.generate(100, (i) => i % 256),
        ]);

        final result = await manager.validatePluginPackage(unsupportedPackage);

        expect(result.isSuccess, isFalse);
        expect(result.statusCode, equals(400));
        expect(result.message, contains('不支持的文件格式'));
      });
    });

    group('安全扫描测试', () {
      test('应该能执行基础安全扫描', () async {
        final packageData = Uint8List.fromList([
          0x50, 0x4B, 0x03, 0x04, // ZIP文件头
          ...List.generate(50, (i) => i % 256),
        ]);

        final result = await manager.validatePluginPackage(packageData);

        expect(result.isSuccess, isTrue);
        expect(result.data!['security'], isNotNull);
        expect(result.data!['security']['safe'], isA<bool>());
        expect(result.data!['security']['score'], isA<int>());
        expect(result.data!['security']['threats'], isA<List<dynamic>>());
        expect(result.data!['security']['warnings'], isA<List<dynamic>>());
      });

      test('应该检测可疑的代码模式', () async {
        final suspiciousContent = 'eval(malicious_code)'.codeUnits;
        final packageData = Uint8List.fromList([
          0x50, 0x4B, 0x03, 0x04, // ZIP文件头
          ...suspiciousContent,
          ...List.generate(50, (i) => i % 256),
        ]);

        final result = await manager.validatePluginPackage(packageData);

        expect(result.isSuccess, isTrue);
        final security = result.data!['security'] as Map<String, dynamic>;
        expect(security['warnings'], isA<List<dynamic>>());
        expect((security['warnings'] as List).isNotEmpty, isTrue);
      });

      test('应该检测过大的文件', () async {
        // 创建一个模拟的大文件（实际不会真的创建100MB）
        final largePackage = Uint8List(1000); // 用小文件模拟
        largePackage[0] = 0x50;
        largePackage[1] = 0x4B;
        largePackage[2] = 0x03;
        largePackage[3] = 0x04;

        final result = await manager.validatePluginPackage(largePackage);

        expect(result.isSuccess, isTrue);
        final security = result.data!['security'] as Map<String, dynamic>;
        expect(security['score'], isA<int>());
      });
    });

    group('依赖验证测试', () {
      test('应该能验证系统依赖', () async {
        final packageData = Uint8List.fromList([
          0x50, 0x4B, 0x03, 0x04, // ZIP文件头
          ...List.generate(100, (i) => i % 256),
        ]);

        final result = await manager.validatePluginPackage(packageData);

        expect(result.isSuccess, isTrue);
        expect(result.data!['dependencies'], isNotNull);
        final deps = result.data!['dependencies'] as Map<String, dynamic>;
        expect(deps['satisfied'], isA<bool>());
        expect(deps['missing'], isA<List<dynamic>>());
        expect(deps['conflicts'], isA<List<dynamic>>());
      });

      test('应该检查插件依赖关系', () async {
        // 先安装一个插件
        await manager.installPlugin('dependency_test');

        // 尝试卸载（会触发依赖检查）
        final result = await manager.uninstallPlugin('dependency_test');

        expect(result.isSuccess, isTrue);
      });

      test('应该能强制卸载有依赖的插件', () async {
        // 安装一个插件
        await manager.installPlugin('dependency_force_test');

        // 强制卸载
        final result = await manager.uninstallPlugin(
          'dependency_force_test',
          force: true,
        );

        expect(result.isSuccess, isTrue);
      });
    });

    group('错误处理测试', () {
      test('应该能处理安装过程中的异常', () async {
        // 使用一个可能导致异常的插件ID
        final result = await manager.installPlugin('error_plugin');

        // 即使有异常，也应该返回适当的错误响应
        expect(result, isNotNull);
        expect(result.statusCode, isNotNull);
      });

      test('应该能处理卸载过程中的异常', () async {
        // 先安装一个插件
        await manager.installPlugin('error_uninstall_plugin');

        // 尝试卸载（可能会有异常）
        final result = await manager.uninstallPlugin('error_uninstall_plugin');

        // 应该返回适当的响应
        expect(result, isNotNull);
        expect(result.statusCode, isNotNull);
      });

      test('应该能处理验证过程中的异常', () async {
        // 创建一个会导致验证异常的包
        final problematicPackage = Uint8List(0);

        final result = await manager.validatePluginPackage(problematicPackage);

        expect(result.isSuccess, isFalse);
        expect(result.statusCode, equals(400));
      });
    });

    group('配置管理测试', () {
      test('应该能应用插件配置', () async {
        final config = <String, dynamic>{
          'setting1': 'value1',
          'setting2': 42,
          'setting3': true,
        };

        final result = await manager.installPlugin(
          'config_test_plugin',
          config: config,
        );

        expect(result.isSuccess, isTrue);
      });

      test('应该能处理空配置', () async {
        final result = await manager.installPlugin(
          'empty_config_plugin',
          config: <String, dynamic>{},
        );

        expect(result.isSuccess, isTrue);
      });

      test('应该能处理null配置', () async {
        final result = await manager.installPlugin('null_config_plugin');

        expect(result.isSuccess, isTrue);
      });
    });
  });
}
