﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <CustomBuild Include="D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\build\windows\x64\_deps\sqlite3-subbuild\CMakeFiles\96a302a2fdf7e66bffa3f2418cad6511\sqlite3-populate-mkdir.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\build\windows\x64\_deps\sqlite3-subbuild\CMakeFiles\96a302a2fdf7e66bffa3f2418cad6511\sqlite3-populate-download.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\build\windows\x64\_deps\sqlite3-subbuild\CMakeFiles\96a302a2fdf7e66bffa3f2418cad6511\sqlite3-populate-update.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\build\windows\x64\_deps\sqlite3-subbuild\CMakeFiles\96a302a2fdf7e66bffa3f2418cad6511\sqlite3-populate-patch.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\build\windows\x64\_deps\sqlite3-subbuild\CMakeFiles\96a302a2fdf7e66bffa3f2418cad6511\sqlite3-populate-configure.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\build\windows\x64\_deps\sqlite3-subbuild\CMakeFiles\96a302a2fdf7e66bffa3f2418cad6511\sqlite3-populate-build.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\build\windows\x64\_deps\sqlite3-subbuild\CMakeFiles\96a302a2fdf7e66bffa3f2418cad6511\sqlite3-populate-install.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\build\windows\x64\_deps\sqlite3-subbuild\CMakeFiles\96a302a2fdf7e66bffa3f2418cad6511\sqlite3-populate-test.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\build\windows\x64\_deps\sqlite3-subbuild\CMakeFiles\89a5df0b5782a6f2247cc532dd54e970\sqlite3-populate-complete.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\build\windows\x64\_deps\sqlite3-subbuild\CMakeFiles\bf1276aec6cf297f89dacba8e4911e40\sqlite3-populate.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\build\windows\x64\_deps\sqlite3-subbuild\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\build\windows\x64\_deps\sqlite3-subbuild\CMakeFiles\sqlite3-populate" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{BBB7510F-68E9-3418-BAC4-8A2C57915BB2}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
