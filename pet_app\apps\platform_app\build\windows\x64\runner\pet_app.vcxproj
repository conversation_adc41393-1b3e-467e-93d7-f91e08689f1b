﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|x64">
      <Configuration>Profile</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{CC984EFC-FD89-3D45-A23F-2826B220F11B}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>pet_app</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\build\windows\x64\runner\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">pet_app.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">pet_app</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\build\windows\x64\runner\Profile\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">pet_app.dir\Profile\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">pet_app</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\build\windows\x64\runner\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">pet_app.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">pet_app</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral\.plugin_symlinks\sqlite3_flutter_libs\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_HAS_EXCEPTIONS=0;_DEBUG;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral\.plugin_symlinks\sqlite3_flutter_libs\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral\.plugin_symlinks\sqlite3_flutter_libs\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Debug\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\sqlite3_flutter_libs\Debug\sqlite3_flutter_libs_plugin.lib;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64/runner/Debug/pet_app.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64/runner/Debug/pet_app.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral\.plugin_symlinks\sqlite3_flutter_libs\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR="Profile"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR=\"Profile\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral\.plugin_symlinks\sqlite3_flutter_libs\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral\.plugin_symlinks\sqlite3_flutter_libs\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Profile\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\sqlite3_flutter_libs\Profile\sqlite3_flutter_libs_plugin.lib;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64/runner/Profile/pet_app.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64/runner/Profile/pet_app.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral\.plugin_symlinks\sqlite3_flutter_libs\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral\.plugin_symlinks\sqlite3_flutter_libs\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral\.plugin_symlinks\sqlite3_flutter_libs\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Release\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\sqlite3_flutter_libs\Release\sqlite3_flutter_libs_plugin.lib;D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64/runner/Release/pet_app.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64/runner/Release/pet_app.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\runner\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\VS\VisualStudio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe -SD:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/windows -BD:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64 --check-stamp-file D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Building Custom Rule D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
D:\VS\VisualStudio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe -SD:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/windows -BD:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64 --check-stamp-file D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\VS\VisualStudio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe -SD:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/windows -BD:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64 --check-stamp-file D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\runner\flutter_window.cpp" />
    <ClCompile Include="D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\runner\main.cpp" />
    <ClCompile Include="D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\runner\utils.cpp" />
    <ClCompile Include="D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\runner\win32_window.cpp" />
    <ClCompile Include="D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\generated_plugin_registrant.cc" />
    <ResourceCompile Include="D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\runner\Runner.rc" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\build\windows\x64\ZERO_CHECK.vcxproj">
      <Project>{5CA7C8BE-4ACC-3555-B6A8-3A391FB2D248}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\build\windows\x64\flutter\flutter_assemble.vcxproj">
      <Project>{CA518CBC-CC03-357A-92EC-941476ACF34F}</Project>
      <Name>flutter_assemble</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\build\windows\x64\flutter\flutter_wrapper_app.vcxproj">
      <Project>{E278ECC2-A98E-380D-BE93-30C9FF0A2E0A}</Project>
      <Name>flutter_wrapper_app</Name>
    </ProjectReference>
    <ProjectReference Include="D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\build\windows\x64\plugins\sqlite3_flutter_libs\sqlite3_flutter_libs_plugin.vcxproj">
      <Project>{413D6285-7691-3586-AE8D-4020616D467A}</Project>
      <Name>sqlite3_flutter_libs_plugin</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>