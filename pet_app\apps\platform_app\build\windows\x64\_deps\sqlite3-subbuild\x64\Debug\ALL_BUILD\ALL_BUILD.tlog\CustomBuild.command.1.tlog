^D:\狗PY\PYTHONPROJECT\CRUD\TASKS_ORGANIZING\PET_APP\APPS\PLATFORM_APP\BUILD\WINDOWS\X64\_DEPS\SQLITE3-SUBBUILD\CMAKELISTS.TXT
setlocal
D:\VS\VisualStudio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe -SD:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64/_deps/sqlite3-subbuild -BD:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64/_deps/sqlite3-subbuild --check-stamp-file D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64/_deps/sqlite3-subbuild/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
