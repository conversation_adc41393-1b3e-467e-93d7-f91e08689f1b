^D:\狗PY\PYTHONPROJECT\CRUD\TASKS_ORGANIZING\PET_APP\APPS\PLATFORM_APP\BUILD\WINDOWS\X64\CMAKEFILES\7B9CBD7BDD949A8F52A6F054482DE362\FLUTTER_WINDOWS.DLL.RULE
setlocal
D:\VS\VisualStudio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe -E env FLUTTER_ROOT=D:\Coding\Flutter\flutter PROJECT_DIR=D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app FLUTTER_ROOT=D:\Coding\Flutter\flutter FLUTTER_EPHEMERAL_DIR=D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\windows\flutter\ephemeral PROJECT_DIR=D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app FLUTTER_TARGET=D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\lib\main.dart DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuNA==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049NmZiYTI0NDdlOQ==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049OGNkMTllNTA5ZA==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE= DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=D:\狗py\pythonProject\CRUD\Tasks_organizing\pet_app\apps\platform_app\.dart_tool\package_config.json D:/Coding/Flutter/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\狗PY\PYTHONPROJECT\CRUD\TASKS_ORGANIZING\PET_APP\APPS\PLATFORM_APP\BUILD\WINDOWS\X64\CMAKEFILES\C18125AC8C57F8ECD5FF8B610C37CCA2\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\狗PY\PYTHONPROJECT\CRUD\TASKS_ORGANIZING\PET_APP\APPS\PLATFORM_APP\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
D:\VS\VisualStudio\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe -SD:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/windows -BD:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64 --check-stamp-file D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
