/*
---------------------------------------------------------------
File name:          creative_workspace.dart
Author:             lgnorant-lu
Date created:       2025-07-22
Last modified:      2025-07-22
Dart Version:       3.2+
Description:        Creative Workshop 应用商店主界面
---------------------------------------------------------------
Change History:
    2025-07-22: Phase ******* - 重构为应用商店界面;
---------------------------------------------------------------
*/

import 'package:creative_workshop/src/ui/developer/developer_platform_page.dart';
import 'package:creative_workshop/src/ui/management/developer_plugin_management_page.dart';
// TODO(职责清晰化): 应用商店功能已迁移到App Manager模块
// import 'package:creative_workshop/src/ui/store/app_store_page.dart';
// import 'package:creative_workshop/src/ui/store/plugin_card.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// 工作区布局模式
enum WorkspaceLayout {
  /// 开发者模式（插件开发和调试）
  developer,

  /// 管理模式（已安装插件管理）
  management,

  /// 自定义布局
  custom,
}

/// Creative Workshop 应用商店主界面
class CreativeWorkspace extends StatefulWidget {
  const CreativeWorkspace({
    super.key,
    this.layout = WorkspaceLayout.developer,
    this.initialCategory,
    this.initialSearchQuery,
    this.onLayoutChanged,
  });

  /// 工作区布局
  final WorkspaceLayout layout;

  /// 初始分类（已废弃）
  @Deprecated('应用商店功能已迁移到App Manager模块')
  final String? initialCategory;

  /// 初始搜索查询
  final String? initialSearchQuery;

  /// 布局变化回调
  final void Function(WorkspaceLayout layout)? onLayoutChanged;

  @override
  State<CreativeWorkspace> createState() => _CreativeWorkspaceState();

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(EnumProperty<WorkspaceLayout>('layout', layout))
      ..add(StringProperty('initialSearchQuery', initialSearchQuery))
      ..add(
        ObjectFlagProperty<void Function(WorkspaceLayout layout)?>.has(
          'onLayoutChanged',
          onLayoutChanged,
        ),
      );
  }
}

class _CreativeWorkspaceState extends State<CreativeWorkspace> {
  @override
  Widget build(BuildContext context) {
    switch (widget.layout) {
      case WorkspaceLayout.developer:
        return const DeveloperPlatformPage();

      case WorkspaceLayout.management:
        return const DeveloperPluginManagementPage();

      case WorkspaceLayout.custom:
        return _buildCustomMode();
    }
  }

  /// 构建自定义模式界面
  Widget _buildCustomMode() => Scaffold(
        appBar: AppBar(
          title: const Text('自定义布局'),
          actions: <Widget>[
            IconButton(
              icon: const Icon(Icons.developer_mode),
              onPressed: () =>
                  widget.onLayoutChanged?.call(WorkspaceLayout.developer),
              tooltip: '切换到开发者模式',
            ),
          ],
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Icon(
                Icons.dashboard_customize,
                size: 64,
                color: Colors.grey,
              ),
              SizedBox(height: 16),
              Text(
                '自定义布局',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 8),
              Text(
                'Phase ******* - 自定义布局功能待实现',
                style: TextStyle(
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      );
}
