/*
---------------------------------------------------------------
File name:          file_operation_result.dart
Author:             lgnorant-lu
Date created:       2025-07-24
Last modified:      2025-07-24
Dart Version:       3.2+
Description:        文件操作结果模型
---------------------------------------------------------------
Change History:
    2025-07-24: Phase ******* - 实现文件操作结果模型;
---------------------------------------------------------------
*/

import 'package:equatable/equatable.dart';
import 'module_file.dart';

/// 文件操作结果
class FileOperationResult extends Equatable {
  /// 模块ID
  final String moduleId;
  
  /// 文件路径
  final String filePath;
  
  /// 是否成功
  final bool isSuccess;
  
  /// 结果消息
  final String message;
  
  /// 错误类型（失败时）
  final FileOperationError? error;
  
  /// 操作时间
  final DateTime timestamp;

  const FileOperationResult._({
    required this.moduleId,
    required this.filePath,
    required this.isSuccess,
    required this.message,
    this.error,
    required this.timestamp,
  });

  /// 创建成功结果
  factory FileOperationResult.success(
    String moduleId,
    String filePath,
    String message,
  ) {
    return FileOperationResult._(
      moduleId: moduleId,
      filePath: filePath,
      isSuccess: true,
      message: message,
      error: null,
      timestamp: DateTime.now(),
    );
  }

  /// 创建失败结果
  factory FileOperationResult.failure(
    String moduleId,
    String filePath,
    String message,
    FileOperationError error,
  ) {
    return FileOperationResult._(
      moduleId: moduleId,
      filePath: filePath,
      isSuccess: false,
      message: message,
      error: error,
      timestamp: DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [moduleId, filePath, isSuccess, message, error, timestamp];

  @override
  String toString() {
    return 'FileOperationResult(moduleId: $moduleId, filePath: $filePath, isSuccess: $isSuccess)';
  }
}

/// 文件操作错误类型
enum FileOperationError {
  /// 文件已存在
  fileExists,
  
  /// 文件不存在
  fileNotFound,
  
  /// 权限不足
  permissionDenied,
  
  /// 磁盘空间不足
  insufficientSpace,
  
  /// 文件被锁定
  fileLocked,
  
  /// 无效的文件名
  invalidFileName,
  
  /// 无效的文件路径
  invalidFilePath,
  
  /// 操作失败
  operationFailed,
  
  /// 未知错误
  unknown,
}

/// 文件操作错误扩展
extension FileOperationErrorExtension on FileOperationError {
  /// 获取错误描述
  String get description {
    switch (this) {
      case FileOperationError.fileExists:
        return '文件已存在';
      case FileOperationError.fileNotFound:
        return '文件不存在';
      case FileOperationError.permissionDenied:
        return '权限不足';
      case FileOperationError.insufficientSpace:
        return '磁盘空间不足';
      case FileOperationError.fileLocked:
        return '文件被锁定';
      case FileOperationError.invalidFileName:
        return '无效的文件名';
      case FileOperationError.invalidFilePath:
        return '无效的文件路径';
      case FileOperationError.operationFailed:
        return '操作失败';
      case FileOperationError.unknown:
        return '未知错误';
    }
  }
}

/// 文件搜索选项
class FileSearchOptions extends Equatable {
  /// 是否区分大小写
  final bool caseSensitive;
  
  /// 是否精确匹配
  final bool exactMatch;
  
  /// 文件类型过滤
  final List<FileType> fileTypes;
  
  /// 最小文件大小
  final int? minSize;
  
  /// 最大文件大小
  final int? maxSize;
  
  /// 修改时间之后
  final DateTime? modifiedAfter;
  
  /// 修改时间之前
  final DateTime? modifiedBefore;
  
  /// 排序方式
  final FileSortBy sortBy;
  
  /// 是否降序排列
  final bool sortDescending;

  const FileSearchOptions({
    this.caseSensitive = false,
    this.exactMatch = false,
    this.fileTypes = const [],
    this.minSize,
    this.maxSize,
    this.modifiedAfter,
    this.modifiedBefore,
    this.sortBy = FileSortBy.name,
    this.sortDescending = false,
  });

  @override
  List<Object?> get props => [
    caseSensitive,
    exactMatch,
    fileTypes,
    minSize,
    maxSize,
    modifiedAfter,
    modifiedBefore,
    sortBy,
    sortDescending,
  ];
}

/// 文件排序方式
enum FileSortBy {
  /// 按名称排序
  name,
  
  /// 按大小排序
  size,
  
  /// 按修改时间排序
  modifiedDate,
  
  /// 按类型排序
  type,
}

/// 模块文件统计信息
class ModuleFileStats extends Equatable {
  /// 模块ID
  final String moduleId;
  
  /// 文件总数
  final int totalFiles;
  
  /// 总大小
  final int totalSize;
  
  /// 按类型统计的文件数量
  final Map<FileType, int> fileTypeCount;
  
  /// 最后修改时间
  final DateTime? lastModified;

  const ModuleFileStats({
    required this.moduleId,
    required this.totalFiles,
    required this.totalSize,
    required this.fileTypeCount,
    this.lastModified,
  });

  /// 创建空统计信息
  factory ModuleFileStats.empty(String moduleId) {
    return ModuleFileStats(
      moduleId: moduleId,
      totalFiles: 0,
      totalSize: 0,
      fileTypeCount: {},
      lastModified: null,
    );
  }

  /// 获取格式化的总大小
  String get formattedTotalSize {
    if (totalSize < 1024) {
      return '${totalSize}B';
    } else if (totalSize < 1024 * 1024) {
      return '${(totalSize / 1024).toStringAsFixed(1)}KB';
    } else if (totalSize < 1024 * 1024 * 1024) {
      return '${(totalSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    } else {
      return '${(totalSize / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
    }
  }

  /// 获取平均文件大小
  double get averageFileSize {
    return totalFiles > 0 ? totalSize / totalFiles : 0.0;
  }

  @override
  List<Object?> get props => [moduleId, totalFiles, totalSize, fileTypeCount, lastModified];

  @override
  String toString() {
    return 'ModuleFileStats(moduleId: $moduleId, totalFiles: $totalFiles, totalSize: $formattedTotalSize)';
  }
}

/// 文件系统事件
class FileSystemEvent extends Equatable {
  /// 模块ID
  final String moduleId;
  
  /// 事件类型
  final FileSystemEventType type;
  
  /// 文件路径（可选）
  final String? filePath;
  
  /// 事件时间
  final DateTime timestamp;

  const FileSystemEvent._({
    required this.moduleId,
    required this.type,
    this.filePath,
    required this.timestamp,
  });

  /// 文件创建事件
  factory FileSystemEvent.fileCreated(String moduleId, String filePath) {
    return FileSystemEvent._(
      moduleId: moduleId,
      type: FileSystemEventType.fileCreated,
      filePath: filePath,
      timestamp: DateTime.now(),
    );
  }

  /// 文件更新事件
  factory FileSystemEvent.fileUpdated(String moduleId, String filePath) {
    return FileSystemEvent._(
      moduleId: moduleId,
      type: FileSystemEventType.fileUpdated,
      filePath: filePath,
      timestamp: DateTime.now(),
    );
  }

  /// 文件删除事件
  factory FileSystemEvent.fileDeleted(String moduleId, String filePath) {
    return FileSystemEvent._(
      moduleId: moduleId,
      type: FileSystemEventType.fileDeleted,
      filePath: filePath,
      timestamp: DateTime.now(),
    );
  }

  /// 模块初始化事件
  factory FileSystemEvent.moduleInitialized(String moduleId) {
    return FileSystemEvent._(
      moduleId: moduleId,
      type: FileSystemEventType.moduleInitialized,
      timestamp: DateTime.now(),
    );
  }

  /// 模块清理事件
  factory FileSystemEvent.moduleCleanedUp(String moduleId) {
    return FileSystemEvent._(
      moduleId: moduleId,
      type: FileSystemEventType.moduleCleanedUp,
      timestamp: DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [moduleId, type, filePath, timestamp];

  @override
  String toString() {
    return 'FileSystemEvent(moduleId: $moduleId, type: $type, filePath: $filePath)';
  }
}

/// 文件系统事件类型
enum FileSystemEventType {
  /// 文件创建
  fileCreated,
  
  /// 文件更新
  fileUpdated,
  
  /// 文件删除
  fileDeleted,
  
  /// 模块初始化
  moduleInitialized,
  
  /// 模块清理
  moduleCleanedUp,
}
