# =============================================================================
# Pet App V3 插件清单文件 (plugin.yaml)
# 插件名称: test_plugin_fixed
# 生成时间: 2025-08-02
# 模板版本: 1.0.0
# =============================================================================

# 插件基本信息
plugin:
  # 插件标识 (必需)
  id: test_plugin_fixed
  name: My Awesome Plugin
  version: 1.0.0
  description: 测试插件修复
  
  # 作者信息
  author: Pet App V3 Team
  author_email: <EMAIL>
  
  # 插件分类
  category: tool
  type: tool
  
  # 插件标签
  tags:
    - tool
    - pet_app
    - creative_workshop
    - ui

# 版本兼容性
compatibility:
  # Pet App版本要求
  min_pet_app_version: "3.0.0"
  max_pet_app_version: "4.0.0"
  
  # SDK版本要求
  min_dart_version: ^3.2.0
  min_flutter_version: &gt;=3.0.0
  
  # 插件系统版本
  plugin_system_version: "^1.0.0"

# 平台支持
platforms:
  - android
  - ios
  - web
  - windows
  - macos
  - linux

# 权限声明
permissions:

# 依赖管理
dependencies:
  # 核心依赖
  required:
    - id: plugin_system
      version: "^1.0.0"
      description: Pet App插件系统核心库
    - id: flutter
      version: &gt;=3.0.0
      description: Flutter UI框架
  
  # 可选依赖
  optional: []

# 插件配置
configuration:
  # 插件设置
  settings:
    # 是否可配置
    configurable: true
    
    # 默认启用状态
    enabled_by_default: false
    
    # 是否支持热重载
    hot_reload_supported: true
    
    # 是否需要重启应用
    requires_app_restart: false
  
  # 资源配置
  
  # UI配置
  ui:
    # 主题支持
    theme_support: true
    
    # 响应式设计
    responsive: true
    
    # 无障碍支持
    accessibility: true

# 插件入口点
entry_points:
  # 主入口
  main: "lib/test_plugin_fixed.dart"
  
  # 插件实现
  widget: "lib/src/test_plugin_fixed_plugin.dart"

# 导出配置
exports:
  # 公共API
  public_api:
    - "TestPluginFixedPlugin"
    - "TestPluginFixedWidget"
  
  # 内部API (仅供插件系统使用)
  internal_api: []

# 生命周期钩子
lifecycle:
  # 初始化钩子
  on_initialize: "initialize"
  
  # 启动钩子
  on_start: "start"
  
  # 暂停钩子
  on_pause: "pause"
  
  # 恢复钩子
  on_resume: "resume"
  
  # 停止钩子
  on_stop: "stop"
  
  # 清理钩子
  on_dispose: "dispose"

# 安全配置
security:
  # 数字签名 (生产环境需要)
  signature: null
  
  # 安全级别
  security_level: "standard"
  
  # 沙箱模式
  sandboxed: true
  
  # 网络访问限制

# 性能配置
performance:
  # 内存限制 (MB)
  max_memory_usage: 100
  
  # CPU使用限制 (%)
  max_cpu_usage: 50
  
  # 启动超时 (秒)
  startup_timeout: 30
  
  # 响应超时 (秒)
  response_timeout: 10

# 元数据
metadata:
  # 项目信息
  homepage: "https://github.com//"
  repository: "https://github.com//.git"
  issue_tracker: "https://github.com///issues"
  documentation: "https://github.com///blob/main/README.md"
  
  # 许可证
  license: MIT
  
  # 关键词
  keywords:
    - tool
    - pet-app
    - plugin
    - creative-workshop
    - ui
  
  # 创建信息
  created_with: "Ming Status CLI"
  template_version: "1.0.0"
  generated_date: "2025-08-02"

# 开发信息 (仅开发环境)
development:
  # 调试模式
  debug_mode: false
  
  # 日志级别
  log_level: "info"
  
  # 热重载端口
  hot_reload_port: 0
  
  # 开发服务器
  dev_server:
    enabled: false
    port: 8080
