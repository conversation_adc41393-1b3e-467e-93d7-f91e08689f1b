/*
---------------------------------------------------------------
File name:          workshop_manager.dart
Author:             lgnorant-lu
Date created:       2025-07-31
Last modified:      2025-07-31
Dart Version:       3.2+
Description:        创意工坊管理器 - 集成项目管理、Ming CLI、开发环境配置等核心功能
---------------------------------------------------------------
Change History:
    2025-07-31: Initial creation - 创意工坊管理器核心功能实现;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:developer' as developer;

import 'package:creative_workshop/src/core/ming_cli/local_ming_cli_service.dart';
import 'package:creative_workshop/src/core/ming_cli/ming_cli_result.dart';
import 'package:creative_workshop/src/core/projects/project_manager.dart';

import 'package:flutter/foundation.dart';

/// 工坊状态枚举
enum WorkshopState {
  /// 未初始化
  uninitialized,

  /// 初始化中
  initializing,

  /// 就绪
  ready,

  /// 运行中
  running,

  /// 暂停
  paused,

  /// 停止
  stopped,

  /// 错误
  error,
}

/// 创意工坊管理器
///
/// 专注于插件开发工作台的核心功能：
/// - 开发项目管理
/// - Ming CLI集成
/// - 开发环境配置
/// - 开发工具集成
///
/// 职责边界：
/// - 不负责插件生命周期管理（由Plugin System模块负责）
/// - 不负责应用商店功能（由App Manager模块负责）
/// - 专注于开发工作台的管理和配置
class WorkshopManager extends ChangeNotifier {
  WorkshopManager._();

  /// 单例实例
  static final WorkshopManager _instance = WorkshopManager._();
  static WorkshopManager get instance => _instance;

  /// 项目管理器
  final ProjectManager _projectManager = ProjectManager.instance;

  /// Ming CLI服务
  final LocalMingCliService _mingCliService = LocalMingCliService.instance;

  /// 当前工坊状态
  WorkshopState _state = WorkshopState.uninitialized;

  /// 状态变更控制器
  final StreamController<WorkshopState> _stateController =
      StreamController<WorkshopState>.broadcast();

  /// 日志记录器名称
  static const String _loggerName = 'WorkshopManager';

  // ===== 公共接口 =====

  /// 获取当前状态
  WorkshopState get state => _state;

  /// 获取项目管理器
  ProjectManager get projectManager => _projectManager;

  /// 获取当前项目数量
  int get projectCount => _projectManager.projects.length;

  /// 获取Ming CLI服务
  LocalMingCliService get mingCliService => _mingCliService;

  /// 获取Ming CLI状态
  String get mingCliStatus => _mingCliService.status;

  /// 获取Ming CLI版本
  String get mingCliVersion => _mingCliService.version;

  /// Ming CLI是否可用
  bool get isMingCliAvailable => _mingCliService.isInstalled;

  /// 状态变更流
  Stream<WorkshopState> get stateChanges => _stateController.stream;

  /// 初始化创意工坊
  Future<bool> initialize() async {
    // 检查是否已经初始化
    if (_state == WorkshopState.ready || _state == WorkshopState.running) {
      _log('info', '创意工坊已经初始化，跳过重复初始化');
      return true;
    }

    // 如果正在初始化中，等待完成
    if (_state == WorkshopState.initializing) {
      _log('info', '创意工坊正在初始化中，等待完成');
      // 等待状态变为ready或error
      await for (final WorkshopState state in _stateController.stream) {
        if (state == WorkshopState.ready) {
          return true;
        } else if (state == WorkshopState.error) {
          return false;
        }
      }
    }

    try {
      _setState(WorkshopState.initializing);

      // 初始化项目管理器
      await _projectManager.initialize();

      // 初始化开发环境
      await _initializeDevelopmentEnvironment();

      // 初始化Ming CLI集成
      await _initializeMingCLI();

      _setState(WorkshopState.ready);
      _log('info', '创意工坊初始化完成');
      return true;
    } catch (e) {
      _log('error', '创意工坊初始化失败', e);
      _setState(WorkshopState.error);
      return false;
    }
  }

  /// 启动创意工坊
  Future<bool> start() async {
    if (_state != WorkshopState.ready) {
      _log('warning', '创意工坊未就绪，无法启动');
      return false;
    }

    try {
      _setState(WorkshopState.running);
      _log('info', '创意工坊启动成功');
      return true;
    } catch (e) {
      _log('error', '创意工坊启动失败', e);
      _setState(WorkshopState.error);
      return false;
    }
  }

  /// 暂停创意工坊
  Future<bool> pause() async {
    if (_state != WorkshopState.running) {
      _log('warning', '创意工坊未运行，无法暂停');
      return false;
    }

    try {
      _setState(WorkshopState.paused);
      _log('info', '创意工坊已暂停');
      return true;
    } catch (e) {
      _log('error', '创意工坊暂停失败', e);
      _setState(WorkshopState.error);
      return false;
    }
  }

  /// 恢复创意工坊
  Future<bool> resume() async {
    if (_state != WorkshopState.paused) {
      _log('warning', '创意工坊未暂停，无法恢复');
      return false;
    }

    try {
      _setState(WorkshopState.running);
      _log('info', '创意工坊已恢复');
      return true;
    } catch (e) {
      _log('error', '创意工坊恢复失败', e);
      _setState(WorkshopState.error);
      return false;
    }
  }

  /// 停止创意工坊
  Future<bool> stop() async {
    try {
      _setState(WorkshopState.stopped);
      _log('info', '创意工坊已停止');
      return true;
    } catch (e) {
      _log('error', '创意工坊停止失败', e);
      _setState(WorkshopState.error);
      return false;
    }
  }

  /// 释放资源
  @override
  Future<void> dispose() async {
    try {
      await _stateController.close();
      _log('info', '创意工坊资源已释放');
    } on Exception catch (e) {
      _log('error', '释放创意工坊资源失败', e);
    }
    super.dispose();
  }

  // ===== 私有方法 =====

  /// 初始化开发环境
  Future<void> _initializeDevelopmentEnvironment() async {
    try {
      _log('info', '初始化开发环境');

      // TODO(Phase 5.0.7): 实现开发环境配置
      // - 配置代码编辑器
      // - 设置调试工具
      // - 初始化性能分析工具
      // 当前为占位符实现，等待后续Phase开发

      await Future<void>.delayed(const Duration(milliseconds: 100));
      _log('info', '开发环境初始化完成');
    } catch (e) {
      _log('error', '开发环境初始化失败', e);
      rethrow;
    }
  }

  /// 初始化Ming CLI集成
  Future<void> _initializeMingCLI() async {
    try {
      _log('info', '初始化Ming CLI集成');

      // 初始化Ming CLI服务
      await _mingCliService.initialize();

      // 验证Ming CLI可用性
      final bool isInstalled = _mingCliService.isInstalled;
      if (!isInstalled) {
        _log('warning', 'Ming CLI未安装，尝试自动安装');

        // 尝试安装Ming CLI
        final bool installResult =
            await _mingCliService.installOrBuildMingCli();
        if (installResult) {
          _log('info', 'Ming CLI安装成功');
        } else {
          _log('warning', 'Ming CLI安装失败，将使用降级模式');
        }
      }

      // 获取Ming CLI状态信息
      final String status = _mingCliService.status;
      final String version = _mingCliService.version;
      final MingCliMode mode = _mingCliService.currentMode;

      _log('info', 'Ming CLI状态: $status');
      _log('info', 'Ming CLI版本: $version');
      _log('info', 'Ming CLI模式: $mode');

      _log('info', 'Ming CLI集成初始化完成');
    } catch (e) {
      _log('error', 'Ming CLI集成初始化失败', e);
      rethrow;
    }
  }

  /// 设置状态
  void _setState(WorkshopState newState) {
    if (_state != newState) {
      final oldState = _state;
      _state = newState;

      if (!_stateController.isClosed) {
        _stateController.add(newState);
      }

      notifyListeners();
      _log('debug', '状态变更: $oldState -> $newState');
    }
  }

  /// 记录日志
  void _log(String level, String message,
      [Object? error, StackTrace? stackTrace]) {
    developer.log(
      message,
      name: _loggerName,
      level: _getLogLevel(level),
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// 获取日志级别
  int _getLogLevel(String level) {
    switch (level) {
      case 'debug':
        return 500;
      case 'info':
        return 800;
      case 'warning':
        return 900;
      case 'error':
        return 1000;
      case 'severe':
        return 1200;
      default:
        return 800;
    }
  }
}
