/*
---------------------------------------------------------------
File name:          workshop_manager.dart
Author:             Pet App Team
Date created:       2025-07-18
Last modified:      2025-07-24
Dart Version:       3.2+
Description:        创意工坊核心管理器
---------------------------------------------------------------
Change History:
    2025-07-18: Initial creation - 创意工坊核心管理器;
    2025-07-19: 优化初始化逻辑，支持重复调用;
    2025-07-24: 集成插件管理器，实现用户插件列表获取;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:plugin_system/plugin_system.dart' as plugin_sys;

// TODO(职责清晰化): 这些导入应该来自Plugin System模块
// import 'package:creative_workshop/src/core/tools/tool_plugin.dart';
// import 'package:creative_workshop/src/core/games/game_plugin.dart';
import 'package:creative_workshop/src/core/plugins/plugin_manager.dart'
    as plugin_mgr;
import 'package:creative_workshop/src/core/plugins/plugin_registry.dart';
import 'package:creative_workshop/src/core/plugins/plugin_manifest.dart' as cw;
import 'package:creative_workshop/src/core/plugins/plugin_manifest_parser.dart'
    as cw;
import 'package:creative_workshop/src/core/projects/project_manager.dart';

/// 工坊状态
enum WorkshopState {
  /// 未初始化
  uninitialized,

  /// 初始化中
  initializing,

  /// 就绪
  ready,

  /// 运行中
  running,

  /// 暂停
  paused,

  /// 错误
  error,
}

/// 创意工坊管理器
///
/// 专注于插件开发工作台的核心功能：
/// - 开发项目管理
/// - Ming CLI集成
/// - 开发环境配置
/// - 开发工具集成
///
/// 职责边界：
/// - 不负责插件生命周期管理（由Plugin System模块负责）
/// - 不负责应用商店功能（由App Manager模块负责）
/// - 专注于开发工作台的管理和配置
class WorkshopManager extends ChangeNotifier {
  WorkshopManager._();

  /// 单例实例
  static final WorkshopManager _instance = WorkshopManager._();
  static WorkshopManager get instance => _instance;

  /// 插件注册中心
  final PluginRegistry _pluginRegistry = PluginRegistry.instance;

  /// 项目管理器
  final ProjectManager _projectManager = ProjectManager.instance;

  /// 当前工坊状态
  WorkshopState _state = WorkshopState.uninitialized;

  /// 当前活跃的工具
  // TODO(职责清晰化): ToolPlugin应该来自Plugin System模块
  // ToolPlugin? _activeTool;
  dynamic _activeTool;

  /// TODO(职责清晰化): 游戏管理功能应该由Plugin System模块负责
  /// 这里保留字段以维持兼容性，后续版本将移除
  // GamePlugin? _activeGame;
  dynamic _activeGame;

  /// 状态变更流控制器
  final StreamController<WorkshopState> _stateController =
      StreamController<WorkshopState>.broadcast();

  /// 获取当前状态
  WorkshopState get state => _state;

  /// 获取项目管理器
  ProjectManager get projectManager => _projectManager;

  /// 获取当前活跃工具
  // TODO(职责清晰化): 返回类型应该是ToolPlugin
  dynamic get activeTool => _activeTool;

  /// TODO(职责清晰化): 游戏管理功能应该由Plugin System模块负责
  /// 获取当前运行游戏 - 后续版本将移除
  dynamic get activeGame => _activeGame;

  /// 状态变更流
  Stream<WorkshopState> get stateChanges => _stateController.stream;

  /// 初始化创意工坊
  Future<bool> initialize() async {
    // 检查是否已经初始化
    if (_state == WorkshopState.ready || _state == WorkshopState.running) {
      _log('info', '创意工坊已经初始化，跳过重复初始化');
      return true;
    }

    // 如果正在初始化中，等待完成
    if (_state == WorkshopState.initializing) {
      _log('info', '创意工坊正在初始化中，等待完成');
      // 等待状态变为ready或error
      await for (final WorkshopState state in _stateController.stream) {
        if (state == WorkshopState.ready) {
          return true;
        } else if (state == WorkshopState.error) {
          return false;
        }
      }
    }

    try {
      _setState(WorkshopState.initializing);

      // TODO(职责清晰化): 插件系统管理应该由Plugin System模块负责
      // 暂时保留以维持兼容性
      await _initializePluginSystem();

      // TODO(职责清晰化): 内置插件注册应该由Plugin System模块负责
      await _registerBuiltinPlugins();

      // TODO(职责清晰化): 用户插件加载应该由Plugin System模块负责
      await _loadUserPlugins();

      _setState(WorkshopState.ready);
      return true;
    } catch (e) {
      debugPrint('创意工坊初始化失败: $e');
      _setState(WorkshopState.error);
      return false;
    }
  }

  /// 启动创意工坊
  Future<bool> start() async {
    if (_state != WorkshopState.ready) {
      return false;
    }

    try {
      _setState(WorkshopState.running);
      return true;
    } catch (e) {
      debugPrint('创意工坊启动失败: $e');
      _setState(WorkshopState.error);
      return false;
    }
  }

  /// 暂停创意工坊
  Future<bool> pause() async {
    if (_state != WorkshopState.running) {
      return false;
    }

    try {
      // 暂停当前活跃的工具和游戏
      if (_activeTool != null) {
        await _activeTool!.deactivate();
      }

      if (_activeGame != null) {
        await _activeGame!.pauseGame();
      }

      _setState(WorkshopState.paused);
      return true;
    } catch (e) {
      debugPrint('创意工坊暂停失败: $e');
      return false;
    }
  }

  /// 恢复创意工坊
  Future<bool> resume() async {
    if (_state != WorkshopState.paused) {
      return false;
    }

    try {
      // 恢复当前活跃的游戏
      if (_activeGame != null) {
        await _activeGame!.resumeGame();
      }

      _setState(WorkshopState.running);
      return true;
    } catch (e) {
      debugPrint('创意工坊恢复失败: $e');
      return false;
    }
  }

  /// 停止创意工坊
  Future<bool> stop() async {
    try {
      // 停止当前活跃的工具和游戏
      if (_activeTool != null) {
        await _activeTool!.deactivate();
        _activeTool = null;
      }

      // TODO(职责清晰化): 游戏管理功能应该由Plugin System模块负责
      if (_activeGame != null) {
        // await _activeGame!.endGame();
        _activeGame = null;
      }

      _setState(WorkshopState.ready);
      return true;
    } catch (e) {
      debugPrint('创意工坊停止失败: $e');
      return false;
    }
  }

  /// TODO(职责清晰化): 工具管理功能应该由Plugin System模块负责
  /// 以下工具相关方法将在后续版本中移除
  /*
  /// 激活工具
  Future<bool> activateTool(String toolId) async {
    try {
      // 停用当前工具
      if (_activeTool != null) {
        await _activeTool!.deactivate();
      }

      // 获取新工具
      final plugin = _pluginRegistry.get(toolId);
      if (plugin is! ToolPlugin) {
        return false;
      }

      // 激活新工具
      final result = await plugin.activate();
      if (result.success) {
        _activeTool = plugin;
        notifyListeners();
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('激活工具失败: $e');
      return false;
    }
  }

  /// 停用当前工具
  Future<bool> deactivateTool() async {
    if (_activeTool == null) {
      return true;
    }

    try {
      await _activeTool!.deactivate();
      _activeTool = null;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('停用工具失败: $e');
      return false;
    }
  }
  */

  /// TODO(职责清晰化): 游戏管理功能应该由Plugin System模块负责
  /// 以下游戏相关方法将在后续版本中移除
  /*
  /// 启动游戏
  Future<bool> startGame(String gameId) async {
    try {
      // 停止当前游戏
      if (_activeGame != null) {
        await _activeGame!.endGame();
      }

      // 获取新游戏
      final plugin = _pluginRegistry.get(gameId);
      if (plugin is! GamePlugin) {
        return false;
      }

      // 启动新游戏
      final result = await plugin.startGame();
      if (result.success) {
        _activeGame = plugin;
        notifyListeners();
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('启动游戏失败: $e');
      return false;
    }
  }

  /// 停止当前游戏
  Future<bool> stopGame() async {
    if (_activeGame == null) {
      return true;
    }

    try {
      await _activeGame!.endGame();
      _activeGame = null;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('停止游戏失败: $e');
      return false;
    }
  }
  */

  /// TODO(职责清晰化): 工具插件管理应该由Plugin System模块负责
  /*
  /// 获取所有工具插件
  List<ToolPlugin> getTools() => _pluginRegistry
      .getByCategory(PluginType.tool)
      .whereType<ToolPlugin>()
      .toList();
  */

  /// TODO(职责清晰化): 游戏和工具插件管理应该由Plugin System模块负责
  /*
  /// 获取所有游戏插件
  List<GamePlugin> getGames() => _pluginRegistry
      .getByCategory(PluginType.game)
      .whereType<GamePlugin>()
      .toList();

  /// 按类型获取工具
  List<ToolPlugin> getToolsByType(ToolType type) =>
      getTools().where((ToolPlugin tool) => tool.toolType == type).toList();

  /// 按类型获取游戏
  List<GamePlugin> getGamesByType(GameType type) =>
      getGames().where((GamePlugin game) => game.gameType == type).toList();
  */

  /// 初始化插件系统
  Future<void> _initializePluginSystem() async {
    // 插件系统已经在 Phase 1 中初始化完成
    // 这里可以添加创意工坊特定的初始化逻辑
  }

  /// 注册内置插件
  Future<void> _registerBuiltinPlugins() async {
    try {
      _log('info', '开始注册内置插件');

      // 注册内置工具插件
      await _registerBuiltinTools();

      // 注册内置游戏插件
      await _registerBuiltinGames();

      _log('info', '内置插件注册完成');
    } catch (e, stackTrace) {
      _log('severe', '注册内置插件失败', e, stackTrace);
      rethrow;
    }
  }

  /// 加载用户插件
  Future<void> _loadUserPlugins() async {
    try {
      _log('info', '开始加载用户插件');

      // 从本地存储获取用户插件列表
      final userPlugins = await _getUserPluginList();

      // 验证和加载每个插件
      for (final pluginInfo in userPlugins) {
        await _loadUserPlugin(pluginInfo);
      }

      _log('info', '用户插件加载完成，共加载 ${userPlugins.length} 个插件');
    } catch (e, stackTrace) {
      _log('severe', '加载用户插件失败', e, stackTrace);
      rethrow;
    }
  }

  /// 设置状态
  void _setState(WorkshopState newState) {
    if (_state != newState) {
      _state = newState;
      _stateController.add(newState);
      notifyListeners();
    }
  }

  /// 清理资源
  @override
  void dispose() {
    _stateController.close();
    super.dispose();
  }

  // ===== 日志和辅助方法 =====

  /// 日志记录方法
  static void _log(
    String level,
    String message, [
    Object? error,
    StackTrace? stackTrace,
  ]) {
    if (kDebugMode) {
      final timestamp = DateTime.now().toIso8601String();
      print('[$timestamp] [$level] [WorkshopManager] $message');
      if (error != null) {
        print('Error: $error');
      }
      if (stackTrace != null) {
        print('StackTrace: $stackTrace');
      }
    }
  }

  /// 注册内置工具插件
  ///
  /// Phase 5.0.6 重构：转型为应用商店模式
  /// 不再注册内置工具，改为从插件市场动态加载
  Future<void> _registerBuiltinTools() async {
    _log('info', '跳过内置工具注册 - 转型为应用商店模式');

    // TODO: Phase 5.0.6.2 - 实现从插件市场加载工具
    // 1. 扫描已安装的工具插件
    // 2. 动态加载插件到注册中心
    // 3. 验证插件兼容性和权限

    _log('info', '工具插件注册完成 - 应用商店模式');
  }

  /// 注册内置游戏插件
  ///
  /// Phase 5.0.6 重构：转型为应用商店模式
  /// 不再注册内置游戏，改为从插件市场动态加载
  Future<void> _registerBuiltinGames() async {
    _log('info', '跳过内置游戏注册 - 转型为应用商店模式');

    // TODO: Phase 5.0.6.2 - 实现从插件市场加载游戏
    // 1. 扫描已安装的游戏插件
    // 2. 动态加载插件到注册中心
    // 3. 验证插件兼容性和权限

    _log('info', '游戏插件注册完成 - 应用商店模式');
  }

  // ===== 用户插件管理方法 =====

  /// 获取用户插件列表
  Future<List<Map<String, dynamic>>> _getUserPluginList() async {
    // Phase 5.0.11 修复 - 集成 PluginManager 获取真实插件数据
    _log('info', '获取用户插件列表');

    try {
      // 1. 从 PluginManager 获取已安装的插件
      final pluginManager = plugin_mgr.PluginManager.instance;
      await pluginManager.initialize(); // 确保已初始化

      final installedPlugins = pluginManager.installedPlugins;
      final List<Map<String, dynamic>> userPlugins = <Map<String, dynamic>>[];

      // 2. 转换为 WorkshopManager 需要的格式
      for (final plugin in installedPlugins) {
        // 跳过内置插件，只返回用户插件
        if (!_isBuiltinPlugin(plugin.id)) {
          userPlugins.add(<String, dynamic>{
            'id': plugin.id,
            'name': plugin.name,
            'type': _inferPluginTypeFromId(plugin.id), // 从插件ID推断类型
            'version': plugin.version,
            'enabled': plugin.state == plugin_mgr.PluginState.enabled,
            'author': 'Unknown', // TODO: 从插件清单获取作者信息
            'description': '用户安装的插件', // TODO: 从插件清单获取描述
            'state': plugin.state.toString(),
            'installedAt': plugin.installedAt.toIso8601String(),
            'size': plugin.size,
            'permissions': plugin.permissions
                .map((plugin_mgr.PluginPermission p) => p.toString())
                .toList(),
          });
        }
      }

      // 3. 如果没有用户插件，从注册表获取已注册的插件作为后备
      if (userPlugins.isEmpty) {
        final registeredPlugins = _pluginRegistry.registrations;
        for (final registration in registeredPlugins) {
          final metadata = registration.metadata;
          if (!_isBuiltinPlugin(metadata.id)) {
            userPlugins.add(<String, dynamic>{
              'id': metadata.id,
              'name': metadata.name,
              'type': 'plugin', // TODO(职责清晰化): 插件类型判断应该由Plugin System模块负责
              'version': metadata.version,
              'enabled': true, // 已注册的插件默认为启用状态
              'author': metadata.author,
              'description': metadata.description,
            });
          }
        }
      }

      _log('info', '找到 ${userPlugins.length} 个用户插件');
      return userPlugins;
    } catch (e) {
      _log('warning', '获取用户插件列表失败: $e');
      return <Map<String, dynamic>>[];
    }
  }

  /// 加载单个用户插件
  Future<void> _loadUserPlugin(Map<String, dynamic> pluginInfo) async {
    final String pluginId = pluginInfo['id'] as String;
    _log('info', '加载用户插件: $pluginId');

    try {
      // 验证插件信息
      if (!_validatePluginInfo(pluginInfo)) {
        _log('warning', '插件信息验证失败: $pluginId');
        return;
      }

      // 检查插件是否已启用
      if (!(pluginInfo['enabled'] as bool? ?? false)) {
        _log('info', '插件已禁用，跳过加载: $pluginId');
        return;
      }

      // 根据插件类型进行加载
      final String pluginType = pluginInfo['type'] as String;
      switch (pluginType) {
        case 'tool':
          await _loadUserToolPlugin(pluginInfo);
        case 'game':
          await _loadUserGamePlugin(pluginInfo);
        default:
          _log('warning', '未知的插件类型: $pluginType');
      }

      _log('info', '用户插件加载成功: $pluginId');
    } catch (e) {
      _log('warning', '加载用户插件失败: $pluginId, 错误: $e');
    }
  }

  /// 验证插件信息
  bool _validatePluginInfo(Map<String, dynamic> pluginInfo) {
    final List<String> requiredFields = <String>[
      'id',
      'name',
      'type',
      'version',
    ];

    for (final String field in requiredFields) {
      if (!pluginInfo.containsKey(field) || pluginInfo[field] == null) {
        _log('warning', '插件信息缺少必需字段: $field');
        return false;
      }
    }

    return true;
  }

  /// 详细验证插件信息
  Future<PluginValidationResult> _validatePluginInfoDetailed(
    Map<String, dynamic> pluginInfo,
  ) async {
    final List<String> errors = [];

    // 基础字段验证
    final List<String> requiredFields = [
      'id',
      'name',
      'type',
      'version',
    ];

    for (final String field in requiredFields) {
      if (!pluginInfo.containsKey(field) || pluginInfo[field] == null) {
        errors.add('缺少必需字段: $field');
      }
    }

    // 字段格式验证
    if (pluginInfo.containsKey('id')) {
      final id = pluginInfo['id'] as String?;
      if (id == null ||
          id.isEmpty ||
          !RegExp(r'^[a-zA-Z0-9_-]+$').hasMatch(id)) {
        errors.add('插件ID格式无效');
      }
    }

    if (pluginInfo.containsKey('version')) {
      final version = pluginInfo['version'] as String?;
      if (version == null || version.isEmpty) {
        errors.add('版本号不能为空');
      }
    }

    if (pluginInfo.containsKey('type')) {
      final type = pluginInfo['type'] as String?;
      if (type == null || !['tool', 'game', 'plugin'].contains(type)) {
        errors.add('插件类型无效，必须是 tool、game 或 plugin');
      }
    }

    return errors.isEmpty
        ? PluginValidationResult.success()
        : PluginValidationResult.failure(errors);
  }

  /// 解析插件路径
  Future<String?> _resolvePluginPath(String pluginId) async {
    try {
      // TODO: 实现真实的插件路径解析逻辑
      // 1. 检查用户插件目录
      // 2. 检查系统插件目录
      // 3. 检查已安装插件注册表

      // 暂时返回模拟路径
      return 'plugins/$pluginId';
    } catch (e) {
      _log('error', '解析插件路径失败: $pluginId', e);
      return null;
    }
  }

  /// 加载插件清单文件
  Future<cw.PluginManifestParseResult> _loadPluginManifest(
      String pluginPath) async {
    try {
      // TODO: 实现真实的清单文件加载
      // 使用PluginManifestParser解析plugin.yaml文件

      // 暂时返回模拟成功结果
      return cw.PluginManifestParseResult.success(
        const cw.PluginManifest(
          id: 'mock_plugin',
          name: 'Mock Plugin',
          version: '1.0.0',
          description: 'Mock plugin for testing',
          author: 'Test Author',
          category: 'tool',
          main: 'lib/main.dart',
          permissions: <String>[],
          dependencies: <cw.PluginManifestDependency>[],
          platforms: <String>['android', 'ios', 'web'],
        ),
      );
    } catch (e) {
      return cw.PluginManifestParseResult.failure('加载清单文件失败: $e');
    }
  }

  /// 执行安全检查
  Future<PluginSecurityResult> _performSecurityCheck(
    String pluginPath,
    cw.PluginManifest manifest,
  ) async {
    try {
      final List<String> issues = [];

      // TODO: 实现真实的安全检查
      // 1. 验证插件签名
      // 2. 检查恶意代码
      // 3. 验证权限请求
      // 4. 检查文件完整性

      // 暂时进行基础检查
      if (manifest.permissions.isNotEmpty) {
        // 检查权限请求是否合理
        for (final permission in manifest.permissions) {
          if (permission == 'systemAccess' || permission == 'fileSystem') {
            issues.add('请求了敏感权限: $permission，需要用户确认');
          }
        }
      }

      return issues.isEmpty
          ? PluginSecurityResult.secure()
          : PluginSecurityResult.insecure(issues);
    } catch (e) {
      return PluginSecurityResult.insecure(['安全检查失败: $e']);
    }
  }

  /// 检查插件依赖
  Future<PluginDependencyResult> _checkPluginDependencies(
    cw.PluginManifest manifest,
  ) async {
    try {
      final List<String> missingDeps = [];

      // TODO: 实现真实的依赖检查
      // 1. 检查Plugin System中已注册的插件
      // 2. 检查版本兼容性
      // 3. 检查循环依赖

      for (final cw.PluginManifestDependency dependency
          in manifest.dependencies) {
        // 暂时假设所有依赖都已满足
        // 实际实现中需要检查依赖是否已安装且版本兼容
        // TODO: 检查依赖 ${dependency.name} 的版本兼容性

        // 记录依赖检查（避免未使用变量警告）
        _log('debug', '检查依赖: ${dependency.id} ${dependency.version}');
      }

      return missingDeps.isEmpty
          ? PluginDependencyResult.resolved()
          : PluginDependencyResult.unresolved(missingDeps);
    } catch (e) {
      return PluginDependencyResult.unresolved(['依赖检查失败: $e']);
    }
  }

  /// 动态实例化工具插件
  Future<plugin_sys.Plugin?> _instantiateToolPlugin(
    String pluginPath,
    cw.PluginManifest manifest,
  ) async {
    try {
      _log('info', '开始实例化工具插件: ${manifest.id}');

      // 1. 读取插件主入口文件
      final mainFilePath = path.join(pluginPath, manifest.main);
      final mainFile = File(mainFilePath);

      if (!await mainFile.exists()) {
        _log('error', '插件主文件不存在: $mainFilePath');
        return null;
      }

      // 2. 读取插件代码
      final pluginCode = await mainFile.readAsString();
      _log('debug', '读取插件代码成功，长度: ${pluginCode.length}');

      // 3. 创建插件实例（使用反射或工厂模式）
      final plugin =
          await _createPluginInstance(manifest, pluginCode, pluginPath);

      if (plugin == null) {
        _log('error', '创建插件实例失败: ${manifest.id}');
        return null;
      }

      // 4. 验证插件接口
      if (!await _validatePluginInterface(plugin)) {
        _log('error', '插件接口验证失败: ${manifest.id}');
        return null;
      }

      _log('info', '工具插件实例化成功: ${manifest.id}');
      return plugin;
    } catch (e) {
      _log('error', '实例化工具插件失败: ${manifest.id}', e);
      return null;
    }
  }

  /// 创建插件实例
  Future<plugin_sys.Plugin?> _createPluginInstance(
    cw.PluginManifest manifest,
    String pluginCode,
    String pluginPath,
  ) async {
    try {
      _log('info', '创建插件实例: ${manifest.id}');

      // 由于Dart不支持运行时动态编译，我们使用工厂模式
      // 根据插件清单创建一个适配器插件实例
      final plugin = _PluginAdapter(
        manifest: manifest,
        pluginPath: pluginPath,
        pluginCode: pluginCode,
      );

      _log('info', '插件实例创建成功: ${manifest.id}');
      return plugin;
    } catch (e) {
      _log('error', '创建插件实例失败: ${manifest.id}', e);
      return null;
    }
  }

  /// 验证插件接口
  Future<bool> _validatePluginInterface(plugin_sys.Plugin plugin) async {
    try {
      _log('debug', '验证插件接口: ${plugin.id}');

      // 验证必需的属性
      if (plugin.id.isEmpty) {
        _log('error', '插件ID为空');
        return false;
      }

      if (plugin.name.isEmpty) {
        _log('error', '插件名称为空');
        return false;
      }

      if (plugin.version.isEmpty) {
        _log('error', '插件版本为空');
        return false;
      }

      // 验证插件状态
      if (plugin.currentState == plugin_sys.PluginState.error) {
        _log('error', '插件处于错误状态');
        return false;
      }

      _log('debug', '插件接口验证通过: ${plugin.id}');
      return true;
    } catch (e) {
      _log('error', '验证插件接口失败: ${plugin.id}', e);
      return false;
    }
  }

  /// 注册插件到Plugin System
  Future<void> _registerPluginToSystem(plugin_sys.Plugin plugin) async {
    try {
      // TODO: 实现真实的插件注册逻辑
      // 1. 获取Plugin System的PluginRegistry
      // 2. 注册插件到系统
      // 3. 更新插件状态

      // 暂时使用本地插件注册表
      final pluginMetadata = PluginMetadata(
        id: plugin.id,
        name: plugin.name,
        version: plugin.version,
        description: plugin.description,
        author: plugin.author,
        category: plugin.category.name,
      );

      _pluginRegistry.registerPlugin(
        pluginMetadata,
        () => plugin as Plugin,
      );

      _log('info', '插件注册成功: ${plugin.id}');
    } catch (e) {
      _log('error', '注册插件失败: ${plugin.id}', e);
      rethrow;
    }
  }

  /// 加载用户工具插件
  Future<void> _loadUserToolPlugin(Map<String, dynamic> pluginInfo) async {
    final String pluginId = pluginInfo['id'] as String;
    _log('info', '开始加载用户工具插件: $pluginId');

    try {
      // 1. 验证插件信息
      final validationResult = await _validatePluginInfoDetailed(pluginInfo);
      if (!validationResult.isValid) {
        throw PluginValidationException(
          'Plugin validation failed for $pluginId: ${validationResult.errors.join(', ')}',
        );
      }

      // 2. 解析插件路径
      final pluginPath = await _resolvePluginPath(pluginId);
      if (pluginPath == null) {
        throw PluginNotFoundException('Plugin path not found for $pluginId');
      }

      // 3. 读取插件清单文件
      final manifestResult = await _loadPluginManifest(pluginPath);
      if (!manifestResult.success) {
        throw PluginManifestException(
          'Failed to load manifest for $pluginId: ${manifestResult.error}',
        );
      }

      final manifest = manifestResult.manifest!;

      // 4. 安全检查
      final securityResult = await _performSecurityCheck(pluginPath, manifest);
      if (!securityResult.isSecure) {
        throw PluginSecurityException(
          'Security check failed for $pluginId: ${securityResult.issues.join(', ')}',
        );
      }

      // 5. 检查依赖关系
      final dependencyResult = await _checkPluginDependencies(manifest);
      if (!dependencyResult.isResolved) {
        throw PluginDependencyException(
          'Dependency check failed for $pluginId: ${dependencyResult.missingDependencies.join(', ')}',
        );
      }

      // 6. 动态实例化插件
      final plugin = await _instantiateToolPlugin(pluginPath, manifest);
      if (plugin == null) {
        throw PluginInstantiationException(
          'Failed to instantiate plugin $pluginId',
        );
      }

      // 7. 注册到Plugin System
      await _registerPluginToSystem(plugin);

      // 8. 初始化插件
      await plugin.initialize();

      _log('info', '用户工具插件加载成功: $pluginId');
    } catch (e, stackTrace) {
      _log('severe', '加载用户工具插件失败: $pluginId', e, stackTrace);
      rethrow;
    }
  }

  /// 加载用户游戏插件
  Future<void> _loadUserGamePlugin(Map<String, dynamic> pluginInfo) async {
    final String pluginId = pluginInfo['id'] as String;
    _log('info', '开始加载用户游戏插件: $pluginId');

    try {
      // 1. 验证插件信息
      final validationResult = await _validatePluginInfoDetailed(pluginInfo);
      if (!validationResult.isValid) {
        throw PluginValidationException(
          'Plugin validation failed for $pluginId: ${validationResult.errors.join(', ')}',
        );
      }

      // 2. 解析插件路径
      final pluginPath = await _resolvePluginPath(pluginId);
      if (pluginPath == null) {
        throw PluginNotFoundException('Plugin path not found for $pluginId');
      }

      // 3. 读取插件清单文件
      final manifestResult = await _loadPluginManifest(pluginPath);
      if (!manifestResult.success) {
        throw PluginManifestException(
          'Failed to load manifest for $pluginId: ${manifestResult.error}',
        );
      }

      final manifest = manifestResult.manifest!;

      // 4. 安全检查
      final securityResult = await _performSecurityCheck(pluginPath, manifest);
      if (!securityResult.isSecure) {
        throw PluginSecurityException(
          'Security check failed for $pluginId: ${securityResult.issues.join(', ')}',
        );
      }

      // 5. 检查依赖关系
      final dependencyResult = await _checkPluginDependencies(manifest);
      if (!dependencyResult.isResolved) {
        throw PluginDependencyException(
          'Dependency check failed for $pluginId: ${dependencyResult.missingDependencies.join(', ')}',
        );
      }

      // 6. 动态实例化插件
      final plugin = await _instantiateGamePlugin(pluginPath, manifest);
      if (plugin == null) {
        throw PluginInstantiationException(
          'Failed to instantiate plugin $pluginId',
        );
      }

      // 7. 注册到Plugin System
      await _registerPluginToSystem(plugin);

      // 8. 初始化插件
      await plugin.initialize();

      _log('info', '用户游戏插件加载成功: $pluginId');
    } catch (e, stackTrace) {
      _log('severe', '加载用户游戏插件失败: $pluginId', e, stackTrace);
      rethrow;
    }
  }

  /// 动态实例化游戏插件
  Future<plugin_sys.Plugin?> _instantiateGamePlugin(
    String pluginPath,
    cw.PluginManifest manifest,
  ) async {
    try {
      // TODO: 实现真实的游戏插件实例化逻辑
      // 1. 读取插件主入口文件
      // 2. 动态加载Dart代码
      // 3. 实例化游戏插件类
      // 4. 验证游戏插件接口

      // 暂时返回null，表示实例化失败
      // 实际实现中需要使用Dart的动态加载机制
      _log('warning', '游戏插件实例化功能暂未实现: ${manifest.id}');
      return null;
    } catch (e) {
      _log('error', '实例化游戏插件失败: ${manifest.id}', e);
      return null;
    }
  }

  // ===== 辅助方法 =====

  /// 判断是否为内置插件
  bool _isBuiltinPlugin(String pluginId) {
    const builtinPluginIds = <String>{
      'simple_brush_tool',
      'simple_pencil_tool',
      'simple_click_game',
    };
    return builtinPluginIds.contains(pluginId);
  }

  /// TODO(职责清晰化): 插件类型判断应该由Plugin System模块负责
  /*
  /// 获取插件类型
  String _getPluginType(Plugin plugin) {
    if (plugin is ToolPlugin) {
      return 'tool';
    } else if (plugin is GamePlugin) {
      return 'game';
    } else {
      return 'unknown';
    }
  }
  */

  /// 从插件ID推断插件类型
  String _inferPluginTypeFromId(String pluginId) {
    // 根据插件ID的命名模式推断类型
    if (pluginId.contains('tool') ||
        pluginId.contains('brush') ||
        pluginId.contains('palette') ||
        pluginId.contains('editor')) {
      return 'tool';
    } else if (pluginId.contains('game') ||
        pluginId.contains('puzzle') ||
        pluginId.contains('click')) {
      return 'game';
    } else {
      // 默认为工具类型
      return 'tool';
    }
  }
}

// ===== 插件加载相关异常类 =====

/// 插件验证异常
class PluginValidationException implements Exception {
  final String message;
  const PluginValidationException(this.message);

  @override
  String toString() => 'PluginValidationException: $message';
}

/// 插件未找到异常
class PluginNotFoundException implements Exception {
  final String message;
  const PluginNotFoundException(this.message);

  @override
  String toString() => 'PluginNotFoundException: $message';
}

/// 插件清单异常
class PluginManifestException implements Exception {
  final String message;
  const PluginManifestException(this.message);

  @override
  String toString() => 'PluginManifestException: $message';
}

/// 插件安全异常
class PluginSecurityException implements Exception {
  final String message;
  const PluginSecurityException(this.message);

  @override
  String toString() => 'PluginSecurityException: $message';
}

/// 插件依赖异常
class PluginDependencyException implements Exception {
  final String message;
  const PluginDependencyException(this.message);

  @override
  String toString() => 'PluginDependencyException: $message';
}

/// 插件实例化异常
class PluginInstantiationException implements Exception {
  final String message;
  const PluginInstantiationException(this.message);

  @override
  String toString() => 'PluginInstantiationException: $message';
}

// ===== 插件加载相关结果类 =====

/// 插件验证结果
class PluginValidationResult {
  final bool isValid;
  final List<String> errors;

  const PluginValidationResult({
    required this.isValid,
    required this.errors,
  });

  factory PluginValidationResult.success() {
    return const PluginValidationResult(isValid: true, errors: []);
  }

  factory PluginValidationResult.failure(List<String> errors) {
    return PluginValidationResult(isValid: false, errors: errors);
  }
}

/// 插件安全检查结果
class PluginSecurityResult {
  final bool isSecure;
  final List<String> issues;

  const PluginSecurityResult({
    required this.isSecure,
    required this.issues,
  });

  factory PluginSecurityResult.secure() {
    return const PluginSecurityResult(isSecure: true, issues: []);
  }

  factory PluginSecurityResult.insecure(List<String> issues) {
    return PluginSecurityResult(isSecure: false, issues: issues);
  }
}

/// 插件依赖检查结果
class PluginDependencyResult {
  final bool isResolved;
  final List<String> missingDependencies;

  const PluginDependencyResult({
    required this.isResolved,
    required this.missingDependencies,
  });

  factory PluginDependencyResult.resolved() {
    return const PluginDependencyResult(
        isResolved: true, missingDependencies: []);
  }

  factory PluginDependencyResult.unresolved(List<String> missing) {
    return PluginDependencyResult(
        isResolved: false, missingDependencies: missing);
  }
}

// ===== 插件适配器类 =====

/// 插件适配器
///
/// 由于Dart不支持运行时动态编译，我们使用适配器模式
/// 根据插件清单创建一个符合Plugin System接口的插件实例
class _PluginAdapter extends plugin_sys.Plugin {
  final cw.PluginManifest _manifest;
  final String _pluginPath;
  final String _pluginCode;

  plugin_sys.PluginState _currentState = plugin_sys.PluginState.unloaded;
  final StreamController<plugin_sys.PluginState> _stateController =
      StreamController<plugin_sys.PluginState>.broadcast();
  DateTime? _loadTime;

  _PluginAdapter({
    required cw.PluginManifest manifest,
    required String pluginPath,
    required String pluginCode,
  })  : _manifest = manifest,
        _pluginPath = pluginPath,
        _pluginCode = pluginCode;

  @override
  String get id => _manifest.id;

  @override
  String get name => _manifest.name;

  @override
  String get version => _manifest.version;

  @override
  String get description => _manifest.description;

  @override
  String get author => _manifest.author;

  @override
  plugin_sys.PluginType get category {
    switch (_manifest.category.toLowerCase()) {
      case 'tool':
        return plugin_sys.PluginType.tool;
      case 'game':
        return plugin_sys.PluginType.game;
      case 'utility':
        return plugin_sys.PluginType.service;
      default:
        return plugin_sys.PluginType.service;
    }
  }

  @override
  plugin_sys.PluginState get currentState => _currentState;

  @override
  Stream<plugin_sys.PluginState> get stateChanges => _stateController.stream;

  @override
  plugin_sys.PluginManifest get manifest => plugin_sys.PluginManifest(
        id: id,
        name: name,
        version: version,
        description: description,
        author: author,
        category: _manifest.category,
        main: _manifest.main,
        permissions: _manifest.permissions,
        dependencies: _manifest.dependencies
            .map((d) => plugin_sys.PluginManifestDependency(
                  id: d.id,
                  version: d.version,
                ))
            .toList(),
        platforms: _manifest.platforms,
      );

  @override
  bool get isEnabled => _currentState != plugin_sys.PluginState.error;

  @override
  Duration? get loadTime => _loadTime;

  @override
  Future<void> initialize() async {
    final startTime = DateTime.now();
    _updateState(plugin_sys.PluginState.loaded);

    try {
      // 模拟初始化过程
      await Future<void>.delayed(const Duration(milliseconds: 100));

      _loadTime = DateTime.now().difference(startTime);
      _updateState(plugin_sys.PluginState.initialized);
    } catch (e) {
      _updateState(plugin_sys.PluginState.error);
      rethrow;
    }
  }

  @override
  Future<void> start() async {
    if (_currentState != plugin_sys.PluginState.initialized) {
      throw StateError('Plugin must be initialized before starting');
    }

    try {
      // 模拟启动过程
      await Future<void>.delayed(const Duration(milliseconds: 50));

      _updateState(plugin_sys.PluginState.started);
    } catch (e) {
      _updateState(plugin_sys.PluginState.error);
      rethrow;
    }
  }

  @override
  Future<void> pause() async {
    if (_currentState != plugin_sys.PluginState.started) {
      throw StateError('Plugin must be started before pausing');
    }

    try {
      // 模拟暂停过程
      await Future<void>.delayed(const Duration(milliseconds: 25));

      _updateState(plugin_sys.PluginState.paused);
    } catch (e) {
      _updateState(plugin_sys.PluginState.error);
      rethrow;
    }
  }

  @override
  Future<void> resume() async {
    if (_currentState != plugin_sys.PluginState.paused) {
      throw StateError('Plugin must be paused before resuming');
    }

    try {
      // 模拟恢复过程
      await Future<void>.delayed(const Duration(milliseconds: 25));

      _updateState(plugin_sys.PluginState.started);
    } catch (e) {
      _updateState(plugin_sys.PluginState.error);
      rethrow;
    }
  }

  @override
  Future<void> stop() async {
    if (_currentState != plugin_sys.PluginState.started &&
        _currentState != plugin_sys.PluginState.paused) {
      throw StateError('Plugin must be started or paused before stopping');
    }

    try {
      // 模拟停止过程
      await Future<void>.delayed(const Duration(milliseconds: 50));

      _updateState(plugin_sys.PluginState.stopped);
    } catch (e) {
      _updateState(plugin_sys.PluginState.error);
      rethrow;
    }
  }

  @override
  Future<void> dispose() async {
    try {
      // 模拟清理过程
      await Future<void>.delayed(const Duration(milliseconds: 25));

      _updateState(plugin_sys.PluginState.unloaded);
      await _stateController.close();
    } catch (e) {
      _updateState(plugin_sys.PluginState.error);
      rethrow;
    }
  }

  @override
  Object? getConfigWidget() => null; // 暂时返回null

  @override
  Object getMainWidget() => Object(); // 返回空对象作为占位符

  @override
  Future<dynamic> handleMessage(
    String action,
    Map<String, dynamic> data,
  ) async {
    // 处理消息
    switch (action) {
      case 'ping':
        return <String, dynamic>{
          'status': 'pong',
          'timestamp': DateTime.now().toIso8601String(),
        };
      case 'getInfo':
        return <String, dynamic>{
          'id': id,
          'name': name,
          'version': version,
          'state': _currentState.toString(),
        };
      default:
        throw UnsupportedError('Unsupported action: $action');
    }
  }

  /// 更新插件状态
  void _updateState(plugin_sys.PluginState newState) {
    if (_currentState != newState) {
      _currentState = newState;
      if (!_stateController.isClosed) {
        _stateController.add(newState);
      }
    }
  }
}
