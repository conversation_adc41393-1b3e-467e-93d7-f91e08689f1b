
/*
---------------------------------------------------------------
File name:          widget_test.dart
Author:             lgnorant-lu
Date created:       2025-07-24
Last modified:      2025-07-24
Dart Version:       3.2+
Description:        首页仪表板组件测试 - Phase *******
---------------------------------------------------------------
*/

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:home_dashboard/home_dashboard.dart';

void main() {
  group('Home Dashboard Widget Tests', () {
    testWidgets('HomePage should render without errors',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: HomePage(),
          ),
        ),
      );

      expect(find.byType(HomePage), findsOneWidget);
    });

    testWidgets('QuickAccessPanel should render', (WidgetTester tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: QuickAccessPanel(),
            ),
          ),
        ),
      );

      expect(find.byType(QuickAccessPanel), findsOneWidget);
    });
  });
}
