import 'package:flutter_test/flutter_test.dart';
import 'package:core_services_v1/core_services_v1.dart';

void main() {
  group('core_services_v1 Tests', () {
    test('should create module instance', () {
      final module = CoreServicesV1Module.instance;
      expect(module, isNotNull);
    });

    test('should have correct module name', () {
      final module = CoreServicesV1Module.instance;
      expect(module.isInitialized, isFalse);
    });
  });
}
