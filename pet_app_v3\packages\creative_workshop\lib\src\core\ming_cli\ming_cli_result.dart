/*
---------------------------------------------------------------
File name:          ming_cli_result.dart
Author:             lgnorant-lu
Date created:       2025-07-31
Last modified:      2025-07-31
Dart Version:       3.2+
Description:        Ming CLI执行结果类 (Ming CLI Execution Result)
---------------------------------------------------------------
Change History:
    2025-07-31: Initial creation - Ming CLI执行结果类;
---------------------------------------------------------------
*/

/// Ming CLI命令执行结果
///
/// 封装Ming CLI命令的执行结果，包括成功状态、输出、错误信息等
class MingCliResult {
  /// 创建Ming CLI执行结果
  const MingCliResult({
    required this.success,
    required this.output,
    required this.error,
    required this.exitCode,
    required this.executionTime,
    required this.mode,
  });

  /// 命令是否执行成功
  final bool success;

  /// 命令输出内容
  final String output;

  /// 错误信息
  final String error;

  /// 退出码
  final int exitCode;

  /// 执行时间
  final Duration executionTime;

  /// 执行模式
  final MingCliMode mode;

  /// 是否有错误
  bool get hasError => !success || error.isNotEmpty;

  /// 是否有输出
  bool get hasOutput => output.isNotEmpty;

  /// 执行时间（毫秒）
  int get executionTimeMs => executionTime.inMilliseconds;

  @override
  String toString() {
    return 'MingCliResult('
        'success: $success, '
        'exitCode: $exitCode, '
        'mode: $mode, '
        'executionTime: ${executionTime.inMilliseconds}ms'
        ')';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MingCliResult &&
        other.success == success &&
        other.output == output &&
        other.error == error &&
        other.exitCode == exitCode &&
        other.executionTime == executionTime &&
        other.mode == mode;
  }

  @override
  int get hashCode {
    return Object.hash(
      success,
      output,
      error,
      exitCode,
      executionTime,
      mode,
    );
  }
}

/// Ming CLI运行模式
enum MingCliMode {
  /// 系统全局安装
  systemInstalled,

  /// 本地构建版本
  localBuilt,

  /// Dart Run模式
  dartRun,

  /// 降级模式
  fallback,
}
