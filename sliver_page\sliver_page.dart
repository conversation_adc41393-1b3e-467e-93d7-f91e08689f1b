// import 'package:flutter/material.dart';

// // 为了让 SliverPersistentHeader 工作，需要一个 delegate，这是一个固定写法
// class _MySliverPersistentHeaderDelegate extends SliverPersistentHeaderDelegate {
//   _MySliverPersistentHeaderDelegate(this.child, {required this.height});
//   final Widget child;
//   final double height;

//   @override
//   double get minExtent => height;
//   @override
//   double get maxExtent => height;

//   @override
//   Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
//     return child;
//   }

//   @override
//   bool shouldRebuild(_MySliverPersistentHeaderDelegate oldDelegate) {
//     return false;
//   }
// }


// class VerticalToHorizontalScrollPage extends StatelessWidget {
//   const VerticalToHorizontalScrollPage({Key? key}) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: NestedScrollView(
//         // ==========================================
//         // 1. “头部”区域：负责垂直滚动的部分
//         // ==========================================
//         headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
//           // 这里返回一个 Widget 列表，它们都是 Sliver 家族的
//           return <Widget>[
//             // A部分: 纯粹的垂直内容
//             SliverList(
//               delegate: SliverChildListDelegate([
//                 // 这里可以放任何你想在顶部展示的内容
//                 Image.network('https://picsum.photos/seed/picsum/400/300', fit: BoxFit.cover),
//                 const SizedBox(height: 20),
//                 const Padding(
//                   padding: EdgeInsets.all(16.0),
//                   child: Text(
//                     '向上滑动，开始探索',
//                     style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
//                     textAlign: TextAlign.center,
//                   ),
//                 ),
//                 Container(height: 200, color: Colors.grey[200]), // 更多垂直占位内容
//                 Container(height: 200, color: Colors.grey[300]), // 更多垂直占位内容
//               ]),
//             ),

//             // B部分: “粘性”导航区
//             SliverPersistentHeader(
//               pinned: true, // 关键！设置为 true 才能固定在顶部
//               delegate: _MySliverPersistentHeaderDelegate(
//                 Container(
//                   height: 60.0,
//                   color: Colors.white, // 给个背景色，以便看清
//                   child: const Center(
//                     child: Text(
//                       '← 请左右滑动 →',
//                       style: TextStyle(fontSize: 18, color: Colors.black),
//                     ),
//                   ),
//                 ),
//                 height: 60.0,
//               ),
//             ),
//           ];
//         },
        
//         // ==========================================
//         // 2. “身体”区域：负责水平滚动的部分
//         // ==========================================
//         body: PageView(
//           children: <Widget>[
//             // 第一个水平页面
//             Container(
//               color: Colors.red[200],
//               child: const Center(child: Text('第一页', style: TextStyle(fontSize: 32))),
//             ),
//             // 第二个水平页面
//             Container(
//               color: Colors.green[200],
//               child: const Center(child: Text('第二页', style: TextStyle(fontSize: 32))),
//             ),
//             // 第三个水平页面
//             Container(
//               color: Colors.blue[200],
//               child: const Center(child: Text('第三页', style: TextStyle(fontSize: 32))),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }