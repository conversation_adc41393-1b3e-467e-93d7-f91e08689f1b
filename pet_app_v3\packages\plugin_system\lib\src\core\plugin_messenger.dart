/*
---------------------------------------------------------------
File name:          plugin_messenger.dart
Author:             lgnorant-lu
Date created:       2025/07/18
Last modified:      2025/07/27
Dart Version:       3.2+
Description:        插件消息传递 (Plugin messaging)
---------------------------------------------------------------
Change History:
    2025/07/18: Initial creation - 插件消息传递 (Plugin messaging);
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:convert';

import 'package:plugin_system/src/core/plugin.dart';
import 'package:plugin_system/src/core/plugin_exceptions.dart';
import 'package:plugin_system/src/core/plugin_registry.dart';

/// 插件消息类型
enum MessageType {
  /// 请求消息
  request,

  /// 响应消息
  response,

  /// 通知消息
  notification,

  /// 广播消息
  broadcast,
}

/// 消息优先级
enum MessagePriority {
  /// 关键优先级
  critical,

  /// 高优先级
  high,

  /// 普通优先级
  normal,

  /// 低优先级
  low,
}

/// 消息路由规则
class MessageRoutingRule {
  const MessageRoutingRule({
    required this.sourcePattern,
    required this.targetPattern,
    required this.actionPattern,
    this.priority = MessagePriority.normal,
    this.enabled = true,
  });

  /// 源插件ID模式
  final String sourcePattern;

  /// 目标插件ID模式
  final String targetPattern;

  /// 动作名称模式
  final String actionPattern;

  /// 优先级
  final MessagePriority priority;

  /// 是否启用
  final bool enabled;

  /// 检查消息是否匹配规则
  bool matches(PluginMessage message) {
    if (!enabled) return false;

    return _matchesPattern(message.senderId, sourcePattern) &&
        _matchesPattern(message.targetId ?? '', targetPattern) &&
        _matchesPattern(message.action, actionPattern);
  }

  /// 模式匹配
  bool _matchesPattern(String value, String pattern) {
    if (pattern == '*') return true;
    if (pattern.contains('*')) {
      final regex = RegExp(pattern.replaceAll('*', '.*'));
      return regex.hasMatch(value);
    }
    return value == pattern;
  }
}

/// 消息过滤器
abstract class MessageFilter {
  /// 过滤器名称
  String get name;

  /// 是否启用
  bool get enabled;

  /// 过滤消息
  Future<bool> filter(PluginMessage message);
}

/// 内容过滤器
class ContentMessageFilter extends MessageFilter {
  ContentMessageFilter({
    required this.name,
    required this.blockedWords,
    this.enabled = true,
  });

  @override
  final String name;

  @override
  final bool enabled;

  /// 被阻止的词汇列表
  final List<String> blockedWords;

  @override
  Future<bool> filter(PluginMessage message) async {
    if (!enabled) return true;

    final String content = message.data.toString().toLowerCase();
    for (final String word in blockedWords) {
      if (content.contains(word.toLowerCase())) {
        return false; // 被过滤
      }
    }
    return true; // 通过过滤
  }
}

/// 频率限制过滤器
class RateLimitMessageFilter extends MessageFilter {
  RateLimitMessageFilter({
    required this.name,
    required this.maxMessagesPerSecond,
    this.enabled = true,
  });

  @override
  final String name;

  @override
  final bool enabled;

  /// 每秒最大消息数
  final int maxMessagesPerSecond;

  /// 消息时间戳记录
  final Map<String, List<DateTime>> _messageTimestamps =
      <String, List<DateTime>>{};

  @override
  Future<bool> filter(PluginMessage message) async {
    if (!enabled) return true;

    final String senderId = message.senderId;
    final DateTime now = DateTime.now();

    _messageTimestamps.putIfAbsent(senderId, () => <DateTime>[]);
    final List<DateTime> timestamps = _messageTimestamps[senderId]!;

    // 清理过期的时间戳（超过1秒）
    timestamps.removeWhere(
        (DateTime timestamp) => now.difference(timestamp).inSeconds >= 1);

    // 检查是否超过限制
    if (timestamps.length >= maxMessagesPerSecond) {
      return false; // 被限制
    }

    // 记录当前消息时间戳
    timestamps.add(now);
    return true; // 通过限制
  }
}

/// 消息审计记录
class MessageAuditRecord {
  const MessageAuditRecord({
    required this.messageId,
    required this.senderId,
    required this.targetId,
    required this.action,
    required this.timestamp,
    required this.success,
    this.error,
    this.processingTime,
  });

  /// 消息ID
  final String messageId;

  /// 发送者ID
  final String senderId;

  /// 目标ID
  final String? targetId;

  /// 动作名称
  final String action;

  /// 时间戳
  final DateTime timestamp;

  /// 是否成功
  final bool success;

  /// 错误信息
  final String? error;

  /// 处理时间（毫秒）
  final int? processingTime;
}

/// 消息加密器
abstract class MessageEncryption {
  /// 加密消息数据
  Future<Map<String, dynamic>> encrypt(Map<String, dynamic> data);

  /// 解密消息数据
  Future<Map<String, dynamic>> decrypt(Map<String, dynamic> encryptedData);
}

/// 简单消息加密器
class SimpleMessageEncryption extends MessageEncryption {
  SimpleMessageEncryption(this.key);

  /// 加密密钥
  final String key;

  @override
  Future<Map<String, dynamic>> encrypt(Map<String, dynamic> data) async {
    // 简单的Base64编码模拟加密
    final String jsonString = jsonEncode(data);
    final List<int> bytes = utf8.encode(jsonString);
    final String encoded = base64Encode(bytes);

    return <String, dynamic>{
      'encrypted': true,
      'data': encoded,
      'algorithm': 'simple_base64',
    };
  }

  @override
  Future<Map<String, dynamic>> decrypt(
      Map<String, dynamic> encryptedData) async {
    if (encryptedData['encrypted'] != true) {
      return encryptedData;
    }

    final String encoded = encryptedData['data'] as String;
    final List<int> bytes = base64Decode(encoded);
    final String jsonString = utf8.decode(bytes);

    return jsonDecode(jsonString) as Map<String, dynamic>;
  }
}

/// 消息签名验证器
abstract class MessageSignatureVerifier {
  /// 生成消息签名
  Future<String> generateSignature(PluginMessage message);

  /// 验证消息签名
  Future<bool> verifySignature(PluginMessage message, String signature);
}

/// 简单消息签名验证器
class SimpleMessageSignatureVerifier extends MessageSignatureVerifier {
  SimpleMessageSignatureVerifier(this.secretKey);

  /// 密钥
  final String secretKey;

  @override
  Future<String> generateSignature(PluginMessage message) async {
    final String content =
        '${message.id}:${message.senderId}:${message.action}:$secretKey';
    final List<int> bytes = utf8.encode(content);
    final String hash = base64Encode(bytes);
    return hash;
  }

  @override
  Future<bool> verifySignature(PluginMessage message, String signature) async {
    final String expectedSignature = await generateSignature(message);
    return expectedSignature == signature;
  }
}

/// 插件消息
class PluginMessage {
  const PluginMessage({
    required this.id,
    required this.type,
    required this.action,
    required this.senderId,
    required this.targetId,
    required this.data,
    this.timestamp,
    this.timeout,
  });

  /// 消息ID
  final String id;

  /// 消息类型
  final MessageType type;

  /// 动作名称
  final String action;

  /// 发送者ID
  final String senderId;

  /// 目标ID（广播消息时为空）
  final String? targetId;

  /// 消息数据
  final Map<String, dynamic> data;

  /// 时间戳
  final DateTime? timestamp;

  /// 超时时间（毫秒）
  final int? timeout;

  @override
  String toString() => 'PluginMessage(id: $id, type: $type, action: $action, '
      'senderId: $senderId, targetId: $targetId)';
}

/// 插件消息响应
class PluginMessageResponse {
  const PluginMessageResponse({
    required this.messageId,
    required this.success,
    this.data,
    this.error,
  });

  /// 原始消息ID
  final String messageId;

  /// 是否成功
  final bool success;

  /// 响应数据
  final dynamic data;

  /// 错误信息
  final String? error;

  @override
  String toString() =>
      'PluginMessageResponse(messageId: $messageId, success: $success)';
}

/// 插件消息处理器
typedef PluginMessageHandler = Future<PluginMessageResponse> Function(
  PluginMessage message,
);

/// 插件消息传递器
///
/// 负责插件间的消息传递和通信
class PluginMessenger {
  PluginMessenger._();

  /// 单例实例
  static final PluginMessenger _instance = PluginMessenger._();
  static PluginMessenger get instance => _instance;

  /// 插件注册中心
  final PluginRegistry _registry = PluginRegistry.instance;

  /// 消息处理器
  final Map<String, Map<String, PluginMessageHandler>> _handlers =
      <String, Map<String, PluginMessageHandler>>{};

  /// 待处理的消息
  final Map<String, Completer<PluginMessageResponse>> _pendingMessages =
      <String, Completer<PluginMessageResponse>>{};

  /// 消息ID计数器
  int _messageIdCounter = 0;

  /// 默认超时时间（毫秒）
  static const int _defaultTimeoutMs = 5000;

  /// 消息路由规则
  final Map<String, MessageRoutingRule> _routingRules =
      <String, MessageRoutingRule>{};

  /// 消息过滤器
  final List<MessageFilter> _messageFilters = <MessageFilter>[];

  /// 消息优先级队列
  final Map<MessagePriority, List<PluginMessage>> _priorityQueues =
      <MessagePriority, List<PluginMessage>>{
    MessagePriority.critical: <PluginMessage>[],
    MessagePriority.high: <PluginMessage>[],
    MessagePriority.normal: <PluginMessage>[],
    MessagePriority.low: <PluginMessage>[],
  };

  /// 消息历史记录
  final List<MessageAuditRecord> _messageHistory = <MessageAuditRecord>[];

  /// 最大历史记录数量
  static const int _maxHistorySize = 10000;

  /// 消息加密器
  MessageEncryption? _messageEncryption;

  /// 消息签名验证器
  MessageSignatureVerifier? _signatureVerifier;

  /// 配置消息路由规则
  void configureRouting(String ruleId, MessageRoutingRule rule) {
    _routingRules[ruleId] = rule;
  }

  /// 移除路由规则
  void removeRoutingRule(String ruleId) {
    _routingRules.remove(ruleId);
  }

  /// 添加消息过滤器
  void addMessageFilter(MessageFilter filter) {
    _messageFilters.add(filter);
  }

  /// 移除消息过滤器
  void removeMessageFilter(String filterName) {
    _messageFilters
        .removeWhere((MessageFilter filter) => filter.name == filterName);
  }

  /// 配置消息加密
  void configureEncryption(MessageEncryption encryption) {
    _messageEncryption = encryption;
  }

  /// 配置消息签名验证
  void configureSignatureVerification(MessageSignatureVerifier verifier) {
    _signatureVerifier = verifier;
  }

  /// 发送消息
  ///
  /// [senderId] 发送者插件ID
  /// [targetId] 目标插件ID
  /// [action] 动作名称
  /// [data] 消息数据
  /// [timeoutMs] 超时时间（毫秒）
  Future<PluginMessageResponse> sendMessage(
    String senderId,
    String targetId,
    String action,
    Map<String, dynamic> data, {
    int timeoutMs = _defaultTimeoutMs,
  }) async {
    // 验证发送者和目标插件
    if (!_registry.contains(senderId)) {
      throw PluginNotFoundException(senderId);
    }

    if (!_registry.contains(targetId)) {
      throw PluginNotFoundException(targetId);
    }

    // 创建消息
    final String messageId = _generateMessageId();
    final PluginMessage message = PluginMessage(
      id: messageId,
      type: MessageType.request,
      action: action,
      senderId: senderId,
      targetId: targetId,
      data: data,
      timestamp: DateTime.now(),
      timeout: timeoutMs,
    );

    // 发送消息并等待响应
    return _sendMessageWithResponse(message, timeoutMs);
  }

  /// 发送通知消息（不等待响应）
  ///
  /// [senderId] 发送者插件ID
  /// [targetId] 目标插件ID
  /// [action] 动作名称
  /// [data] 消息数据
  Future<void> sendNotification(
    String senderId,
    String targetId,
    String action,
    Map<String, dynamic> data,
  ) async {
    // 验证发送者和目标插件
    if (!_registry.contains(senderId)) {
      throw PluginNotFoundException(senderId);
    }

    if (!_registry.contains(targetId)) {
      throw PluginNotFoundException(targetId);
    }

    // 创建通知消息
    final String messageId = _generateMessageId();
    final PluginMessage message = PluginMessage(
      id: messageId,
      type: MessageType.notification,
      action: action,
      senderId: senderId,
      targetId: targetId,
      data: data,
      timestamp: DateTime.now(),
    );

    // 发送通知
    await _deliverMessage(message);
  }

  /// 广播消息
  ///
  /// [senderId] 发送者插件ID
  /// [action] 动作名称
  /// [data] 消息数据
  /// [excludeIds] 排除的插件ID列表
  Future<void> broadcastMessage(
    String senderId,
    String action,
    Map<String, dynamic> data, {
    List<String> excludeIds = const <String>[],
  }) async {
    // 验证发送者插件
    if (!_registry.contains(senderId)) {
      throw PluginNotFoundException(senderId);
    }

    // 创建广播消息
    final String messageId = _generateMessageId();
    final PluginMessage message = PluginMessage(
      id: messageId,
      type: MessageType.broadcast,
      action: action,
      senderId: senderId,
      targetId: null,
      data: data,
      timestamp: DateTime.now(),
    );

    // 获取所有活跃插件
    final List<Plugin> activePlugins = _registry.getAllActive();

    // 广播给所有插件（除了发送者和排除列表）
    for (final Plugin plugin in activePlugins) {
      if (plugin.id != senderId && !excludeIds.contains(plugin.id)) {
        try {
          await _deliverMessage(message.copyWith(targetId: plugin.id));
        } catch (e) {
          // 广播时忽略单个插件的错误
        }
      }
    }
  }

  /// 注册消息处理器
  ///
  /// [pluginId] 插件ID
  /// [action] 动作名称
  /// [handler] 消息处理器
  void registerHandler(
    String pluginId,
    String action,
    PluginMessageHandler handler,
  ) {
    _handlers.putIfAbsent(pluginId, () => <String, PluginMessageHandler>{});
    _handlers[pluginId]![action] = handler;
  }

  /// 注销消息处理器
  ///
  /// [pluginId] 插件ID
  /// [action] 动作名称，如果为null则注销所有处理器
  void unregisterHandler(String pluginId, [String? action]) {
    if (action == null) {
      _handlers.remove(pluginId);
    } else {
      _handlers[pluginId]?.remove(action);
    }
  }

  /// 发送带响应的消息
  Future<PluginMessageResponse> _sendMessageWithResponse(
    PluginMessage message,
    int timeoutMs,
  ) async {
    final Completer<PluginMessageResponse> completer =
        Completer<PluginMessageResponse>();

    _pendingMessages[message.id] = completer;

    try {
      // 发送消息
      await _deliverMessage(message);

      // 等待响应或超时
      return await Future.any(<Future<PluginMessageResponse>>[
        completer.future,
        Future<PluginMessageResponse>.delayed(
          Duration(milliseconds: timeoutMs),
          () => PluginMessageResponse(
            messageId: message.id,
            success: false,
            error: 'Message timeout',
          ),
        ),
      ]);
    } finally {
      _pendingMessages.remove(message.id);
    }
  }

  /// 投递消息
  Future<void> _deliverMessage(PluginMessage message) async {
    final DateTime startTime = DateTime.now();

    try {
      // 1. 应用消息过滤器
      final bool passedFilters = await _applyMessageFilters(message);
      if (!passedFilters) {
        _recordMessageAudit(message, false, 'Message filtered', startTime);
        throw PluginCommunicationException(
          message.senderId,
          message.targetId ?? 'null',
          'Message was filtered',
        );
      }

      // 2. 应用路由规则
      final PluginMessage routedMessage = await _applyRoutingRules(message);

      // 3. 加密消息数据（如果配置了加密）
      final PluginMessage encryptedMessage =
          await _encryptMessage(routedMessage);

      // 4. 验证消息签名（如果配置了签名验证）
      await _verifyMessageSignature(encryptedMessage);

      final String? targetId = encryptedMessage.targetId;
      if (targetId == null) {
        throw PluginCommunicationException(
          encryptedMessage.senderId,
          'null',
          'Target ID is null',
        );
      }

      // 获取目标插件
      final Plugin? targetPlugin = _registry.get(targetId);
      if (targetPlugin == null) {
        throw PluginNotFoundException(targetId);
      }

      // 检查插件状态
      final PluginState? state = _registry.getState(targetId);
      if (state != PluginState.started) {
        throw PluginCommunicationException(
          encryptedMessage.senderId,
          targetId,
          'Target plugin is not active',
        );
      }

      // 解密消息数据（如果需要）
      final Map<String, dynamic> decryptedData =
          await _decryptMessageData(encryptedMessage.data);

      // 使用插件的消息处理方法
      final dynamic result = await targetPlugin.handleMessage(
        encryptedMessage.action,
        decryptedData,
      );

      // 如果是请求消息，发送响应
      if (encryptedMessage.type == MessageType.request) {
        final PluginMessageResponse response = PluginMessageResponse(
          messageId: encryptedMessage.id,
          success: true,
          data: result,
        );
        _completeMessage(encryptedMessage.id, response);
      }

      // 记录成功的审计日志
      _recordMessageAudit(message, true, null, startTime);
    } catch (e) {
      // 记录失败的审计日志
      _recordMessageAudit(message, false, e.toString(), startTime);

      // 如果是请求消息，发送错误响应
      if (message.type == MessageType.request) {
        final PluginMessageResponse response = PluginMessageResponse(
          messageId: message.id,
          success: false,
          error: e.toString(),
        );
        _completeMessage(message.id, response);
      }
    }
  }

  /// 完成消息处理
  void _completeMessage(String messageId, PluginMessageResponse response) {
    final Completer<PluginMessageResponse>? completer =
        _pendingMessages[messageId];
    if (completer != null && !completer.isCompleted) {
      completer.complete(response);
    }
  }

  /// 生成消息ID
  String _generateMessageId() =>
      'msg_${++_messageIdCounter}_${DateTime.now().millisecondsSinceEpoch}';

  /// 获取通信状态
  Map<String, dynamic> getStatus() => <String, dynamic>{
        'registeredHandlers': _handlers.length,
        'pendingMessages': _pendingMessages.length,
        'messageCounter': _messageIdCounter,
      };

  /// 清理插件的所有消息处理器
  void cleanupPlugin(String pluginId) {
    unregisterHandler(pluginId);

    // 取消该插件的所有待处理消息
    final List<String> toRemove = <String>[];
    for (final MapEntry<String, Completer<PluginMessageResponse>> entry
        in _pendingMessages.entries) {
      // 这里需要更复杂的逻辑来识别插件相关的消息
      // 简化处理：如果消息ID包含插件ID则取消
      if (entry.key.contains(pluginId)) {
        entry.value.complete(
          PluginMessageResponse(
            messageId: entry.key,
            success: false,
            error: 'Plugin unloaded',
          ),
        );
        toRemove.add(entry.key);
      }
    }

    for (final String messageId in toRemove) {
      _pendingMessages.remove(messageId);
    }
  }

  /// 应用消息过滤器
  Future<bool> _applyMessageFilters(PluginMessage message) async {
    for (final MessageFilter filter in _messageFilters) {
      if (!await filter.filter(message)) {
        return false; // 被过滤
      }
    }
    return true; // 通过所有过滤器
  }

  /// 应用路由规则
  Future<PluginMessage> _applyRoutingRules(PluginMessage message) async {
    // 查找匹配的路由规则
    MessageRoutingRule? matchedRule;
    for (final MessageRoutingRule rule in _routingRules.values) {
      if (rule.matches(message)) {
        matchedRule = rule;
        break;
      }
    }

    // 如果有匹配的规则，应用优先级
    if (matchedRule != null) {
      // 将消息添加到优先级队列
      _priorityQueues[matchedRule.priority]?.add(message);

      // 这里可以实现更复杂的路由逻辑
      // 目前简单返回原消息
    }

    return message;
  }

  /// 加密消息
  Future<PluginMessage> _encryptMessage(PluginMessage message) async {
    if (_messageEncryption == null) {
      return message; // 没有配置加密，直接返回
    }

    try {
      final Map<String, dynamic> encryptedData =
          await _messageEncryption!.encrypt(message.data);

      return PluginMessage(
        id: message.id,
        type: message.type,
        action: message.action,
        senderId: message.senderId,
        targetId: message.targetId,
        data: encryptedData,
        timestamp: message.timestamp,
        timeout: message.timeout,
      );
    } catch (e) {
      // 加密失败，返回原消息
      return message;
    }
  }

  /// 验证消息签名
  Future<void> _verifyMessageSignature(PluginMessage message) async {
    if (_signatureVerifier == null) {
      return; // 没有配置签名验证，直接返回
    }

    // 检查消息是否包含签名
    final String? signature = message.data['_signature'] as String?;
    if (signature == null) {
      throw PluginCommunicationException(
        message.senderId,
        message.targetId ?? 'null',
        'Message signature is missing',
      );
    }

    // 验证签名
    final bool isValid =
        await _signatureVerifier!.verifySignature(message, signature);
    if (!isValid) {
      throw PluginCommunicationException(
        message.senderId,
        message.targetId ?? 'null',
        'Message signature verification failed',
      );
    }
  }

  /// 解密消息数据
  Future<Map<String, dynamic>> _decryptMessageData(
      Map<String, dynamic> data) async {
    if (_messageEncryption == null) {
      return data; // 没有配置加密，直接返回
    }

    try {
      return await _messageEncryption!.decrypt(data);
    } catch (e) {
      // 解密失败，返回原数据
      return data;
    }
  }

  /// 记录消息审计日志
  void _recordMessageAudit(
    PluginMessage message,
    bool success,
    String? error,
    DateTime startTime,
  ) {
    final DateTime endTime = DateTime.now();
    final int processingTime = endTime.difference(startTime).inMilliseconds;

    final MessageAuditRecord record = MessageAuditRecord(
      messageId: message.id,
      senderId: message.senderId,
      targetId: message.targetId,
      action: message.action,
      timestamp: message.timestamp ?? startTime,
      success: success,
      error: error,
      processingTime: processingTime,
    );

    _messageHistory.add(record);

    // 限制历史记录大小
    if (_messageHistory.length > _maxHistorySize) {
      _messageHistory.removeAt(0);
    }
  }

  /// 获取消息历史记录
  List<MessageAuditRecord> getMessageHistory({
    String? pluginId,
    DateTime? since,
    int? limit,
  }) {
    List<MessageAuditRecord> filtered = _messageHistory;

    // 按插件ID过滤
    if (pluginId != null) {
      filtered = filtered
          .where((MessageAuditRecord record) =>
              record.senderId == pluginId || record.targetId == pluginId)
          .toList();
    }

    // 按时间过滤
    if (since != null) {
      filtered = filtered
          .where((MessageAuditRecord record) => record.timestamp.isAfter(since))
          .toList();
    }

    // 限制数量
    if (limit != null && filtered.length > limit) {
      filtered = filtered.sublist(filtered.length - limit);
    }

    return filtered;
  }

  /// 清理消息历史记录
  void clearMessageHistory() {
    _messageHistory.clear();
  }
}

/// PluginMessage的扩展方法
extension PluginMessageExtension on PluginMessage {
  /// 复制消息并修改目标ID
  PluginMessage copyWith({String? targetId}) => PluginMessage(
        id: id,
        type: type,
        action: action,
        senderId: senderId,
        targetId: targetId ?? this.targetId,
        data: data,
        timestamp: timestamp,
        timeout: timeout,
      );
}
