{"version": 2, "entries": [{"package": "app_config", "rootUri": "../../../packages/app_config/", "packageUri": "lib/"}, {"package": "app_routing", "rootUri": "../../../packages/app_routing/", "packageUri": "lib/"}, {"package": "core_services", "rootUri": "../../../packages/core_services/", "packageUri": "lib/"}, {"package": "desktop_environment", "rootUri": "../../../packages/desktop_environment/", "packageUri": "lib/"}, {"package": "notes_hub", "rootUri": "../../../packages/notes_hub/", "packageUri": "lib/"}, {"package": "punch_in", "rootUri": "../../../packages/punch_in/", "packageUri": "lib/"}, {"package": "ui_framework", "rootUri": "../../../packages/ui_framework/", "packageUri": "lib/"}, {"package": "workshop", "rootUri": "../../../packages/workshop/", "packageUri": "lib/"}, {"package": "pet_app", "rootUri": "../", "packageUri": "lib/"}]}