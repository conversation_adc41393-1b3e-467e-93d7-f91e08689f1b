/*
---------------------------------------------------------------
File name:          module_info.dart
Author:             lgnorant-lu
Date created:       2025-07-24
Last modified:      2025-07-24
Dart Version:       3.2+
Description:        模块信息数据模型 - Phase 5.0.10
---------------------------------------------------------------
*/

import 'package:flutter/material.dart';

/// 模块状态枚举
enum ModuleStatus {
  /// 活跃状态
  active,
  /// 正常状态
  normal,
  /// 警告状态
  warning,
  /// 错误状态
  error,
  /// 禁用状态
  disabled;

  /// 获取状态标签
  String get label {
    switch (this) {
      case ModuleStatus.active:
        return '活跃';
      case ModuleStatus.normal:
        return '正常';
      case ModuleStatus.warning:
        return '警告';
      case ModuleStatus.error:
        return '错误';
      case ModuleStatus.disabled:
        return '禁用';
    }
  }

  /// 获取状态颜色
  Color get color {
    switch (this) {
      case ModuleStatus.active:
        return Colors.green;
      case ModuleStatus.normal:
        return Colors.blue;
      case ModuleStatus.warning:
        return Colors.orange;
      case ModuleStatus.error:
        return Colors.red;
      case ModuleStatus.disabled:
        return Colors.grey;
    }
  }
}

/// 模块信息数据模型
class ModuleInfo {
  /// 模块ID
  final String id;
  
  /// 模块标题
  final String title;
  
  /// 模块图标
  final IconData icon;
  
  /// 模块状态
  final ModuleStatus status;
  
  /// 模块副标题
  final String subtitle;
  
  /// 模块元数据
  final Map<String, dynamic> metadata;

  const ModuleInfo({
    required this.id,
    required this.title,
    required this.icon,
    required this.status,
    required this.subtitle,
    this.metadata = const {},
  });

  /// 复制并修改
  ModuleInfo copyWith({
    String? id,
    String? title,
    IconData? icon,
    ModuleStatus? status,
    String? subtitle,
    Map<String, dynamic>? metadata,
  }) {
    return ModuleInfo(
      id: id ?? this.id,
      title: title ?? this.title,
      icon: icon ?? this.icon,
      status: status ?? this.status,
      subtitle: subtitle ?? this.subtitle,
      metadata: metadata ?? this.metadata,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'icon': icon.codePoint,
      'status': status.name,
      'subtitle': subtitle,
      'metadata': metadata,
    };
  }

  /// 从JSON创建
  factory ModuleInfo.fromJson(Map<String, dynamic> json) {
    return ModuleInfo(
      id: json['id'] as String,
      title: json['title'] as String,
      icon: IconData(json['icon'] as int, fontFamily: 'MaterialIcons'),
      status: ModuleStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ModuleStatus.normal,
      ),
      subtitle: json['subtitle'] as String,
      metadata: json['metadata'] as Map<String, dynamic>? ?? {},
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ModuleInfo &&
        other.id == id &&
        other.title == title &&
        other.icon == icon &&
        other.status == status &&
        other.subtitle == subtitle;
  }

  @override
  int get hashCode {
    return Object.hash(id, title, icon, status, subtitle);
  }

  @override
  String toString() {
    return 'ModuleInfo(id: $id, title: $title, status: $status, subtitle: $subtitle)';
  }
}
