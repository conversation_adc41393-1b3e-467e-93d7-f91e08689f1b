/*
---------------------------------------------------------------
File name:          plugin_development_manager_test.dart
Author:             lgnorant-lu
Date created:       2025/07/31
Last modified:      2025/07/31
Dart Version:       3.2+
Description:        插件开发管理器测试
---------------------------------------------------------------
Change History:
    2025/07/31: Initial creation - 插件开发管理器测试;
---------------------------------------------------------------
*/

import 'dart:io';
import 'package:test/test.dart';
import 'package:path/path.dart' as path;
import 'package:creative_workshop/src/core/plugin_development/plugin_development_manager.dart';

void main() {
  group('插件开发管理器测试', () {
    late PluginDevelopmentManager manager;
    late Directory tempDir;

    setUpAll(() async {
      manager = PluginDevelopmentManager.instance;
      
      // 创建临时测试目录
      tempDir = await Directory.systemTemp.createTemp('plugin_dev_test_');
    });

    tearDownAll(() async {
      // 清理临时目录
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
      }
      
      // 清理管理器
      if (manager.isInitialized) {
        await manager.dispose();
      }
    });

    group('初始化测试', () {
      test('应该能够初始化管理器', () async {
        await manager.initialize();
        
        expect(manager.isInitialized, isTrue);
        expect(manager.activeProjects, isEmpty);
        expect(manager.events, isA<Stream<PluginDevelopmentEvent>>());
      });

      test('重复初始化应该是安全的', () async {
        await manager.initialize();
        await manager.initialize(); // 第二次初始化
        
        expect(manager.isInitialized, isTrue);
      });
    });

    group('项目管理测试', () {
      setUp(() async {
        if (!manager.isInitialized) {
          await manager.initialize();
        }
      });

      test('应该能够创建插件项目', () async {
        // 注意：这个测试可能会失败，因为需要真实的Ming CLI
        // 但我们测试的是API的正确性
        
        try {
          final project = await manager.createPluginProject(
            name: 'test_plugin',
            description: 'A test plugin',
            template: 'basic',
          );
          
          expect(project, isA<PluginDevelopmentProject>());
          expect(project.name, equals('test_plugin'));
          expect(project.description, equals('A test plugin'));
          expect(project.template, equals('basic'));
          expect(manager.activeProjects.containsKey(project.id), isTrue);
        } catch (e) {
          // 如果Ming CLI不可用，这是预期的
          expect(e, isA<Exception>());
        }
      });

      test('应该能够处理项目创建失败', () async {
        // 使用无效的项目名称来触发失败
        try {
          await manager.createPluginProject(
            name: '', // 空名称应该失败
            description: 'Invalid project',
          );
          fail('应该抛出异常');
        } catch (e) {
          expect(e, isA<Exception>());
        }
      });

      test('应该能够打开现有项目', () async {
        // 创建一个模拟的插件项目目录
        final mockProjectDir = Directory(path.join(tempDir.path, 'mock_plugin'));
        await mockProjectDir.create(recursive: true);
        
        // 创建plugin.yaml文件
        final pluginYaml = File(path.join(mockProjectDir.path, 'plugin.yaml'));
        await pluginYaml.writeAsString('''
plugin:
  id: mock_plugin
  name: Mock Plugin
  version: 1.0.0
''');
        
        final project = await manager.openPluginProject(mockProjectDir.path);
        
        expect(project, isNotNull);
        expect(project!.name, isNotEmpty);
        expect(project.path, equals(mockProjectDir.path));
        expect(manager.activeProjects.containsKey(project.id), isTrue);
      });

      test('应该能够关闭项目', () async {
        // 创建一个模拟项目
        final mockProjectDir = Directory(path.join(tempDir.path, 'close_test'));
        await mockProjectDir.create(recursive: true);
        
        final pluginYaml = File(path.join(mockProjectDir.path, 'plugin.yaml'));
        await pluginYaml.writeAsString('plugin:\n  id: close_test');
        
        final project = await manager.openPluginProject(mockProjectDir.path);
        expect(project, isNotNull);
        expect(manager.activeProjects.containsKey(project!.id), isTrue);
        
        await manager.closePluginProject(project.id);
        expect(manager.activeProjects.containsKey(project.id), isFalse);
      });
    });

    group('项目操作测试', () {
      setUp(() async {
        if (!manager.isInitialized) {
          await manager.initialize();
        }
      });

      test('应该能够构建项目', () async {
        // 创建一个模拟项目
        final mockProjectDir = Directory(path.join(tempDir.path, 'build_test'));
        await mockProjectDir.create(recursive: true);
        
        final pluginYaml = File(path.join(mockProjectDir.path, 'plugin.yaml'));
        await pluginYaml.writeAsString('plugin:\n  id: build_test');
        
        final project = await manager.openPluginProject(mockProjectDir.path);
        expect(project, isNotNull);
        
        // 尝试构建（可能会失败，但不应该抛出异常）
        final buildResult = await manager.buildPluginProject(project!.id);
        expect(buildResult, isA<bool>());
      });

      test('应该能够测试项目', () async {
        // 创建一个模拟项目
        final mockProjectDir = Directory(path.join(tempDir.path, 'test_test'));
        await mockProjectDir.create(recursive: true);
        
        final pluginYaml = File(path.join(mockProjectDir.path, 'plugin.yaml'));
        await pluginYaml.writeAsString('plugin:\n  id: test_test');
        
        final project = await manager.openPluginProject(mockProjectDir.path);
        expect(project, isNotNull);
        
        // 尝试测试（可能会失败，但不应该抛出异常）
        final testResult = await manager.testPluginProject(project!.id);
        expect(testResult, isA<bool>());
      });

      test('应该能够处理不存在的项目', () async {
        try {
          await manager.buildPluginProject('non_existent_project');
          fail('应该抛出异常');
        } catch (e) {
          expect(e, isA<Exception>());
        }
      });
    });

    group('事件系统测试', () {
      setUp(() async {
        if (!manager.isInitialized) {
          await manager.initialize();
        }
      });

      test('应该能够监听事件', () async {
        final events = <PluginDevelopmentEvent>[];
        final subscription = manager.events.listen(events.add);
        
        // 创建一个模拟项目来触发事件
        final mockProjectDir = Directory(path.join(tempDir.path, 'event_test'));
        await mockProjectDir.create(recursive: true);
        
        final pluginYaml = File(path.join(mockProjectDir.path, 'plugin.yaml'));
        await pluginYaml.writeAsString('plugin:\n  id: event_test');
        
        final project = await manager.openPluginProject(mockProjectDir.path);
        expect(project, isNotNull);
        
        // 等待事件处理
        await Future.delayed(Duration(milliseconds: 100));
        
        // 应该有项目打开事件
        expect(events, isNotEmpty);
        expect(events.any((e) => e is _ProjectOpenedEvent), isTrue);
        
        await subscription.cancel();
      });
    });

    group('状态管理测试', () {
      setUp(() async {
        if (!manager.isInitialized) {
          await manager.initialize();
        }
      });

      test('应该能够跟踪活跃项目', () async {
        expect(manager.activeProjects, isEmpty);
        
        // 打开一个项目
        final mockProjectDir = Directory(path.join(tempDir.path, 'active_test'));
        await mockProjectDir.create(recursive: true);
        
        final pluginYaml = File(path.join(mockProjectDir.path, 'plugin.yaml'));
        await pluginYaml.writeAsString('plugin:\n  id: active_test');
        
        final project = await manager.openPluginProject(mockProjectDir.path);
        expect(project, isNotNull);
        expect(manager.activeProjects, hasLength(1));
        expect(manager.activeProjects.containsKey(project!.id), isTrue);
        
        // 关闭项目
        await manager.closePluginProject(project.id);
        expect(manager.activeProjects, isEmpty);
      });
    });

    group('清理测试', () {
      test('应该能够正确销毁管理器', () async {
        await manager.initialize();
        expect(manager.isInitialized, isTrue);
        
        await manager.dispose();
        expect(manager.isInitialized, isFalse);
        expect(manager.activeProjects, isEmpty);
      });
    });
  });
}
