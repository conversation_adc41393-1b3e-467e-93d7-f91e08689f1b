name: ui_framework
description: 现代化的Flutter UI框架包，提供主题系统、热切换主题、响应式设计、国际化支持和高级UI组件
version: 1.0.0
publish_to: none

environment:
  sdk: ^3.8.1
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  # 添加go_router依赖用于导航
  go_router: ^14.6.2
  # 添加对本地包的依赖
  core_services:
    path: ../core_services
  app_routing:
    path: ../app_routing
  # 添加业务模块包依赖
  notes_hub:
    path: ../notes_hub
  workshop:
    path: ../workshop
  punch_in:
    path: ../punch_in
  # 桌面环境依赖 (Phase 2.0 Sprint 2.0b)
  desktop_environment:
    path: ../desktop_environment

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

flutter: 