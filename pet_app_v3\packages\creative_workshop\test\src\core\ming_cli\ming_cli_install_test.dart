/*
--------------------------------------------------------------- 
File name:          ming_cli_install_test.dart
Author:             lgnorant-lu
Date created:       2025-07-31
Last modified:      2025-07-31
Dart Version:       3.2+
Description:        Ming CLI安装功能测试
---------------------------------------------------------------
Change History:
    2025-07-31: Initial creation - Ming CLI安装功能测试;
---------------------------------------------------------------
*/

import 'dart:io';
import 'package:test/test.dart';
import 'package:path/path.dart' as path;
import 'package:creative_workshop/src/core/ming_cli/local_ming_cli_service.dart';
import 'package:creative_workshop/src/core/ming_cli/ming_cli_result.dart';

void main() {
  group('Ming CLI安装功能测试', () {
    late LocalMingCliService service;
    late Directory tempDir;

    setUpAll(() async {
      service = LocalMingCliService.instance;
      
      // 创建临时测试目录
      tempDir = await Directory.systemTemp.createTemp('ming_cli_install_test_');
    });

    tearDownAll(() async {
      // 清理临时目录
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
      }
    });

    group('安装方法测试', () {
      test('应该能够调用installOrBuildMingCli方法', () async {
        // 这个测试验证方法存在且可以调用
        // 注意：实际安装可能失败，但方法应该能够处理
        final result = await service.installOrBuildMingCli();
        
        expect(result, isA<bool>());
        // 无论成功失败，都应该有一个有效的状态
        expect(service.currentMode, isA<MingCliMode>());
        expect(service.status, isNotEmpty);
      });

      test('应该能够处理安装失败的情况', () async {
        // 在没有网络或Git的环境中，安装应该优雅地失败
        final result = await service.installOrBuildMingCli();
        
        // 即使安装失败，也应该有合理的状态
        expect(service.currentMode, isA<MingCliMode>());
        expect(service.status, isNotEmpty);
        
        // 如果安装失败，应该切换到降级模式
        if (!result) {
          expect(service.currentMode, equals(MingCliMode.fallback));
        }
      });
    });

    group('检测功能测试', () {
      test('应该能够检测现有的Ming CLI', () async {
        await service.detectMingCli();
        
        expect(service.currentMode, isA<MingCliMode>());
        expect(service.status, isNotEmpty);
        expect(service.statusDescription, isNotEmpty);
      });

      test('应该能够刷新状态', () async {
        final oldMode = service.currentMode;
        await service.refresh();
        
        // 刷新后应该有有效的状态
        expect(service.currentMode, isA<MingCliMode>());
        expect(service.status, isNotEmpty);
      });
    });

    group('命令执行测试', () {
      test('安装后应该能够执行基本命令', () async {
        // 尝试安装
        await service.installOrBuildMingCli();
        
        // 无论安装是否成功，都应该能够执行基本命令
        final versionResult = await service.executeCommand(['--version']);
        expect(versionResult, isA<MingCliResult>());
        expect(versionResult.output, isNotEmpty);
        
        final doctorResult = await service.executeCommand(['doctor']);
        expect(doctorResult, isA<MingCliResult>());
        expect(doctorResult.output, isNotEmpty);
      });
    });

    group('状态管理测试', () {
      test('应该能够获取详细状态信息', () async {
        await service.installOrBuildMingCli();
        
        final detailedStatus = service.getDetailedStatus();
        expect(detailedStatus, isA<Map<String, dynamic>>());
        expect(detailedStatus['currentMode'], isA<String>());
        expect(detailedStatus['isInstalled'], isA<bool>());
        expect(detailedStatus['version'], isA<String>());
        expect(detailedStatus['timestamp'], isA<String>());
      });

      test('应该能够处理多次安装尝试', () async {
        // 第一次安装
        final result1 = await service.installOrBuildMingCli();
        final mode1 = service.currentMode;
        
        // 第二次安装
        final result2 = await service.installOrBuildMingCli();
        final mode2 = service.currentMode;
        
        // 两次结果应该一致
        expect(result1, equals(result2));
        expect(mode1, equals(mode2));
      });
    });

    group('错误处理测试', () {
      test('应该能够处理网络错误', () async {
        // 这个测试验证在没有网络连接时的行为
        // 实际的网络错误处理在installOrBuildMingCli中
        
        final result = await service.installOrBuildMingCli();
        
        // 即使有网络错误，也应该有合理的状态
        expect(service.currentMode, isA<MingCliMode>());
        expect(service.status, isNotEmpty);
      });

      test('应该能够处理Git不可用的情况', () async {
        // 验证在Git不可用时的降级行为
        final result = await service.installOrBuildMingCli();
        
        // 应该能够处理Git不可用的情况
        expect(service.currentMode, isA<MingCliMode>());
        expect(service.status, isNotEmpty);
      });

      test('应该能够处理权限错误', () async {
        // 验证在没有写权限时的行为
        final result = await service.installOrBuildMingCli();
        
        // 应该能够处理权限错误
        expect(service.currentMode, isA<MingCliMode>());
        expect(service.status, isNotEmpty);
      });
    });

    group('模式优先级测试', () {
      test('应该按正确的优先级尝试安装方式', () async {
        // 验证安装方式的优先级：
        // 1. pub global activate
        // 2. GitHub克隆并构建
        // 3. 本地源码构建
        // 4. Dart Run模式
        // 5. 降级模式
        
        await service.installOrBuildMingCli();
        
        final mode = service.currentMode;
        expect([
          MingCliMode.systemInstalled,
          MingCliMode.localBuilt,
          MingCliMode.dartRun,
          MingCliMode.fallback,
        ].contains(mode), isTrue);
      });
    });

    group('集成测试', () {
      test('完整的安装和使用流程', () async {
        // 1. 初始化服务
        await service.initialize();
        expect(service.currentMode, isA<MingCliMode>());
        
        // 2. 尝试安装
        final installResult = await service.installOrBuildMingCli();
        expect(installResult, isA<bool>());
        
        // 3. 验证状态
        expect(service.currentMode, isA<MingCliMode>());
        expect(service.status, isNotEmpty);
        
        // 4. 执行命令
        final commandResult = await service.executeCommand(['--version']);
        expect(commandResult, isA<MingCliResult>());
        expect(commandResult.output, isNotEmpty);
        
        // 5. 获取详细状态
        final detailedStatus = service.getDetailedStatus();
        expect(detailedStatus, isA<Map<String, dynamic>>());
      });
    });
  });
}
