/*
---------------------------------------------------------------
File name:          plugin_core.dart
Author:             {{author}}
Date created:       2025-07-25
Last modified:      2025-07-25
Dart Version:       {{dart_version}}
Description:        {{plugin_display_name}} - {{description}}
---------------------------------------------------------------
Change History:
    2025-07-25: Initial creation - {{description}};
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:developer' as developer;

// 导入Flutter框架
import 'package:flutter/widgets.dart';

// 导入Plugin System的真实基类
import 'package:plugin_system/plugin_system.dart';

// 所有插件相关的枚举和类现在都从Plugin System导入
// 不再需要本地定义

/// {{plugin_display_name}} 插件实现
///
/// 使用Plugin System的真实基类实现
class {{plugin_name.pascalCase()}}Plugin extends Plugin {
  // 状态管理
  PluginState _currentState = PluginState.unloaded;
  final StreamController<PluginState> _stateController =
      StreamController<PluginState>.broadcast();

  // 插件加载时间
  DateTime? _loadStartTime;
  Duration? _loadTime;

  @override
  String get id => '{{plugin_name}}';

  @override
  String get name => '{{plugin_display_name}}';

  @override
  String get version => '{{version}}';

  @override
  String get description => '{{description}}';

  @override
  String get author => '{{author}}';

  @override
  PluginType get category => PluginType.{{plugin_type}};

  @override
  List<PluginPermission> get requiredPermissions => [];

  @override
  List<PluginDependency> get dependencies => [
    const PluginDependency(
      pluginId: 'pet_app_core',
      versionConstraint: '^3.0.0',
    ),
    const PluginDependency(
      pluginId: 'plugin_system',
      versionConstraint: '^1.0.0',
    ),
  ];

  @override
  List<SupportedPlatform> get supportedPlatforms => [
    SupportedPlatform.android,
    SupportedPlatform.ios,
    SupportedPlatform.web,
    SupportedPlatform.windows,
    SupportedPlatform.macos,
    SupportedPlatform.linux,
  ];

  @override
  PluginState get currentState => _currentState;

  @override
  Stream<PluginState> get stateChanges => _stateController.stream;

  @override
  PluginManifest get manifest => PluginManifest(
    id: id,
    name: name,
    version: version,
    description: description,
    author: author,
    category: category,
    main: 'lib/src/plugin_core.dart',
    requiredPermissions: requiredPermissions,
    dependencies: dependencies,
    supportedPlatforms: supportedPlatforms,
  );

  @override
  bool get isEnabled => _currentState != PluginState.disabled;

  @override
  Duration? get loadTime => _loadTime;

  @override
  Future<void> initialize() async {
    _loadStartTime = DateTime.now();
    _updateState(PluginState.loaded);

    try {
      developer.log('初始化插件: $name', name: 'PluginCore');

      // 在这里添加初始化逻辑
      await Future.delayed(const Duration(milliseconds: 100));

      _loadTime = DateTime.now().difference(_loadStartTime!);
      _updateState(PluginState.initialized);
      developer.log('插件初始化完成', name: 'PluginCore');
    } catch (e) {
      _updateState(PluginState.error);
      developer.log('插件初始化失败: $e', name: 'PluginCore', level: 1000);
      rethrow;
    }
  }

  @override
  Future<void> start() async {
    if (_currentState != PluginState.initialized &&
        _currentState != PluginState.stopped) {
      throw StateError('插件必须先初始化才能启动');
    }

    try {
      developer.log('启动插件: $name', name: 'PluginCore');

      // 在这里添加启动逻辑
      await Future<void>.delayed(const Duration(milliseconds: 100));

      _updateState(PluginState.started);
      developer.log('插件启动完成', name: 'PluginCore');
    } catch (e) {
      _updateState(PluginState.error);
      developer.log('插件启动失败: $e', name: 'PluginCore', level: 1000);
      rethrow;
    }
  }

  @override
  Future<void> pause() async {
    if (_currentState != PluginState.started) {
      throw StateError('只有运行中的插件才能暂停');
    }

    try {
      developer.log('暂停插件: $name', name: 'PluginCore');

      // 在这里添加暂停逻辑
      await Future<void>.delayed(const Duration(milliseconds: 50));

      _updateState(PluginState.paused);
      developer.log('插件暂停完成', name: 'PluginCore');
    } catch (e) {
      _updateState(PluginState.error);
      developer.log('插件暂停失败: $e', name: 'PluginCore', level: 1000);
      rethrow;
    }
  }

  @override
  Future<void> resume() async {
    if (_currentState != PluginState.paused) {
      throw StateError('只有暂停的插件才能恢复');
    }

    try {
      developer.log('恢复插件: $name', name: 'PluginCore');

      // 在这里添加恢复逻辑
      await Future<void>.delayed(const Duration(milliseconds: 50));

      _updateState(PluginState.started);
      developer.log('插件恢复完成', name: 'PluginCore');
    } catch (e) {
      _updateState(PluginState.error);
      developer.log('插件恢复失败: $e', name: 'PluginCore', level: 1000);
      rethrow;
    }
  }

  @override
  Future<void> stop() async {
    if (_currentState != PluginState.started &&
        _currentState != PluginState.paused) {
      throw StateError('只有运行中或暂停的插件才能停止');
    }

    try {
      developer.log('停止插件: $name', name: 'PluginCore');

      // 在这里添加停止逻辑
      await Future<void>.delayed(const Duration(milliseconds: 100));

      _updateState(PluginState.stopped);
      developer.log('插件停止完成', name: 'PluginCore');
    } catch (e) {
      _updateState(PluginState.error);
      developer.log('插件停止失败: $e', name: 'PluginCore', level: 1000);
      rethrow;
    }
  }

  @override
  Future<void> dispose() async {
    try {
      developer.log('销毁插件: $name', name: 'PluginCore');

      // 在这里添加清理逻辑
      await Future<void>.delayed(const Duration(milliseconds: 50));

      _updateState(PluginState.unloaded);
      await _stateController.close();

      developer.log('插件销毁完成', name: 'PluginCore');
    } catch (e) {
      _updateState(PluginState.error);
      developer.log('插件销毁失败: $e', name: 'PluginCore', level: 1000);
      rethrow;
    }
  }

  @override
  Object? getConfigWidget() {
    // 返回配置界面Widget
    // 在实际实现中，这里应该返回一个Flutter Widget
    return null;
  }

  @override
  Object getMainWidget() {
    // 返回主界面Widget
    // 在实际实现中，这里应该返回一个Flutter Widget
    // Plugin System要求此方法必须返回非null值
    return Container(); // 返回空容器作为占位符
  }

  @override
  Future<dynamic> handleMessage(String action, Map<String, dynamic> data) async {
    developer.log('处理消息: $action', name: 'PluginCore');
    
    switch (action) {
      case 'ping':
        return {
          'status': 'pong',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        };
      
      case 'getInfo':
        return getInfo();
      
      case 'getState':
        return {
          'state': _currentState.toString(),
        };
      
      default:
        throw UnsupportedError('不支持的操作: $action');
    }
  }

  /// 获取插件信息
  Map<String, dynamic> getInfo() {
    return {
      'id': id,
      'name': name,
      'version': version,
      'description': description,
      'author': author,
      'category': category.toString(),
      'platforms': supportedPlatforms.map((p) => p.toString()).toList(),
      'state': _currentState.toString(),
    };
  }

  /// 更新插件状态
  void _updateState(PluginState newState) {
    if (_currentState != newState) {
      _currentState = newState;
      if (!_stateController.isClosed) {
        _stateController.add(newState);
      }
    }
  }
}
