/*
---------------------------------------------------------------
File name:          plugin_core.dart
Author:             {{author}}
Date created:       2025-07-25
Last modified:      2025-07-25
Dart Version:       {{dart_version}}
Description:        {{plugin_display_name}} - {{description}}
---------------------------------------------------------------
Change History:
    2025-07-25: Initial creation - {{description}};
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:developer' as developer;

// 导入Flutter框架
import 'package:flutter/widgets.dart';

// 导入Plugin System的真实基类
import 'package:plugin_system/plugin_system.dart' as plugin_sys;

// 所有插件相关的枚举和类现在都从Plugin System导入
// 不再需要本地定义

/// {{plugin_display_name}} 插件实现
///
/// 使用Plugin System的真实基类实现
class {{plugin_name.pascalCase()}}Plugin extends plugin_sys.Plugin {
  // 状态管理
  plugin_sys.PluginState _currentState = plugin_sys.PluginState.uninitialized;
  final StreamController<plugin_sys.PluginState> _stateController =
      StreamController<plugin_sys.PluginState>.broadcast();

  // 插件加载时间
  DateTime? _loadStartTime;
  Duration? _loadTime;

  @override
  String get id => '{{plugin_name}}';

  @override
  String get name => '{{plugin_display_name}}';

  @override
  String get version => '{{version}}';

  @override
  String get description => '{{description}}';

  @override
  String get author => '{{author}}';

  @override
  plugin_sys.PluginType get category => plugin_sys.PluginType.{{plugin_type}};

  @override
  List<plugin_sys.PluginPermission> get requiredPermissions => [];

  @override
  List<plugin_sys.PluginDependency> get dependencies => [
    const plugin_sys.PluginDependency(
      pluginId: 'pet_app_core',
      versionConstraint: '^3.0.0',
    ),
    const plugin_sys.PluginDependency(
      pluginId: 'plugin_system',
      versionConstraint: '^1.0.0',
    ),
  ];

  @override
  List<plugin_sys.SupportedPlatform> get supportedPlatforms => [
    plugin_sys.SupportedPlatform.android,
    plugin_sys.SupportedPlatform.ios,
    plugin_sys.SupportedPlatform.web,
    plugin_sys.SupportedPlatform.windows,
    plugin_sys.SupportedPlatform.macos,
    plugin_sys.SupportedPlatform.linux,
  ];

  @override
  plugin_sys.PluginState get currentState => _currentState;

  @override
  Stream<plugin_sys.PluginState> get stateChanges => _stateController.stream;

  @override
  plugin_sys.PluginManifest get manifest => plugin_sys.PluginManifest(
    id: id,
    name: name,
    version: version,
    description: description,
    author: author,
    category: category,
    main: 'lib/src/plugin_core.dart',
    requiredPermissions: requiredPermissions,
    dependencies: dependencies,
    supportedPlatforms: supportedPlatforms,
  );

  @override
  bool get isEnabled => _currentState != plugin_sys.PluginState.disabled;

  @override
  Duration? get loadTime => _loadTime;

  @override
  Future<void> initialize() async {
    _loadStartTime = DateTime.now();
    _updateState(plugin_sys.PluginState.initializing);

    try {
      developer.log('初始化插件: $name', name: name);

      // 在这里添加初始化逻辑
      await Future.delayed(const Duration(milliseconds: 100));

      _loadTime = DateTime.now().difference(_loadStartTime!);
      _updateState(plugin_sys.PluginState.initialized);
      developer.log('插件初始化完成', name: name);
    } catch (e) {
      _updateState(plugin_sys.PluginState.error);
      developer.log('插件初始化失败: $e', name: name, level: 1000);
      rethrow;
    }
  }

  @override
  Future<void> start() async {
    if (_currentState != plugin_sys.PluginState.initialized &&
        _currentState != plugin_sys.PluginState.stopped) {
      throw StateError('插件必须先初始化才能启动');
    }

    _updateState(plugin_sys.PluginState.starting);

    try {
      developer.log('启动插件: $name', name: name);

      // 在这里添加启动逻辑
      await Future.delayed(const Duration(milliseconds: 100));

      _updateState(plugin_sys.PluginState.started);
      developer.log('插件启动完成', name: name);
    } catch (e) {
      _updateState(plugin_sys.PluginState.error);
      developer.log('插件启动失败: $e', name: name, level: 1000);
      rethrow;
    }
  }

  @override
  Future<void> pause() async {
    if (_currentState != plugin_sys.PluginState.started) {
      throw StateError('只有运行中的插件才能暂停');
    }

    _updateState(plugin_sys.PluginState.pausing);

    try {
      developer.log('暂停插件: $name', name: name);

      // 在这里添加暂停逻辑
      await Future.delayed(const Duration(milliseconds: 50));

      _updateState(plugin_sys.PluginState.paused);
      developer.log('插件暂停完成', name: name);
    } catch (e) {
      _updateState(plugin_sys.PluginState.error);
      developer.log('插件暂停失败: $e', name: name, level: 1000);
      rethrow;
    }
  }

  @override
  Future<void> resume() async {
    if (_currentState != plugin_sys.PluginState.paused) {
      throw StateError('只有暂停的插件才能恢复');
    }

    _updateState(plugin_sys.PluginState.resuming);

    try {
      developer.log('恢复插件: $name', name: name);

      // 在这里添加恢复逻辑
      await Future.delayed(const Duration(milliseconds: 50));

      _updateState(plugin_sys.PluginState.started);
      developer.log('插件恢复完成', name: name);
    } catch (e) {
      _updateState(plugin_sys.PluginState.error);
      developer.log('插件恢复失败: $e', name: name, level: 1000);
      rethrow;
    }
  }

  @override
  Future<void> stop() async {
    if (_currentState != plugin_sys.PluginState.started &&
        _currentState != plugin_sys.PluginState.paused) {
      throw StateError('只有运行中或暂停的插件才能停止');
    }

    _updateState(plugin_sys.PluginState.stopping);

    try {
      developer.log('停止插件: $name', name: name);

      // 在这里添加停止逻辑
      await Future.delayed(const Duration(milliseconds: 100));

      _updateState(plugin_sys.PluginState.stopped);
      developer.log('插件停止完成', name: name);
    } catch (e) {
      _updateState(plugin_sys.PluginState.error);
      developer.log('插件停止失败: $e', name: name, level: 1000);
      rethrow;
    }
  }

  @override
  Future<void> dispose() async {
    _updateState(plugin_sys.PluginState.disposing);

    try {
      developer.log('销毁插件: $name', name: name);

      // 在这里添加清理逻辑
      await Future.delayed(const Duration(milliseconds: 50));

      _updateState(plugin_sys.PluginState.disposed);
      await _stateController.close();

      developer.log('插件销毁完成', name: name);
    } catch (e) {
      _updateState(plugin_sys.PluginState.error);
      developer.log('插件销毁失败: $e', name: name, level: 1000);
      rethrow;
    }
  }

  @override
  Object? getConfigWidget() {
    // 返回配置界面Widget
    // 在实际实现中，这里应该返回一个Flutter Widget
    return null;
  }

  @override
  Object getMainWidget() {
    // 返回主界面Widget
    // 在实际实现中，这里应该返回一个Flutter Widget
    // Plugin System要求此方法必须返回非null值
    return Container(); // 返回空容器作为占位符
  }

  @override
  Future<dynamic> handleMessage(String action, Map<String, dynamic> data) async {
    developer.log('处理消息: $action', name: name);
    
    switch (action) {
      case 'ping':
        return {
          'status': 'pong',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        };
      
      case 'getInfo':
        return getInfo();
      
      case 'getState':
        return {
          'state': _currentState.toString(),
        };
      
      default:
        throw UnsupportedError('不支持的操作: $action');
    }
  }

  /// 获取插件信息
  Map<String, dynamic> getInfo() {
    return {
      'id': id,
      'name': name,
      'version': version,
      'description': description,
      'author': author,
      'category': category.toString(),
      'platforms': supportedPlatforms.map((p) => p.toString()).toList(),
      'state': _currentState.toString(),
    };
  }

  /// 更新插件状态
  void _updateState(plugin_sys.PluginState newState) {
    if (_currentState != newState) {
      _currentState = newState;
      if (!_stateController.isClosed) {
        _stateController.add(newState);
      }
    }
  }
}
