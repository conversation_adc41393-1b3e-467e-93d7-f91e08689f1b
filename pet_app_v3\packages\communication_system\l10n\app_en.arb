{"@@locale": "en", "@@last_modified": "2025-07-21T09:01:07.688218", "@@author": "Pet App V3 Team", "appTitle": "My App", "welcome": "Welcome", "hello": "Hello, {name}!", "settings": "Settings", "about": "About", "ok": "OK", "cancel": "Cancel", "home": "Home", "profile": "Profile", "notifications": "Notifications", "search": "Search", "loading": "Loading...", "error": "Error", "retry": "Retry", "save": "Save", "delete": "Delete", "edit": "Edit", "itemCount": "{count, plural, =0{No items} =1{1 item} other{{count} items}}", "@appTitle": {"description": "The title of the application"}, "@welcome": {"description": "Welcome message displayed on the home screen"}, "@hello": {"description": "Personalized greeting message", "placeholders": {"name": {"type": "String", "example": "<PERSON>", "description": "User name"}}}, "@itemCount": {"description": "Number of items with plural support", "placeholders": {"count": {"type": "int", "format": "compact"}}}, "appName": "communication_system", "appDescription": "跨模块通信系统 - 统一消息总线、事件路由、数据同步", "@appName": {"description": "Application name", "context": "app_info"}, "@appDescription": {"description": "Application description", "context": "app_info"}}