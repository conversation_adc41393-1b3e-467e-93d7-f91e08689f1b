import 'package:test/test.dart';
import 'package:app_manager/src/services/plugin_installation_service.dart';
import 'package:app_manager/src/services/app_store_service.dart';

void main() {
  group('完整插件集成测试', () {
    late PluginInstallationService installationService;
    late AppStoreService appStoreService;

    setUpAll(() async {
      installationService = PluginInstallationService.instance;
      appStoreService = AppStoreService.instance;

      // 初始化服务
      await installationService.initialize();
      await appStoreService.initialize();
    });

    test('服务初始化验证', () {
      expect(installationService, isNotNull);
      expect(appStoreService, isNotNull);
    });

    test('插件安装错误类型完整性', () {
      // 验证所有错误类型都存在
      expect(PluginInstallationError.values.length, greaterThan(5));
      expect(PluginInstallationError.values,
          contains(PluginInstallationError.alreadyInstalled));
      expect(PluginInstallationError.values,
          contains(PluginInstallationError.pluginNotFound));
      expect(PluginInstallationError.values,
          contains(PluginInstallationError.networkError));
    });

    test('应用商店项目类型完整性', () {
      // 验证所有项目类型都存在
      expect(AppStoreItemType.values.length, greaterThanOrEqualTo(4));
      expect(AppStoreItemType.values, contains(AppStoreItemType.plugin));
      expect(AppStoreItemType.values, contains(AppStoreItemType.module));
      expect(AppStoreItemType.values, contains(AppStoreItemType.theme));
      expect(AppStoreItemType.values, contains(AppStoreItemType.template));
    });

    test('插件安装结果工厂方法', () {
      // 测试成功结果
      final success = PluginInstallationResult.success(
        pluginId: 'test',
        message: 'Success',
        installedVersion: '1.0.0',
        installationPath: '/path/to/plugin',
        installationTime: const Duration(seconds: 5),
      );

      expect(success.success, isTrue);
      expect(success.pluginId, equals('test'));
      expect(success.message, equals('Success'));
      expect(success.installedVersion, equals('1.0.0'));
      expect(success.installationPath, equals('/path/to/plugin'));
      expect(success.installationTime, equals(const Duration(seconds: 5)));
      expect(success.error, isNull);

      // 测试失败结果
      final failure = PluginInstallationResult.failure(
        pluginId: 'test',
        message: 'Failed',
        error: PluginInstallationError.networkError,
      );

      expect(failure.success, isFalse);
      expect(failure.pluginId, equals('test'));
      expect(failure.message, equals('Failed'));
      expect(failure.error, equals(PluginInstallationError.networkError));
      expect(failure.installedVersion, isNull);
      expect(failure.installationPath, isNull);
      expect(failure.installationTime, isNull);
    });

    test('应用商店项目创建和属性', () {
      const item = AppStoreItem(
        id: 'test_plugin',
        name: '测试插件',
        description: '这是一个测试插件',
        type: AppStoreItemType.plugin,
        version: '2.1.0',
        author: '测试开发者',
        iconUrl: 'https://example.com/icon.png',
        screenshots: ['screenshot1.png', 'screenshot2.png'],
        tags: ['test', 'demo', 'utility'],
        rating: 4.8,
        downloadCount: 5000,
        price: 9.99,
        isFree: false,
        isOfficial: true,
        metadata: {'category': 'utility', 'platform': 'all'},
      );

      expect(item.id, equals('test_plugin'));
      expect(item.name, equals('测试插件'));
      expect(item.description, equals('这是一个测试插件'));
      expect(item.type, equals(AppStoreItemType.plugin));
      expect(item.version, equals('2.1.0'));
      expect(item.author, equals('测试开发者'));
      expect(item.iconUrl, equals('https://example.com/icon.png'));
      expect(item.screenshots, hasLength(2));
      expect(item.screenshots, contains('screenshot1.png'));
      expect(item.screenshots, contains('screenshot2.png'));
      expect(item.tags, hasLength(3));
      expect(item.tags, contains('test'));
      expect(item.tags, contains('demo'));
      expect(item.tags, contains('utility'));
      expect(item.rating, equals(4.8));
      expect(item.downloadCount, equals(5000));
      expect(item.price, equals(9.99));
      expect(item.isFree, isFalse);
      expect(item.isOfficial, isTrue);
      expect(item.metadata, isA<Map<String, dynamic>>());
      expect(item.metadata['category'], equals('utility'));
      expect(item.metadata['platform'], equals('all'));
    });

    test('应用商店搜索结果分页逻辑', () {
      const item1 = AppStoreItem(
        id: 'plugin1',
        name: '插件1',
        description: '描述1',
        type: AppStoreItemType.plugin,
        version: '1.0.0',
        author: '作者1',
      );

      const item2 = AppStoreItem(
        id: 'plugin2',
        name: '插件2',
        description: '描述2',
        type: AppStoreItemType.plugin,
        version: '1.0.0',
        author: '作者2',
      );

      // 测试有更多页面的情况
      const searchResultWithMore = AppStoreSearchResult(
        items: [item1, item2],
        totalCount: 100,
        page: 1,
        pageSize: 20,
      );

      expect(searchResultWithMore.items, hasLength(2));
      expect(searchResultWithMore.totalCount, equals(100));
      expect(searchResultWithMore.page, equals(1));
      expect(searchResultWithMore.pageSize, equals(20));
      expect(searchResultWithMore.hasMore, isTrue); // (1 * 20) < 100

      // 测试没有更多页面的情况
      const searchResultNoMore = AppStoreSearchResult(
        items: [item1, item2],
        totalCount: 2,
        page: 1,
        pageSize: 20,
      );

      expect(searchResultNoMore.hasMore, isFalse); // (1 * 20) >= 2
    });

    test('插件安装进度信息', () {
      const progress1 = PluginInstallationProgress(
        pluginId: 'test_plugin',
        progress: 0.0,
        stage: '开始下载',
        message: '正在连接服务器...',
      );

      expect(progress1.pluginId, equals('test_plugin'));
      expect(progress1.progress, equals(0.0));
      expect(progress1.stage, equals('开始下载'));
      expect(progress1.message, equals('正在连接服务器...'));

      const progress2 = PluginInstallationProgress(
        pluginId: 'test_plugin',
        progress: 0.5,
        stage: '下载中',
        message: '已下载 50%',
      );

      expect(progress2.progress, equals(0.5));
      expect(progress2.stage, equals('下载中'));

      const progress3 = PluginInstallationProgress(
        pluginId: 'test_plugin',
        progress: 1.0,
        stage: '安装完成',
      );

      expect(progress3.progress, equals(1.0));
      expect(progress3.stage, equals('安装完成'));
      expect(progress3.message, isNull);
    });

    test('插件安装服务功能测试', () async {
      // 测试安装不存在的插件
      final result1 = await installationService.installPlugin(
        pluginId: 'non_existent_plugin_12345',
        version: '1.0.0',
      );

      expect(result1, isNotNull);
      expect(result1.pluginId, equals('non_existent_plugin_12345'));
      expect(result1.success, isA<bool>());

      // 测试卸载不存在的插件
      final result2 = await installationService
          .uninstallPlugin('non_existent_plugin_12345');

      expect(result2, isNotNull);
      expect(result2.pluginId, equals('non_existent_plugin_12345'));
      expect(result2.success, isFalse);
      // 错误类型可能是pluginNotFound或unknown，都是合理的
      expect(
          result2.error,
          anyOf(
            equals(PluginInstallationError.pluginNotFound),
            equals(PluginInstallationError.unknown),
          ));

      // 测试获取不存在插件的安装进度
      final progressStream =
          installationService.getInstallationProgress('non_existent_plugin');
      expect(progressStream, isNull);
    });

    test('应用商店服务功能测试', () async {
      // 测试搜索插件
      final searchResult = await appStoreService.searchItems(
        type: AppStoreItemType.plugin,
        query: 'test',
        page: 1,
        pageSize: 5,
      );

      expect(searchResult, isNotNull);
      expect(searchResult.items, isA<List<AppStoreItem>>());
      expect(searchResult.totalCount, isA<int>());
      expect(searchResult.page, equals(1));
      expect(searchResult.pageSize, equals(5));

      // 测试获取热门项目
      final popularItems = await appStoreService.getPopularItems(
        type: AppStoreItemType.plugin,
        limit: 3,
      );

      expect(popularItems, isA<List<AppStoreItem>>());

      // 测试卸载项目
      final uninstallResult =
          await appStoreService.uninstallItem('non_existent_item');

      expect(uninstallResult, isNotNull);
      expect(uninstallResult.pluginId, equals('non_existent_item'));
      expect(uninstallResult.success, isFalse);
    });
  });
}
