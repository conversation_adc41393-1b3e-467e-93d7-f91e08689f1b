/*
---------------------------------------------------------------
File name:          dependency_manager_test.dart
Author:             lgnorant-lu
Date created:       2025/07/28
Last modified:      2025/07/28
Dart Version:       3.2+
Description:        依赖管理器测试
---------------------------------------------------------------
Change History:
    2025/07/28: Initial creation - 依赖管理器测试;
---------------------------------------------------------------
*/

import 'package:test/test.dart';
import 'package:plugin_system/src/core/dependency_manager.dart';
import 'package:plugin_system/src/core/plugin_registry.dart';
import 'package:plugin_system/src/core/plugin.dart';
import 'package:plugin_system/src/core/plugin_manifest.dart';

void main() {
  group('DependencyManager', () {
    late DependencyManager dependencyManager;
    late PluginRegistry registry;

    setUp(() {
      registry = PluginRegistry.instance;
      dependencyManager = DependencyManager.instance;

      // 清理注册表
      registry.clear();
    });

    tearDown(() {
      registry.clear();
    });

    group('基础功能测试', () {
      test('应该创建依赖管理器', () {
        expect(dependencyManager, isNotNull);
        expect(dependencyManager, isA<DependencyManager>());
      });

      test('应该更新依赖图', () {
        final plugin = MockPlugin('test_plugin', '1.0.0', dependencies: [
          const PluginDependency(pluginId: 'dep1', versionConstraint: '^1.0.0'),
        ]);

        dependencyManager.updateDependencyGraph(plugin);
        final dependencies =
            dependencyManager.getPluginDependencies('test_plugin');

        expect(dependencies, contains('dep1'));
      });

      test('应该清理插件依赖信息', () {
        final plugin = MockPlugin('cleanup_plugin', '1.0.0', dependencies: [
          const PluginDependency(pluginId: 'dep1', versionConstraint: '^1.0.0'),
        ]);

        dependencyManager.updateDependencyGraph(plugin);
        expect(dependencyManager.getPluginDependencies('cleanup_plugin'),
            isNotEmpty);

        dependencyManager.cleanupPlugin('cleanup_plugin');
        expect(
            dependencyManager.getPluginDependencies('cleanup_plugin'), isEmpty);
      });

      test('应该获取反向依赖', () {
        final pluginB = MockPlugin('plugin_b', '1.0.0', dependencies: [
          const PluginDependency(
              pluginId: 'plugin_a', versionConstraint: '^1.0.0'),
        ]);

        dependencyManager.updateDependencyGraph(pluginB);

        final dependents = dependencyManager.getPluginDependents('plugin_a');

        expect(dependents, contains('plugin_b'));
      });

      test('应该检查是否可以卸载插件', () {
        final pluginB = MockPlugin('plugin_b', '1.0.0', dependencies: [
          const PluginDependency(
              pluginId: 'plugin_a', versionConstraint: '^1.0.0'),
        ]);

        // 更新依赖图
        dependencyManager.updateDependencyGraph(pluginB);

        // 检查插件B是否可以卸载（没有其他插件依赖它）
        final canUnloadB = dependencyManager.canUnloadPlugin('plugin_b');

        expect(canUnloadB, isTrue); // 没有其他插件依赖
      });
    });

    group('自动安装依赖测试', () {
      test('应该尝试自动安装依赖', () async {
        final plugin =
            MockPlugin('auto_install_plugin', '1.0.0', dependencies: [
          const PluginDependency(
              pluginId: 'auto_dep1', versionConstraint: '^1.0.0'),
          const PluginDependency(
              pluginId: 'auto_dep2',
              versionConstraint: '^1.0.0',
              optional: true),
        ]);

        final installed =
            await dependencyManager.autoInstallDependencies(plugin);

        expect(installed, isA<List<String>>());
        // 注意：由于是模拟安装，结果可能不确定
      });
    });
  });
}

/// 简化的模拟插件类用于测试
class MockPlugin implements Plugin {
  MockPlugin(this.id, this.version, {this.dependencies = const []});

  @override
  final String id;

  @override
  final String version;

  @override
  final List<PluginDependency> dependencies;

  @override
  String get name => 'Mock Plugin $id';

  @override
  String get description => 'Mock plugin for testing';

  @override
  String get author => 'Test Author';

  @override
  List<SupportedPlatform> get supportedPlatforms => [SupportedPlatform.android];

  Map<String, dynamic> get metadata => {};

  @override
  PluginType get category => PluginType.tool;

  @override
  PluginState get currentState => PluginState.loaded;

  @override
  bool get isEnabled => true;

  @override
  Duration? get loadTime => const Duration(milliseconds: 100);

  @override
  List<PluginPermission> get requiredPermissions => [];

  @override
  PluginManifest get manifest => PluginManifest(
        id: id,
        name: name,
        version: version,
        description: description,
        author: author,
        category: 'tool',
        main: 'lib/main.dart',
      );

  @override
  Stream<PluginState> get stateChanges => const Stream<PluginState>.empty();

  @override
  Future<void> initialize() async {}

  @override
  Future<void> start() async {}

  @override
  Future<void> stop() async {}

  @override
  Future<void> dispose() async {}

  @override
  Future<void> pause() async {}

  @override
  Future<void> resume() async {}

  @override
  Future<void> handleMessage(String message, Map<String, dynamic> data) async {}

  @override
  Object getMainWidget() => Object();

  @override
  Object getConfigWidget() => Object();
}
