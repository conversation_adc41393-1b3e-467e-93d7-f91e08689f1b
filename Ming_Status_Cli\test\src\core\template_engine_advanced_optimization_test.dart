/*
---------------------------------------------------------------
File name:          template_engine_advanced_optimization_test.dart
Author:             lgnorant-lu
Date created:       2025/06/30
Last modified:      2025/06/30
Dart Version:       3.2+
Description:        Task 36.* 模板系统最终优化测试
                    (Template system final optimization tests)
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:io';

import 'package:ming_status_cli/src/core/extensions/template_engine_extensions.dart';
import 'package:ming_status_cli/src/core/managers/async_manager.dart';
import 'package:ming_status_cli/src/core/managers/cache_manager.dart';
import 'package:ming_status_cli/src/core/managers/ux_manager.dart';
import 'package:ming_status_cli/src/core/template_engine/template_engine.dart';
import 'package:ming_status_cli/src/core/template_engine/template_exceptions.dart';
import 'package:ming_status_cli/src/core/template_engine/template_models.dart';
import 'package:test/test.dart';

void main() {
  group('Task 36.* 模板系统最终优化测试', () {
    late TemplateEngine templateEngine;
    late Directory tempDir;
    late String templatesPath;

    setUpAll(() async {
      // 设置临时目录和测试环境
      tempDir = await Directory.systemTemp.createTemp(
        'template_optimization_test',
      );
      templatesPath = '${tempDir.path}/templates';
      await Directory(templatesPath).create(recursive: true);

      // 创建测试模板
      await _createTestTemplate(templatesPath, 'test_template');
      await _createTestTemplate(templatesPath, 'cache_template');

      // 初始化模板引擎（使用实际的构造函数）
      templateEngine = TemplateEngine(workingDirectory: tempDir.path);
      await templateEngine.initialize();
    });

    tearDownAll(() async {
      await tempDir.delete(recursive: true);
    });

    group('Task 36.1: 高级模板缓存和预编译优化系统', () {
      test('应该支持高级缓存管理器', () async {
        // 获取缓存管理器（通过实际存在的方法）
        final cacheManager = templateEngine.cacheManager;
        expect(cacheManager, isNotNull);

        // 测试缓存配置
        expect(AdvancedTemplateCacheManager.maxCacheSize, equals(50));
        expect(
          AdvancedTemplateCacheManager.cacheExpiry,
          equals(const Duration(hours: 2)),
        );
      });

      test('应该支持模板预编译缓存', () async {
        // 预编译模板
        final result = await templateEngine.cacheManager.precompileTemplate(
          'test_template',
        );
        expect(result, isNotNull);

        // 验证预编译缓存
        final stats = templateEngine.cacheManager.getCacheStatistics();
        expect(stats, isNotNull);
        expect(stats['cache_size'], greaterThan(0));
      });

      test('应该支持缓存访问统计', () async {
        // 直接获取访问统计，不依赖实际生成
        final stats = templateEngine.cacheManager.getCacheStatistics();
        expect(stats, isNotNull);
        expect(stats['total_hits'], greaterThanOrEqualTo(0));
        expect(stats['total_misses'], greaterThanOrEqualTo(0));
      });

      test('应该支持缓存预热', () async {
        // 执行缓存预热
        await templateEngine.cacheManager.warmUpCache();

        // 验证预热结果
        final stats = templateEngine.cacheManager.getCacheStatistics();
        expect(stats['cache_size'], greaterThanOrEqualTo(0));
      });

      test('应该支持缓存清理', () async {
        // 预编译模板创建缓存
        await templateEngine.cacheManager.precompileTemplate('test_template');

        // 验证有缓存
        var stats = templateEngine.cacheManager.getCacheStatistics();
        expect(stats['cache_size'], greaterThan(0));

        // 清理缓存
        await templateEngine.cacheManager.clearCache();

        // 验证清理结果
        stats = templateEngine.cacheManager.getCacheStatistics();
        expect(stats['cache_size'], equals(0));
      });

      test('应该支持缓存过期管理', () async {
        // 预编译模板创建缓存条目
        await templateEngine.cacheManager.precompileTemplate('cache_template');

        // 获取统计验证缓存存在
        final stats = templateEngine.cacheManager.getCacheStatistics();
        expect(stats['cache_size'], greaterThanOrEqualTo(0));
        expect(stats['expired_count'], greaterThanOrEqualTo(0));
      });
    });

    group('Task 36.2: 异步生成和并发处理系统', () {
      test('应该支持异步生成管理器', () async {
        // 获取异步生成管理器
        final asyncManager = templateEngine.asyncManager;
        expect(asyncManager, isNotNull);

        // 测试并发配置
        expect(
          AsyncTemplateGenerationManager.maxConcurrentGenerations,
          equals(5),
        );
        expect(
          AsyncTemplateGenerationManager.generationTimeout,
          equals(const Duration(minutes: 10)),
        );
      });

      test('应该支持异步模板生成', () async {
        // 使用异步生成管理器进行异步生成
        final result = await templateEngine.asyncManager.generateTemplateAsync(
          templateName: 'test_template',
          outputPath: '${tempDir.path}/async_test_1',
          variables: {'module_name': 'AsyncTest1'},
        );

        expect(result.success, isTrue);
      });

      test('应该支持并发任务队列管理', () async {
        // 创建多个生成规格
        final specs = <TemplateGenerationSpec>[];

        for (var i = 0; i < 3; i++) {
          specs.add(
            TemplateGenerationSpec(
              templateName: 'test_template',
              outputPath: '${tempDir.path}/concurrent_test_$i',
              variables: {'module_name': 'ConcurrentTest$i'},
            ),
          );
        }

        // 批量异步生成
        final results =
            await templateEngine.asyncManager.generateMultipleTemplatesAsync(
          specs: specs,
        );

        // 验证所有任务成功
        for (final result in results) {
          expect(result.success, isTrue);
        }
      });

      test('应该支持任务优先级管理', () async {
        // 创建不同优先级的任务
        final highPriorityResult =
            await templateEngine.asyncManager.generateTemplateAsync(
          templateName: 'test_template',
          outputPath: '${tempDir.path}/high_priority',
          variables: {'module_name': 'HighPriority'},
          priority: 10,
        );

        final lowPriorityResult =
            await templateEngine.asyncManager.generateTemplateAsync(
          templateName: 'test_template',
          outputPath: '${tempDir.path}/low_priority',
          variables: {'module_name': 'LowPriority'},
          priority: 1,
        );

        // 验证结果
        expect(highPriorityResult.success, isTrue);
        expect(lowPriorityResult.success, isTrue);
      });

      test('应该支持流式生成', () async {
        // 创建生成规格列表
        final specs = <TemplateGenerationSpec>[];
        for (var i = 0; i < 3; i++) {
          specs.add(
            TemplateGenerationSpec(
              templateName: 'test_template',
              outputPath: '${tempDir.path}/stream_test_$i',
              variables: {'module_name': 'StreamTest$i'},
            ),
          );
        }

        // 使用流式生成
        final results = <GenerationResult>[];
        await for (final result in templateEngine.asyncManager
            .generateTemplatesStream(specs: specs)) {
          results.add(result);
        }

        // 验证所有结果
        expect(results.length, equals(3));
        for (final result in results) {
          expect(result.success, isTrue);
        }
      });

      test('应该支持生成统计', () async {
        // 执行一些生成任务
        await templateEngine.asyncManager.generateTemplateAsync(
          templateName: 'test_template',
          outputPath: '${tempDir.path}/stats_test',
          variables: {'module_name': 'StatsTest'},
        );

        // 获取统计信息
        final stats = templateEngine.asyncManager.getGenerationStatistics();
        expect(stats, isNotNull);
        expect(stats['max_concurrent_generations'], equals(5));
      });
    });

    group('Task 36.* 集成测试', () {
      test('应该支持缓存和异步生成的集成使用', () async {
        // 预热缓存
        await templateEngine.cacheManager.warmUpCache();

        // 使用缓存进行异步生成
        final result = await templateEngine.asyncManager.generateTemplateAsync(
          templateName: 'test_template',
          outputPath: '${tempDir.path}/integration_test',
          variables: {'module_name': 'IntegrationTest'},
        );

        expect(result.success, isTrue);

        // 验证缓存统计更新
        final stats = templateEngine.cacheManager.getCacheStatistics();
        expect(stats['cache_size'], greaterThanOrEqualTo(0));
      });

      test('应该支持性能监控和统计', () async {
        // 执行多个生成任务
        for (var i = 0; i < 3; i++) {
          await templateEngine.generateModule(
            templateName: 'test_template',
            outputPath: '${tempDir.path}/perf_test_$i',
            variables: {'module_name': 'PerfTest$i'},
          );
        }

        // 获取性能统计
        final cacheStats = templateEngine.cacheManager.getCacheStatistics();
        final asyncStats =
            templateEngine.asyncManager.getGenerationStatistics();

        expect(cacheStats, isNotNull);
        expect(asyncStats, isNotNull);
      });

      test('应该支持完整的优化工作流', () async {
        // 1. 预热缓存
        await templateEngine.cacheManager.warmUpCache();

        // 2. 创建多个生成任务
        final specs = <TemplateGenerationSpec>[];
        for (var i = 0; i < 5; i++) {
          specs.add(
            TemplateGenerationSpec(
              templateName: 'test_template',
              outputPath: '${tempDir.path}/workflow_test_$i',
              variables: {'module_name': 'WorkflowTest$i'},
              priority: i,
            ),
          );
        }

        // 3. 批量异步生成
        final results =
            await templateEngine.asyncManager.generateMultipleTemplatesAsync(
          specs: specs,
        );

        // 4. 验证结果
        expect(results.length, equals(5));
        for (final result in results) {
          expect(result.success, isTrue);
        }

        // 5. 检查统计信息
        final cacheStats = templateEngine.cacheManager.getCacheStatistics();
        final asyncStats =
            templateEngine.asyncManager.getGenerationStatistics();

        expect(cacheStats['total_hits'], greaterThanOrEqualTo(0));
        expect(asyncStats['max_concurrent_generations'], equals(5));
      });
    });

    // ==================== Task 36.3: 错误恢复机制测试 ====================

    group('Task 36.3: 完善的错误恢复机制', () {
      test('应该支持智能错误恢复管理器', () async {
        final recoveryManager = templateEngine.intelligentErrorRecoveryManager;
        expect(recoveryManager, isNotNull);

        final stats = recoveryManager.getRecoveryStatistics();
        expect(stats, isNotNull);
        expect(stats['total_attempts'], equals(0));
      });

      test('应该支持错误模式分析', () async {
        // 模拟错误
        final error =
            TemplateEngineException.templateNotFound('nonexistent_template');

        final recoveryResult = await templateEngine
            .intelligentErrorRecoveryManager
            .intelligentRecover(error, null);

        expect(recoveryResult, isNotNull);
        // 错误恢复可能成功或失败，但应该提供有用信息
        expect(recoveryResult.message, isNotNull);
      });

      test('应该支持智能错误恢复生成', () async {
        // 使用不存在的模板测试智能恢复
        final result = await templateEngine.generateWithIntelligentRecovery(
          templateName: 'test_template',
          outputPath: '${tempDir.path}/recovery_test',
          variables: {
            'module_id': 'recovery_test',
            'module_name': 'RecoveryTest',
          },
        );

        // 应该成功，因为test_template存在且有正确变量
        expect(result, isNotNull);
        expect(result.success, isTrue);
      });

      test('应该支持错误恢复统计', () async {
        final stats = templateEngine.getErrorRecoveryStatistics();
        expect(stats, isNotNull);
        expect(stats.containsKey('total_attempts'), isTrue);
        expect(stats.containsKey('success_rate'), isTrue);
      });

      test('应该支持变量验证错误恢复', () async {
        // 创建一个变量验证错误
        final validationErrors = {'module_id': '必需变量未提供'};
        final error =
            TemplateEngineException.variableValidationError(validationErrors);

        final recoveryResult = await templateEngine
            .intelligentErrorRecoveryManager
            .intelligentRecover(error, null);

        expect(recoveryResult, isNotNull);
        // 智能恢复可能会成功或失败，但应该提供有用信息
      });
    });

    // ==================== Task 36.4: 用户体验优化测试 ====================

    group('Task 36.4: 用户体验优化和反馈改进', () {
      test('应该支持用户体验管理器', () async {
        final uxManager = templateEngine.userExperienceManager;
        expect(uxManager, isNotNull);

        final report = uxManager.getUserExperienceReport();
        expect(report, isNotNull);
        expect(report['summary'], isNotNull);
      });

      test('应该支持进度反馈生成', () async {
        final progressUpdates = <ProgressUpdate>[];

        final result = await templateEngine.generateWithOptimizedUX(
          templateName: 'test_template',
          outputPath: '${tempDir.path}/ux_test',
          variables: {
            'module_id': 'ux_test',
            'module_name': 'UXTest',
          },
          onProgress: progressUpdates.add,
        );

        expect(result.success, isTrue);
        expect(progressUpdates, isNotEmpty);
        expect(progressUpdates.any((u) => u.progress == 1.0), isTrue);
      });

      test('应该支持智能模板推荐', () async {
        final recommendations = await templateEngine.getTemplateRecommendations(
          context: 'test',
        );

        expect(recommendations, isNotNull);
        expect(recommendations, isList);
        // 应该推荐包含'test'的模板
        if (recommendations.isNotEmpty) {
          expect(recommendations.first.templateName, contains('test'));
        }
      });

      test('应该支持用户体验报告生成', () async {
        // 先执行一些操作生成历史
        await templateEngine.generateWithOptimizedUX(
          templateName: 'test_template',
          outputPath: '${tempDir.path}/ux_history',
          variables: {
            'module_id': 'ux_history',
            'module_name': 'UXHistory',
          },
        );

        final report = templateEngine.getUserExperienceReport();
        expect(report, isNotNull);
        expect(report['summary'], isNotNull);
        expect(report['usage_patterns'], isNotNull);
        expect(report['performance_metrics'], isNotNull);
        expect(report['recommendations'], isNotNull);
      });

      test('应该支持增强的用户体验生成', () async {
        final result =
            await templateEngine.userExperienceManager.generateWithEnhancedUX(
          templateName: 'test_template',
          outputPath: '${tempDir.path}/enhanced_ux',
          variables: {
            'module_id': 'enhanced_ux',
            'module_name': 'EnhancedUX',
          },
        );

        expect(result.success, isTrue);
        expect((result.metadata as Map)['enhanced'], isTrue);
      });
    });

    // ==================== Task 36.* 综合集成测试 ====================

    group('Task 36.* 综合功能集成测试', () {
      test('应该支持Task 36完整功能报告', () async {
        final report = await templateEngine.getTask36CompleteReport();

        expect(report, isNotNull);
        expect(
          (report['task_36_1_cache_optimization'] as Map)['status'],
          equals('completed'),
        );
        expect(
          (report['task_36_2_async_generation'] as Map)['status'],
          equals('completed'),
        );
        expect(
          (report['task_36_3_error_recovery'] as Map)['status'],
          equals('completed'),
        );
        expect(
          (report['task_36_4_ux_optimization'] as Map)['status'],
          equals('completed'),
        );
        expect(
          (report['integration'] as Map)['all_features_integrated'],
          isTrue,
        );
        expect(
          (report['summary'] as Map)['completion_rate'],
          equals(1.0),
        );
      });

      test('应该支持所有Task 36功能的协同工作', () async {
        // 1. 预热缓存
        await templateEngine.cacheManager.warmUpCache();

        // 2. 异步生成with智能恢复和UX优化
        final progressUpdates = <String>[];

        final result = await templateEngine.generateWithOptimizedUX(
          templateName: 'test_template',
          outputPath: '${tempDir.path}/integrated_test',
          variables: {
            'module_id': 'integrated_test',
            'module_name': 'IntegratedTest',
          },
          onProgress: (update) => progressUpdates.add(update.message),
        );

        expect(result.success, isTrue);
        expect(progressUpdates, isNotEmpty);

        // 3. 验证各种统计和报告
        final cacheStats = templateEngine.cacheManager.getCacheStatistics();
        final asyncStats =
            templateEngine.asyncManager.getGenerationStatistics();
        final recoveryStats = templateEngine.getErrorRecoveryStatistics();
        final uxReport = templateEngine.getUserExperienceReport();

        expect(cacheStats['cache_size'], greaterThanOrEqualTo(0));
        expect(asyncStats['active_generations'], greaterThanOrEqualTo(0));
        expect(recoveryStats['total_attempts'], greaterThanOrEqualTo(0));
        expect(
          (uxReport['summary'] as Map)['total_interactions'],
          greaterThanOrEqualTo(0),
        );
      });

      test('应该支持完整的错误恢复和UX优化工作流', () async {
        // 模拟复杂的生成场景，包含错误恢复和UX优化
        final specs = <TemplateGenerationSpec>[];
        for (var i = 0; i < 3; i++) {
          specs.add(
            TemplateGenerationSpec(
              templateName: 'test_template',
              outputPath: '${tempDir.path}/full_workflow_$i',
              variables: {
                'module_id': 'full_workflow_$i',
                'module_name': 'FullWorkflow$i',
              },
              priority: i,
            ),
          );
        }

        // 使用完整的优化工作流
        final results = <GenerationResult>[];
        await for (final result in templateEngine.asyncManager
            .generateTemplatesStream(specs: specs)) {
          results.add(result);
        }

        // 验证结果
        expect(results.length, equals(3));
        expect(results.every((r) => r.success), isTrue);

        // 获取最终报告
        final finalReport = await templateEngine.getTask36CompleteReport();
        expect(
          (finalReport['summary'] as Map)['completed_tasks'],
          equals(4),
        );
      });
    });
  });
}

// 辅助方法：创建测试模板
Future<void> _createTestTemplate(
  String templatesPath,
  String templateName,
) async {
  final templateDir = Directory('$templatesPath/$templateName');
  await templateDir.create(recursive: true);

  // 创建 brick.yaml
  final brickYamlFile = File('${templateDir.path}/brick.yaml');
  await brickYamlFile.writeAsString('''
    name: $templateName
    description: Test template for $templateName
    version: 1.0.0

    vars:
      module_name:
        type: string
        description: The name of the module
        default: DefaultModule
    ''');

  // 创建模板文件
  final brickDir = Directory('${templateDir.path}/__brick__');
  await brickDir.create(recursive: true);

  final mainFile =
      File('${brickDir.path}/lib/{{module_name.snakeCase()}}.dart');
  await mainFile.parent.create(recursive: true);
  await mainFile.writeAsString('''
    // {{module_name}} module
    class {{module_name.pascalCase()}} {
      // Implementation
    }
    ''');
}
