/*
---------------------------------------------------------------
File name:          app_manager_integration.dart
Author:             lgnorant-lu
Date created:       2025-07-24
Last modified:      2025-07-24
Dart Version:       3.2+
Description:        App Manager 主应用集成
---------------------------------------------------------------
Change History:
    2025-07-24: Phase 5.0.9 - 实现App Manager主应用集成;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:developer' as developer;
import '../services/module_state_controller.dart';
import '../services/module_resource_manager.dart';
import '../services/module_permission_manager.dart';
import '../services/module_monitoring_dashboard.dart';
import '../services/module_file_system_service.dart';
import '../models/module_permission.dart';
import '../models/module_state.dart';
import '../models/integration_models.dart';

/// App Manager 主应用集成器
/// 
/// 提供与主应用的集成接口和服务
class AppManagerIntegration {
  static AppManagerIntegration? _instance;
  static AppManagerIntegration get instance => _instance ??= AppManagerIntegration._();
  
  AppManagerIntegration._();

  bool _isIntegrated = false;
  final StreamController<IntegrationEvent> _eventController = StreamController.broadcast();

  /// 集成事件流
  Stream<IntegrationEvent> get integrationEvents => _eventController.stream;

  /// 是否已集成
  bool get isIntegrated => _isIntegrated;

  /// 初始化集成
  Future<IntegrationResult> initializeIntegration({
    required Map<String, dynamic> config,
  }) async {
    try {
      _log('info', '开始初始化App Manager集成');

      // 1. 初始化权限管理器
      await ModulePermissionManager.instance.initialize();
      _log('info', '权限管理器初始化完成');

      // 2. 启动监控仪表板
      final monitoringInterval = Duration(
        seconds: config['monitoring_interval'] as int? ?? 5,
      );
      await ModuleMonitoringDashboard.instance.startMonitoring(
        interval: monitoringInterval,
      );
      _log('info', '监控仪表板启动完成');

      // 3. 设置默认权限策略
      await _setupDefaultPermissionPolicies();
      _log('info', '默认权限策略设置完成');

      // 4. 注册系统模块
      await _registerSystemModules();
      _log('info', '系统模块注册完成');

      _isIntegrated = true;
      _eventController.add(IntegrationEvent.initialized());

      _log('info', 'App Manager集成初始化完成');
      return IntegrationResult.success('App Manager集成初始化成功');

    } catch (e, stackTrace) {
      _log('severe', 'App Manager集成初始化失败', e, stackTrace);
      _eventController.add(IntegrationEvent.failed(e.toString()));
      return IntegrationResult.failure('集成初始化失败: $e');
    }
  }

  /// 注册应用模块
  Future<ModuleRegistrationResult> registerAppModule({
    required String moduleId,
    required String moduleName,
    required List<ModulePermission> requiredPermissions,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      _log('info', '注册应用模块: $moduleId');

      // 1. 注册模块状态
      ModuleStateController.instance.registerModule(moduleId);

      // 2. 初始化模块文件系统
      await ModuleFileSystemService.instance.initializeModuleFileSystem(moduleId);

      // 3. 初始化模块资源监控
      ModuleResourceManager.instance.initializeResourceMonitoring(moduleId);

      // 4. 请求必要权限
      final permissionResults = await ModulePermissionManager.instance
          .grantPermissions(moduleId, requiredPermissions, reason: '模块注册');

      final failedPermissions = permissionResults
          .where((result) => !result.isSuccess)
          .map((result) => result.permission)
          .toList();

      if (failedPermissions.isNotEmpty) {
        _log('warning', '模块 $moduleId 部分权限授予失败: $failedPermissions');
      }

      _eventController.add(IntegrationEvent.moduleRegistered(moduleId));

      _log('info', '应用模块注册完成: $moduleId');
      return ModuleRegistrationResult.success(
        moduleId,
        '模块注册成功',
        failedPermissions,
      );

    } catch (e, stackTrace) {
      _log('severe', '应用模块注册失败: $moduleId', e, stackTrace);
      return ModuleRegistrationResult.failure(moduleId, '注册失败: $e');
    }
  }

  /// 卸载应用模块
  Future<ModuleUnregistrationResult> unregisterAppModule(String moduleId) async {
    try {
      _log('info', '卸载应用模块: $moduleId');

      // 1. 停止模块
      await ModuleStateController.instance.stopModule(moduleId);

      // 2. 撤销所有权限
      await ModulePermissionManager.instance.revokeAllPermissions(
        moduleId,
        reason: '模块卸载',
      );

      // 3. 停止资源监控
      ModuleResourceManager.instance.stopResourceMonitoring(moduleId);

      // 4. 清理文件系统
      await ModuleFileSystemService.instance.cleanupModuleFileSystem(moduleId);

      // 5. 注销模块状态
      ModuleStateController.instance.unregisterModule(moduleId);

      _eventController.add(IntegrationEvent.moduleUnregistered(moduleId));

      _log('info', '应用模块卸载完成: $moduleId');
      return ModuleUnregistrationResult.success(moduleId, '模块卸载成功');

    } catch (e, stackTrace) {
      _log('severe', '应用模块卸载失败: $moduleId', e, stackTrace);
      return ModuleUnregistrationResult.failure(moduleId, '卸载失败: $e');
    }
  }

  /// 获取集成状态
  IntegrationStatus getIntegrationStatus() {
    final stateController = ModuleStateController.instance;
    final resourceManager = ModuleResourceManager.instance;
    final permissionManager = ModulePermissionManager.instance;

    final moduleStates = stateController.moduleStates;
    final systemOverview = resourceManager.getSystemResourceOverview();
    final permissionStats = permissionManager.getPermissionStatistics();

    return IntegrationStatus(
      isIntegrated: _isIntegrated,
      totalModules: moduleStates.length,
      runningModules: moduleStates.values
          .where((state) => state.status == ModuleStatus.running)
          .length,
      systemResourceOverview: systemOverview,
      permissionStatistics: permissionStats,
      lastUpdated: DateTime.now(),
    );
  }

  /// 执行系统健康检查
  Future<SystemHealthReport> performSystemHealthCheck() async {
    try {
      _log('info', '执行系统健康检查');

      final healthResult = await ModuleMonitoringDashboard.instance.performHealthCheck();
      final alerts = ModuleMonitoringDashboard.instance.getSystemAlerts();
      final integrationStatus = getIntegrationStatus();

      final report = SystemHealthReport(
        overallHealth: healthResult.overallStatus,
        healthChecks: healthResult.checks,
        systemAlerts: alerts,
        integrationStatus: integrationStatus,
        timestamp: DateTime.now(),
      );

      _eventController.add(IntegrationEvent.healthCheckCompleted(report));

      _log('info', '系统健康检查完成');
      return report;

    } catch (e, stackTrace) {
      _log('severe', '系统健康检查失败', e, stackTrace);
      return SystemHealthReport.failed();
    }
  }

  /// 清理集成
  Future<void> cleanupIntegration() async {
    try {
      _log('info', '开始清理App Manager集成');

      // 停止监控仪表板
      ModuleMonitoringDashboard.instance.stopMonitoring();

      // 清理各个服务
      ModuleStateController.instance.dispose();
      ModuleResourceManager.instance.dispose();
      ModulePermissionManager.instance.dispose();
      ModuleMonitoringDashboard.instance.dispose();

      _isIntegrated = false;
      _eventController.add(IntegrationEvent.cleaned());

      _log('info', 'App Manager集成清理完成');

    } catch (e, stackTrace) {
      _log('severe', 'App Manager集成清理失败', e, stackTrace);
      rethrow;
    }
  }

  /// 设置默认权限策略
  Future<void> _setupDefaultPermissionPolicies() async {
    final permissionManager = ModulePermissionManager.instance;

    // 基础权限 - 自动允许
    permissionManager.setPermissionPolicy(ModulePermission.basic, PermissionPolicy.allow);
    permissionManager.setPermissionPolicy(ModulePermission.systemInfo, PermissionPolicy.allow);

    // 文件和网络权限 - 需要询问
    permissionManager.setPermissionPolicy(ModulePermission.fileSystemRead, PermissionPolicy.ask);
    permissionManager.setPermissionPolicy(ModulePermission.fileSystemWrite, PermissionPolicy.ask);
    permissionManager.setPermissionPolicy(ModulePermission.networkAccess, PermissionPolicy.ask);

    // 敏感权限 - 默认拒绝
    permissionManager.setPermissionPolicy(ModulePermission.admin, PermissionPolicy.deny);

    _log('info', '默认权限策略设置完成');
  }

  /// 注册系统模块
  Future<void> _registerSystemModules() async {
    // 注册 App Manager 自身作为系统模块
    const systemModuleId = 'app_manager_system';
    
    ModuleStateController.instance.registerModule(systemModuleId);
    await ModuleStateController.instance.startModule(systemModuleId);
    
    // 授予系统模块管理员权限
    await ModulePermissionManager.instance.grantPermission(
      systemModuleId,
      ModulePermission.admin,
      reason: '系统模块',
    );

    _log('info', '系统模块注册完成');
  }

  /// 日志记录
  void _log(String level, String message, [Object? error, StackTrace? stackTrace]) {
    developer.log(
      message,
      name: 'AppManagerIntegration',
      level: _getLogLevel(level),
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// 获取日志级别
  int _getLogLevel(String level) {
    switch (level) {
      case 'info':
        return 800;
      case 'warning':
        return 900;
      case 'severe':
        return 1000;
      default:
        return 700;
    }
  }

  /// 清理资源
  void dispose() {
    _eventController.close();
  }
}
