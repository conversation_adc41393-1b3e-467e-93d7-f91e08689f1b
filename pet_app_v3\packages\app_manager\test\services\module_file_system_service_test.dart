/*
---------------------------------------------------------------
File name:          module_file_system_service_test.dart
Author:             lgnorant-lu
Date created:       2025-07-24
Last modified:      2025-07-24
Dart Version:       3.2+
Description:        模块文件系统服务测试
---------------------------------------------------------------
*/

import 'package:test/test.dart';
import 'package:app_manager/src/services/module_file_system_service.dart';
import 'package:app_manager/src/models/module_file.dart';
import 'package:app_manager/src/models/file_operation_result.dart';

void main() {
  group('ModuleFileSystemService Tests', () {
    late ModuleFileSystemService fileService;

    setUp(() {
      fileService = ModuleFileSystemService.instance;
    });

    tearDown(() async {
      // 清理测试数据
      await fileService.cleanupModuleFileSystem('test_module');
    });

    group('文件创建和读取', () {
      test('应该能够创建文件', () async {
        const moduleId = 'test_module';
        const filePath = 'test.txt';
        const content = 'Hello, World!';

        final result = await fileService.createFile(
          moduleId,
          filePath,
          content,
          type: FileType.text,
        );

        expect(result.isSuccess, isTrue);
        expect(result.moduleId, equals(moduleId));
        expect(result.filePath, equals(filePath));

        final file = await fileService.getFile(moduleId, filePath);
        expect(file, isNotNull);
        expect(file!.content, equals(content));
        expect(file.type, equals(FileType.text));
      });

      test('创建已存在的文件应该失败', () async {
        const moduleId = 'test_module';
        const filePath = 'existing.txt';
        const content = 'Content';

        // 先创建文件
        await fileService.createFile(moduleId, filePath, content);

        // 再次创建应该失败
        final result =
            await fileService.createFile(moduleId, filePath, content);

        expect(result.isSuccess, isFalse);
        expect(result.error, equals(FileOperationError.fileExists));
      });

      test('读取不存在的文件应该返回null', () async {
        const moduleId = 'test_module';
        const filePath = 'non_existent.txt';

        final file = await fileService.getFile(moduleId, filePath);
        expect(file, isNull);
      });
    });

    group('文件更新和删除', () {
      test('应该能够更新文件内容', () async {
        const moduleId = 'test_module';
        const filePath = 'update_test.txt';
        const originalContent = 'Original content';
        const updatedContent = 'Updated content';

        // 创建文件
        await fileService.createFile(moduleId, filePath, originalContent);

        // 更新文件
        final result = await fileService.updateFile(
          moduleId,
          filePath,
          updatedContent,
        );

        expect(result.isSuccess, isTrue);

        final file = await fileService.getFile(moduleId, filePath);
        expect(file!.content, equals(updatedContent));
        expect(file.size, equals(updatedContent.length));
      });

      test('更新不存在的文件应该失败', () async {
        const moduleId = 'test_module';
        const filePath = 'non_existent.txt';
        const content = 'Content';

        final result =
            await fileService.updateFile(moduleId, filePath, content);

        expect(result.isSuccess, isFalse);
        expect(result.error, equals(FileOperationError.fileNotFound));
      });

      test('应该能够删除文件', () async {
        const moduleId = 'test_module';
        const filePath = 'delete_test.txt';
        const content = 'To be deleted';

        // 创建文件
        await fileService.createFile(moduleId, filePath, content);
        expect(await fileService.getFile(moduleId, filePath), isNotNull);

        // 删除文件
        final result = await fileService.deleteFile(moduleId, filePath);

        expect(result.isSuccess, isTrue);
        expect(await fileService.getFile(moduleId, filePath), isNull);
      });

      test('删除不存在的文件应该失败', () async {
        const moduleId = 'test_module';
        const filePath = 'non_existent.txt';

        final result = await fileService.deleteFile(moduleId, filePath);

        expect(result.isSuccess, isFalse);
        expect(result.error, equals(FileOperationError.fileNotFound));
      });
    });

    group('文件列表和搜索', () {
      test('应该能够列出模块文件', () async {
        const moduleId = 'test_module';

        // 创建测试文件
        await fileService.createFile(moduleId, 'file1.txt', 'Content 1');
        await fileService.createFile(moduleId, 'file2.md', 'Content 2');
        await fileService.createFile(moduleId, 'dir/file3.txt', 'Content 3');

        final files = await fileService.listFiles(moduleId);

        expect(files.length, greaterThanOrEqualTo(3));
        expect(files.any((f) => f.name == 'file1.txt'), isTrue);
        expect(files.any((f) => f.name == 'file2.md'), isTrue);
        expect(files.any((f) => f.name == 'file3.txt'), isTrue);
      });

      test('应该能够按路径过滤文件', () async {
        const moduleId = 'test_module';

        await fileService.createFile(moduleId, 'root.txt', 'Root file');
        await fileService.createFile(moduleId, 'dir/sub.txt', 'Sub file');

        final rootFiles = await fileService.listFiles(moduleId, path: '');
        final dirFiles = await fileService.listFiles(moduleId, path: 'dir');

        expect(rootFiles.any((f) => f.name == 'root.txt'), isTrue);
        expect(dirFiles.any((f) => f.name == 'sub.txt'), isTrue);
      });

      test('应该能够搜索文件', () async {
        const moduleId = 'test_module';

        await fileService.createFile(moduleId, 'test1.txt', 'Content');
        await fileService.createFile(moduleId, 'test2.md', 'Content');
        await fileService.createFile(moduleId, 'other.json', 'Content');

        final searchResults = await fileService.searchFiles(
          moduleId,
          'test',
          options: const FileSearchOptions(caseSensitive: false),
        );

        expect(searchResults.length, equals(2));
        expect(searchResults.every((f) => f.name.contains('test')), isTrue);
      });

      test('应该能够按文件类型搜索', () async {
        const moduleId = 'test_module';

        await fileService.createFile(moduleId, 'doc.txt', 'Text',
            type: FileType.text);
        await fileService.createFile(moduleId, 'readme.md', 'Markdown',
            type: FileType.markdown);
        await fileService.createFile(moduleId, 'data.json', 'JSON',
            type: FileType.json);

        final textFiles = await fileService.searchFiles(
          moduleId,
          '',
          options: const FileSearchOptions(fileTypes: [FileType.text]),
        );

        expect(textFiles.length, equals(1));
        expect(textFiles.first.type, equals(FileType.text));
      });
    });

    group('文件统计', () {
      test('应该提供准确的文件统计信息', () async {
        const moduleId = 'test_module';

        await fileService.createFile(moduleId, 'file1.txt', 'Content 1',
            type: FileType.text);
        await fileService.createFile(moduleId, 'file2.md', 'Content 2',
            type: FileType.markdown);
        await fileService.createFile(moduleId, 'file3.txt', 'Content 3',
            type: FileType.text);

        final stats = fileService.getModuleFileStats(moduleId);

        expect(stats.moduleId, equals(moduleId));
        expect(stats.totalFiles, greaterThanOrEqualTo(3));
        expect(stats.totalSize, greaterThan(0));
        expect(stats.fileTypeCount[FileType.text], greaterThanOrEqualTo(2));
        expect(stats.fileTypeCount[FileType.markdown], greaterThanOrEqualTo(1));
      });

      test('空模块应该返回空统计', () {
        const moduleId = 'empty_module';

        final stats = fileService.getModuleFileStats(moduleId);

        expect(stats.totalFiles, equals(0));
        expect(stats.totalSize, equals(0));
        expect(stats.fileTypeCount.isEmpty, isTrue);
      });
    });

    group('模块文件系统管理', () {
      test('应该能够初始化模块文件系统', () async {
        const moduleId = 'new_module';

        await fileService.initializeModuleFileSystem(moduleId);

        final files = await fileService.listFiles(moduleId);
        expect(files.isNotEmpty, isTrue);
        expect(files.any((f) => f.name == 'README.md'), isTrue);
      });

      test('应该能够清理模块文件系统', () async {
        const moduleId = 'cleanup_module';

        // 初始化并添加文件
        await fileService.initializeModuleFileSystem(moduleId);
        await fileService.createFile(moduleId, 'temp.txt', 'Temporary');

        // 清理
        await fileService.cleanupModuleFileSystem(moduleId);

        final files = await fileService.listFiles(moduleId);
        expect(files.isEmpty, isTrue);
      });
    });

    group('文件系统事件', () {
      test('应该发送文件创建事件', () async {
        const moduleId = 'test_module';
        const filePath = 'event_test.txt';

        FileSystemEvent? receivedEvent;
        final subscription = fileService.fileSystemEvents.listen((event) {
          if (event.type == FileSystemEventType.fileCreated) {
            receivedEvent = event;
          }
        });

        await fileService.createFile(moduleId, filePath, 'Content');

        await Future<void>.delayed(const Duration(milliseconds: 10));

        expect(receivedEvent, isNotNull);
        expect(receivedEvent!.moduleId, equals(moduleId));
        expect(receivedEvent!.filePath, equals(filePath));

        await subscription.cancel();
      });
    });
  });
}
