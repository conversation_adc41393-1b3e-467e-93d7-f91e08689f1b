/*
---------------------------------------------------------------
File name:          core_services_v1_model.dart
Author:             Ming Status CLI
Date created:       2025-07-14
Last modified:      2025-07-14
Dart Version:       3.2+
Description:        core_services_v1数据模型类
---------------------------------------------------------------
Change History:
    2025-07-14: Initial creation - core_services_v1数据模型类;
---------------------------------------------------------------
*/

import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'core_services_v1_model.g.dart';
import 'core_services_v1_model.freezed.dart';

/// core_services_v1数据模型
///
/// ## 使用示例
///
/// ```dart
/// final model = Core_services_v1Model(id: 1, name: '示例');
/// ```
///
/// ```dart
/// final json = model.toJson();
/// ```
///
/// ```dart
/// final fromJson = Core_services_v1Model.fromJson(json);
/// ```
///
part 'core_services_v1_model.freezed.dart';
part 'core_services_v1_model.g.dart';

@freezed
class Core_services_v1Model with _$Core_services_v1Model {
  /// 创建Core_services_v1Model实例
  const factory Core_services_v1Model({
    required int id,
    required String name,
    String? description,
    @Default('active') String status,
    Map<String, dynamic>? metadata,
    @JsonKey(name: 'created_at') DateTime? createdAt,
    @JsonKey(name: 'updated_at') DateTime? updatedAt,
  }) = _Core_services_v1Model;

  /// 从JSON创建实例
  factory Core_services_v1Model.fromJson(Map<String, dynamic> json) =>
      _$Core_services_v1ModelFromJson(json);
}

/// core_services_v1列表响应模型
///
@freezed
class Core_services_v1ModelListResponse with _$Core_services_v1ModelListResponse {
  /// 创建Core_services_v1ModelListResponse实例
  const factory Core_services_v1ModelListResponse({
    required List<Core_services_v1Model> data,
    required int total,
    required int page,
    @JsonKey(name: 'per_page') required int perPage,
    @JsonKey(name: 'total_pages') required int totalPages,
  }) = _Core_services_v1ModelListResponse;

  /// 从JSON创建实例
  factory Core_services_v1ModelListResponse.fromJson(Map<String, dynamic> json) =>
      _$Core_services_v1ModelListResponseFromJson(json);
}

/// core_services_v1创建请求模型
///
class Core_services_v1ModelCreateRequest extends Equatable {
  /// 名称
  final String name;

  /// 描述
  final String? description;

  /// 元数据
  final Map<String, dynamic>? metadata;

  /// 创建Core_services_v1ModelCreateRequest实例
  const Core_services_v1ModelCreateRequest({
    required this.name,
    this.description,
    this.metadata,
  });

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      if (description != null) 'description': description,
      if (metadata != null) 'metadata': metadata,
    };
  }

  @override
  List<Object?> get props => [name, description, metadata];
}

/// core_services_v1更新请求模型
///
class Core_services_v1ModelUpdateRequest extends Equatable {
  /// 名称
  final String? name;

  /// 描述
  final String? description;

  /// 状态
  final String? status;

  /// 元数据
  final Map<String, dynamic>? metadata;

  /// 创建Core_services_v1ModelUpdateRequest实例
  const Core_services_v1ModelUpdateRequest({
    this.name,
    this.description,
    this.status,
    this.metadata,
  });

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (name != null) json['name'] = name;
    if (description != null) json['description'] = description;
    if (status != null) json['status'] = status;
    if (metadata != null) json['metadata'] = metadata;
    return json;
  }

  @override
  List<Object?> get props => [name, description, status, metadata];
}

