import 'package:flutter_test/flutter_test.dart';
import 'package:creative_workshop/src/core/workshop_manager.dart';

void main() {
  group('WorkshopManager热重载集成测试', () {
    late WorkshopManager workshopManager;

    setUp(() {
      workshopManager = WorkshopManager.instance;
    });

    test('WorkshopManager应该能够初始化', () async {
      // 测试基本初始化
      expect(workshopManager.state, WorkshopState.uninitialized);

      // 初始化应该成功
      final result = await workshopManager.initialize();
      expect(result, isTrue);
      expect(workshopManager.state, WorkshopState.ready);
    });

    test('WorkshopManager应该能够启动', () async {
      // 先初始化
      await workshopManager.initialize();

      // 启动应该成功
      final result = await workshopManager.start();
      expect(result, isTrue);
      expect(workshopManager.state, WorkshopState.running);
    });

    test('WorkshopManager应该包含开发环境初始化', () async {
      // 这个测试验证开发环境初始化方法存在
      // 实际的热重载功能需要真实的项目路径才能测试

      // 初始化应该包含开发环境设置
      final result = await workshopManager.initialize();
      expect(result, isTrue);

      // 验证状态正确（可能是ready或running）
      expect(workshopManager.state,
          anyOf(WorkshopState.ready, WorkshopState.running));
    });
  });
}
