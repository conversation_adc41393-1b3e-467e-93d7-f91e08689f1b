name: {{plugin_name}}
description: {{description}}
version: {{version}}
{{#repository_url}}homepage: "{{repository_url}}"{{/repository_url}}{{^repository_url}}homepage: "https://github.com/{{author}}/{{plugin_name}}"{{/repository_url}}

environment:
  sdk: '{{dart_version}}'
  flutter: ">=3.0.0"

dependencies:
  flutter:
    sdk: flutter

  # Pet App V3 插件系统
  plugin_system:
    path: ../../packages/plugin_system

  # 核心依赖
  meta: ^1.9.0

  # 状态管理（可选）
  {{#include_state_management}}
  flutter_bloc: ^8.1.0
  provider: ^6.0.0
  {{/include_state_management}}

  # HTTP网络（可选）
  {{#include_http}}
  http: ^1.1.0
  dio: ^5.3.0
  {{/include_http}}

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  test: ^1.24.0

# Flutter 配置
flutter:
  # 插件配置
  plugin:
    platforms:
      android:
        package: com.example.{{plugin_name}}
        pluginClass: {{plugin_name.pascalCase()}}Plugin
      ios:
        pluginClass: {{plugin_name.pascalCase()}}Plugin
      web:
        pluginClass: {{plugin_name.pascalCase()}}Plugin
        fileName: {{plugin_name}}_web.dart
      windows:
        pluginClass: {{plugin_name.pascalCase()}}Plugin
      macos:
        pluginClass: {{plugin_name.pascalCase()}}Plugin
      linux:
        pluginClass: {{plugin_name.pascalCase()}}Plugin

{{#include_ui_components}}
  # UI 资源（请创建对应目录）
  # assets:
  #   - assets/images/
  #   - assets/icons/
{{/include_ui_components}}

{{#include_l10n}}
  # 国际化配置
  generate: true
{{/include_l10n}}
