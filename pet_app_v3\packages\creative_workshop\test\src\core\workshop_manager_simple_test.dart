import 'package:test/test.dart';
import 'package:creative_workshop/creative_workshop.dart';
import 'package:plugin_system/plugin_system.dart' as plugin_sys;

void main() {
  group('WorkshopManager简单集成测试', () {
    test('应该能够初始化WorkshopManager', () async {
      final workshopManager = WorkshopManager.instance;
      expect(workshopManager, isNotNull);
      
      // 验证初始状态
      expect(workshopManager.state, equals(WorkshopState.uninitialized));
    });

    test('应该能够访问Plugin System', () async {
      final pluginSystemRegistry = plugin_sys.PluginRegistry.instance;
      expect(pluginSystemRegistry, isNotNull);
      
      // 验证基本功能可用
      expect(() => pluginSystemRegistry.contains('test'), returnsNormally);
    });

    test('应该能够完成初始化流程', () async {
      final workshopManager = WorkshopManager.instance;
      
      // 执行初始化
      final result = await workshopManager.initialize();
      expect(result, isTrue, reason: '初始化应该成功');
      
      // 验证状态变化
      expect(workshopManager.state, equals(WorkshopState.ready));
    });

    test('应该能够启动WorkshopManager', () async {
      final workshopManager = WorkshopManager.instance;
      
      // 确保已初始化
      await workshopManager.initialize();
      
      // 执行启动
      final result = await workshopManager.start();
      expect(result, isTrue, reason: '启动应该成功');
      
      // 验证状态变化
      expect(workshopManager.state, equals(WorkshopState.running));
    });

    test('应该能够注册内置插件', () async {
      final workshopManager = WorkshopManager.instance;
      final pluginSystemRegistry = plugin_sys.PluginRegistry.instance;
      
      // 初始化（这会触发内置插件注册）
      await workshopManager.initialize();
      
      // 验证内置插件是否已注册
      final expectedPlugins = [
        'builtin_brush_tool',
        'builtin_text_editor',
        'builtin_image_viewer',
      ];
      
      for (final pluginId in expectedPlugins) {
        expect(pluginSystemRegistry.contains(pluginId), isTrue,
               reason: '内置插件应该已注册: $pluginId');
      }
    });

    test('应该能够获取内置插件信息', () async {
      final workshopManager = WorkshopManager.instance;
      final pluginSystemRegistry = plugin_sys.PluginRegistry.instance;
      
      await workshopManager.initialize();
      
      const pluginId = 'builtin_brush_tool';
      expect(pluginSystemRegistry.contains(pluginId), isTrue);
      
      final metadata = pluginSystemRegistry.getMetadata(pluginId);
      expect(metadata, isNotNull);
      expect(metadata!.name, equals('内置画笔工具'));
      expect(metadata.version, equals('1.0.0'));
      
      final plugin = pluginSystemRegistry.get(pluginId);
      expect(plugin, isNotNull);
      expect(plugin!.id, equals(pluginId));
    });

    test('应该能够与内置插件通信', () async {
      final workshopManager = WorkshopManager.instance;
      final pluginSystemRegistry = plugin_sys.PluginRegistry.instance;
      
      await workshopManager.initialize();
      
      const pluginId = 'builtin_brush_tool';
      final plugin = pluginSystemRegistry.get(pluginId);
      expect(plugin, isNotNull);
      
      // 测试ping消息
      final response = await plugin!.handleMessage('ping', <String, dynamic>{});
      expect(response, isA<Map<String, dynamic>>());
      expect(response['status'], equals('pong'));
    });

    test('验证完整的初始化和启动流程', () async {
      final workshopManager = WorkshopManager.instance;
      final pluginSystemRegistry = plugin_sys.PluginRegistry.instance;
      
      // 1. 初始化
      final initResult = await workshopManager.initialize();
      expect(initResult, isTrue);
      expect(workshopManager.state, equals(WorkshopState.ready));
      
      // 2. 启动
      final startResult = await workshopManager.start();
      expect(startResult, isTrue);
      expect(workshopManager.state, equals(WorkshopState.running));
      
      // 3. 验证内置插件已注册并可用
      const pluginId = 'builtin_text_editor';
      expect(pluginSystemRegistry.contains(pluginId), isTrue);
      
      final plugin = pluginSystemRegistry.get(pluginId);
      expect(plugin, isNotNull);
      
      final state = pluginSystemRegistry.getState(pluginId);
      expect(state, equals(plugin_sys.PluginState.initialized));
      
      // 4. 验证插件可以响应消息
      final response = await plugin!.handleMessage('getInfo', <String, dynamic>{});
      expect(response['id'], equals(pluginId));
      expect(response['name'], equals('内置文本编辑器'));
    });
  });
}
