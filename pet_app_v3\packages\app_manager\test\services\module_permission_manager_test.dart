/*
---------------------------------------------------------------
File name:          module_permission_manager_test.dart
Author:             lgnorant-lu
Date created:       2025-07-24
Last modified:      2025-07-24
Dart Version:       3.2+
Description:        模块权限管理器测试
---------------------------------------------------------------
*/

import 'package:test/test.dart';
import 'package:app_manager/src/services/module_permission_manager.dart';
import 'package:app_manager/src/models/module_permission.dart';

void main() {
  group('ModulePermissionManager Tests', () {
    late ModulePermissionManager permissionManager;

    setUp(() {
      permissionManager = ModulePermissionManager.instance;
    });

    tearDown(() async {
      // 清理测试数据
      await permissionManager.revokeAllPermissions('test_module');
    });

    group('权限授予和撤销', () {
      test('应该能够授予权限', () async {
        const moduleId = 'test_module';
        const permission = ModulePermission.fileSystemRead;

        final result = await permissionManager.grantPermission(
          moduleId,
          permission,
          reason: '测试权限授予',
        );

        expect(result.isSuccess, isTrue);
        expect(result.moduleId, equals(moduleId));
        expect(result.permission, equals(permission));
        expect(permissionManager.hasPermission(moduleId, permission), isTrue);
      });

      test('应该能够撤销权限', () async {
        const moduleId = 'test_module';
        const permission = ModulePermission.fileSystemRead;

        // 先授予权限
        await permissionManager.grantPermission(moduleId, permission);
        expect(permissionManager.hasPermission(moduleId, permission), isTrue);

        // 撤销权限
        final result = await permissionManager.revokePermission(
          moduleId,
          permission,
          reason: '测试权限撤销',
        );

        expect(result.isSuccess, isTrue);
        expect(permissionManager.hasPermission(moduleId, permission), isFalse);
      });

      test('撤销不存在的权限应该失败', () async {
        const moduleId = 'test_module';
        const permission = ModulePermission.fileSystemRead;

        final result = await permissionManager.revokePermission(
          moduleId,
          permission,
        );

        expect(result.isSuccess, isFalse);
        expect(
            result.error, equals(PermissionOperationError.permissionNotFound));
      });
    });

    group('批量权限操作', () {
      test('应该能够批量授予权限', () async {
        const moduleId = 'test_module';
        const permissions = [
          ModulePermission.fileSystemRead,
          ModulePermission.networkAccess,
          ModulePermission.systemInfo,
        ];

        final results = await permissionManager.grantPermissions(
          moduleId,
          permissions,
          reason: '批量权限测试',
        );

        expect(results.length, equals(permissions.length));
        expect(results.every((r) => r.isSuccess), isTrue);

        for (final permission in permissions) {
          expect(permissionManager.hasPermission(moduleId, permission), isTrue);
        }
      });

      test('应该能够批量撤销权限', () async {
        const moduleId = 'test_module';
        const permissions = [
          ModulePermission.fileSystemRead,
          ModulePermission.networkAccess,
        ];

        // 先授予权限
        await permissionManager.grantPermissions(moduleId, permissions);

        // 批量撤销
        final results = await permissionManager.revokePermissions(
          moduleId,
          permissions,
          reason: '批量撤销测试',
        );

        expect(results.length, equals(permissions.length));
        expect(results.every((r) => r.isSuccess), isTrue);

        for (final permission in permissions) {
          expect(
              permissionManager.hasPermission(moduleId, permission), isFalse);
        }
      });
    });

    group('权限策略', () {
      test('应该能够设置和获取权限策略', () {
        const permission = ModulePermission.admin;
        const policy = PermissionPolicy.deny;

        permissionManager.setPermissionPolicy(permission, policy);
        final retrievedPolicy =
            permissionManager.getPermissionPolicy(permission);

        expect(retrievedPolicy, equals(policy));
      });

      test('拒绝策略应该阻止权限授予', () async {
        const moduleId = 'test_module';
        const permission = ModulePermission.admin;

        // 设置拒绝策略
        permissionManager.setPermissionPolicy(
            permission, PermissionPolicy.deny);

        final result =
            await permissionManager.grantPermission(moduleId, permission);

        expect(result.isSuccess, isFalse);
        expect(result.error, equals(PermissionOperationError.policyDenied));
      });
    });

    group('审计日志', () {
      test('应该记录权限操作的审计日志', () async {
        const moduleId = 'test_module';
        const permission = ModulePermission.fileSystemRead;

        await permissionManager.grantPermission(
          moduleId,
          permission,
          reason: '测试审计日志',
        );

        final logs = permissionManager.getAuditLogs(moduleId: moduleId);
        expect(logs.isNotEmpty, isTrue);

        final log = logs.first;
        expect(log.moduleId, equals(moduleId));
        expect(log.permission, equals(permission));
        expect(log.action, equals(PermissionAction.granted));
        expect(log.reason, equals('测试审计日志'));
      });

      test('应该能够按时间范围过滤审计日志', () async {
        const moduleId = 'test_module';
        const permission = ModulePermission.fileSystemRead;

        final startTime = DateTime.now();
        await Future<void>.delayed(const Duration(milliseconds: 10));

        await permissionManager.grantPermission(moduleId, permission);

        await Future<void>.delayed(const Duration(milliseconds: 10));
        final endTime = DateTime.now();

        final logs = permissionManager.getAuditLogs(
          moduleId: moduleId,
          startTime: startTime,
          endTime: endTime,
        );

        expect(logs.isNotEmpty, isTrue);
        expect(logs.first.timestamp.isAfter(startTime), isTrue);
        expect(logs.first.timestamp.isBefore(endTime), isTrue);
      });
    });

    group('权限统计', () {
      test('应该提供准确的权限统计信息', () async {
        const moduleId1 = 'test_module_1';
        const moduleId2 = 'test_module_2';

        await permissionManager.grantPermission(
            moduleId1, ModulePermission.fileSystemRead);
        await permissionManager.grantPermission(
            moduleId1, ModulePermission.networkAccess);
        await permissionManager.grantPermission(
            moduleId2, ModulePermission.fileSystemRead);

        final stats = permissionManager.getPermissionStatistics();

        expect(stats.totalModules, equals(2));
        expect(stats.totalPermissions, equals(3));
        expect(
            stats.permissionCounts[ModulePermission.fileSystemRead], equals(2));
        expect(
            stats.permissionCounts[ModulePermission.networkAccess], equals(1));
      });
    });

    group('权限事件', () {
      test('应该发送权限授予事件', () async {
        const moduleId = 'test_module';
        const permission = ModulePermission.fileSystemRead;

        PermissionEvent? receivedEvent;
        final subscription = permissionManager.permissionEvents.listen((event) {
          receivedEvent = event;
        });

        await permissionManager.grantPermission(moduleId, permission);

        await Future<void>.delayed(const Duration(milliseconds: 10));

        expect(receivedEvent, isNotNull);
        expect(receivedEvent!.moduleId, equals(moduleId));
        expect(receivedEvent!.type, equals(PermissionEventType.granted));
        expect(receivedEvent!.permission, equals(permission));

        await subscription.cancel();
      });
    });
  });
}
