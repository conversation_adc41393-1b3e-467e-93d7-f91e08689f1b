import 'package:test/test.dart';
import 'package:creative_workshop/src/services/local_ming_cli_service.dart';

void main() {
  group('插件管理器集成测试', () {
    late LocalMingCliService service;

    setUp(() {
      service = LocalMingCliService.instance;
    });

    test('应该能够初始化插件安装服务', () async {
      await service.initialize();
      
      // 验证服务已初始化
      expect(service.isCliAvailable, isA<bool>());
      
      // 验证插件安装服务可用
      // 这里我们通过间接方式测试，因为_pluginInstallationService是私有的
      expect(service, isNotNull);
    });

    test('应该能够处理插件生成通知', () async {
      await service.initialize();
      
      // 创建一个模拟的插件生成通知
      // 注意：这里我们无法直接测试私有方法，但可以通过执行create命令来间接测试
      final result = await service.executeCommand('create test_plugin_integration');
      
      expect(result, isNotNull);
      expect(result.command, contains('create test_plugin_integration'));
      expect(result.success, isA<bool>());
    });

    test('应该能够提取插件名称', () {
      // 测试插件名称提取逻辑
      final testCommands = [
        'create my_awesome_plugin',
        'create another_plugin --type tool',
        'create test_plugin_123',
        'template create simple_plugin',
      ];
      
      for (final command in testCommands) {
        // 这里我们无法直接测试私有方法，但可以验证命令格式
        expect(command, contains('create'));
        
        final parts = command.split(' ');
        String pluginName = 'default_plugin';
        
        for (int i = 0; i < parts.length - 1; i++) {
          if (parts[i] == 'create') {
            pluginName = parts[i + 1];
            break;
          }
        }
        
        expect(pluginName, isNotEmpty);
        expect(pluginName, isNot(equals('default_plugin')));
        print('从命令 "$command" 提取插件名: $pluginName');
      }
    });

    test('应该能够生成插件ID', () {
      // 测试插件ID生成逻辑
      final testNames = [
        'My-Awesome_Plugin123',
        'simple plugin',
        'Test@Plugin#Name',
        'normal_plugin_name',
      ];
      
      for (final name in testNames) {
        // 模拟插件ID生成逻辑
        final pluginId = name.toLowerCase().replaceAll(RegExp(r'[^a-z0-9_]'), '_');
        expect(pluginId, matches(RegExp(r'^[a-z0-9_]+$')));
        print('插件名 "$name" 生成ID: $pluginId');
      }
    });

    test('应该能够检测插件生成命令', () {
      final testCommands = [
        'create my_plugin',
        'create another_plugin --type tool',
        'build project',
        'test coverage',
        'template create workspace_plugin',
      ];
      
      for (final command in testCommands) {
        final isPluginCommand = command.contains('create') && 
                               !command.contains('build') && 
                               !command.contains('test');
        
        if (command.contains('create')) {
          expect(isPluginCommand, isTrue);
          print('命令 "$command" 是插件生成命令: $isPluginCommand');
        }
      }
    });

    test('应该能够处理插件安装服务错误', () async {
      await service.initialize();
      
      // 测试当插件安装服务不可用时的降级处理
      // 这里我们通过执行一个可能失败的命令来测试错误处理
      final result = await service.executeCommand('create invalid_plugin_name_with_special_chars@#$');
      
      expect(result, isNotNull);
      expect(result.success, isA<bool>());
      
      // 无论成功还是失败，都应该有合理的响应
      expect(result.command, isNotEmpty);
    });

    test('应该能够处理企业级插件安装服务集成', () async {
      await service.initialize();
      
      // 测试企业级插件安装服务的集成
      // 这里我们通过执行一个标准的插件创建命令来测试
      final result = await service.executeCommand('create enterprise_test_plugin');
      
      expect(result, isNotNull);
      expect(result.command, contains('create enterprise_test_plugin'));
      
      // 验证命令执行结果
      if (result.success) {
        print('企业级插件创建成功');
      } else {
        print('企业级插件创建失败，但错误处理正常');
      }
    });

    test('应该能够处理插件元数据', () {
      // 测试插件元数据处理
      final testMetadata = {
        'name': 'test_plugin',
        'generated_by': 'ming_cli',
        'project_path': './test_plugin',
        'template_type': 'basic',
        'generated_at': DateTime.now().toIso8601String(),
      };
      
      expect(testMetadata['name'], isNotEmpty);
      expect(testMetadata['generated_by'], equals('ming_cli'));
      expect(testMetadata['project_path'], isNotEmpty);
      expect(testMetadata['template_type'], isNotEmpty);
      expect(testMetadata['generated_at'], isNotEmpty);
      
      // 验证时间戳格式
      final timestamp = DateTime.tryParse(testMetadata['generated_at']!);
      expect(timestamp, isNotNull);
      expect(timestamp!.isBefore(DateTime.now().add(const Duration(seconds: 1))), isTrue);
    });

    test('应该能够处理插件消息系统', () async {
      await service.initialize();
      
      // 测试插件消息系统的基础功能
      // 这里我们通过执行命令来间接测试消息系统
      final result = await service.executeCommand('--version');
      
      expect(result, isNotNull);
      expect(result.success, isA<bool>());
      
      // 验证消息系统不会阻止正常的命令执行
      expect(result.executionTime, isNotNull);
      expect(result.executionTime!.inMilliseconds, greaterThan(0));
    });

    test('应该能够处理并发插件操作', () async {
      await service.initialize();
      
      // 测试并发插件操作
      final futures = <Future>[];
      
      for (int i = 0; i < 3; i++) {
        futures.add(service.executeCommand('create concurrent_plugin_$i'));
      }
      
      final results = await Future.wait(futures);
      
      expect(results.length, equals(3));
      for (final result in results) {
        expect(result, isNotNull);
        expect(result.command, contains('create concurrent_plugin_'));
      }
    });

    test('应该能够维护插件操作历史', () async {
      await service.initialize();
      
      // 执行几个插件相关的命令
      await service.executeCommand('create history_test_plugin_1');
      await service.executeCommand('create history_test_plugin_2');
      await service.executeCommand('--version');
      
      final history = service.commandHistory;
      expect(history, isNotEmpty);
      expect(history.length, lessThanOrEqualTo(3));
      
      // 验证历史记录包含我们执行的命令
      final commandTexts = history.map((h) => h.command).toList();
      expect(commandTexts.any((cmd) => cmd.contains('--version')), isTrue);
    });
  });
}
