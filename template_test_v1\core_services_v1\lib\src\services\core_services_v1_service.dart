/*
---------------------------------------------------------------
File name:          core_services_v1_service.dart
Author:             Ming Status CLI
Date created:       2025-07-14
Last modified:      2025-07-14
Dart Version:       3.2+
Description:        core_services_v1业务逻辑服务类
---------------------------------------------------------------
Change History:
    2025-07-14: Initial creation - core_services_v1业务逻辑服务类;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';

import '../models/core_services_v1_model.dart';
import '../repositories/core_services_v1_repository.dart';
import '../utils/logger.dart';
import '../utils/validators.dart';

/// core_services_v1业务逻辑服务
///
/// ## 使用示例
///
/// ```dart
/// final service = CoreServicesV1Service();
/// ```
///
/// ```dart
/// await service.initialize();
/// ```
///
/// ```dart
/// final data = await service.fetchData();
/// ```
///
/// ## 相关类
///
/// * [Repository]
/// * [Provider]
///
class CoreServicesV1Service {
  /// 是否已初始化
  bool _isInitialized = false;

  /// HTTP客户端
  late final Dio _dio;

  /// 基础URL
  static const String _baseUrl = 'https://api.example.com';

  /// 数据仓库
  late final Core_services_v1Repository _repository;

  /// 缓存过期时间（分钟）
  static const int _cacheExpirationMinutes = 30;

  /// 缓存数据
  final Map<String, _CacheEntry> _cache = {};

  /// 获取初始化状态
  bool get isInitialized => _isInitialized;

  /// 创建CoreServicesV1Service实例
  CoreServicesV1Service({
    Core_services_v1Repository? repository,
  }) {
    _repository = repository ?? Core_services_v1Repository();
  }

  /// 初始化服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    // 初始化Dio客户端
    _dio = Dio(BaseOptions(
      baseUrl: _baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // 初始化数据仓库
    await _repository.initialize();

    _isInitialized = true;
  }

  /// 获取数据列表
  Future<List<Map<String, dynamic>>> fetchData({
    int page = 1,
    int limit = 20,
    bool useCache = true,
  }) async {
    _ensureInitialized();

    final cacheKey = 'data_${page}_$limit';
    
    // 检查缓存
    if (useCache && _cache.containsKey(cacheKey)) {
      final entry = _cache[cacheKey]!;
      if (!entry.isExpired) {
        return List<Map<String, dynamic>>.from(entry.data);
      }
    }

    try {
      final response = await _dio.get('/data', queryParameters: {
        'page': page,
        'limit': limit,
      });

      if (response.statusCode == 200) {
        final data = List<Map<String, dynamic>>.from(response.data['data'] ?? []);

        // 缓存数据
        _cache[cacheKey] = _CacheEntry(data);

        return data;
      } else {
        throw CoreServicesV1ServiceException(
          'Failed to fetch data: HTTP ${response.statusCode}',
        );
      }
    } catch (e) {
      throw CoreServicesV1ServiceException(
        'Error fetching data: $e',
      );
    }
  }

  /// 根据ID获取单个数据项
  Future<Map<String, dynamic>?> fetchById(int id) async {
    _ensureInitialized();

    try {
      final response = await _dio.get('/data/$id');
      
      if (response.statusCode == 200) {
        return Map<String, dynamic>.from(response.data);
      } else if (response.statusCode == 404) {
        return null;
      } else {
        throw CoreServicesV1ServiceException(
          'Failed to fetch item: HTTP ${response.statusCode}',
        );
      }
    } catch (e) {
      throw CoreServicesV1ServiceException(
        'Error fetching item: $e',
      );
    }
  }

  /// 创建新数据项
  Future<Map<String, dynamic>> createData(Map<String, dynamic> data) async {
    _ensureInitialized();

    try {
      final response = await _dio.post('/data', data: data);
      
      if (response.statusCode == 201) {
        final createdData = Map<String, dynamic>.from(response.data);
        
        // 清除相关缓存
        _clearDataCache();
        
        return createdData;
      } else {
        throw CoreServicesV1ServiceException(
          'Failed to create data: HTTP ${response.statusCode}',
        );
      }
    } catch (e) {
      throw CoreServicesV1ServiceException(
        'Error creating data: $e',
      );
    }
  }

  /// 确保服务已初始化
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('Service not initialized. Call initialize() first.');
    }
  }

  /// 清除数据缓存
  void _clearDataCache() {
    _cache.removeWhere((key, value) => key.startsWith('data_'));
  }

  /// 清除所有缓存
  void clearCache() {
    _cache.clear();
  }

  /// 清理资源
  void dispose() {
    _cache.clear();
    _repository.dispose();
    _isInitialized = false;
  }

}

/// core_services_v1服务异常
class CoreServicesV1ServiceException implements Exception {
  /// 错误消息
  final String message;

  /// 创建异常实例
  const CoreServicesV1ServiceException(this.message);

  @override
  String toString() => 'CoreServicesV1ServiceException: $message';
}

/// 缓存条目
class _CacheEntry {
  /// 缓存数据
  final dynamic data;

  /// 创建时间
  final DateTime createdAt;

  /// 创建缓存条目
  _CacheEntry(this.data) : createdAt = DateTime.now();

  /// 是否已过期
  bool get isExpired {
    final expirationTime = createdAt.add(
      const Duration(minutes: CoreServicesV1Service._cacheExpirationMinutes),
    );
    return DateTime.now().isAfter(expirationTime);
  }
}
