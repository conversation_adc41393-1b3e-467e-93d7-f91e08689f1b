# Changelog

All notable changes to the Basic Module Template will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2025-06-30

### Added
- Initial release of Basic Module Template
- Standard Dart project directory structure
- Configurable template variables:
  - `module_name`: Module name configuration
  - `description`: Module description
  - `author`: Author name
  - `dart_version`: Dart version constraint
  - `license`: Open source license
  - `use_analysis`: Code analysis configuration option
  - `create_example`: Example file creation option
- Pre/post generation hooks
- Comprehensive documentation
- Support for lib/, test/, example/, and doc/ directories
- Template validation and metadata

### Features
- Mason-compatible brick.yaml configuration
- Flexible variable system with defaults and prompts
- Organized directory structure following Dart conventions
- Built-in support for code analysis configuration
- Optional example file generation
- Comprehensive README and documentation templates

### Technical Details
- Minimum Dart version: 3.0.0
- Compatible with Ming Status CLI v1.0.0
- Mason template format compliance
- Cross-platform directory structure support

---

*For more information about changes, please refer to the project repository.* 