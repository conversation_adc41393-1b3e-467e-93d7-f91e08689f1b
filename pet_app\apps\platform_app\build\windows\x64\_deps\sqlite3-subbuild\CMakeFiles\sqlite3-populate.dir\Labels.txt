# Target labels
 sqlite3-populate
# Source files and their labels
D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64/_deps/sqlite3-subbuild/CMakeFiles/sqlite3-populate
D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64/_deps/sqlite3-subbuild/CMakeFiles/bf1276aec6cf297f89dacba8e4911e40/sqlite3-populate.rule
D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64/_deps/sqlite3-subbuild/CMakeFiles/89a5df0b5782a6f2247cc532dd54e970/sqlite3-populate-complete.rule
D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64/_deps/sqlite3-subbuild/CMakeFiles/96a302a2fdf7e66bffa3f2418cad6511/sqlite3-populate-build.rule
D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64/_deps/sqlite3-subbuild/CMakeFiles/96a302a2fdf7e66bffa3f2418cad6511/sqlite3-populate-configure.rule
D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64/_deps/sqlite3-subbuild/CMakeFiles/96a302a2fdf7e66bffa3f2418cad6511/sqlite3-populate-download.rule
D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64/_deps/sqlite3-subbuild/CMakeFiles/96a302a2fdf7e66bffa3f2418cad6511/sqlite3-populate-install.rule
D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64/_deps/sqlite3-subbuild/CMakeFiles/96a302a2fdf7e66bffa3f2418cad6511/sqlite3-populate-mkdir.rule
D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64/_deps/sqlite3-subbuild/CMakeFiles/96a302a2fdf7e66bffa3f2418cad6511/sqlite3-populate-patch.rule
D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64/_deps/sqlite3-subbuild/CMakeFiles/96a302a2fdf7e66bffa3f2418cad6511/sqlite3-populate-test.rule
D:/狗py/pythonProject/CRUD/Tasks_organizing/pet_app/apps/platform_app/build/windows/x64/_deps/sqlite3-subbuild/CMakeFiles/96a302a2fdf7e66bffa3f2418cad6511/sqlite3-populate-update.rule
