/*
---------------------------------------------------------------
File name:          flutter_gen_config_generator.dart
Author:             lgnorant-lu
Date created:       2025/07/12
Last modified:      2025/07/12
Dart Version:       3.2+
Description:        flutter_gen.yaml配置文件生成器 
                      (Flutter Gen Configuration Generator)
---------------------------------------------------------------
Change History:
    2025/07/12: Extracted from template_scaffold.dart - 模块化重构;
---------------------------------------------------------------
TODO:
    - [ ] 添加更多资源类型支持
    - [ ] 优化代码生成性能
    - [ ] 添加自定义资源处理器
---------------------------------------------------------------
*/

import 'package:ming_status_cli/src/core/template_creator/config/index.dart';
import 'package:ming_status_cli/src/core/template_creator/generators/config/config_generator_base.dart';

/// flutter_gen.yaml配置文件生成器
///
/// 负责生成Flutter资源代码生成配置文件
class FlutterGenConfigGenerator extends ConfigGeneratorBase {
  /// 创建flutter_gen.yaml生成器实例
  const FlutterGenConfigGenerator();

  @override
  String getFileName() => 'flutter_gen.yaml';

  @override
  String generateContent(ScaffoldConfig config) {
    final buffer = StringBuffer()
      ..writeln('# Flutter Gen资源代码生成配置')
      ..writeln('# 更多信息: https://pub.dev/packages/flutter_gen')
      ..writeln()
      ..writeln('# 输出配置')
      ..writeln('output: lib/generated/')
      ..writeln()
      ..writeln('# 代码生成配置')
      ..writeln('flutter_gen:')
      ..writeln('  # 输出目录')
      ..writeln('  output: lib/generated/assets.gen.dart')
      ..writeln('  ')
      ..writeln('  # 代码格式')
      ..writeln('  line_length: 80')
      ..writeln('  ')
      ..writeln('  # Null Safety')
      ..writeln('  null_safety: true')
      ..writeln('  ')
      ..writeln('  # 生成文档注释')
      ..writeln('  generate_docs: ${config.includeDocumentation}')
      ..writeln()
      ..writeln('  # === 资源配置 ===')
      ..writeln('  ')
      ..writeln('  # 图片资源')
      ..writeln('  assets:')
      ..writeln('    enabled: true')
      ..writeln('    package_parameter_enabled: true')
      ..writeln('    style: dot-delimiter')
      ..writeln('    ')
      ..writeln('    # 输出配置')
      ..writeln('    outputs:')
      ..writeln('      # 生成类名')
      ..writeln('      class_name: Assets')
      ..writeln('      # 生成文件名')
      ..writeln('      file_name: assets.gen.dart')
      ..writeln('      # 生成目录')
      ..writeln('      directory_name: generated')
      ..writeln('    ')
      ..writeln('    # 排除文件')
      ..writeln('    exclude:')
      ..writeln('      - assets/images/.gitkeep')
      ..writeln('      - assets/icons/.gitkeep')
      ..writeln('      - assets/fonts/.gitkeep')
      ..writeln()
      ..writeln('  # 字体资源')
      ..writeln('  fonts:')
      ..writeln('    enabled: true')
      ..writeln('    ')
      ..writeln('    # 输出配置')
      ..writeln('    outputs:')
      ..writeln('      class_name: FontFamily')
      ..writeln('      file_name: fonts.gen.dart')
      ..writeln()
      ..writeln('  # 颜色资源')
      ..writeln('  colors:')
      ..writeln('    enabled: true')
      ..writeln('    ')
      ..writeln('    # 输入文件')
      ..writeln('    inputs:')
      ..writeln('      - assets/colors/colors.xml')
      ..writeln('      - assets/colors/material_colors.xml')
      ..writeln('      - assets/colors/brand_colors.json')
      ..writeln('    ')
      ..writeln('    # 输出配置')
      ..writeln('    outputs:')
      ..writeln('      class_name: ColorName')
      ..writeln('      file_name: colors.gen.dart')
      ..writeln()
      ..writeln('# === 第三方库集成 ===')
      ..writeln()
      ..writeln('# Flutter SVG集成')
      ..writeln('flutter_svg:')
      ..writeln('  enabled: true')
      ..writeln('  # 生成SvgPicture.asset()方法')
      ..writeln('  generate_for_assets: true')
      ..writeln()
      ..writeln('# Flare集成')
      ..writeln('flare_flutter:')
      ..writeln('  enabled: false')
      ..writeln('  # 生成FlareActor()方法')
      ..writeln('  generate_for_assets: false')
      ..writeln()
      ..writeln('# Rive集成')
      ..writeln('rive:')
      ..writeln('  enabled: false')
      ..writeln('  # 生成RiveAnimation.asset()方法')
      ..writeln('  generate_for_assets: false')
      ..writeln()
      ..writeln('# Lottie集成')
      ..writeln('lottie:')
      ..writeln('  enabled: false')
      ..writeln('  # 生成Lottie.asset()方法')
      ..writeln('  generate_for_assets: false')
      ..writeln()
      ..writeln('# Cached Network Image集成')
      ..writeln('cached_network_image:')
      ..writeln('  enabled: true')
      ..writeln('  # 生成CachedNetworkImage()方法')
      ..writeln('  generate_for_network: true')
      ..writeln()
      ..writeln('# === 高级配置 ===')
      ..writeln()
      ..writeln('# 性能优化')
      ..writeln('performance:')
      ..writeln('  # 启用懒加载')
      ..writeln('  lazy_loading: true')
      ..writeln('  ')
      ..writeln('  # 缓存策略')
      ..writeln('  cache:')
      ..writeln('    enabled: true')
      ..writeln('    max_size: 100')
      ..writeln('    ttl: 3600')
      ..writeln('  ')
      ..writeln('  # 预加载配置')
      ..writeln('  preload:')
      ..writeln('    enabled: false')
      ..writeln('    critical_assets:')
      ..writeln('      - assets/images/logo.png')
      ..writeln('      - assets/images/splash.png')
      ..writeln()
      ..writeln('# 代码生成选项')
      ..writeln('code_generation:')
      ..writeln('  # 生成常量')
      ..writeln('  generate_constants: true')
      ..writeln('  ')
      ..writeln('  # 生成扩展方法')
      ..writeln('  generate_extensions: true')
      ..writeln('  ')
      ..writeln('  # 生成工厂方法')
      ..writeln('  generate_factories: true')
      ..writeln('  ')
      ..writeln('  # 生成类型安全的访问器')
      ..writeln('  type_safe_accessors: true')
      ..writeln('  ')
      ..writeln('  # 生成文档注释')
      ..writeln('  generate_documentation: ${config.includeDocumentation}')
      ..writeln('  ')
      ..writeln('  # 生成示例代码')
      ..writeln('  generate_examples: ${config.includeExamples}')
      ..writeln()
      ..writeln('# 验证配置')
      ..writeln('validation:')
      ..writeln('  # 验证资源文件存在')
      ..writeln('  validate_assets: true')
      ..writeln('  ')
      ..writeln('  # 验证文件格式')
      ..writeln('  validate_formats: true')
      ..writeln('  ')
      ..writeln('  # 验证命名规范')
      ..writeln('  validate_naming: true')
      ..writeln('  ')
      ..writeln('  # 严格模式')
      ..writeln('  strict_mode: false')
      ..writeln()
      ..writeln('# 调试配置')
      ..writeln('debug:')
      ..writeln('  # 详细输出')
      ..writeln('  verbose: false')
      ..writeln('  ')
      ..writeln('  # 输出生成统计')
      ..writeln('  show_stats: true')
      ..writeln('  ')
      ..writeln('  # 输出时间信息')
      ..writeln('  show_timing: false')
      ..writeln()
      ..writeln('# 自定义配置')
      ..writeln('custom:')
      ..writeln('  # 自定义资源处理器')
      ..writeln('  processors: []')
      ..writeln('  ')
      ..writeln('  # 自定义模板')
      ..writeln('  templates: []')
      ..writeln('  ')
      ..writeln('  # 自定义钩子')
      ..writeln('  hooks:')
      ..writeln('    pre_generate: []')
      ..writeln('    post_generate: []');

    return buffer.toString();
  }
}
