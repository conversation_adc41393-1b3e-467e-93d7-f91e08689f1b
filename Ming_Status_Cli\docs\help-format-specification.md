# Ming Status CLI 帮助信息格式规范

## 📋 概述

本文档定义了Ming Status CLI所有命令的`--help`输出格式标准，确保用户体验的一致性和专业性。

## 🎯 统一格式模板

```
<命令简洁描述>

使用方法:
  ming <command> [子命令] <必需参数> [选项]

[如果有子命令]
子命令:
  subcommand1    描述1
  subcommand2    描述2

[如果有必需参数]
参数:
  <param1>       参数1描述
  <param2>       参数2描述

选项:
  -s, --short=<值>         选项描述 (默认值/允许值)
      --long-only          仅长选项描述
  -f, --[no-]flag          布尔选项描述 (默认: on/off)

[如果是复杂命令，按功能分组]
基础选项:
  -h, --help               显示帮助信息
  -v, --verbose            详细输出

高级选项:
  --advanced-option        高级选项描述

示例:
  # 基础用法
  ming command basic_example

  # 高级用法
  ming command --option=value advanced_example

  # 复杂用法
  ming command subcommand --multiple --options example

更多信息:
  使用 'ming help <command>' 查看详细文档
```

## 📐 格式规范细节

### 1. 标题部分
- **格式**: 一行简洁的命令描述
- **要求**: 不超过80字符，描述命令的核心功能
- **示例**: `基于模板创建新的模块或项目`

### 2. 使用方法
- **格式**: `ming <command> [子命令] <必需参数> [选项]`
- **要求**: 
  - 使用尖括号`<>`表示必需参数
  - 使用方括号`[]`表示可选参数
  - 子命令放在必需参数之前

### 3. 子命令部分 (如适用)
- **格式**: 两列对齐，命令名和描述
- **对齐**: 命令名左对齐，描述从第15列开始
- **排序**: 按使用频率或逻辑顺序排列

### 4. 参数部分 (如适用)
- **格式**: `<param_name>       参数描述`
- **要求**: 仅列出位置参数，不包括选项参数

### 5. 选项部分
- **基本格式**: 
  ```
  -s, --long=<类型>        描述 (默认: 值)
  -s, --long               布尔选项描述
      --long-only          仅长选项
  -s, --[no-]toggle        可否定选项 (默认: on)
  ```
- **对齐要求**:
  - 短选项: 2字符宽度 (`-s`)
  - 长选项: 从第5列开始
  - 描述: 从第26列开始
- **分组**: 复杂命令按功能分组 (基础选项、高级选项、输出选项等)

### 6. 示例部分
- **要求**: 至少3个示例，从简单到复杂
- **格式**: 使用注释说明每个示例的用途
- **内容**: 提供实际可运行的命令

### 7. 更多信息部分
- **标准文本**: `使用 'ming help <command>' 查看详细文档`
- **位置**: 帮助信息的最后一行

## 🎨 选项格式标准

### 选项类型和格式

| 选项类型 | 格式 | 示例 |
|---------|------|------|
| 短+长选项 | `-s, --long=<值>` | `-t, --template=<名称>` |
| 仅长选项 | `    --long-only` | `    --dry-run` |
| 布尔选项 | `-f, --flag` | `-v, --verbose` |
| 可否定选项 | `-f, --[no-]flag` | `-i, --[no-]interactive` |
| 多值选项 | `--option=<值>...` | `--var=<key=value>...` |

### 描述格式

- **长度**: 不超过50字符
- **默认值**: 在描述末尾用括号标注 `(默认: 值)`
- **允许值**: 用括号列出 `(允许: a, b, c)`
- **组合**: `(默认: basic, 允许: basic, standard, strict)`

## 📋 实现检查清单

### 每个命令必须包含:
- [ ] 简洁的命令描述
- [ ] 标准化的使用方法
- [ ] 完整的选项列表
- [ ] 至少3个实用示例
- [ ] 标准的更多信息行

### 质量要求:
- [ ] 所有选项都有清晰描述
- [ ] 对齐格式正确
- [ ] 示例可以实际运行
- [ ] 描述简洁准确
- [ ] 符合80字符行长度限制

## 🔧 实现方式

### 在Dart命令类中实现:

```dart
@override
String get usage => '''
命令描述

使用方法:
  ming command <参数> [选项]

选项:
  -t, --template=<名称>     指定模板名称 (默认: basic)
  -o, --output=<路径>       输出目录路径
  -f, --force              强制覆盖已存在的文件
  -v, --verbose            显示详细输出

示例:
  # 基础用法
  ming command example

  # 高级用法
  ming command --template=custom --output=./dist example

更多信息:
  使用 'ming help command' 查看详细文档
''';
```

## 📚 参考示例

参见以下命令的标准实现:
- `template info` - 完整选项格式
- `registry stats` - 分组选项格式
- `validate` - 复杂命令格式

---

**版本**: 1.0  
**创建日期**: 2025-07-12  
**最后更新**: 2025-07-12
