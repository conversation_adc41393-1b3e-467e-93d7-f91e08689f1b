import 'package:flutter/material.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      title: 'Flutter Demo',
      debugShowCheckedModeBanner: false,
      home: FinalScrollPage(),
    );
  }
}

// Delegate 部分保持不变
class _MySliverPersistentHeaderDelegate extends SliverPersistentHeaderDelegate {
  _MySliverPersistentHeaderDelegate(this.child, {required this.height});
  final Widget child;
  final double height;

  @override
  double get minExtent => height;
  @override
  double get maxExtent => height;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return child;
  }

  @override
  bool shouldRebuild(_MySliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}

// 页面主体
class FinalScrollPage extends StatelessWidget {
  const FinalScrollPage({super.key});

  @override
  Widget build(BuildContext context) {
    // 准备一些横向卡片的数据
    final List<Color> horizontalCardColors = [
      Colors.red[200]!,
      Colors.green[200]!,
      Colors.blue[200]!,
      Colors.orange[200]!,
      Colors.purple[200]!,
    ];

    return Scaffold(
      body: NestedScrollView(
        // headerSliverBuilder 部分完全不变
        headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
          return <Widget>[
            SliverList(
              delegate: SliverChildListDelegate([
                Image.network('https://picsum.photos/seed/picsum/400/300',
                    fit: BoxFit.cover),
                const SizedBox(height: 20),
                const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Text(
                    '向上滑动，开始探索',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),
                Container(height: 200, color: Colors.grey[200]),
                Container(height: 200, color: Colors.grey[300]),
              ]),
            ),
            SliverPersistentHeader(
              pinned: true,
              delegate: _MySliverPersistentHeaderDelegate(
                Container(
                  height: 60.0,
                  color: Colors.white,
                  child: const Center(
                    child: Text(
                      '流畅横向滚动区域',
                      style: TextStyle(fontSize: 18, color: Colors.black),
                    ),
                  ),
                ),
                height: 60.0,
              ),
            ),
          ];
        },
        
        // ================================================================
        //  最终方案：使用 PageView 并修改其滚动物理效果
        // ================================================================
        body: PageView(
          // 关键！修改滚动物理效果，禁用“翻页吸附”
          physics: const ClampingScrollPhysics(), 
          
          // PageView 的 children 会自动撑满全屏，无需我们手动设置宽度
          children: horizontalCardColors.map((color) {
            final index = horizontalCardColors.indexOf(color);
            return Container(
              color: color,
              child: Center(
                child: Text(
                  '页面 ${index + 1}',
                  style: const TextStyle(fontSize: 32, color: Colors.white),
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}