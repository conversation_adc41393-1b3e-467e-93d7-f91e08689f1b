/*
---------------------------------------------------------------
File name:          local_ming_cli_service.dart
Author:             lgnorant-lu
Date created:       2025-08-02
Last modified:      2025-08-02
Dart Version:       3.2+
Description:        本地Ming CLI服务 - 增强插件生成通知
---------------------------------------------------------------
Change History:
    2025-08-02: Initial creation - 本地Ming CLI服务集成;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:plugin_system/plugin_system.dart';
import 'package:path/path.dart' as path;
import 'package:app_manager/app_manager.dart';

/// Ming CLI命令执行结果
class MingCliResult {
  const MingCliResult({
    required this.success,
    required this.command,
    this.output,
    this.error,
    this.exitCode = 0,
    this.executionTime,
    this.generatedFiles = const <String>[],
    this.projectPath,
  });

  /// 是否成功
  final bool success;

  /// 执行的命令
  final String command;

  /// 输出内容
  final String? output;

  /// 错误信息
  final String? error;

  /// 退出码
  final int exitCode;

  /// 执行时间
  final Duration? executionTime;

  /// 生成的文件列表
  final List<String> generatedFiles;

  /// 项目路径（如果是创建项目命令）
  final String? projectPath;
}

/// 插件生成通知
class PluginGenerationNotification {
  const PluginGenerationNotification({
    required this.pluginId,
    required this.pluginName,
    required this.projectPath,
    required this.templateType,
    required this.generatedAt,
    this.version = '1.0.0',
    this.description,
    this.author,
    this.generatedFiles = const <String>[],
  });

  /// 插件ID
  final String pluginId;

  /// 插件名称
  final String pluginName;

  /// 项目路径
  final String projectPath;

  /// 模板类型
  final String templateType;

  /// 生成时间
  final DateTime generatedAt;

  /// 版本号
  final String version;

  /// 描述
  final String? description;

  /// 作者
  final String? author;

  /// 生成的文件列表
  final List<String> generatedFiles;
}

/// 本地Ming CLI服务
///
/// 负责执行Ming CLI命令并提供插件生成通知功能
class LocalMingCliService extends ChangeNotifier {
  LocalMingCliService._();

  /// 单例实例
  static final LocalMingCliService _instance = LocalMingCliService._();
  static LocalMingCliService get instance => _instance;

  /// 插件安装服务（企业级）
  final PluginInstallationService _pluginInstallationService =
      PluginInstallationService.instance;

  /// 插件消息系统
  final PluginMessenger _messenger = PluginMessenger.instance;

  /// 是否已初始化
  bool _isInitialized = false;

  /// Ming CLI是否可用
  bool _isCliAvailable = false;

  /// CLI版本
  String? _cliVersion;

  /// 命令执行历史
  final List<MingCliResult> _commandHistory = <MingCliResult>[];

  /// 插件生成通知流
  final StreamController<PluginGenerationNotification> _notificationController =
      StreamController<PluginGenerationNotification>.broadcast();

  /// 获取插件生成通知流
  Stream<PluginGenerationNotification> get pluginGenerationNotifications =>
      _notificationController.stream;

  /// 获取命令历史
  List<MingCliResult> get commandHistory => List.unmodifiable(_commandHistory);

  /// 获取CLI是否可用
  bool get isCliAvailable => _isCliAvailable;

  /// 获取CLI版本
  String? get cliVersion => _cliVersion;

  /// 初始化服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // 检查Ming CLI是否可用
      await _checkCliAvailability();

      _isInitialized = true;
      _log('info', 'LocalMingCliService初始化完成');
    } catch (e) {
      _log('error', 'LocalMingCliService初始化失败: $e');
      rethrow;
    }
  }

  /// 检查CLI可用性
  Future<void> _checkCliAvailability() async {
    try {
      // 尝试多种方式检查Ming CLI
      final cliPaths = await _findMingCliPaths();

      for (final cliPath in cliPaths) {
        try {
          final result = await Process.run(
            cliPath,
            ['--version'],
            runInShell: true,
          );

          if (result.exitCode == 0) {
            _isCliAvailable = true;
            _cliVersion = _extractVersionFromOutput(result.stdout.toString());
            _log('info', 'Ming CLI可用，路径: $cliPath，版本: $_cliVersion');
            return;
          }
        } catch (e) {
          _log('debug', '尝试CLI路径失败: $cliPath, 错误: $e');
          continue;
        }
      }

      // 所有路径都失败，使用模拟模式
      _isCliAvailable = false;
      _cliVersion = null;
      _log('warning', 'Ming CLI不可用，已尝试路径: ${cliPaths.join(', ')}');

      // 在开发环境中启用模拟模式
      if (kDebugMode) {
        _isCliAvailable = true;
        _cliVersion = '1.0.0-simulated';
        _log('info', 'Ming CLI模拟模式已启用（开发环境）');
      }
    } catch (e) {
      _isCliAvailable = false;
      _cliVersion = null;
      _log('error', 'CLI可用性检查失败: $e');
    }
  }

  /// 执行Ming CLI命令
  Future<MingCliResult> executeCommand(
    String command, {
    String? workingDirectory,
    Map<String, String>? environment,
    bool notifyOnPluginGeneration = true,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    if (!_isCliAvailable) {
      return MingCliResult(
        success: false,
        command: command,
        error: 'Ming CLI不可用',
        exitCode: 1,
      );
    }

    final startTime = DateTime.now();
    _log('info', '执行Ming CLI命令: $command');

    try {
      MingCliResult result;

      if (_isCliAvailable && !_cliVersion!.contains('simulated')) {
        // 使用真实的CLI执行
        result =
            await _executeRealCommand(command, workingDirectory, environment);
      } else {
        // 使用模拟执行（开发环境或CLI不可用时）
        result = await _simulateCommandExecution(command, workingDirectory);
      }

      // 添加到历史记录
      _commandHistory.insert(0, result);
      if (_commandHistory.length > 100) {
        _commandHistory.removeLast();
      }

      // 如果是插件生成命令，发送通知
      if (notifyOnPluginGeneration && _isPluginGenerationCommand(command)) {
        await _handlePluginGeneration(command, result);
      }

      notifyListeners();
      return result;
    } catch (e) {
      final result = MingCliResult(
        success: false,
        command: command,
        error: e.toString(),
        exitCode: 1,
        executionTime: DateTime.now().difference(startTime),
      );

      _commandHistory.insert(0, result);
      notifyListeners();
      return result;
    }
  }

  /// 模拟命令执行
  Future<MingCliResult> _simulateCommandExecution(
    String command,
    String? workingDirectory,
  ) async {
    final startTime = DateTime.now();

    // 模拟执行时间
    await Future<void>.delayed(const Duration(seconds: 2));

    final executionTime = DateTime.now().difference(startTime);

    // 根据命令类型生成不同的结果
    if (command.startsWith('create')) {
      return _simulateCreateCommand(command, executionTime);
    } else if (command.startsWith('build')) {
      return _simulateBuildCommand(command, executionTime);
    } else if (command.startsWith('test')) {
      return _simulateTestCommand(command, executionTime);
    } else {
      return MingCliResult(
        success: true,
        command: command,
        output:
            '✅ 命令执行成功\n📝 输出: 模拟命令输出\n⏱️ 执行时间: ${executionTime.inMilliseconds}ms',
        executionTime: executionTime,
      );
    }
  }

  /// 模拟创建命令
  MingCliResult _simulateCreateCommand(String command, Duration executionTime) {
    final pluginName = _extractPluginNameFromCommand(command);
    final projectPath = './projects/$pluginName';

    return MingCliResult(
      success: true,
      command: command,
      output: '''✅ 插件项目创建成功
📁 项目路径: $projectPath
🎯 插件名称: $pluginName
📦 模板类型: 工具插件
⏱️ 执行时间: ${executionTime.inMilliseconds}ms

生成的文件:
  📄 lib/main.dart
  📄 lib/src/plugin_core.dart
  📄 pubspec.yaml
  📄 README.md
  📄 test/plugin_test.dart''',
      executionTime: executionTime,
      generatedFiles: [
        '$projectPath/lib/main.dart',
        '$projectPath/lib/src/plugin_core.dart',
        '$projectPath/pubspec.yaml',
        '$projectPath/README.md',
        '$projectPath/test/plugin_test.dart',
      ],
      projectPath: projectPath,
    );
  }

  /// 模拟构建命令
  MingCliResult _simulateBuildCommand(String command, Duration executionTime) {
    return MingCliResult(
      success: true,
      command: command,
      output: '''🔨 开始构建插件...
✅ 构建完成
📦 输出文件: ./build/plugin.zip
📊 包大小: 2.1 MB
⏱️ 执行时间: ${executionTime.inMilliseconds}ms''',
      executionTime: executionTime,
      generatedFiles: ['./build/plugin.zip'],
    );
  }

  /// 模拟测试命令
  MingCliResult _simulateTestCommand(String command, Duration executionTime) {
    // 模拟测试失败
    return MingCliResult(
      success: false,
      command: command,
      error: '''❌ 测试失败
📝 错误详情:
  - 缺少必要的测试文件
  - 测试覆盖率不足 (45% < 80%)
💡 建议:
  - 添加单元测试文件
  - 增加测试用例覆盖率''',
      exitCode: 1,
      executionTime: executionTime,
    );
  }

  /// 检查是否为插件生成命令
  bool _isPluginGenerationCommand(String command) {
    return command.startsWith('create') &&
        (command.contains('plugin') || command.contains('--type'));
  }

  /// 处理插件生成
  Future<void> _handlePluginGeneration(
      String command, MingCliResult result) async {
    if (!result.success || result.projectPath == null) return;

    try {
      final pluginName = _extractPluginNameFromCommand(command);
      final templateType = _extractTemplateTypeFromCommand(command);

      // 创建插件生成通知
      final notification = PluginGenerationNotification(
        pluginId: _generatePluginId(pluginName),
        pluginName: pluginName,
        projectPath: result.projectPath!,
        templateType: templateType,
        generatedAt: DateTime.now(),
        generatedFiles: result.generatedFiles,
        description: '通过Ming CLI生成的$templateType插件',
        author: 'Creative Workshop',
      );

      // 发送通知
      _notificationController.add(notification);

      // 通知插件管理器
      await _notifyPluginManager(notification);

      // 发送系统消息
      await _sendSystemNotification(notification);

      _log('info', '插件生成通知已发送: ${notification.pluginName}');
    } catch (e) {
      _log('error', '处理插件生成通知失败: $e');
    }
  }

  /// 通知插件管理器
  Future<void> _notifyPluginManager(
      PluginGenerationNotification notification) async {
    try {
      _log('info', '通知插件安装服务: 新插件生成 - ${notification.pluginName}');

      // 使用真实的企业级插件安装服务
      try {
        // 尝试安装生成的插件
        final installResult = await _pluginInstallationService.installPlugin(
          pluginId: notification.pluginId,
          source: notification.projectPath,
          config: {
            'name': notification.pluginName,
            'generated_by': 'ming_cli',
            'project_path': notification.projectPath,
            'template_type': notification.templateType,
            'generated_at': notification.generatedAt.toIso8601String(),
          },
        );

        if (installResult.success) {
          _log('info', '插件安装成功: ${notification.pluginId}');
        } else {
          _log('warning', '插件安装失败: ${installResult.error}');
        }
      } catch (installError) {
        _log('error', '插件安装服务调用失败: $installError');
        // TODO: 实现降级处理 - 如果企业级服务不可用，使用基础通知
      }
    } catch (e) {
      _log('error', '通知插件管理器失败: $e');
    }
  }

  /// 发送系统通知
  Future<void> _sendSystemNotification(
      PluginGenerationNotification notification) async {
    try {
      // 使用插件消息系统发送通知
      await _messenger.sendNotification(
        'creative_workshop',
        'system',
        'plugin_generated',
        {
          'pluginId': notification.pluginId,
          'pluginName': notification.pluginName,
          'projectPath': notification.projectPath,
          'templateType': notification.templateType,
          'generatedAt': notification.generatedAt.toIso8601String(),
        },
      );
    } catch (e) {
      _log('error', '发送系统通知失败: $e');
    }
  }

  /// 从命令中提取插件名称
  String _extractPluginNameFromCommand(String command) {
    final parts = command.split(' ');
    for (int i = 0; i < parts.length - 1; i++) {
      if (parts[i] == 'create') {
        return parts[i + 1];
      }
    }
    return 'my_plugin';
  }

  /// 从命令中提取模板类型
  String _extractTemplateTypeFromCommand(String command) {
    if (command.contains('--type')) {
      final parts = command.split(' ');
      for (int i = 0; i < parts.length - 1; i++) {
        if (parts[i] == '--type') {
          return parts[i + 1];
        }
      }
    }
    return 'tool';
  }

  /// 生成插件ID
  String _generatePluginId(String pluginName) {
    return pluginName.toLowerCase().replaceAll(RegExp(r'[^a-z0-9_]'), '_');
  }

  /// 执行真实的CLI命令
  Future<MingCliResult> _executeRealCommand(
    String command,
    String? workingDirectory,
    Map<String, String>? environment,
  ) async {
    final startTime = DateTime.now();

    try {
      // 查找可用的CLI路径
      final cliPaths = await _findMingCliPaths();
      String? workingCliPath;

      // 找到第一个可用的CLI路径
      for (final cliPath in cliPaths) {
        try {
          final testResult = await Process.run(
            cliPath,
            ['--version'],
            runInShell: true,
          );
          if (testResult.exitCode == 0) {
            workingCliPath = cliPath;
            break;
          }
        } catch (e) {
          continue;
        }
      }

      if (workingCliPath == null) {
        throw Exception('没有找到可用的Ming CLI');
      }

      // 解析命令参数
      final commandParts = command.split(' ');
      final executable = workingCliPath;
      final arguments = commandParts;

      _log('debug', '执行真实命令: $executable ${arguments.join(' ')}');

      // 执行命令
      final result = await Process.run(
        executable,
        arguments,
        workingDirectory: workingDirectory,
        environment: environment,
        runInShell: true,
      );

      final executionTime = DateTime.now().difference(startTime);
      final output = result.stdout.toString();
      final error = result.stderr.toString();

      // 检查是否为插件创建命令，提取生成的文件
      final generatedFiles = <String>[];
      String? projectPath;

      if (result.exitCode == 0 && _isPluginGenerationCommand(command)) {
        projectPath = _extractProjectPathFromOutput(output, command);
        generatedFiles.addAll(_extractGeneratedFilesFromOutput(output));
      }

      return MingCliResult(
        success: result.exitCode == 0,
        command: command,
        output: output.isNotEmpty ? output : null,
        error: error.isNotEmpty ? error : null,
        exitCode: result.exitCode,
        executionTime: executionTime,
        generatedFiles: generatedFiles,
        projectPath: projectPath,
      );
    } catch (e) {
      final executionTime = DateTime.now().difference(startTime);
      return MingCliResult(
        success: false,
        command: command,
        error: '真实命令执行失败: $e',
        exitCode: 1,
        executionTime: executionTime,
      );
    }
  }

  /// 从输出中提取项目路径
  String? _extractProjectPathFromOutput(String output, String command) {
    try {
      // 从命令中提取插件名称
      final pluginName = _extractPluginNameFromCommand(command);

      // 尝试从输出中找到项目路径
      final pathRegex = RegExp(
          r'(?:created|generated|project)\s+(?:at|in)?\s*[:\s]*([^\n\r]+)',
          caseSensitive: false);
      final match = pathRegex.firstMatch(output);

      if (match != null) {
        return match.group(1)?.trim();
      }

      // 如果没有找到，使用默认路径
      return './$pluginName';
    } catch (e) {
      _log('warning', '提取项目路径失败: $e');
      return null;
    }
  }

  /// 从输出中提取生成的文件列表
  List<String> _extractGeneratedFilesFromOutput(String output) {
    final files = <String>[];

    try {
      // 匹配文件路径模式
      final fileRegex = RegExp(
          r'(?:created|generated|wrote)\s+([^\n\r]+\.(?:dart|yaml|md|json))',
          caseSensitive: false);
      final matches = fileRegex.allMatches(output);

      for (final match in matches) {
        final filePath = match.group(1)?.trim();
        if (filePath != null && filePath.isNotEmpty) {
          files.add(filePath);
        }
      }

      // 如果没有找到具体文件，添加常见的插件文件
      if (files.isEmpty) {
        files.addAll([
          'lib/main.dart',
          'lib/src/plugin_core.dart',
          'pubspec.yaml',
          'README.md',
          'test/plugin_test.dart',
        ]);
      }
    } catch (e) {
      _log('warning', '提取生成文件列表失败: $e');
    }

    return files;
  }

  /// 查找Ming CLI可能的路径
  Future<List<String>> _findMingCliPaths() async {
    final paths = <String>[];

    try {
      // 1. 检查项目相对路径中的Ming CLI
      final projectRoot = Directory.current.path;
      final relativePaths = [
        path.join(projectRoot, '..', 'Ming_Status_Cli', 'ming_status_cli.exe'),
        path.join(projectRoot, '..', 'Ming_Status_Cli', 'build', 'ming.exe'),
        path.join(projectRoot, '..', 'Ming_Status_Cli', 'bin',
            'ming_status_cli.dart'),
        path.join(projectRoot, 'Ming_Status_Cli', 'ming_status_cli.exe'),
        path.join(projectRoot, 'Ming_Status_Cli', 'build', 'ming.exe'),
      ];

      for (final relativePath in relativePaths) {
        if (await File(relativePath).exists()) {
          paths.add(relativePath);
        }
      }

      // 2. 检查PATH环境变量中的ming命令
      paths.addAll(
          ['ming', 'ming.exe', 'ming_status_cli', 'ming_status_cli.exe']);

      // 3. 检查常见安装位置
      if (Platform.isWindows) {
        paths.addAll([
          r'C:\Program Files\Ming CLI\ming.exe',
          r'C:\Program Files (x86)\Ming CLI\ming.exe',
          path.join(Platform.environment['USERPROFILE'] ?? '', 'AppData',
              'Local', 'Ming CLI', 'ming.exe'),
        ]);
      } else if (Platform.isMacOS) {
        paths.addAll([
          '/usr/local/bin/ming',
          '/opt/homebrew/bin/ming',
          path.join(
              Platform.environment['HOME'] ?? '', '.local', 'bin', 'ming'),
        ]);
      } else if (Platform.isLinux) {
        paths.addAll([
          '/usr/local/bin/ming',
          '/usr/bin/ming',
          path.join(
              Platform.environment['HOME'] ?? '', '.local', 'bin', 'ming'),
        ]);
      }
    } catch (e) {
      _log('warning', '查找CLI路径时发生错误: $e');
    }

    return paths;
  }

  /// 从输出中提取版本号
  String _extractVersionFromOutput(String output) {
    try {
      // 匹配版本号模式，如 "Ming Status CLI 1.0.0" 或 "v1.0.0"
      final versionRegex = RegExp(r'(?:v|version\s+)?(\d+\.\d+\.\d+(?:-\w+)?)',
          caseSensitive: false);
      final match = versionRegex.firstMatch(output);

      if (match != null) {
        return match.group(1) ?? '1.0.0';
      }

      // 如果没有匹配到标准版本号，尝试提取数字
      final numberRegex = RegExp(r'(\d+\.\d+\.\d+)');
      final numberMatch = numberRegex.firstMatch(output);

      if (numberMatch != null) {
        return numberMatch.group(1) ?? '1.0.0';
      }

      return '1.0.0';
    } catch (e) {
      _log('warning', '提取版本号失败: $e');
      return '1.0.0';
    }
  }

  /// 清理资源
  @override
  void dispose() {
    _notificationController.close();
    super.dispose();
  }

  /// 日志记录
  void _log(String level, String message) {
    if (kDebugMode) {
      print('[$level] [LocalMingCliService] $message');
    }
  }
}
