/*
---------------------------------------------------------------
File name:          local_ming_cli_service.dart
Author:             lgnorant-lu
Date created:       2025-08-02
Last modified:      2025-08-02
Dart Version:       3.2+
Description:        本地Ming CLI服务 - 增强插件生成通知
---------------------------------------------------------------
Change History:
    2025-08-02: Initial creation - 本地Ming CLI服务集成;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:plugin_system/plugin_system.dart';
import '../core/plugins/plugin_manager.dart';

/// Ming CLI命令执行结果
class MingCliResult {
  const MingCliResult({
    required this.success,
    required this.command,
    this.output,
    this.error,
    this.exitCode = 0,
    this.executionTime,
    this.generatedFiles = const <String>[],
    this.projectPath,
  });

  /// 是否成功
  final bool success;
  
  /// 执行的命令
  final String command;
  
  /// 输出内容
  final String? output;
  
  /// 错误信息
  final String? error;
  
  /// 退出码
  final int exitCode;
  
  /// 执行时间
  final Duration? executionTime;
  
  /// 生成的文件列表
  final List<String> generatedFiles;
  
  /// 项目路径（如果是创建项目命令）
  final String? projectPath;
}

/// 插件生成通知
class PluginGenerationNotification {
  const PluginGenerationNotification({
    required this.pluginId,
    required this.pluginName,
    required this.projectPath,
    required this.templateType,
    required this.generatedAt,
    this.version = '1.0.0',
    this.description,
    this.author,
    this.generatedFiles = const <String>[],
  });

  /// 插件ID
  final String pluginId;
  
  /// 插件名称
  final String pluginName;
  
  /// 项目路径
  final String projectPath;
  
  /// 模板类型
  final String templateType;
  
  /// 生成时间
  final DateTime generatedAt;
  
  /// 版本号
  final String version;
  
  /// 描述
  final String? description;
  
  /// 作者
  final String? author;
  
  /// 生成的文件列表
  final List<String> generatedFiles;
}

/// 本地Ming CLI服务
/// 
/// 负责执行Ming CLI命令并提供插件生成通知功能
class LocalMingCliService extends ChangeNotifier {
  LocalMingCliService._();

  /// 单例实例
  static final LocalMingCliService _instance = LocalMingCliService._();
  static LocalMingCliService get instance => _instance;

  /// 插件管理器
  final PluginManager _pluginManager = PluginManager.instance;
  
  /// 插件消息系统
  final PluginMessenger _messenger = PluginMessenger.instance;

  /// 是否已初始化
  bool _isInitialized = false;
  
  /// Ming CLI是否可用
  bool _isCliAvailable = false;
  
  /// CLI版本
  String? _cliVersion;
  
  /// 命令执行历史
  final List<MingCliResult> _commandHistory = <MingCliResult>[];
  
  /// 插件生成通知流
  final StreamController<PluginGenerationNotification> _notificationController = 
      StreamController<PluginGenerationNotification>.broadcast();

  /// 获取插件生成通知流
  Stream<PluginGenerationNotification> get pluginGenerationNotifications => 
      _notificationController.stream;

  /// 获取命令历史
  List<MingCliResult> get commandHistory => List.unmodifiable(_commandHistory);

  /// 获取CLI是否可用
  bool get isCliAvailable => _isCliAvailable;

  /// 获取CLI版本
  String? get cliVersion => _cliVersion;

  /// 初始化服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // 检查Ming CLI是否可用
      await _checkCliAvailability();
      
      _isInitialized = true;
      _log('info', 'LocalMingCliService初始化完成');
    } catch (e) {
      _log('error', 'LocalMingCliService初始化失败: $e');
      rethrow;
    }
  }

  /// 检查CLI可用性
  Future<void> _checkCliAvailability() async {
    try {
      // TODO: 实现真实的CLI检查
      // 当前使用模拟检查
      _isCliAvailable = true;
      _cliVersion = '1.0.0';
      _log('info', 'Ming CLI可用，版本: $_cliVersion');
    } catch (e) {
      _isCliAvailable = false;
      _cliVersion = null;
      _log('warning', 'Ming CLI不可用: $e');
    }
  }

  /// 执行Ming CLI命令
  Future<MingCliResult> executeCommand(
    String command, {
    String? workingDirectory,
    Map<String, String>? environment,
    bool notifyOnPluginGeneration = true,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    if (!_isCliAvailable) {
      return MingCliResult(
        success: false,
        command: command,
        error: 'Ming CLI不可用',
        exitCode: 1,
      );
    }

    final startTime = DateTime.now();
    _log('info', '执行Ming CLI命令: $command');

    try {
      // TODO: 实现真实的命令执行
      // 当前使用模拟执行
      final result = await _simulateCommandExecution(command, workingDirectory);
      
      // 添加到历史记录
      _commandHistory.insert(0, result);
      if (_commandHistory.length > 100) {
        _commandHistory.removeLast();
      }

      // 如果是插件生成命令，发送通知
      if (notifyOnPluginGeneration && _isPluginGenerationCommand(command)) {
        await _handlePluginGeneration(command, result);
      }

      notifyListeners();
      return result;
    } catch (e) {
      final result = MingCliResult(
        success: false,
        command: command,
        error: e.toString(),
        exitCode: 1,
        executionTime: DateTime.now().difference(startTime),
      );
      
      _commandHistory.insert(0, result);
      notifyListeners();
      return result;
    }
  }

  /// 模拟命令执行
  Future<MingCliResult> _simulateCommandExecution(
    String command,
    String? workingDirectory,
  ) async {
    final startTime = DateTime.now();
    
    // 模拟执行时间
    await Future.delayed(const Duration(seconds: 2));
    
    final executionTime = DateTime.now().difference(startTime);
    
    // 根据命令类型生成不同的结果
    if (command.startsWith('create')) {
      return _simulateCreateCommand(command, executionTime);
    } else if (command.startsWith('build')) {
      return _simulateBuildCommand(command, executionTime);
    } else if (command.startsWith('test')) {
      return _simulateTestCommand(command, executionTime);
    } else {
      return MingCliResult(
        success: true,
        command: command,
        output: '✅ 命令执行成功\n📝 输出: 模拟命令输出\n⏱️ 执行时间: ${executionTime.inMilliseconds}ms',
        executionTime: executionTime,
      );
    }
  }

  /// 模拟创建命令
  MingCliResult _simulateCreateCommand(String command, Duration executionTime) {
    final pluginName = _extractPluginNameFromCommand(command);
    final projectPath = './projects/$pluginName';
    
    return MingCliResult(
      success: true,
      command: command,
      output: '''✅ 插件项目创建成功
📁 项目路径: $projectPath
🎯 插件名称: $pluginName
📦 模板类型: 工具插件
⏱️ 执行时间: ${executionTime.inMilliseconds}ms

生成的文件:
  📄 lib/main.dart
  📄 lib/src/plugin_core.dart
  📄 pubspec.yaml
  📄 README.md
  📄 test/plugin_test.dart''',
      executionTime: executionTime,
      generatedFiles: [
        '$projectPath/lib/main.dart',
        '$projectPath/lib/src/plugin_core.dart',
        '$projectPath/pubspec.yaml',
        '$projectPath/README.md',
        '$projectPath/test/plugin_test.dart',
      ],
      projectPath: projectPath,
    );
  }

  /// 模拟构建命令
  MingCliResult _simulateBuildCommand(String command, Duration executionTime) {
    return MingCliResult(
      success: true,
      command: command,
      output: '''🔨 开始构建插件...
✅ 构建完成
📦 输出文件: ./build/plugin.zip
📊 包大小: 2.1 MB
⏱️ 执行时间: ${executionTime.inMilliseconds}ms''',
      executionTime: executionTime,
      generatedFiles: ['./build/plugin.zip'],
    );
  }

  /// 模拟测试命令
  MingCliResult _simulateTestCommand(String command, Duration executionTime) {
    // 模拟测试失败
    return MingCliResult(
      success: false,
      command: command,
      error: '''❌ 测试失败
📝 错误详情:
  - 缺少必要的测试文件
  - 测试覆盖率不足 (45% < 80%)
💡 建议:
  - 添加单元测试文件
  - 增加测试用例覆盖率''',
      exitCode: 1,
      executionTime: executionTime,
    );
  }

  /// 检查是否为插件生成命令
  bool _isPluginGenerationCommand(String command) {
    return command.startsWith('create') && 
           (command.contains('plugin') || command.contains('--type'));
  }

  /// 处理插件生成
  Future<void> _handlePluginGeneration(String command, MingCliResult result) async {
    if (!result.success || result.projectPath == null) return;

    try {
      final pluginName = _extractPluginNameFromCommand(command);
      final templateType = _extractTemplateTypeFromCommand(command);
      
      // 创建插件生成通知
      final notification = PluginGenerationNotification(
        pluginId: _generatePluginId(pluginName),
        pluginName: pluginName,
        projectPath: result.projectPath!,
        templateType: templateType,
        generatedAt: DateTime.now(),
        generatedFiles: result.generatedFiles,
        description: '通过Ming CLI生成的$templateType插件',
        author: 'Creative Workshop',
      );

      // 发送通知
      _notificationController.add(notification);

      // 通知插件管理器
      await _notifyPluginManager(notification);

      // 发送系统消息
      await _sendSystemNotification(notification);

      _log('info', '插件生成通知已发送: ${notification.pluginName}');
    } catch (e) {
      _log('error', '处理插件生成通知失败: $e');
    }
  }

  /// 通知插件管理器
  Future<void> _notifyPluginManager(PluginGenerationNotification notification) async {
    try {
      // TODO: 集成真实的插件管理器通知
      _log('info', '通知插件管理器: 新插件生成 - ${notification.pluginName}');
    } catch (e) {
      _log('error', '通知插件管理器失败: $e');
    }
  }

  /// 发送系统通知
  Future<void> _sendSystemNotification(PluginGenerationNotification notification) async {
    try {
      // 使用插件消息系统发送通知
      await _messenger.sendNotification(
        'creative_workshop',
        'system',
        'plugin_generated',
        {
          'pluginId': notification.pluginId,
          'pluginName': notification.pluginName,
          'projectPath': notification.projectPath,
          'templateType': notification.templateType,
          'generatedAt': notification.generatedAt.toIso8601String(),
        },
      );
    } catch (e) {
      _log('error', '发送系统通知失败: $e');
    }
  }

  /// 从命令中提取插件名称
  String _extractPluginNameFromCommand(String command) {
    final parts = command.split(' ');
    for (int i = 0; i < parts.length - 1; i++) {
      if (parts[i] == 'create') {
        return parts[i + 1];
      }
    }
    return 'my_plugin';
  }

  /// 从命令中提取模板类型
  String _extractTemplateTypeFromCommand(String command) {
    if (command.contains('--type')) {
      final parts = command.split(' ');
      for (int i = 0; i < parts.length - 1; i++) {
        if (parts[i] == '--type') {
          return parts[i + 1];
        }
      }
    }
    return 'tool';
  }

  /// 生成插件ID
  String _generatePluginId(String pluginName) {
    return pluginName.toLowerCase().replaceAll(RegExp(r'[^a-z0-9_]'), '_');
  }

  /// 清理资源
  @override
  void dispose() {
    _notificationController.close();
    super.dispose();
  }

  /// 日志记录
  void _log(String level, String message) {
    if (kDebugMode) {
      print('[$level] [LocalMingCliService] $message');
    }
  }
}
