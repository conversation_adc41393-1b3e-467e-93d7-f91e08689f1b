/*
---------------------------------------------------------------
File name:          dependency_injection_container.dart
Author:             lgnorant-lu
Date created:       2025-07-26
Last modified:      2025-07-26
Dart Version:       3.2+
Description:        依赖注入容器 - Phase 4.2 架构重构和模块解耦
---------------------------------------------------------------
Change History:
    2025-07-26: Phase 4.2 - 依赖注入容器实现;
---------------------------------------------------------------
*/

import 'dart:async';

/// 依赖生命周期
enum DependencyLifecycle {
  /// 单例 - 全局唯一实例
  singleton,

  /// 瞬态 - 每次请求创建新实例
  transient,

  /// 作用域 - 在特定作用域内单例
  scoped,
}

/// 依赖工厂函数
typedef DependencyFactory<T> = T Function(DIContainer container);

/// 依赖销毁函数
typedef DependencyDisposer<T> = Future<void> Function(T instance);

/// 依赖注册信息
class DependencyRegistration<T> {
  const DependencyRegistration({
    required this.factory,
    required this.lifecycle,
    this.disposer,
    this.dependencies = const <Type>[],
    this.tags = const <String>[],
  });

  /// 工厂函数
  final DependencyFactory<T> factory;

  /// 生命周期
  final DependencyLifecycle lifecycle;

  /// 销毁函数
  final DependencyDisposer<T>? disposer;

  /// 依赖列表
  final List<Type> dependencies;

  /// 标签
  final List<String> tags;
}

/// 依赖作用域
class DependencyScope {
  DependencyScope({required this.name});

  /// 作用域名称
  final String name;

  /// 作用域实例
  final Map<Type, dynamic> _instances = <Type, dynamic>{};

  /// 获取实例
  T? getInstance<T>() => _instances[T] as T?;

  /// 设置实例
  void setInstance<T>(T instance) {
    _instances[T] = instance;
  }

  /// 移除实例
  void removeInstance<T>() {
    _instances.remove(T);
  }

  /// 清空作用域
  void clear() {
    _instances.clear();
  }

  /// 获取所有实例
  Map<Type, dynamic> getAllInstances() => Map<Type, dynamic>.from(_instances);
}

/// 依赖注入容器
///
/// 提供依赖注册、解析、生命周期管理等功能
class DIContainer {
  DIContainer({this.name = 'default'});

  /// 容器名称
  final String name;

  /// 依赖注册表
  final Map<Type, DependencyRegistration<dynamic>> _registrations =
      <Type, DependencyRegistration<dynamic>>{};

  /// 单例实例
  final Map<Type, dynamic> _singletons = <Type, dynamic>{};

  /// 作用域管理
  final Map<String, DependencyScope> _scopes = <String, DependencyScope>{};

  /// 当前作用域
  String? _currentScope;

  /// 解析栈（用于检测循环依赖）
  final List<Type> _resolutionStack = <Type>[];

  /// 注册单例依赖
  void registerSingleton<T>(
    DependencyFactory<T> factory, {
    DependencyDisposer<T>? disposer,
    List<Type> dependencies = const <Type>[],
    List<String> tags = const <String>[],
  }) {
    _registrations[T] = DependencyRegistration<T>(
      factory: factory,
      lifecycle: DependencyLifecycle.singleton,
      disposer: disposer,
      dependencies: dependencies,
      tags: tags,
    );
  }

  /// 注册瞬态依赖
  void registerTransient<T>(
    DependencyFactory<T> factory, {
    DependencyDisposer<T>? disposer,
    List<Type> dependencies = const <Type>[],
    List<String> tags = const <String>[],
  }) {
    _registrations[T] = DependencyRegistration<T>(
      factory: factory,
      lifecycle: DependencyLifecycle.transient,
      disposer: disposer,
      dependencies: dependencies,
      tags: tags,
    );
  }

  /// 注册作用域依赖
  void registerScoped<T>(
    DependencyFactory<T> factory, {
    DependencyDisposer<T>? disposer,
    List<Type> dependencies = const <Type>[],
    List<String> tags = const <String>[],
  }) {
    _registrations[T] = DependencyRegistration<T>(
      factory: factory,
      lifecycle: DependencyLifecycle.scoped,
      disposer: disposer,
      dependencies: dependencies,
      tags: tags,
    );
  }

  /// 注册实例
  void registerInstance<T>(T instance) {
    _singletons[T] = instance;
    _registrations[T] = DependencyRegistration<T>(
      factory: (_) => instance,
      lifecycle: DependencyLifecycle.singleton,
    );
  }

  /// 解析依赖
  T resolve<T>() => _resolveInternal<T>();

  /// 尝试解析依赖
  T? tryResolve<T>() {
    try {
      return resolve<T>();
    } catch (e) {
      return null;
    }
  }

  /// 解析所有指定标签的依赖
  List<T> resolveAll<T>(String tag) {
    final results = <T>[];

    for (final entry in _registrations.entries) {
      final registration = entry.value;
      if (registration.tags.contains(tag)) {
        try {
          final instance = _resolveByType(entry.key);
          if (instance is T) {
            results.add(instance);
          }
        } catch (e) {
          // 忽略解析失败的依赖
        }
      }
    }

    return results;
  }

  /// 检查是否已注册
  bool isRegistered<T>() => _registrations.containsKey(T);

  /// 创建作用域
  void createScope(String name) {
    _scopes[name] = DependencyScope(name: name);
  }

  /// 进入作用域
  void enterScope(String name) {
    if (!_scopes.containsKey(name)) {
      createScope(name);
    }
    _currentScope = name;
  }

  /// 退出作用域
  void exitScope() {
    _currentScope = null;
  }

  /// 销毁作用域
  Future<void> disposeScope(String name) async {
    final scope = _scopes[name];
    if (scope == null) return;

    // 销毁作用域内的所有实例
    for (final entry in scope.getAllInstances().entries) {
      final registration = _registrations[entry.key];
      if (registration?.disposer != null) {
        await registration!.disposer!(entry.value);
      }
    }

    scope.clear();
    _scopes.remove(name);

    if (_currentScope == name) {
      _currentScope = null;
    }
  }

  /// 内部解析方法
  T _resolveInternal<T>() {
    // 检查循环依赖
    if (_resolutionStack.contains(T)) {
      throw StateError(
          'Circular dependency detected: ${_resolutionStack.join(' -> ')} -> $T');
    }

    _resolutionStack.add(T);

    try {
      return _resolveByType(T) as T;
    } finally {
      _resolutionStack.removeLast();
    }
  }

  /// 根据类型解析
  dynamic _resolveByType(Type type) {
    final registration = _registrations[type];
    if (registration == null) {
      throw StateError('Type $type is not registered');
    }

    switch (registration.lifecycle) {
      case DependencyLifecycle.singleton:
        return _resolveSingleton(type, registration);
      case DependencyLifecycle.transient:
        return _resolveTransient(registration);
      case DependencyLifecycle.scoped:
        return _resolveScoped(type, registration);
    }
  }

  /// 解析单例
  dynamic _resolveSingleton(
      Type type, DependencyRegistration<dynamic> registration) {
    if (_singletons.containsKey(type)) {
      return _singletons[type];
    }

    final instance = registration.factory(this);
    _singletons[type] = instance;
    return instance;
  }

  /// 解析瞬态
  dynamic _resolveTransient(DependencyRegistration<dynamic> registration) =>
      registration.factory(this);

  /// 解析作用域
  dynamic _resolveScoped(
      Type type, DependencyRegistration<dynamic> registration) {
    if (_currentScope == null) {
      throw StateError('No active scope for scoped dependency $type');
    }

    final scope = _scopes[_currentScope!]!;
    final existing = scope.getInstance<dynamic>();
    if (existing != null) {
      return existing;
    }

    final instance = registration.factory(this);
    scope.setInstance<dynamic>(instance);
    return instance;
  }

  /// 获取注册信息
  DependencyRegistration<dynamic>? getRegistration<T>() => _registrations[T];

  /// 获取所有注册信息
  Map<Type, DependencyRegistration<dynamic>> getAllRegistrations() =>
      Map<Type, DependencyRegistration<dynamic>>.from(_registrations);

  /// 获取容器统计信息
  Map<String, dynamic> getStats() => <String, dynamic>{
        'name': name,
        'registrations': _registrations.length,
        'singletons': _singletons.length,
        'scopes': _scopes.length,
        'currentScope': _currentScope,
        'resolutionStackDepth': _resolutionStack.length,
      };

  /// 验证依赖图
  List<String> validateDependencies() {
    final errors = <String>[];

    for (final entry in _registrations.entries) {
      final type = entry.key;
      final registration = entry.value;

      // 检查依赖是否都已注册
      for (final dependency in registration.dependencies) {
        if (!_registrations.containsKey(dependency)) {
          errors.add('Type $type depends on unregistered type $dependency');
        }
      }

      // 检查循环依赖
      try {
        _checkCircularDependency(type, <Type>[]);
      } catch (e) {
        errors.add(e.toString());
      }
    }

    return errors;
  }

  /// 检查循环依赖
  void _checkCircularDependency(Type type, List<Type> visited) {
    if (visited.contains(type)) {
      throw StateError(
          'Circular dependency detected: ${visited.join(' -> ')} -> $type');
    }

    final registration = _registrations[type];
    if (registration == null) return;

    visited.add(type);

    for (final dependency in registration.dependencies) {
      _checkCircularDependency(dependency, List<Type>.from(visited));
    }
  }

  /// 清理容器
  Future<void> dispose() async {
    // 销毁所有作用域
    for (final scopeName in _scopes.keys.toList()) {
      await disposeScope(scopeName);
    }

    // 销毁所有单例
    for (final entry in _singletons.entries) {
      final registration = _registrations[entry.key];
      if (registration?.disposer != null) {
        await registration!.disposer!(entry.value);
      }
    }

    _registrations.clear();
    _singletons.clear();
    _scopes.clear();
    _resolutionStack.clear();
    _currentScope = null;
  }
}
