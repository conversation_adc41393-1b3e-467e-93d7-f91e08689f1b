/*
---------------------------------------------------------------
File name:          plugin_main.dart
Author:             Pet App V3 Team
Date created:       2025-07-25
Last modified:      2025-07-25
Dart Version:       ^3.2.0
Description:        My Awesome Plugin - 测试插件修复
---------------------------------------------------------------
Change History:
    2025-07-25: Initial creation - 测试插件修复;
---------------------------------------------------------------
*/

/// My Awesome Plugin Plugin Library
///
/// 测试插件修复
///
/// 这是一个为Pet App V3设计的tool插件，提供以下功能：
/// - 插件核心功能实现

/// - UI组件和界面元素

/// - 多平台兼容性支持
///
/// ## 使用方法
///
/// ```dart
/// import 'package:test_plugin_fixed/test_plugin_fixed.dart';
///
/// final plugin = TestPluginFixedPlugin();
/// await plugin.initialize();
/// ```
///
/// ## 权限要求
///
///
/// ## 平台支持
///
/// - ✅ Android
/// - ✅ iOS
/// - ✅ Web
/// - ✅ Desktop (Windows/macOS/Linux)

library test_plugin_fixed;

// 导出Pet App V3插件系统
export 'package:plugin_system/plugin_system.dart';

// 导出核心插件类
export 'src/plugin_core.dart';
