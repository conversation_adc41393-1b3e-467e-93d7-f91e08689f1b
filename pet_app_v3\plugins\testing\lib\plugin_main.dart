/*
---------------------------------------------------------------
File name:          plugin_main.dart
Author:             Pet App V3 Team
Date created:       2025-07-25
Last modified:      2025-07-25
Dart Version:       ^3.2.0
Description:        Test Plugin Fixed - A new Flutter project created with Ming Status CLI
---------------------------------------------------------------
Change History:
    2025-07-25: Initial creation - A new Flutter project created with Ming Status CLI;
---------------------------------------------------------------
*/

/// Test Plugin Fixed Plugin Library
///
/// A new Flutter project created with Ming Status CLI
///
/// 这是一个为Pet App V3设计的tool插件，提供以下功能：
/// - 插件核心功能实现

/// - UI组件和界面元素

/// - 多平台兼容性支持
///
/// ## 使用方法
///
/// ```dart
/// import 'package:test_plugin_fixed/test_plugin_fixed.dart';
///
/// final plugin = TestPluginFixedPlugin();
/// await plugin.initialize();
/// ```
///
/// ## 权限要求
///
///
/// ## 平台支持
///
/// - ✅ Android
/// - ✅ iOS
/// - ✅ Web
/// - ✅ Desktop (Windows/macOS/Linux)

library test_plugin_fixed;

// 导出核心插件类
export 'src/plugin_core.dart';
