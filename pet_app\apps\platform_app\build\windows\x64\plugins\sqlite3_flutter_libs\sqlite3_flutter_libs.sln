﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{3E80566F-0AF5-3CBC-9BB1-200B81FC4031}"
	ProjectSection(ProjectDependencies) = postProject
		{5CA7C8BE-4ACC-3555-B6A8-3A391FB2D248} = {5CA7C8BE-4ACC-3555-B6A8-3A391FB2D248}
		{904B575A-63F6-3D57-8C59-ACDC1098FD83} = {904B575A-63F6-3D57-8C59-ACDC1098FD83}
		{413D6285-7691-3586-AE8D-4020616D467A} = {413D6285-7691-3586-AE8D-4020616D467A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{0B0E1296-2500-3E7D-9E98-2DA848301D65}"
	ProjectSection(ProjectDependencies) = postProject
		{3E80566F-0AF5-3CBC-9BB1-200B81FC4031} = {3E80566F-0AF5-3CBC-9BB1-200B81FC4031}
		{5CA7C8BE-4ACC-3555-B6A8-3A391FB2D248} = {5CA7C8BE-4ACC-3555-B6A8-3A391FB2D248}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\\ZERO_CHECK.vcxproj", "{5CA7C8BE-4ACC-3555-B6A8-3A391FB2D248}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "..\..\flutter\flutter_assemble.vcxproj", "{CA518CBC-CC03-357A-92EC-941476ACF34F}"
	ProjectSection(ProjectDependencies) = postProject
		{5CA7C8BE-4ACC-3555-B6A8-3A391FB2D248} = {5CA7C8BE-4ACC-3555-B6A8-3A391FB2D248}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "..\..\flutter\flutter_wrapper_plugin.vcxproj", "{9C80F184-BF45-3F69-B6A0-145290D47789}"
	ProjectSection(ProjectDependencies) = postProject
		{5CA7C8BE-4ACC-3555-B6A8-3A391FB2D248} = {5CA7C8BE-4ACC-3555-B6A8-3A391FB2D248}
		{CA518CBC-CC03-357A-92EC-941476ACF34F} = {CA518CBC-CC03-357A-92EC-941476ACF34F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "sqlite3", "sqlite3.vcxproj", "{904B575A-63F6-3D57-8C59-ACDC1098FD83}"
	ProjectSection(ProjectDependencies) = postProject
		{5CA7C8BE-4ACC-3555-B6A8-3A391FB2D248} = {5CA7C8BE-4ACC-3555-B6A8-3A391FB2D248}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "sqlite3_flutter_libs_plugin", "sqlite3_flutter_libs_plugin.vcxproj", "{413D6285-7691-3586-AE8D-4020616D467A}"
	ProjectSection(ProjectDependencies) = postProject
		{5CA7C8BE-4ACC-3555-B6A8-3A391FB2D248} = {5CA7C8BE-4ACC-3555-B6A8-3A391FB2D248}
		{CA518CBC-CC03-357A-92EC-941476ACF34F} = {CA518CBC-CC03-357A-92EC-941476ACF34F}
		{9C80F184-BF45-3F69-B6A0-145290D47789} = {9C80F184-BF45-3F69-B6A0-145290D47789}
		{904B575A-63F6-3D57-8C59-ACDC1098FD83} = {904B575A-63F6-3D57-8C59-ACDC1098FD83}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{3E80566F-0AF5-3CBC-9BB1-200B81FC4031}.Debug|x64.ActiveCfg = Debug|x64
		{3E80566F-0AF5-3CBC-9BB1-200B81FC4031}.Debug|x64.Build.0 = Debug|x64
		{3E80566F-0AF5-3CBC-9BB1-200B81FC4031}.Profile|x64.ActiveCfg = Profile|x64
		{3E80566F-0AF5-3CBC-9BB1-200B81FC4031}.Profile|x64.Build.0 = Profile|x64
		{3E80566F-0AF5-3CBC-9BB1-200B81FC4031}.Release|x64.ActiveCfg = Release|x64
		{3E80566F-0AF5-3CBC-9BB1-200B81FC4031}.Release|x64.Build.0 = Release|x64
		{0B0E1296-2500-3E7D-9E98-2DA848301D65}.Debug|x64.ActiveCfg = Debug|x64
		{0B0E1296-2500-3E7D-9E98-2DA848301D65}.Profile|x64.ActiveCfg = Profile|x64
		{0B0E1296-2500-3E7D-9E98-2DA848301D65}.Release|x64.ActiveCfg = Release|x64
		{5CA7C8BE-4ACC-3555-B6A8-3A391FB2D248}.Debug|x64.ActiveCfg = Debug|x64
		{5CA7C8BE-4ACC-3555-B6A8-3A391FB2D248}.Debug|x64.Build.0 = Debug|x64
		{5CA7C8BE-4ACC-3555-B6A8-3A391FB2D248}.Profile|x64.ActiveCfg = Profile|x64
		{5CA7C8BE-4ACC-3555-B6A8-3A391FB2D248}.Profile|x64.Build.0 = Profile|x64
		{5CA7C8BE-4ACC-3555-B6A8-3A391FB2D248}.Release|x64.ActiveCfg = Release|x64
		{5CA7C8BE-4ACC-3555-B6A8-3A391FB2D248}.Release|x64.Build.0 = Release|x64
		{CA518CBC-CC03-357A-92EC-941476ACF34F}.Debug|x64.ActiveCfg = Debug|x64
		{CA518CBC-CC03-357A-92EC-941476ACF34F}.Debug|x64.Build.0 = Debug|x64
		{CA518CBC-CC03-357A-92EC-941476ACF34F}.Profile|x64.ActiveCfg = Profile|x64
		{CA518CBC-CC03-357A-92EC-941476ACF34F}.Profile|x64.Build.0 = Profile|x64
		{CA518CBC-CC03-357A-92EC-941476ACF34F}.Release|x64.ActiveCfg = Release|x64
		{CA518CBC-CC03-357A-92EC-941476ACF34F}.Release|x64.Build.0 = Release|x64
		{9C80F184-BF45-3F69-B6A0-145290D47789}.Debug|x64.ActiveCfg = Debug|x64
		{9C80F184-BF45-3F69-B6A0-145290D47789}.Debug|x64.Build.0 = Debug|x64
		{9C80F184-BF45-3F69-B6A0-145290D47789}.Profile|x64.ActiveCfg = Profile|x64
		{9C80F184-BF45-3F69-B6A0-145290D47789}.Profile|x64.Build.0 = Profile|x64
		{9C80F184-BF45-3F69-B6A0-145290D47789}.Release|x64.ActiveCfg = Release|x64
		{9C80F184-BF45-3F69-B6A0-145290D47789}.Release|x64.Build.0 = Release|x64
		{904B575A-63F6-3D57-8C59-ACDC1098FD83}.Debug|x64.ActiveCfg = Debug|x64
		{904B575A-63F6-3D57-8C59-ACDC1098FD83}.Debug|x64.Build.0 = Debug|x64
		{904B575A-63F6-3D57-8C59-ACDC1098FD83}.Profile|x64.ActiveCfg = Profile|x64
		{904B575A-63F6-3D57-8C59-ACDC1098FD83}.Profile|x64.Build.0 = Profile|x64
		{904B575A-63F6-3D57-8C59-ACDC1098FD83}.Release|x64.ActiveCfg = Release|x64
		{904B575A-63F6-3D57-8C59-ACDC1098FD83}.Release|x64.Build.0 = Release|x64
		{413D6285-7691-3586-AE8D-4020616D467A}.Debug|x64.ActiveCfg = Debug|x64
		{413D6285-7691-3586-AE8D-4020616D467A}.Debug|x64.Build.0 = Debug|x64
		{413D6285-7691-3586-AE8D-4020616D467A}.Profile|x64.ActiveCfg = Profile|x64
		{413D6285-7691-3586-AE8D-4020616D467A}.Profile|x64.Build.0 = Profile|x64
		{413D6285-7691-3586-AE8D-4020616D467A}.Release|x64.ActiveCfg = Release|x64
		{413D6285-7691-3586-AE8D-4020616D467A}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {18CEEA8D-B9C1-3F03-90D7-60C5E42C0631}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
