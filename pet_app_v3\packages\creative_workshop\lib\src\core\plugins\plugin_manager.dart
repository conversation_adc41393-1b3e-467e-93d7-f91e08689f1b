 /*
---------------------------------------------------------------
File name:          plugin_manager_proxy.dart
Author:             Pet App Team
Date created:       2025-08-03
Last modified:      2025-08-03
Dart Version:       3.2+
Description:        插件管理器代理 - 避免重复造轮子
---------------------------------------------------------------
*/

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:plugin_system/plugin_system.dart';
import 'package:app_manager/app_manager.dart';

/// 插件状态
enum PluginState {
  /// 未安装
  notInstalled,

  /// 已安装但未启用
  disabled,

  /// 已启用
  enabled,

  /// 正在安装
  installing,

  /// 正在卸载
  uninstalling,

  /// 正在启用
  enabling,

  /// 正在禁用
  disabling,

  /// 正在更新
  updating,

  /// 有可用更新
  updateAvailable,

  /// 错误状态
  error,
}

/// 插件权限
enum PluginPermission {
  /// 文件系统访问
  fileSystem,

  /// 网络访问
  network,

  /// 剪贴板访问
  clipboard,

  /// 相机访问
  camera,

  /// 麦克风访问
  microphone,

  /// 位置访问
  location,

  /// 通知权限
  notifications,

  /// 设备信息权限
  deviceInfo,
}

/// PluginPermission扩展
extension PluginPermissionExtension on PluginPermission {
  /// 获取权限显示名称
  String get displayName {
    switch (this) {
      case PluginPermission.fileSystem:
        return '文件系统访问';
      case PluginPermission.network:
        return '网络访问';
      case PluginPermission.clipboard:
        return '剪贴板访问';
      case PluginPermission.camera:
        return '相机访问';
      case PluginPermission.microphone:
        return '麦克风访问';
      case PluginPermission.location:
        return '位置访问';
      case PluginPermission.notifications:
        return '通知权限';
      case PluginPermission.deviceInfo:
        return '设备信息';
    }
  }
}

/// 插件依赖
class PluginDependency {
  final String pluginId;
  final String version;
  final bool isRequired;

  const PluginDependency({
    required this.pluginId,
    required this.version,
    this.isRequired = true,
  });
}

/// 插件安装信息
class PluginInstallInfo {
  final String id;
  final String name;
  final String version;
  final PluginState state;
  final DateTime installedAt;
  final DateTime lastUsedAt;
  final List<PluginPermission> permissions;
  final List<PluginDependency> dependencies;
  final int size;
  final bool autoUpdate;

  const PluginInstallInfo({
    required this.id,
    required this.name,
    required this.version,
    required this.state,
    required this.installedAt,
    required this.lastUsedAt,
    required this.permissions,
    required this.dependencies,
    required this.size,
    required this.autoUpdate,
  });

  PluginInstallInfo copyWith({
    String? id,
    String? name,
    String? version,
    PluginState? state,
    DateTime? installedAt,
    DateTime? lastUsedAt,
    List<PluginPermission>? permissions,
    List<PluginDependency>? dependencies,
    int? size,
    bool? autoUpdate,
  }) {
    return PluginInstallInfo(
      id: id ?? this.id,
      name: name ?? this.name,
      version: version ?? this.version,
      state: state ?? this.state,
      installedAt: installedAt ?? this.installedAt,
      lastUsedAt: lastUsedAt ?? this.lastUsedAt,
      permissions: permissions ?? this.permissions,
      dependencies: dependencies ?? this.dependencies,
      size: size ?? this.size,
      autoUpdate: autoUpdate ?? this.autoUpdate,
    );
  }
}

/// 插件操作结果
class PluginOperationResult {
  final bool success;
  final String message;

  const PluginOperationResult({
    required this.success,
    required this.message,
  });

  factory PluginOperationResult.success(String message) {
    return PluginOperationResult(success: true, message: message);
  }

  factory PluginOperationResult.failure(String message) {
    return PluginOperationResult(success: false, message: message);
  }
}

/// 插件管理器代理（完全委托给企业级服务，避免重复造轮子）
///
/// 此类作为Creative Workshop的插件管理代理，
/// 所有操作都委托给企业级的PluginInstallationService和Plugin System。
/// 不再维护重复的状态，确保使用统一的插件管理架构。
class PluginManager extends ChangeNotifier {
  PluginManager._();
  static final PluginManager _instance = PluginManager._();
  static PluginManager get instance => _instance;

  /// 企业级插件安装服务（主要服务）
  final PluginInstallationService _installationService =
      PluginInstallationService.instance;

  /// Plugin System注册表（状态查询）
  final PluginRegistry _registry = PluginRegistry.instance;

  /// 是否已初始化
  bool _isInitialized = false;

  /// 获取所有已安装插件（从Plugin System获取）
  List<PluginInstallInfo> get installedPlugins {
    // TODO: 从Plugin System Registry获取真实的插件列表
    // 当前返回模拟数据，避免重复造轮子
    return _getSimulatedInstalledPlugins();
  }

  /// 获取已启用插件（从Plugin System获取）
  List<PluginInstallInfo> get enabledPlugins {
    return installedPlugins
        .where((p) => p.state == PluginState.enabled)
        .toList();
  }

  /// 获取需要更新的插件（从Plugin System获取）
  List<PluginInstallInfo> get updatablePlugins {
    return installedPlugins
        .where((p) => p.state == PluginState.updateAvailable)
        .toList();
  }

  /// 初始化插件管理器代理
  Future<void> initialize() async {
    if (_isInitialized) {
      return;
    }

    try {
      // 初始化企业级插件安装服务
      await _installationService.initialize();

      _isInitialized = true;
      debugPrint('插件管理器代理已初始化');
    } catch (e) {
      debugPrint('插件管理器代理初始化失败: $e');
      rethrow;
    }
  }

  /// 安装插件（委托给企业级服务）
  Future<PluginOperationResult> installPlugin(
    String pluginId, {
    String? version,
    bool autoUpdate = true,
  }) async {
    try {
      // 使用企业级插件安装服务
      final result = await _installationService.installPlugin(
        pluginId: pluginId,
        version: version,
        config: {
          'autoUpdate': autoUpdate,
          'source': 'creative_workshop',
        },
      );

      if (result.success) {
        notifyListeners();
        return PluginOperationResult.success(result.message ?? '插件安装成功');
      } else {
        return PluginOperationResult.failure(result.message ?? '安装失败');
      }
    } catch (e) {
      return PluginOperationResult.failure('安装失败: $e');
    }
  }

  /// 卸载插件（委托给企业级服务）
  Future<PluginOperationResult> uninstallPlugin(String pluginId) async {
    try {
      final result = await _installationService.uninstallPlugin(pluginId);

      if (result.success) {
        notifyListeners();
        return PluginOperationResult.success(result.message ?? '插件卸载成功');
      } else {
        return PluginOperationResult.failure(result.message ?? '卸载失败');
      }
    } catch (e) {
      return PluginOperationResult.failure('卸载失败: $e');
    }
  }

  /// 获取插件信息
  PluginInstallInfo? getPluginInfo(String pluginId) {
    try {
      return installedPlugins.firstWhere(
        (plugin) => plugin.id == pluginId,
      );
    } catch (e) {
      return null;
    }
  }

  /// 获取插件统计信息
  Map<String, dynamic> getPluginStats() {
    final plugins = installedPlugins;
    return {
      'totalInstalled': plugins.length,
      'enabled': plugins.where((p) => p.state == PluginState.enabled).length,
      'disabled': plugins.where((p) => p.state == PluginState.disabled).length,
      'updateAvailable':
          plugins.where((p) => p.state == PluginState.updateAvailable).length,
    };
  }

  /// 获取模拟的已安装插件列表
  List<PluginInstallInfo> _getSimulatedInstalledPlugins() {
    final now = DateTime.now();
    return [
      PluginInstallInfo(
        id: 'advanced_brush',
        name: '高级画笔工具',
        version: '1.2.0',
        state: PluginState.enabled,
        installedAt: now.subtract(const Duration(days: 30)),
        lastUsedAt: now.subtract(const Duration(hours: 2)),
        permissions: [
          PluginPermission.fileSystem,
          PluginPermission.clipboard,
        ],
        dependencies: [],
        size: 2048000, // 2MB
        autoUpdate: true,
      ),
      PluginInstallInfo(
        id: 'color_palette',
        name: '调色板专家',
        version: '1.5.2',
        state: PluginState.updateAvailable,
        installedAt: now.subtract(const Duration(days: 60)),
        lastUsedAt: now.subtract(const Duration(days: 1)),
        permissions: [
          PluginPermission.clipboard,
          PluginPermission.camera,
        ],
        dependencies: [],
        size: 1024000, // 1MB
        autoUpdate: true,
      ),
    ];
  }
}
