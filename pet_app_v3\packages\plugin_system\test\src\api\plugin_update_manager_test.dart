/*
---------------------------------------------------------------
File name:          plugin_update_manager_test.dart
Author:             lgnorant-lu
Date created:       2025/07/27
Last modified:      2025/07/28
Dart Version:       3.2+
Description:        插件更新管理器测试 (Plugin update manager tests)
---------------------------------------------------------------
Change History:
    2025/07/27: Initial creation - 插件更新管理器测试;
---------------------------------------------------------------
*/

import 'package:flutter_test/flutter_test.dart';
import 'package:plugin_system/src/api/plugin_update_manager.dart';
import 'package:plugin_system/src/core/plugin_registry.dart';

import '../../helpers/test_plugin.dart';

void main() {
  group('PluginUpdateManager', () {
    late PluginUpdateManager updateManager;
    late PluginRegistry registry;

    setUp(() {
      registry = PluginRegistry.instance;
      updateManager = PluginUpdateManager(registry: registry);
    });

    tearDown(() async {
      // 清理注册表
      final allPlugins = registry.getAllPlugins().toList(); // 创建副本避免并发修改
      for (final plugin in allPlugins) {
        try {
          await registry.unregister(plugin.id);
        } catch (e) {
          // 忽略卸载错误
        }
      }
    });

    group('更新检查测试', () {
      test('应该能检查插件更新', () async {
        // 注册测试插件
        final testPlugin = TestPlugin(
          pluginId: 'update_test_plugin',
          pluginName: 'Update Test Plugin',
          pluginVersion: '1.0.0',
        );
        await registry.register(testPlugin);

        // 检查更新
        final result =
            await updateManager.checkPluginUpdate('update_test_plugin');

        expect(result.isSuccess, isTrue);
        expect(result.data, isNotNull);
        expect(result.data!['pluginId'], equals('update_test_plugin'));
        expect(result.data!['currentVersion'], equals('1.0.0'));
        expect(result.data!['hasUpdate'], isA<bool>());
        expect(result.data!['checkedAt'], isNotNull);
      });

      test('应该拒绝空插件ID', () async {
        final result = await updateManager.checkPluginUpdate('');

        expect(result.isSuccess, isFalse);
        expect(result.statusCode, equals(400));
        expect(result.message, contains('插件ID不能为空'));
      });

      test('应该拒绝不存在的插件', () async {
        final result =
            await updateManager.checkPluginUpdate('non_existent_plugin');

        expect(result.isSuccess, isFalse);
        expect(result.statusCode, equals(404));
        expect(result.message, contains('插件不存在'));
      });
    });

    group('插件更新测试', () {
      test('应该能更新插件', () async {
        // 注册测试插件
        final testPlugin = TestPlugin(
          pluginId: 'update_plugin_test',
          pluginName: 'Update Plugin Test',
          pluginVersion: '1.0.0',
        );
        await registry.register(testPlugin);

        // 执行更新
        final result = await updateManager.updatePlugin('update_plugin_test');

        expect(result.isSuccess, isTrue);
        expect(result.data, isNotNull);
        expect(result.data!['oldVersion'], equals('1.0.0'));
        expect(result.data!['newVersion'], isNotNull);
        expect(result.data!['updateSize'], isNotNull);
        expect(result.data!['updatedAt'], isNotNull);
      });

      test('应该能更新到指定版本', () async {
        // 注册测试插件
        final testPlugin = TestPlugin(
          pluginId: 'target_version_test',
          pluginName: 'Target Version Test',
        );
        await registry.register(testPlugin);

        // 更新到指定版本
        final result = await updateManager.updatePlugin(
          'target_version_test',
          targetVersion: '2.0.0',
        );

        expect(result.isSuccess, isTrue);
        expect(result.data!['newVersion'], equals('2.0.0'));
      });

      test('应该拒绝空插件ID', () async {
        final result = await updateManager.updatePlugin('');

        expect(result.isSuccess, isFalse);
        expect(result.statusCode, equals(400));
        expect(result.message, contains('插件ID不能为空'));
      });

      test('应该拒绝不存在的插件', () async {
        final result = await updateManager.updatePlugin('non_existent_plugin');

        expect(result.isSuccess, isFalse);
        expect(result.statusCode, equals(404));
        expect(result.message, contains('插件不存在'));
      });
    });

    group('插件回滚测试', () {
      test('应该能回滚插件', () async {
        // 注册测试插件
        final testPlugin = TestPlugin(
          pluginId: 'rollback_test_plugin',
          pluginName: 'Rollback Test Plugin',
          pluginVersion: '2.0.0',
        );
        await registry.register(testPlugin);

        // 执行回滚
        final result = await updateManager.rollbackPlugin(
          'rollback_test_plugin',
          targetVersion: '1.0.0',
        );

        expect(result.isSuccess, isTrue);
        expect(result.data, isNotNull);
        expect(result.data!['rolledBackFrom'], equals('2.0.0'));
        expect(result.data!['rolledBackTo'], equals('1.0.0'));
        expect(result.data!['rolledBackAt'], isNotNull);
      });

      test('应该拒绝空插件ID', () async {
        final result =
            await updateManager.rollbackPlugin('', targetVersion: '1.0.0');

        expect(result.isSuccess, isFalse);
        expect(result.statusCode, equals(400));
        expect(result.message, contains('插件ID不能为空'));
      });

      test('应该拒绝不存在的插件', () async {
        final result = await updateManager.rollbackPlugin(
          'non_existent_plugin',
          targetVersion: '1.0.0',
        );

        expect(result.isSuccess, isFalse);
        expect(result.statusCode, equals(404));
        expect(result.message, contains('插件不存在'));
      });
    });

    group('版本管理测试', () {
      test('应该能获取最新版本', () async {
        // 注册测试插件
        final testPlugin = TestPlugin(
          pluginId: 'version_test_plugin',
          pluginName: 'Version Test Plugin',
        );
        await registry.register(testPlugin);

        // 检查更新以触发版本获取
        final result =
            await updateManager.checkPluginUpdate('version_test_plugin');

        expect(result.isSuccess, isTrue);
        expect(result.data!['latestVersion'], isNotNull);
        expect(result.data!['latestVersion'], isA<String>());
      });

      test('应该能计算更新包大小', () async {
        // 注册测试插件
        final testPlugin = TestPlugin(
          pluginId: 'size_test_plugin',
          pluginName: 'Size Test Plugin',
        );
        await registry.register(testPlugin);

        // 执行更新以触发大小计算
        final result = await updateManager.updatePlugin('size_test_plugin');

        expect(result.isSuccess, isTrue);
        expect(result.data!['updateSize'], isNotNull);
        expect(result.data!['updateSize'], isA<String>());
      });
    });

    group('错误处理测试', () {
      test('应该能处理更新过程中的异常', () async {
        // 注册测试插件
        final testPlugin = TestPlugin(
          pluginId: 'error_test_plugin',
          pluginName: 'Error Test Plugin',
        );
        await registry.register(testPlugin);

        // 模拟更新过程中的错误
        // 这里通过正常流程测试，因为模拟实现通常会成功
        final result = await updateManager.updatePlugin('error_test_plugin');

        // 验证结果格式正确
        expect(result, isNotNull);
        expect(result.isSuccess, isA<bool>());
      });

      test('应该能处理检查更新过程中的异常', () async {
        // 注册测试插件
        final testPlugin = TestPlugin(
          pluginId: 'check_error_plugin',
          pluginName: 'Check Error Plugin',
        );
        await registry.register(testPlugin);

        // 检查更新
        final result =
            await updateManager.checkPluginUpdate('check_error_plugin');

        // 验证结果格式正确
        expect(result, isNotNull);
        expect(result.isSuccess, isA<bool>());
      });
    });

    group('备份和恢复测试', () {
      test('应该能创建插件备份', () async {
        // 注册测试插件
        final testPlugin = TestPlugin(
          pluginId: 'backup_test_plugin',
          pluginName: 'Backup Test Plugin',
        );
        await registry.register(testPlugin);

        // 执行更新（会触发备份）
        final result = await updateManager.updatePlugin('backup_test_plugin');

        expect(result.isSuccess, isTrue);
        // 备份过程在更新中自动执行
      });
    });
  });
}
