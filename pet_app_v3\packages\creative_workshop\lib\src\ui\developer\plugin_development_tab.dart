/*
---------------------------------------------------------------
File name:          plugin_development_tab.dart
Author:             lgnorant-lu
Date created:       2025-07-22
Last modified:      2025-08-02
Dart Version:       3.2+
Description:        插件开发标签页
---------------------------------------------------------------
Change History:
    2025-07-22: Phase ******* - 插件开发功能实现;
---------------------------------------------------------------
*/

import 'package:flutter/material.dart';

/// 开发工具类型
enum DevelopmentTool {
  codeEditor('代码编辑器'),
  debugger('调试器'),
  tester('测试工具'),
  profiler('性能分析'),
  documentation('文档生成'),
  packaging('打包工具');

  const DevelopmentTool(this.displayName);
  final String displayName;
}

/// 插件开发标签页
class PluginDevelopmentTab extends StatefulWidget {
  const PluginDevelopmentTab({super.key});

  @override
  State<PluginDevelopmentTab> createState() => _PluginDevelopmentTabState();
}

class _PluginDevelopmentTabState extends State<PluginDevelopmentTab> {
  String? _selectedProjectId;
  DevelopmentTool _selectedTool = DevelopmentTool.codeEditor;
  bool _isToolLoading = false;

  // 模拟项目列表
  final List<Map<String, String>> _projects = [
    {'id': 'proj_001', 'name': '高级画笔工具'},
    {'id': 'proj_002', 'name': '拼图游戏引擎'},
    {'id': 'proj_003', 'name': '颜色管理器'},
    {'id': 'proj_004', 'name': '暗色主题包'},
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 项目选择和工具栏
        _buildToolbar(),

        // 主要开发区域
        Expanded(
          child: _selectedProjectId == null
              ? _buildProjectSelector()
              : _buildDevelopmentArea(),
        ),
      ],
    );
  }

  /// 构建工具栏
  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          // 项目选择
          Row(
            children: [
              const Icon(Icons.folder),
              const SizedBox(width: 8),
              const Text('当前项目:'),
              const SizedBox(width: 12),
              Expanded(
                child: DropdownButtonFormField<String?>(
                  value: _selectedProjectId,
                  onChanged: (value) {
                    setState(() {
                      _selectedProjectId = value;
                    });
                  },
                  decoration: InputDecoration(
                    hintText: '选择项目',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  items: [
                    const DropdownMenuItem<String?>(
                      value: null,
                      child: Text('请选择项目'),
                    ),
                    ..._projects.map((project) => DropdownMenuItem(
                          value: project['id'],
                          child: Text(project['name']!),
                        )),
                  ],
                ),
              ),
            ],
          ),

          if (_selectedProjectId != null) ...[
            const SizedBox(height: 12),

            // 开发工具选择
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: DevelopmentTool.values.map((tool) {
                  final isSelected = _selectedTool == tool;
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(tool.displayName),
                      selected: isSelected,
                      onSelected: (selected) {
                        if (selected) {
                          setState(() {
                            _selectedTool = tool;
                          });
                        }
                      },
                      avatar: Icon(
                        _getToolIcon(tool),
                        size: 18,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建项目选择器
  Widget _buildProjectSelector() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.code,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '选择项目开始开发',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '请在上方选择一个项目来开始插件开发',
            style: TextStyle(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _createNewProject,
            icon: const Icon(Icons.add),
            label: const Text('创建新项目'),
          ),
        ],
      ),
    );
  }

  /// 构建开发区域
  Widget _buildDevelopmentArea() {
    if (_isToolLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在加载开发工具...'),
          ],
        ),
      );
    }

    switch (_selectedTool) {
      case DevelopmentTool.codeEditor:
        return _buildCodeEditor();
      case DevelopmentTool.debugger:
        return _buildDebugger();
      case DevelopmentTool.tester:
        return _buildTester();
      case DevelopmentTool.profiler:
        return _buildProfiler();
      case DevelopmentTool.documentation:
        return _buildDocumentation();
      case DevelopmentTool.packaging:
        return _buildPackaging();
    }
  }

  /// 构建代码编辑器
  Widget _buildCodeEditor() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.code,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                '代码编辑器',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: _openExternalEditor,
                icon: const Icon(Icons.open_in_new),
                label: const Text('在 VS Code 中打开'),
              ),
              const SizedBox(width: 8),
              ElevatedButton.icon(
                onPressed: _openVSCodeOnline,
                icon: const Icon(Icons.web),
                label: const Text('VS Code 在线版'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: _buildVSCodeOnlineFrame(),
          ),
        ],
      ),
    );
  }

  /// 构建VS Code在线版框架
  Widget _buildVSCodeOnlineFrame() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          // VS Code 标题栏
          Container(
            height: 40,
            decoration: BoxDecoration(
              color: Colors.grey[800],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                const SizedBox(width: 12),
                // 窗口控制按钮
                Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      width: 12,
                      height: 12,
                      decoration: const BoxDecoration(
                        color: Colors.yellow,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      width: 12,
                      height: 12,
                      decoration: const BoxDecoration(
                        color: Colors.green,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ],
                ),
                const SizedBox(width: 16),
                const Text(
                  'VS Code - 插件开发',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon:
                      const Icon(Icons.refresh, color: Colors.white, size: 18),
                  onPressed: _refreshVSCodeOnline,
                ),
              ],
            ),
          ),
          // VS Code 内容区域
          Expanded(
            child: _buildVSCodeContent(),
          ),
        ],
      ),
    );
  }

  /// 构建VS Code内容区域
  Widget _buildVSCodeContent() {
    return Row(
      children: [
        // 侧边栏
        Container(
          width: 60,
          color: Colors.grey[800],
          child: Column(
            children: [
              _buildSidebarIcon(Icons.folder, '文件浏览器', true),
              _buildSidebarIcon(Icons.search, '搜索', false),
              _buildSidebarIcon(Icons.source, '源代码管理', false),
              _buildSidebarIcon(Icons.bug_report, '调试', false),
              _buildSidebarIcon(Icons.extension, '扩展', false),
            ],
          ),
        ),
        // 文件浏览器
        Container(
          width: 200,
          color: Colors.grey[200],
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: 30,
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: const Row(
                  children: [
                    Text(
                      '资源管理器',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: _buildFileExplorer(),
              ),
            ],
          ),
        ),
        // 编辑器区域
        Expanded(
          child: _buildEditorArea(),
        ),
      ],
    );
  }

  /// 构建调试器
  Widget _buildDebugger() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.bug_report,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                '调试器',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: _startDebugging,
                icon: const Icon(Icons.play_arrow),
                label: const Text('开始调试'),
              ),
              const SizedBox(width: 8),
              ElevatedButton.icon(
                onPressed: _stopDebugging,
                icon: const Icon(Icons.stop),
                label: const Text('停止调试'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Row(
              children: [
                // 调试控制面板
                Expanded(
                  flex: 1,
                  child: _buildDebugControlPanel(),
                ),
                const SizedBox(width: 16),
                // 变量监视和调用堆栈
                Expanded(
                  flex: 1,
                  child: _buildDebugInfoPanel(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建调试控制面板
  Widget _buildDebugControlPanel() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: const Row(
              children: [
                Icon(Icons.settings, size: 16),
                SizedBox(width: 8),
                Text(
                  '调试控制',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '断点管理',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  _buildBreakpointItem('main.dart:15', true),
                  _buildBreakpointItem('plugin_core.dart:42', false),
                  _buildBreakpointItem('services.dart:28', true),
                  const SizedBox(height: 16),
                  const Text(
                    '调试操作',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.skip_next),
                        onPressed: _stepOver,
                        tooltip: '单步跳过',
                      ),
                      IconButton(
                        icon: const Icon(Icons.arrow_downward),
                        onPressed: _stepInto,
                        tooltip: '单步进入',
                      ),
                      IconButton(
                        icon: const Icon(Icons.arrow_upward),
                        onPressed: _stepOut,
                        tooltip: '单步跳出',
                      ),
                      IconButton(
                        icon: const Icon(Icons.refresh),
                        onPressed: _restart,
                        tooltip: '重启调试',
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建断点项目
  Widget _buildBreakpointItem(String location, bool isEnabled) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Checkbox(
            value: isEnabled,
            onChanged: (value) => _toggleBreakpoint(location, value ?? false),
          ),
          Expanded(
            child: Text(
              location,
              style: const TextStyle(fontSize: 12),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.delete, size: 16),
            onPressed: () => _removeBreakpoint(location),
          ),
        ],
      ),
    );
  }

  /// 构建调试信息面板
  Widget _buildDebugInfoPanel() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: const Row(
              children: [
                Icon(Icons.info, size: 16),
                SizedBox(width: 8),
                Text(
                  '调试信息',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
          Expanded(
            child: DefaultTabController(
              length: 2,
              child: Column(
                children: [
                  const TabBar(
                    tabs: [
                      Tab(text: '变量'),
                      Tab(text: '调用堆栈'),
                    ],
                  ),
                  Expanded(
                    child: TabBarView(
                      children: [
                        _buildVariablesView(),
                        _buildCallStackView(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建变量视图
  Widget _buildVariablesView() {
    return ListView(
      padding: const EdgeInsets.all(8),
      children: [
        _buildVariableItem('pluginId', 'String', '"my_plugin"'),
        _buildVariableItem('isInitialized', 'bool', 'true'),
        _buildVariableItem('config', 'Map<String, dynamic>', '{...}'),
        _buildVariableItem('_state', 'PluginState', 'PluginState.running'),
      ],
    );
  }

  /// 构建变量项目
  Widget _buildVariableItem(String name, String type, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              name,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              type,
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              value,
              style: const TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建调用堆栈视图
  Widget _buildCallStackView() {
    return ListView(
      padding: const EdgeInsets.all(8),
      children: [
        _buildStackFrameItem('MyPlugin.initialize()', 'main.dart:15'),
        _buildStackFrameItem(
            'PluginLoader.loadPlugin()', 'plugin_loader.dart:42'),
        _buildStackFrameItem(
            'WorkshopManager.start()', 'workshop_manager.dart:128'),
        _buildStackFrameItem('main()', 'main.dart:8'),
      ],
    );
  }

  /// 构建堆栈帧项目
  Widget _buildStackFrameItem(String function, String location) {
    return ListTile(
      dense: true,
      title: Text(
        function,
        style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
      ),
      subtitle: Text(
        location,
        style: TextStyle(fontSize: 10, color: Colors.grey[600]),
      ),
      onTap: () => _jumpToStackFrame(location),
    );
  }

  /// 构建测试工具
  Widget _buildTester() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.verified,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                '测试工具',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: _runAllTests,
                icon: const Icon(Icons.play_arrow),
                label: const Text('运行所有测试'),
              ),
              const SizedBox(width: 8),
              ElevatedButton.icon(
                onPressed: _runTestsWithCoverage,
                icon: const Icon(Icons.assessment),
                label: const Text('测试覆盖率'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Row(
              children: [
                // 测试文件列表
                Expanded(
                  flex: 1,
                  child: _buildTestFileList(),
                ),
                const SizedBox(width: 16),
                // 测试结果区域
                Expanded(
                  flex: 2,
                  child: _buildTestResults(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建测试文件列表
  Widget _buildTestFileList() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: const Row(
              children: [
                Icon(Icons.folder_open, size: 16),
                SizedBox(width: 8),
                Text(
                  '测试文件',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView(
              children: [
                _buildTestFileItem('test/plugin_test.dart', 'passed', 5),
                _buildTestFileItem('test/models_test.dart', 'passed', 3),
                _buildTestFileItem('test/services_test.dart', 'failed', 2),
                _buildTestFileItem('test/integration_test.dart', 'running', 0),
                _buildTestFileItem('test/widget_test.dart', 'pending', 0),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建测试文件项目
  Widget _buildTestFileItem(String filename, String status, int testCount) {
    Color statusColor;
    IconData statusIcon;

    switch (status) {
      case 'passed':
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case 'failed':
        statusColor = Colors.red;
        statusIcon = Icons.error;
        break;
      case 'running':
        statusColor = Colors.blue;
        statusIcon = Icons.hourglass_empty;
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.radio_button_unchecked;
    }

    return ListTile(
      dense: true,
      leading: Icon(statusIcon, color: statusColor, size: 16),
      title: Text(
        filename,
        style: const TextStyle(fontSize: 12),
      ),
      subtitle: testCount > 0
          ? Text('$testCount 个测试', style: const TextStyle(fontSize: 10))
          : null,
      trailing: IconButton(
        icon: const Icon(Icons.play_arrow, size: 16),
        onPressed: () => _runSingleTest(filename),
      ),
    );
  }

  /// 构建测试结果区域
  Widget _buildTestResults() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                const Icon(Icons.terminal, size: 16),
                const SizedBox(width: 8),
                const Text(
                  '测试输出',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                Text(
                  '通过: 8  失败: 2  跳过: 1',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: Container(
              color: Colors.black,
              padding: const EdgeInsets.all(12),
              child: const SingleChildScrollView(
                child: Text(
                  '''Running "flutter test"...

✓ test/plugin_test.dart: All tests passed! (5 tests)
✓ test/models_test.dart: All tests passed! (3 tests)
✗ test/services_test.dart: 2 tests failed
  ✗ should initialize service correctly
    Expected: true
    Actual: false
  ✗ should handle errors gracefully
    Exception: Service not initialized
⏳ test/integration_test.dart: Running...
⏸ test/widget_test.dart: Pending...

Test Summary:
- Total: 11 tests
- Passed: 8
- Failed: 2
- Skipped: 1
- Coverage: 85.2%''',
                  style: TextStyle(
                    color: Colors.white,
                    fontFamily: 'monospace',
                    fontSize: 12,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建性能分析器
  Widget _buildProfiler() {
    return _buildToolPlaceholder(
      DevelopmentTool.profiler,
      '性能分析和优化',
      Icons.speed,
      [
        'CPU 使用率',
        '内存分析',
        '渲染性能',
        '网络请求',
      ],
    );
  }

  /// 构建文档生成器
  Widget _buildDocumentation() {
    return _buildToolPlaceholder(
      DevelopmentTool.documentation,
      'API文档自动生成',
      Icons.description,
      [
        'API 文档',
        '用户手册',
        '示例代码',
        '发布说明',
      ],
    );
  }

  /// 构建打包工具
  Widget _buildPackaging() {
    return _buildToolPlaceholder(
      DevelopmentTool.packaging,
      '插件打包和分发',
      Icons.archive,
      [
        '代码编译',
        '资源打包',
        '签名验证',
        '版本管理',
      ],
    );
  }

  /// 构建工具占位符
  Widget _buildToolPlaceholder(
    DevelopmentTool tool,
    String description,
    IconData icon,
    List<String> features,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                tool.displayName,
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    icon,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    tool.displayName,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    description,
                    style: TextStyle(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    '功能特性:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[700],
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...features.map((feature) => Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.check_circle,
                              size: 16,
                              color: Colors.green[600],
                            ),
                            const SizedBox(width: 8),
                            Text(feature),
                          ],
                        ),
                      )),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () => _launchTool(tool),
                    child: Text('启动${tool.displayName}'),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 获取工具图标
  IconData _getToolIcon(DevelopmentTool tool) {
    switch (tool) {
      case DevelopmentTool.codeEditor:
        return Icons.code;
      case DevelopmentTool.debugger:
        return Icons.bug_report;
      case DevelopmentTool.tester:
        return Icons.verified;
      case DevelopmentTool.profiler:
        return Icons.speed;
      case DevelopmentTool.documentation:
        return Icons.description;
      case DevelopmentTool.packaging:
        return Icons.archive;
    }
  }

  /// 打开外部编辑器
  void _openExternalEditor() {
    // TODO: 集成外部编辑器
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('正在启动 VS Code...'),
      ),
    );
  }

  /// 打开VS Code在线版
  void _openVSCodeOnline() {
    // TODO: 实现VS Code在线版集成
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('正在启动 VS Code 在线版...'),
      ),
    );
  }

  /// 刷新VS Code在线版
  void _refreshVSCodeOnline() {
    // TODO: 实现VS Code在线版刷新
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('正在刷新 VS Code 在线版...'),
      ),
    );
  }

  /// 构建侧边栏图标
  Widget _buildSidebarIcon(IconData icon, String tooltip, bool isActive) {
    return Container(
      height: 50,
      width: 50,
      margin: const EdgeInsets.symmetric(vertical: 2),
      child: Material(
        color: isActive ? Colors.grey[600] : Colors.transparent,
        borderRadius: BorderRadius.circular(4),
        child: InkWell(
          borderRadius: BorderRadius.circular(4),
          onTap: () {
            // TODO: 实现侧边栏切换
          },
          child: Tooltip(
            message: tooltip,
            child: Icon(
              icon,
              color: Colors.white,
              size: 20,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建文件浏览器
  Widget _buildFileExplorer() {
    return ListView(
      children: [
        _buildFileTreeItem('📁 lib', 0, true),
        _buildFileTreeItem('📄 main.dart', 1, false),
        _buildFileTreeItem('📄 plugin_core.dart', 1, false),
        _buildFileTreeItem('📁 src', 1, true),
        _buildFileTreeItem('📄 models.dart', 2, false),
        _buildFileTreeItem('📄 services.dart', 2, false),
        _buildFileTreeItem('📁 test', 0, true),
        _buildFileTreeItem('📄 plugin_test.dart', 1, false),
        _buildFileTreeItem('📄 pubspec.yaml', 0, false),
        _buildFileTreeItem('📄 README.md', 0, false),
      ],
    );
  }

  /// 构建文件树项目
  Widget _buildFileTreeItem(String name, int level, bool isFolder) {
    return Container(
      height: 24,
      padding: EdgeInsets.only(left: 8.0 + (level * 16.0)),
      child: Row(
        children: [
          if (isFolder)
            const Icon(Icons.keyboard_arrow_down, size: 16)
          else
            const SizedBox(width: 16),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              name,
              style: const TextStyle(fontSize: 12),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建编辑器区域
  Widget _buildEditorArea() {
    return Column(
      children: [
        // 标签栏
        Container(
          height: 30,
          color: Colors.grey[300],
          child: Row(
            children: [
              _buildEditorTab('main.dart', true),
              _buildEditorTab('plugin_core.dart', false),
              const Spacer(),
            ],
          ),
        ),
        // 编辑器内容
        Expanded(
          child: Container(
            color: Colors.grey[900],
            padding: const EdgeInsets.all(16),
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '// 插件主文件 - main.dart',
                  style: TextStyle(
                    color: Colors.green,
                    fontFamily: 'monospace',
                    fontSize: 14,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'import \'package:plugin_system/plugin_system.dart\';\n\nclass MyPlugin extends Plugin {\n  @override\n  String get id => \'my_plugin\';\n\n  @override\n  String get name => \'我的插件\';\n\n  @override\n  String get version => \'1.0.0\';\n\n  @override\n  Future<void> initialize() async {\n    // 插件初始化逻辑\n    print(\'插件初始化完成\');\n  }\n\n  @override\n  Future<void> start() async {\n    // 插件启动逻辑\n  }\n}',
                  style: TextStyle(
                    color: Colors.white,
                    fontFamily: 'monospace',
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 构建编辑器标签
  Widget _buildEditorTab(String filename, bool isActive) {
    return Container(
      height: 30,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: isActive ? Colors.white : Colors.grey[400],
        border: Border(
          right: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            filename,
            style: TextStyle(
              fontSize: 12,
              color: isActive ? Colors.black : Colors.grey[700],
            ),
          ),
          const SizedBox(width: 8),
          Icon(
            Icons.close,
            size: 14,
            color: Colors.grey[600],
          ),
        ],
      ),
    );
  }

  /// 启动开发工具
  void _launchTool(DevelopmentTool tool) {
    setState(() {
      _isToolLoading = true;
    });

    // 模拟工具启动
    Future.delayed(const Duration(seconds: 2), () {
      setState(() {
        _isToolLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${tool.displayName}启动完成'),
        ),
      );
    });
  }

  /// 创建新项目
  void _createNewProject() {
    // TODO: 实现新建项目功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('新建项目功能即将推出...'),
      ),
    );
  }

  /// 运行所有测试
  void _runAllTests() {
    // TODO: 实现运行所有测试
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('正在运行所有测试...'),
      ),
    );
  }

  /// 运行测试覆盖率
  void _runTestsWithCoverage() {
    // TODO: 实现测试覆盖率分析
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('正在分析测试覆盖率...'),
      ),
    );
  }

  /// 运行单个测试文件
  void _runSingleTest(String filename) {
    // TODO: 实现单个测试文件运行
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('正在运行测试: $filename'),
      ),
    );
  }

  /// 开始调试
  void _startDebugging() {
    // TODO: 实现开始调试功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('正在启动调试器...'),
      ),
    );
  }

  /// 停止调试
  void _stopDebugging() {
    // TODO: 实现停止调试功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('正在停止调试器...'),
      ),
    );
  }

  /// 单步跳过
  void _stepOver() {
    // TODO: 实现单步跳过功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('单步跳过'),
      ),
    );
  }

  /// 单步进入
  void _stepInto() {
    // TODO: 实现单步进入功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('单步进入'),
      ),
    );
  }

  /// 单步跳出
  void _stepOut() {
    // TODO: 实现单步跳出功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('单步跳出'),
      ),
    );
  }

  /// 重启调试
  void _restart() {
    // TODO: 实现重启调试功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('正在重启调试器...'),
      ),
    );
  }

  /// 切换断点
  void _toggleBreakpoint(String location, bool enabled) {
    // TODO: 实现断点切换功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${enabled ? "启用" : "禁用"}断点: $location'),
      ),
    );
  }

  /// 移除断点
  void _removeBreakpoint(String location) {
    // TODO: 实现移除断点功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('移除断点: $location'),
      ),
    );
  }

  /// 跳转到堆栈帧
  void _jumpToStackFrame(String location) {
    // TODO: 实现跳转到堆栈帧功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('跳转到: $location'),
      ),
    );
  }
}
