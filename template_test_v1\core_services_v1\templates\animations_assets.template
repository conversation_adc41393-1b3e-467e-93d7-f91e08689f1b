/*
---------------------------------------------------------------
File name:          animations.dart
Author:             Ming Status CLI
Date created:       2025-07-14
Last modified:      2025-07-14
Dart Version:       3.2+
Description:        core_services_v1应用程序动画资源
---------------------------------------------------------------
Change History:
    2025-07-14: Initial creation - core_services_v1应用程序动画资源;
---------------------------------------------------------------
*/


/// 动画资源常量
///
/// 定义应用程序中使用的所有动画
class AnimationAssets {
  /// 私有构造函数，防止实例化
  AnimationAssets._();

  /// 资源根路径
  static const String _basePath = 'assets/animations';

  /// 加载动画
  static const String loading = '$_basePath/loading.json';
  
  /// 成功动画
  static const String success = '$_basePath/success.json';
  
  /// 错误动画
  static const String error = '$_basePath/error.json';
  
  /// 空状态动画
  static const String empty = '$_basePath/empty.json';

  /// 动画持续时间（毫秒）
  static const Map<String, int> durations = {
    'fast': 200,
    'normal': 300,
    'slow': 500,
    'verySlow': 1000,
  };
}
