/*
---------------------------------------------------------------
File name:          core_services_v1_utils_test.dart
Author:             Ming Status CLI
Date created:       2025-07-14
Last modified:      2025-07-14
Dart Version:       3.2+
Description:        core_services_v1 Utils 单元测试
---------------------------------------------------------------
Change History:
    2025-07-14: Initial creation - core_services_v1 Utils 单元测试;
---------------------------------------------------------------
*/

import 'package:test/test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:core_services_v1/lib/src/utils/core_services_v1_utils.dart';

void main() {
  group('CoreServicesV1Utils 单元测试', () {
    late CoreServicesV1Utils utils;

    setUp(() {
      utils = CoreServicesV1Utils();
    });

    tearDown(() async {
    });

    group('验证器测试', () {
      test('邮箱验证应该正确', () {
        expect(CoreServicesV1Validator.isValidEmail('<EMAIL>'), true);
        expect(CoreServicesV1Validator.isValidEmail('invalid-email'), false);
      });

      test('密码验证应该正确', () {
        expect(CoreServicesV1Validator.isValidPassword('password123'), true);
        expect(CoreServicesV1Validator.isValidPassword('123'), false);
      });
    });

    group('格式化器测试', () {
      test('日期格式化应该正确', () {
        final date = DateTime(2023, 12, 25);
        expect(CoreServicesV1Formatter.formatDate(date), '2023-12-25');
      });

      test('文件大小格式化应该正确', () {
        expect(CoreServicesV1Formatter.formatFileSize(1024), '1.0 KB');
        expect(CoreServicesV1Formatter.formatFileSize(1048576), '1.0 MB');
      });
    });

  });
}
