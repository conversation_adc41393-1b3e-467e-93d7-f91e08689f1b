/*
---------------------------------------------------------------
File name:          core_services_v1_dialog.dart
Author:             Ming Status CLI
Date created:       2025-07-14
Last modified:      2025-07-14
Dart Version:       3.2+
Description:        core_services_v1 对话框组件
---------------------------------------------------------------
Change History:
    2025-07-14: Initial creation - core_services_v1 对话框组件;
---------------------------------------------------------------
*/

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:provider/provider.dart';

import '../providers/core_services_v1_provider.dart';
import '../constants/core_services_v1_constants.dart';
import '../utils/core_services_v1_utils.dart';

/// core_services_v1对话框组件
///
/// ## 使用示例
///
/// ```dart
/// showDialog(
/// ```
///
/// ```dart
///   context: context,
/// ```
///
/// ```dart
///   builder: (context) => CoreServicesV1Dialog(),
/// ```
///
/// ```dart
/// )
/// ```
///
class CoreServicesV1Dialog extends StatefulWidget {
  /// 标题
  final String? title;

  /// 内容
  final String? content;

  /// 确认回调
  final VoidCallback? onConfirm;

  /// 取消回调
  final VoidCallback? onCancel;

  /// 创建CoreServicesV1Dialog实例
  const CoreServicesV1Dialog({
    super.key,
    this.title,
    this.content,
    this.onConfirm,
    this.onCancel,
  });

  @override
  State<CoreServicesV1Dialog> createState() => _CoreServicesV1DialogState();

  /// 显示对话框
  static Future<bool?> show(
    BuildContext context, {
    String? title,
    String? content,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => CoreServicesV1Dialog(
        title: title,
        content: content,
        onConfirm: onConfirm,
        onCancel: onCancel,
      ),
    );
  }
}

class _CoreServicesV1DialogState extends State<CoreServicesV1Dialog> {
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: widget.title != null ? Text(widget.title!) : null,
      content: widget.content != null ? Text(widget.content!) : null,
      actions: [
        TextButton(
          onPressed: () {
            widget.onCancel?.call();
            Navigator.of(context).pop(false);
          },
          child: const Text('取消'),
        ),
        TextButton(
          onPressed: () {
            widget.onConfirm?.call();
            Navigator.of(context).pop(true);
          },
          child: const Text('确认'),
        ),
      ],
    );
  }
}
