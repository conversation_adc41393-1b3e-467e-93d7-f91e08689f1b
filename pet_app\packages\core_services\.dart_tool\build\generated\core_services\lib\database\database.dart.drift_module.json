{"elements": {"AppDatabase": {"id": {"library_uri": "package:core_services/database/database.dart", "name": "AppDatabase"}, "declaration": {"source_uri": "package:core_services/database/database.dart", "offset": 2414, "name": "AppDatabase"}, "references": [{"library_uri": "package:core_services/database/database.dart", "name": "base_items"}, {"library_uri": "package:core_services/database/database.dart", "name": "sync_status"}], "type": "database", "tables": [{"library_uri": "package:core_services/database/database.dart", "name": "base_items"}, {"library_uri": "package:core_services/database/database.dart", "name": "sync_status"}], "views": [], "includes": [], "queries": [], "schema_version": 1, "daos": [], "has_constructor_arg": false}, "base_items": {"id": {"library_uri": "package:core_services/database/database.dart", "name": "base_items"}, "declaration": {"source_uri": "package:core_services/database/database.dart", "offset": 616, "name": "BaseItems"}, "references": [], "type": "table", "columns": [{"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "id", "nameInDart": "id", "declaration": {"source_uri": "package:core_services/database/database.dart", "offset": 672, "name": "id"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": "/// 主键ID", "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "title", "nameInDart": "title", "declaration": {"source_uri": "package:core_services/database/database.dart", "offset": 720, "name": "title"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": "/// 标题", "constraints": [{"type": "limit_text_length", "min_length": 1, "max_length": 255}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "description", "nameInDart": "description", "declaration": {"source_uri": "package:core_services/database/database.dart", "offset": 802, "name": "description"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "('')"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": "/// 描述内容", "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "item_type", "nameInDart": "itemType", "declaration": {"source_uri": "package:core_services/database/database.dart", "offset": 893, "name": "itemType"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": "/// 项目类型", "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "metadata", "nameInDart": "metadata", "declaration": {"source_uri": "package:core_services/database/database.dart", "offset": 952, "name": "metadata"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "('{}')"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": "/// 元数据JSON", "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "status", "nameInDart": "status", "declaration": {"source_uri": "package:core_services/database/database.dart", "offset": 1040, "name": "status"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "('draft')"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": "/// 状态", "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "created_at", "nameInDart": "createdAt", "declaration": {"source_uri": "package:core_services/database/database.dart", "offset": 1135, "name": "createdAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": "/// 创建时间", "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "updated_at", "nameInDart": "updatedAt", "declaration": {"source_uri": "package:core_services/database/database.dart", "offset": 1202, "name": "updatedAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": "/// 更新时间  ", "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "tags", "nameInDart": "tags", "declaration": {"source_uri": "package:core_services/database/database.dart", "offset": 1267, "name": "tags"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "('[]')"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": "/// 标签JSON数组", "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "content_hash", "nameInDart": "contentHash", "declaration": {"source_uri": "package:core_services/database/database.dart", "offset": 1364, "name": "contentHash"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": "/// 内容哈希 - 用于数据变更检测", "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "bool"}, "nullable": false, "nameInSql": "needs_sync", "nameInDart": "needsSync", "declaration": {"source_uri": "package:core_services/database/database.dart", "offset": 1434, "name": "needsSync"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(false)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": "/// 同步标记", "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "bool"}, "nullable": false, "nameInSql": "is_deleted", "nameInDart": "isDeleted", "declaration": {"source_uri": "package:core_services/database/database.dart", "offset": 1530, "name": "isDeleted"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(false)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": "/// 软删除标记", "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [{"type": "primary_key", "columns": ["id"]}], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "BaseItems", "row_class_name": "BaseItem", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}, "sync_status": {"id": {"library_uri": "package:core_services/database/database.dart", "name": "sync_status"}, "declaration": {"source_uri": "package:core_services/database/database.dart", "offset": 1710, "name": "SyncStatus"}, "references": [], "type": "table", "columns": [{"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "id", "nameInDart": "id", "declaration": {"source_uri": "package:core_services/database/database.dart", "offset": 1766, "name": "id"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": "/// 同步ID", "constraints": [{"type": "primary", "is_auto_increment": true}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "entity_id", "nameInDart": "entityId", "declaration": {"source_uri": "package:core_services/database/database.dart", "offset": 1835, "name": "entityId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": "/// 实体ID", "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "entity_type", "nameInDart": "entityType", "declaration": {"source_uri": "package:core_services/database/database.dart", "offset": 1891, "name": "entityType"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": "/// 实体类型", "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "status", "nameInDart": "status", "declaration": {"source_uri": "package:core_services/database/database.dart", "offset": 1986, "name": "status"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": "/// 同步状态: pending, syncing, completed, failed", "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": true, "nameInSql": "sync_timestamp", "nameInDart": "syncTimestamp", "declaration": {"source_uri": "package:core_services/database/database.dart", "offset": 2045, "name": "syncTimestamp"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": "/// 同步时间戳", "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "retry_count", "nameInDart": "retryCount", "declaration": {"source_uri": "package:core_services/database/database.dart", "offset": 2120, "name": "retryCount"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(0)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": "/// 重试次数", "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "error_message", "nameInDart": "errorMessage", "declaration": {"source_uri": "package:core_services/database/database.dart", "offset": 2212, "name": "errorMessage"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": "/// 错误信息", "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "created_at", "nameInDart": "createdAt", "declaration": {"source_uri": "package:core_services/database/database.dart", "offset": 2287, "name": "createdAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": "/// 创建时间", "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "SyncStatus", "row_class_name": "SyncStatusData", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}}, "imports": ["package:drift/drift.dart", "package:drift/native.dart", "package:path/path.dart"]}