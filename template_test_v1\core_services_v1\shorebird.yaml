# Shorebird代码推送配置
# 更多信息: https://shorebird.dev

# 应用配置
app_id: core-services-v1
app_name: core_services_v1
app_display_name: "企业级核心服务模板"

# === 多环境配置 ===

# 开发环境
environments:
  development:
    app_id: core_services_v1-dev
    # 自动更新配置
    auto_update:
      enabled: true
      check_interval: 300  # 5分钟
      # 网络策略
      network_policy:
        wifi_only: false
        cellular_allowed: true
        roaming_allowed: false
      # 电池策略
      battery_policy:
        low_battery_threshold: 20
        charging_required: false
    # 用户体验
    user_experience:
      show_update_dialog: true
      force_update: false
      background_download: true

  # 预发布环境
  staging:
    app_id: core_services_v1-staging
    auto_update:
      enabled: true
      check_interval: 600  # 10分钟
      network_policy:
        wifi_only: false
        cellular_allowed: true
        roaming_allowed: true
      battery_policy:
        low_battery_threshold: 15
        charging_required: false
    user_experience:
      show_update_dialog: true
      force_update: false
      background_download: true

  # 生产环境
  production:
    app_id: core_services_v1-prod
    auto_update:
      enabled: true
      check_interval: 1800  # 30分钟
      network_policy:
        wifi_only: true
        cellular_allowed: false
        roaming_allowed: false
      battery_policy:
        low_battery_threshold: 30
        charging_required: true
    user_experience:
      show_update_dialog: false
      force_update: false
      background_download: true

# === 回滚配置 ===

rollback:
  # 自动回滚
  auto_rollback:
    enabled: true
    # 触发条件
    triggers:
      crash_rate_threshold: 5.0  # 崩溃率超过5%
      error_rate_threshold: 10.0  # 错误率超过10%
      performance_degradation: 20.0  # 性能下降超过20%
    # 检测窗口
    detection_window: 3600  # 1小时
    # 最小样本数
    min_sample_size: 100
  
  # 手动回滚
  manual_rollback:
    enabled: true
    # 回滚历史保留
    history_retention: 30  # 30天
    # 快速回滚
    quick_rollback: true

# === 分阶段发布 ===

staged_rollout:
  # 启用分阶段发布
  enabled: true
  
  # 发布阶段
  stages:
    - name: "canary"
      percentage: 1.0
      duration: 3600  # 1小时
      success_criteria:
        crash_rate: 1.0
        error_rate: 2.0
    
    - name: "early_adopters"
      percentage: 5.0
      duration: 7200  # 2小时
      success_criteria:
        crash_rate: 2.0
        error_rate: 5.0
    
    - name: "general"
      percentage: 25.0
      duration: 14400  # 4小时
      success_criteria:
        crash_rate: 3.0
        error_rate: 7.0
    
    - name: "full"
      percentage: 100.0
      duration: 0  # 无限制
      success_criteria:
        crash_rate: 5.0
        error_rate: 10.0
  
  # 暂停条件
  pause_conditions:
    high_crash_rate: 10.0
    high_error_rate: 15.0
    negative_feedback: 20.0

# === 监控和分析 ===

monitoring:
  # 性能监控
  performance:
    enabled: true
    metrics:
      - app_start_time
      - frame_rate
      - memory_usage
      - cpu_usage
      - network_latency
  
  # 用户反馈
  user_feedback:
    enabled: true
    collection_methods:
      - in_app_rating
      - crash_reports
      - user_surveys
  
  # 崩溃报告
  crash_reporting:
    enabled: true
    providers:
      - firebase_crashlytics
      - sentry

# === 安全配置 ===

security:
  # 代码签名
  code_signing:
    enabled: true
    certificate_validation: true
  
  # 传输加密
  transport_encryption:
    enabled: true
    tls_version: "1.3"
  
  # 访问控制
  access_control:
    api_key_required: true
    rate_limiting: true
    ip_whitelist: []

# === 高级配置 ===

advanced:
  # 缓存配置
  cache:
    max_size: "100MB"
    ttl: 86400  # 24小时
    cleanup_policy: "lru"
  
  # 网络配置
  network:
    timeout: 30
    retry_attempts: 3
    retry_delay: 1000
  
  # 日志配置
  logging:
    level: "info"
    max_file_size: "10MB"
    max_files: 5

# === 开发配置 ===

development:
  # 调试模式
  debug_mode: true
  
  # 本地测试
  local_testing:
    enabled: true
    mock_server: false
  
  # 开发者工具
  dev_tools:
    enabled: true
    show_overlay: false
