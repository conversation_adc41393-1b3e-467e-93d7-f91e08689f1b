/*
---------------------------------------------------------------
File name:          plugin_development_manager.dart
Author:             lgnorant-lu
Date created:       2025/07/31
Last modified:      2025/07/31
Dart Version:       3.2+
Description:        插件开发工作台管理器 (Plugin Development Manager)
---------------------------------------------------------------
Change History:
    2025/07/31: Initial creation - 插件开发工作台;
*/

import 'dart:async';
import 'dart:developer' as developer;
import 'dart:io';

import 'package:path/path.dart' as path;
import 'package:plugin_system/plugin_system.dart' as plugin_sys;

import '../ming_cli/local_ming_cli_service.dart';
import '../projects/project_manager.dart';

/// 插件开发工作台管理器
///
/// 负责插件开发的完整工作流程，包括：
/// - 插件项目创建和管理
/// - 与Plugin System的集成
/// - 开发工具集成
/// - 调试和测试支持
class PluginDevelopmentManager {
  static PluginDevelopmentManager? _instance;

  /// 获取单例实例
  static PluginDevelopmentManager get instance {
    return _instance ??= PluginDevelopmentManager._();
  }

  PluginDevelopmentManager._();

  // 依赖服务
  late LocalMingCliService _mingCliService;
  late ProjectManager _projectManager;
  late plugin_sys.PluginRegistry _pluginRegistry;

  // 状态管理
  bool _isInitialized = false;
  final Map<String, PluginDevelopmentProject> _activeProjects = {};
  final StreamController<PluginDevelopmentEvent> _eventController =
      StreamController<PluginDevelopmentEvent>.broadcast();

  /// 是否已初始化
  bool get isInitialized => _isInitialized;

  /// 活跃的插件开发项目
  Map<String, PluginDevelopmentProject> get activeProjects =>
      Map.unmodifiable(_activeProjects);

  /// 事件流
  Stream<PluginDevelopmentEvent> get events => _eventController.stream;

  /// 初始化插件开发管理器
  Future<void> initialize() async {
    if (_isInitialized) return;

    developer.log('初始化插件开发管理器', name: 'PluginDevelopmentManager');

    try {
      // 初始化依赖服务
      _mingCliService = LocalMingCliService.instance;
      _projectManager = ProjectManager.instance;
      _pluginRegistry = plugin_sys.PluginRegistry.instance;

      // 确保Ming CLI可用
      await _mingCliService.initialize();

      // 确保项目管理器可用
      await _projectManager.initialize();

      // 扫描现有的插件开发项目
      await _scanExistingProjects();

      _isInitialized = true;
      _emitEvent(PluginDevelopmentEvent.initialized());

      developer.log('插件开发管理器初始化完成', name: 'PluginDevelopmentManager');
    } catch (e, stackTrace) {
      developer.log(
        '插件开发管理器初始化失败',
        name: 'PluginDevelopmentManager',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// 创建新的插件开发项目
  Future<PluginDevelopmentProject> createPluginProject({
    required String name,
    required String description,
    String? template,
    Map<String, dynamic>? options,
  }) async {
    _ensureInitialized();

    developer.log('创建插件开发项目: $name', name: 'PluginDevelopmentManager');

    try {
      // 1. 使用Ming CLI创建插件项目
      final createResult = await _mingCliService.executeCommand([
        'plugin',
        'create',
        name,
        if (template != null) '--template=$template',
        ...?_buildCreateOptions(options),
      ]);

      if (!createResult.success) {
        throw Exception('插件项目创建失败: ${createResult.error}');
      }

      // 2. 创建项目管理记录
      final projectResult = await _projectManager.createProject(
        name: name,
        description: description,
        type: ProjectType.plugin,
        metadata: {
          'template': template ?? 'basic',
          'mingCliOutput': createResult.output,
          'createdAt': DateTime.now().toIso8601String(),
          ...?options,
        },
      );

      if (!projectResult.success) {
        throw Exception('项目管理记录创建失败: ${projectResult.error}');
      }

      final projectId = projectResult.data!.id;

      // 3. 创建插件开发项目实例
      final pluginProject = PluginDevelopmentProject(
        id: projectId,
        name: name,
        description: description,
        path: path.join(Directory.current.path, name),
        template: template ?? 'basic',
        createdAt: DateTime.now(),
        manager: this,
      );

      // 4. 注册到活跃项目列表
      _activeProjects[projectId] = pluginProject;

      // 5. 发送事件
      _emitEvent(PluginDevelopmentEvent.projectCreated(pluginProject));

      developer.log('插件开发项目创建成功: $name', name: 'PluginDevelopmentManager');
      return pluginProject;
    } catch (e, stackTrace) {
      developer.log(
        '插件开发项目创建失败: $name',
        name: 'PluginDevelopmentManager',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// 打开现有的插件开发项目
  Future<PluginDevelopmentProject?> openPluginProject(
      String projectPath) async {
    _ensureInitialized();

    developer.log('打开插件开发项目: $projectPath', name: 'PluginDevelopmentManager');

    try {
      final projectDir = Directory(projectPath);
      if (!await projectDir.exists()) {
        throw Exception('项目目录不存在: $projectPath');
      }

      // 验证是否为有效的插件项目
      final isValid = await _validatePluginProject(projectPath);
      if (!isValid) {
        throw Exception('不是有效的插件项目: $projectPath');
      }

      // 读取项目信息
      final projectInfo = await _readProjectInfo(projectPath);

      // 创建插件开发项目实例
      final pluginProject = PluginDevelopmentProject(
        id: (projectInfo['id'] as String?) ?? path.basename(projectPath),
        name: (projectInfo['name'] as String?) ?? path.basename(projectPath),
        description: (projectInfo['description'] as String?) ?? '',
        path: projectPath,
        template: (projectInfo['template'] as String?) ?? 'unknown',
        createdAt:
            DateTime.tryParse((projectInfo['createdAt'] as String?) ?? '') ??
                DateTime.now(),
        manager: this,
      );

      // 注册到活跃项目列表
      _activeProjects[pluginProject.id] = pluginProject;

      // 发送事件
      _emitEvent(PluginDevelopmentEvent.projectOpened(pluginProject));

      developer.log('插件开发项目打开成功: $projectPath',
          name: 'PluginDevelopmentManager');
      return pluginProject;
    } catch (e, stackTrace) {
      developer.log(
        '插件开发项目打开失败: $projectPath',
        name: 'PluginDevelopmentManager',
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  /// 关闭插件开发项目
  Future<void> closePluginProject(String projectId) async {
    _ensureInitialized();

    final project = _activeProjects[projectId];
    if (project == null) return;

    developer.log('关闭插件开发项目: ${project.name}',
        name: 'PluginDevelopmentManager');

    try {
      // 清理项目资源
      await project.dispose();

      // 从活跃项目列表中移除
      _activeProjects.remove(projectId);

      // 发送事件
      _emitEvent(PluginDevelopmentEvent.projectClosed(project));

      developer.log('插件开发项目关闭成功: ${project.name}',
          name: 'PluginDevelopmentManager');
    } catch (e, stackTrace) {
      developer.log(
        '插件开发项目关闭失败: ${project.name}',
        name: 'PluginDevelopmentManager',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// 构建插件项目
  Future<bool> buildPluginProject(String projectId) async {
    _ensureInitialized();

    final project = _activeProjects[projectId];
    if (project == null) {
      throw Exception('项目不存在: $projectId');
    }

    developer.log('构建插件项目: ${project.name}', name: 'PluginDevelopmentManager');

    try {
      // 使用Ming CLI构建插件
      final buildResult = await _mingCliService.executeCommand([
        'plugin',
        'build',
        project.path,
      ]);

      if (buildResult.success) {
        _emitEvent(PluginDevelopmentEvent.projectBuilt(project));
        developer.log('插件项目构建成功: ${project.name}',
            name: 'PluginDevelopmentManager');
        return true;
      } else {
        _emitEvent(PluginDevelopmentEvent.projectBuildFailed(
            project, buildResult.error));
        developer.log('插件项目构建失败: ${project.name}',
            name: 'PluginDevelopmentManager');
        return false;
      }
    } catch (e, stackTrace) {
      developer.log(
        '插件项目构建异常: ${project.name}',
        name: 'PluginDevelopmentManager',
        error: e,
        stackTrace: stackTrace,
      );
      _emitEvent(
          PluginDevelopmentEvent.projectBuildFailed(project, e.toString()));
      return false;
    }
  }

  /// 测试插件项目
  Future<bool> testPluginProject(String projectId) async {
    _ensureInitialized();

    final project = _activeProjects[projectId];
    if (project == null) {
      throw Exception('项目不存在: $projectId');
    }

    developer.log('测试插件项目: ${project.name}', name: 'PluginDevelopmentManager');

    try {
      // 使用Ming CLI测试插件
      final testResult = await _mingCliService.executeCommand([
        'plugin',
        'test',
        project.path,
      ]);

      if (testResult.success) {
        _emitEvent(PluginDevelopmentEvent.projectTested(project));
        developer.log('插件项目测试成功: ${project.name}',
            name: 'PluginDevelopmentManager');
        return true;
      } else {
        _emitEvent(PluginDevelopmentEvent.projectTestFailed(
            project, testResult.error));
        developer.log('插件项目测试失败: ${project.name}',
            name: 'PluginDevelopmentManager');
        return false;
      }
    } catch (e, stackTrace) {
      developer.log(
        '插件项目测试异常: ${project.name}',
        name: 'PluginDevelopmentManager',
        error: e,
        stackTrace: stackTrace,
      );
      _emitEvent(
          PluginDevelopmentEvent.projectTestFailed(project, e.toString()));
      return false;
    }
  }

  /// 销毁管理器
  Future<void> dispose() async {
    if (!_isInitialized) return;

    developer.log('销毁插件开发管理器', name: 'PluginDevelopmentManager');

    try {
      // 关闭所有活跃项目
      final projectIds = _activeProjects.keys.toList();
      for (final projectId in projectIds) {
        await closePluginProject(projectId);
      }

      // 关闭事件流
      await _eventController.close();

      _isInitialized = false;
      developer.log('插件开发管理器销毁完成', name: 'PluginDevelopmentManager');
    } catch (e, stackTrace) {
      developer.log(
        '插件开发管理器销毁失败',
        name: 'PluginDevelopmentManager',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  // 私有方法

  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('PluginDevelopmentManager未初始化');
    }
  }

  void _emitEvent(PluginDevelopmentEvent event) {
    if (!_eventController.isClosed) {
      _eventController.add(event);
    }
  }

  Future<void> _scanExistingProjects() async {
    // TODO: 扫描现有的插件开发项目
    developer.log('扫描现有插件开发项目', name: 'PluginDevelopmentManager');
  }

  List<String> _buildCreateOptions(Map<String, dynamic>? options) {
    if (options == null) return [];

    final result = <String>[];
    for (final entry in options.entries) {
      result.add('--${entry.key}=${entry.value}');
    }
    return result;
  }

  Future<bool> _validatePluginProject(String projectPath) async {
    // 检查是否存在plugin.yaml或pubspec.yaml
    final pluginYaml = File(path.join(projectPath, 'plugin.yaml'));
    final pubspecYaml = File(path.join(projectPath, 'pubspec.yaml'));

    return await pluginYaml.exists() || await pubspecYaml.exists();
  }

  Future<Map<String, dynamic>> _readProjectInfo(String projectPath) async {
    // TODO: 从项目文件中读取项目信息
    return {
      'name': path.basename(projectPath),
      'description': '',
      'template': 'unknown',
      'createdAt': DateTime.now().toIso8601String(),
    };
  }
}

/// 插件开发项目
class PluginDevelopmentProject {
  const PluginDevelopmentProject({
    required this.id,
    required this.name,
    required this.description,
    required this.path,
    required this.template,
    required this.createdAt,
    required this.manager,
  });

  /// 项目ID
  final String id;

  /// 项目名称
  final String name;

  /// 项目描述
  final String description;

  /// 项目路径
  final String path;

  /// 使用的模板
  final String template;

  /// 创建时间
  final DateTime createdAt;

  /// 管理器引用
  final PluginDevelopmentManager manager;

  /// 构建项目
  Future<bool> build() => manager.buildPluginProject(id);

  /// 测试项目
  Future<bool> test() => manager.testPluginProject(id);

  /// 关闭项目
  Future<void> close() => manager.closePluginProject(id);

  /// 销毁项目资源
  Future<void> dispose() async {
    // TODO: 清理项目特定的资源
  }

  @override
  String toString() =>
      'PluginDevelopmentProject(id: $id, name: $name, path: $path)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PluginDevelopmentProject && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// 插件开发事件
abstract class PluginDevelopmentEvent {
  const PluginDevelopmentEvent();

  /// 管理器初始化完成
  factory PluginDevelopmentEvent.initialized() = _ManagerInitializedEvent;

  /// 项目创建
  factory PluginDevelopmentEvent.projectCreated(
      PluginDevelopmentProject project) = _ProjectCreatedEvent;

  /// 项目打开
  factory PluginDevelopmentEvent.projectOpened(
      PluginDevelopmentProject project) = _ProjectOpenedEvent;

  /// 项目关闭
  factory PluginDevelopmentEvent.projectClosed(
      PluginDevelopmentProject project) = _ProjectClosedEvent;

  /// 项目构建成功
  factory PluginDevelopmentEvent.projectBuilt(
      PluginDevelopmentProject project) = _ProjectBuiltEvent;

  /// 项目构建失败
  factory PluginDevelopmentEvent.projectBuildFailed(
          PluginDevelopmentProject project, String error) =
      _ProjectBuildFailedEvent;

  /// 项目测试成功
  factory PluginDevelopmentEvent.projectTested(
      PluginDevelopmentProject project) = _ProjectTestedEvent;

  /// 项目测试失败
  factory PluginDevelopmentEvent.projectTestFailed(
      PluginDevelopmentProject project, String error) = _ProjectTestFailedEvent;
}

class _ManagerInitializedEvent extends PluginDevelopmentEvent {
  const _ManagerInitializedEvent();
}

class _ProjectCreatedEvent extends PluginDevelopmentEvent {
  const _ProjectCreatedEvent(this.project);
  final PluginDevelopmentProject project;
}

class _ProjectOpenedEvent extends PluginDevelopmentEvent {
  const _ProjectOpenedEvent(this.project);
  final PluginDevelopmentProject project;
}

class _ProjectClosedEvent extends PluginDevelopmentEvent {
  const _ProjectClosedEvent(this.project);
  final PluginDevelopmentProject project;
}

class _ProjectBuiltEvent extends PluginDevelopmentEvent {
  const _ProjectBuiltEvent(this.project);
  final PluginDevelopmentProject project;
}

class _ProjectBuildFailedEvent extends PluginDevelopmentEvent {
  const _ProjectBuildFailedEvent(this.project, this.error);
  final PluginDevelopmentProject project;
  final String error;
}

class _ProjectTestedEvent extends PluginDevelopmentEvent {
  const _ProjectTestedEvent(this.project);
  final PluginDevelopmentProject project;
}

class _ProjectTestFailedEvent extends PluginDevelopmentEvent {
  const _ProjectTestFailedEvent(this.project, this.error);
  final PluginDevelopmentProject project;
  final String error;
}
