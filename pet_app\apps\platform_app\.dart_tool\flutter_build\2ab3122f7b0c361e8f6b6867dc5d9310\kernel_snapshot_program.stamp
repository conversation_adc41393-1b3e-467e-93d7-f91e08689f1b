{"inputs": ["D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\apps\\platform_app\\.dart_tool\\package_config_subset", "D:\\Coding\\Flutter\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\common.dart", "D:\\Coding\\Flutter\\flutter\\bin\\cache\\engine.stamp", "D:\\Coding\\Flutter\\flutter\\bin\\cache\\engine.stamp", "D:\\Coding\\Flutter\\flutter\\bin\\cache\\engine.stamp", "D:\\Coding\\Flutter\\flutter\\bin\\cache\\engine.stamp", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\apps\\platform_app\\lib\\main.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\apps\\platform_app\\.dart_tool\\flutter_build\\dart_plugin_registrant.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\dart_plugin_registrant.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\foundation.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\material.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\services.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter_localizations\\lib\\flutter_localizations.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\app_routing\\lib\\app_routing.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\core_services\\lib\\core_services.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\core_services\\lib\\database\\database.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\ui_framework\\lib\\ui_framework.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\notes_hub\\lib\\notes_hub.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\workshop\\lib\\workshop.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\punch_in\\lib\\punch_in.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\apps\\platform_app\\lib\\l10n\\app_localizations.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_android-2.2.17\\lib\\path_provider_android.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\shared_preferences_android.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\lib\\path_provider_foundation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\lib\\shared_preferences_foundation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\path_provider_linux.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\lib\\shared_preferences_linux.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\path_provider_windows.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\lib\\shared_preferences_windows.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.16.0\\lib\\meta.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\annotations.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\assertions.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\basic_types.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\binding.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\bitfield.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\capabilities.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\change_notifier.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\collections.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\consolidate_response.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\constants.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\debug.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\diagnostics.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\isolates.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\key.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\licenses.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\memory_allocations.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\node.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\object.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\observer_list.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\persistent_hash_map.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\platform.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\print.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\serialization.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\service_extensions.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\stack_frame.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\synchronous_future.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\timeline.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\unicode.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\about.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\action_buttons.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\action_chip.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\action_icons_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\adaptive_text_selection_toolbar.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\app.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\app_bar.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\app_bar_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\arc.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\autocomplete.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\badge.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\badge_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\banner.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\banner_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_app_bar.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_app_bar_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_sheet.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_sheet_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\button.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\button_bar.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\button_bar_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\button_style.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\button_style_button.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\button_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\calendar_date_picker.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\card.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\card_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\carousel.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox_list_tile.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\chip.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\chip_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\choice_chip.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\circle_avatar.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\color_scheme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\colors.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\constants.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\curves.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\data_table.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\data_table_source.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\data_table_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\date.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\date_picker.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\date_picker_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\debug.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar_button.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\dialog.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\dialog_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\divider.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\divider_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\drawer.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\drawer_header.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\drawer_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown_menu.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown_menu_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\elevated_button.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\elevated_button_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\elevation_overlay.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\expand_icon.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_panel.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_tile.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_tile_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\filled_button.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\filled_button_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\filter_chip.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\flexible_space_bar.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button_location.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\grid_tile.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\grid_tile_bar.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\icon_button.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\icon_button_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\icons.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\ink_decoration.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\ink_highlight.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\ink_ripple.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\ink_sparkle.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\ink_splash.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\ink_well.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\input_border.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\input_chip.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\input_date_picker_form_field.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\input_decorator.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\list_tile.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\list_tile_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\magnifier.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\material.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\material_button.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\material_localizations.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\material_state.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\material_state_mixin.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\menu_anchor.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\menu_bar_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\menu_button_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\menu_style.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\menu_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\mergeable_material.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\motion.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_bar.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_bar_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_drawer.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_drawer_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_rail.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_rail_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\no_splash.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\outlined_button.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\outlined_button_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\page.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\page_transitions_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\paginated_data_table.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\popup_menu.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\popup_menu_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\predictive_back_page_transitions_builder.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\progress_indicator.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\progress_indicator_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\radio.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\radio_list_tile.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\radio_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\range_slider.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\refresh_indicator.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\reorderable_list.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\scaffold.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\scrollbar.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\scrollbar_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\search.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\search_anchor.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\search_bar_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\search_view_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\segmented_button.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\segmented_button_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\selectable_text.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\selection_area.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\shadows.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\slider.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\slider_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\slider_value_indicator_shape.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\snack_bar.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\snack_bar_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar_layout_delegate.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\stepper.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\switch.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\switch_list_tile.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\switch_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\tab_bar_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\tab_controller.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\tab_indicator.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\tabs.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\text_button.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\text_button_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\text_field.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\text_form_field.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar_text_button.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\text_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\theme_data.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\time.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\time_picker.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\time_picker_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\toggle_buttons.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\toggle_buttons_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip_visibility.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\typography.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\user_accounts_drawer_header.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\widgets.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\asset_bundle.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\asset_manifest.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\autofill.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\binary_messenger.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\binding.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\browser_context_menu.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\clipboard.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\debug.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\deferred_component.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\flavor.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\flutter_version.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\font_loader.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\haptic_feedback.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\hardware_keyboard.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_inserted_content.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_key.g.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_maps.g.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\live_text.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\message_codec.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\message_codecs.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\mouse_cursor.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\mouse_tracking.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\platform_channel.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\platform_views.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\predictive_back_event.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\process_text.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_android.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_fuchsia.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_ios.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_linux.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_macos.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_web.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_windows.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\restoration.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\scribe.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\service_extensions.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\spell_check.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\system_channels.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\system_chrome.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\system_navigator.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\system_sound.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\text_boundary.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\text_editing.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\text_editing_delta.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\text_formatter.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\text_input.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\text_layout_metrics.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\undo_manager.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter_localizations\\lib\\src\\cupertino_localizations.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter_localizations\\lib\\src\\l10n\\generated_cupertino_localizations.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter_localizations\\lib\\src\\l10n\\generated_material_localizations.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter_localizations\\lib\\src\\l10n\\generated_widgets_localizations.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter_localizations\\lib\\src\\material_localizations.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter_localizations\\lib\\src\\widgets_localizations.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\app_routing\\lib\\app_router.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\app_routing\\lib\\route_definitions.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\app_routing\\lib\\l10n\\routing_l10n.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\core_services\\lib\\di\\service_locator.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\core_services\\lib\\models\\base_item.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\core_services\\lib\\models\\repository_result.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\core_services\\lib\\models\\user_model.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\core_services\\lib\\event_bus.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\core_services\\lib\\module_interface.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\core_services\\lib\\services\\module_manager.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\core_services\\lib\\services\\navigation_service.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\core_services\\lib\\services\\logging_service.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\core_services\\lib\\services\\error_handling_service.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\core_services\\lib\\services\\performance_monitoring_service.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\core_services\\lib\\services\\display_mode_service.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\core_services\\lib\\services\\locale_service.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\core_services\\lib\\services\\i18n_service.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\core_services\\lib\\services\\repositories\\persistence_repository.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\core_services\\lib\\services\\repositories\\in_memory_repository.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\core_services\\lib\\services\\repositories\\drift_persistence_repository.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\core_services\\lib\\services\\security\\encryption_service.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\core_services\\lib\\services\\auth\\auth_service_interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\drift.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\native.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\path.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\core_services\\lib\\database\\database.g.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\ui_framework\\lib\\app.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\ui_framework\\lib\\shell\\main_shell.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\ui_framework\\lib\\shell\\navigation_drawer.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\ui_framework\\lib\\shell\\page_container.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\ui_framework\\lib\\shell\\app_shell.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\ui_framework\\lib\\shell\\responsive_web_shell.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\ui_framework\\lib\\shell\\display_mode_aware_shell.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\ui_framework\\lib\\shell\\modular_mobile_shell.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\ui_framework\\lib\\components\\edit_dialog.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\ui_framework\\lib\\core\\theme_service.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\ui_framework\\lib\\l10n\\ui_l10n.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\notes_hub\\lib\\notes_hub_module.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\notes_hub\\lib\\notes_hub_widget.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\notes_hub\\lib\\l10n\\notes_hub_l10n.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\notes_hub\\lib\\l10n\\notes_hub_zh.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\notes_hub\\lib\\l10n\\notes_hub_en.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\workshop\\lib\\workshop_module.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\workshop\\lib\\workshop_widget.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\workshop\\lib\\l10n\\workshop_l10n.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\punch_in\\lib\\punch_in_module.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\punch_in\\lib\\punch_in_widget.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\punch_in\\lib\\l10n\\punch_in_l10n.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\lib\\intl.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\apps\\platform_app\\lib\\l10n\\app_localizations_en.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\apps\\platform_app\\lib\\l10n\\app_localizations_zh.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\path_provider_platform_interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_android-2.2.17\\lib\\messages.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\src\\shared_preferences_android.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\src\\shared_preferences_async_android.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\lib\\messages.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\lib\\src\\shared_preferences_async_foundation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\lib\\src\\shared_preferences_foundation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\path_provider_linux.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\file.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\local.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\shared_preferences_async_platform_interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\shared_preferences_platform_interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\types.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\folders.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\path_provider_windows_real.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.16.0\\lib\\meta_meta.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\_bitfield_io.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\_capabilities_io.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\_isolates_io.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\_platform_io.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\foundation\\_timeline_io.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\cupertino.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\scheduler.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\back_button.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\rendering.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons_data.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\add_event.g.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\arrow_menu.g.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\close_menu.g.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\ellipsis_search.g.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\event_add.g.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\home_menu.g.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\list_view.g.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_arrow.g.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_close.g.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_home.g.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\pause_play.g.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\play_pause.g.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\search_ellipsis.g.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\view_list.g.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\animation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\vector_math_64.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\gestures.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\collection.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\material_color_utilities.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\painting.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\characters.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\actions.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\adapter.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_cross_fade.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_scroll_view.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_size.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_switcher.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\annotated_region.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\app.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\app_lifecycle_listener.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\async.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\autocomplete.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\autofill.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\automatic_keep_alive.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\banner.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\basic.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\binding.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\bottom_navigation_bar_item.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\color_filter.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\container.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\context_menu_button_item.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\context_menu_controller.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\debug.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\decorated_sliver.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\default_selection_style.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\default_text_editing_shortcuts.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\desktop_text_selection_toolbar_layout_delegate.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\dismissible.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\display_feature_sub_screen.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\disposable_build_context.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\drag_boundary.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\drag_target.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\draggable_scrollable_sheet.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\dual_transition_builder.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\editable_text.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\expansible.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\fade_in_image.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\feedback.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\flutter_logo.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_manager.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_scope.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_traversal.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\form.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\framework.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\gesture_detector.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\grid_paper.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\heroes.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_data.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_theme_data.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\image.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\image_filter.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\image_icon.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\implicit_animations.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_model.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_notifier.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\interactive_viewer.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\keyboard_listener.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\layout_builder.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\list_wheel_scroll_view.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\localizations.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\lookup_boundary.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\magnifier.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\media_query.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\modal_barrier.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigation_toolbar.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigator.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigator_pop_handler.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\nested_scroll_view.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\notification_listener.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\orientation_builder.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\overflow_bar.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\overlay.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\overscroll_indicator.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\page_storage.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\page_view.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\pages.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\performance_overlay.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\pinned_header_sliver.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\placeholder.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_menu_bar.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_selectable_region_context_menu.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_view.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\pop_scope.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\preferred_size.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\primary_scroll_controller.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\raw_keyboard_listener.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\raw_menu_anchor.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\reorderable_list.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\restoration.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\restoration_properties.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\router.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\routes.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\safe_area.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_activity.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_aware_image_provider.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_configuration.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_context.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_controller.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_delegate.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_metrics.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_notification.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_notification_observer.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_physics.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_position.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_position_with_single_context.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_simulation.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_view.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollable.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollable_helpers.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollbar.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\selectable_region.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\selection_container.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\semantics_debugger.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\service_extensions.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\shared_app_data.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\shortcuts.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\single_child_scroll_view.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\size_changed_layout_notifier.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_fill.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_floating_header.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_layout_builder.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_persistent_header.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_prototype_extent_list.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_resizing_header.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_tree.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\slotted_render_object_widget.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\snapshot_widget.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\spacer.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\spell_check.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\standard_component_type.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\status_transitions.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\system_context_menu.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\table.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\tap_region.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\text.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_editing_intents.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_anchors.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_layout_delegate.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\texture.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\ticker_provider.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\title.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\toggleable.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\transitions.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\tween_animation_builder.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_scroll_view.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_viewport.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\undo_history.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\unique_widget.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\value_listenable_builder.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\view.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\viewport.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\visibility.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_inspector.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_preview.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_span.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_state.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\will_pop_scope.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\services\\_background_isolate_binary_messenger_io.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter_localizations\\lib\\src\\utils\\date_localizations.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-14.8.1\\lib\\go_router.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\app_routing\\lib\\l10n\\routing_zh.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\app_routing\\lib\\l10n\\routing_en.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get_it-8.0.3\\lib\\get_it.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\crypto.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\rxdart.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\shared_preferences.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\dsl\\dsl.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\api\\options.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\api\\runtime_api.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\cancellation_zone.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\custom_result_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\data_class.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\data_verification.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\exceptions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\executor\\connection_pool.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\executor\\executor.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\executor\\interceptor.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\query_builder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\types\\converters.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\types\\mapping.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\manager\\manager.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\utils\\lazy_database.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\async.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\isolate.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\common.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\sqlite3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\backends.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\sqlite3\\database.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\sqlite3\\database_tracker.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\context.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\path_exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\path_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\path_set.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\desktop_environment\\lib\\desktop_environment.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\ui_framework\\lib\\l10n\\ui_zh.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\ui_framework\\lib\\l10n\\ui_en.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\workshop\\lib\\l10n\\workshop_zh.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\workshop\\lib\\l10n\\workshop_en.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\punch_in\\lib\\l10n\\punch_in_zh.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\punch_in\\lib\\l10n\\punch_in_en.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\lib\\src\\global_state.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\lib\\src\\intl\\date_format.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\lib\\src\\intl_helpers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\lib\\src\\plural_rules.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\lib\\src\\intl\\bidi.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\lib\\src\\intl\\bidi_formatter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\lib\\src\\intl\\micro_money.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\lib\\src\\intl\\number_format.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\lib\\src\\intl\\number_parser_base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\lib\\src\\intl\\text_direction.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\lib\\plugin_platform_interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\src\\enums.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\src\\method_channel_path_provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\src\\messages.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\src\\strings.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\src\\messages_async.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\lib\\src\\messages.g.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xdg_directories-1.1.0\\lib\\xdg_directories.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\get_application_id.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\method_channel_shared_preferences.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.4\\lib\\ffi.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\guid.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\win32_wrappers.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\activity_indicator.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\adaptive_text_selection_toolbar.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\app.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\bottom_tab_bar.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\button.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\checkbox.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\colors.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\constants.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\context_menu.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\context_menu_action.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\date_picker.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\debug.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar_button.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\dialog.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\form_row.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\form_section.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\icon_theme_data.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\icons.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\interface_level.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\list_section.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\list_tile.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\localizations.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\magnifier.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\nav_bar.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\page_scaffold.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\picker.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\radio.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\refresh.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\route.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\scrollbar.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\search_field.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\segmented_control.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\sheet.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\slider.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\sliding_segmented_control.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\spell_check_suggestions_toolbar.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\switch.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\tab_scaffold.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\tab_view.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_field.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_form_field_row.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar_button.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\theme.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\cupertino\\thumb_painter.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\scheduler\\binding.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\scheduler\\debug.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\scheduler\\priority.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\scheduler\\service_extensions.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\scheduler\\ticker.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\semantics.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\animated_size.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\binding.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\box.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\custom_layout.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\custom_paint.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\debug.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\debug_overflow_indicator.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\decorated_sliver.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\editable.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\error.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\flex.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\flow.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\image.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\layer.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\layout_helper.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\list_body.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\list_wheel_viewport.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\mouse_tracker.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\object.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\paragraph.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\performance_overlay.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\platform_view.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\proxy_box.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\proxy_sliver.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\rotated_box.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\selection.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\service_extensions.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\shifted_box.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_fill.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_fixed_extent_list.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_grid.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_group.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_list.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_multi_box_adaptor.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_padding.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_persistent_header.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_tree.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\stack.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\table.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\table_border.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\texture.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\tweens.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\view.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\viewport.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\viewport_offset.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\rendering\\wrap.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\animation\\animation.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\animation\\animation_controller.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\animation\\animation_style.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\animation\\animations.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\animation\\curves.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\animation\\listener_helpers.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\animation\\tween.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\animation\\tween_sequence.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\colors.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\constants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\error_helpers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\frustum.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\intersection_result.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix4.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\noise.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\obb3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\opengl.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\plane.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quad.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quaternion.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\ray.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\sphere.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\triangle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\utilities.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector2.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector4.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\arena.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\binding.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\constants.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\converter.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\debug.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\drag.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\drag_details.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\eager.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\events.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\force_press.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\gesture_settings.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\hit_test.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\long_press.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\lsq_solver.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\monodrag.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\multidrag.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\multitap.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\pointer_router.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\pointer_signal_resolver.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\recognizer.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\resampler.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\scale.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\tap.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\tap_and_drag.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\team.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\gestures\\velocity_tracker.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\algorithms.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\boollist.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\canonicalized_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_iterable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_list.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\comparators.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\equality.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\equality_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\equality_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\functions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\iterable_extensions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\iterable_zip.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\list_extensions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\priority_queue.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\queue_list.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\union_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\union_set_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\unmodifiable_wrappers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\wrappers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\blend\\blend.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\contrast\\contrast.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dislike\\dislike_analyzer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\dynamic_color.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\dynamic_scheme.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\material_dynamic_colors.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\variant.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\cam16.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\hct.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\viewing_conditions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\palettes\\core_palette.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\palettes\\tonal_palette.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_celebi.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_wsmeans.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_wu.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_content.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_expressive.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_fidelity.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_fruit_salad.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_monochrome.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_neutral.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_rainbow.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_tonal_spot.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_vibrant.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\score\\score.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\temperature\\temperature_cache.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\color_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\math_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\string_utils.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\alignment.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\basic_types.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\beveled_rectangle_border.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\binding.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\border_radius.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\borders.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\box_border.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\box_decoration.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\box_fit.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\box_shadow.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\circle_border.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\clip.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\colors.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\continuous_rectangle_border.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\debug.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\decoration.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\decoration_image.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\edge_insets.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\flutter_logo.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\fractional_offset.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\geometry.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\gradient.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\image_cache.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\image_decoder.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\image_provider.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\image_resolution.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\image_stream.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\inline_span.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\linear_border.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\matrix_utils.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\notched_shapes.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\oval_border.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\paint_utilities.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\placeholder_span.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\rounded_rectangle_border.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\shader_warm_up.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\shape_decoration.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\stadium_border.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\star_border.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\strut_style.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\text_painter.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\text_scaler.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\text_span.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\text_style.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\characters.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\extensions.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\constants.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\_web_image_info_io.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\_web_image_io.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\physics.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\_platform_selectable_region_context_menu_io.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\widgets\\_html_element_view_io.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\lib\\date_symbol_data_custom.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\lib\\date_symbols.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter_localizations\\lib\\src\\l10n\\generated_date_localizations.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-14.8.1\\lib\\src\\builder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-14.8.1\\lib\\src\\configuration.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-14.8.1\\lib\\src\\delegate.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-14.8.1\\lib\\src\\information_provider.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-14.8.1\\lib\\src\\match.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-14.8.1\\lib\\src\\misc\\errors.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-14.8.1\\lib\\src\\misc\\extensions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-14.8.1\\lib\\src\\misc\\inherited_router.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-14.8.1\\lib\\src\\pages\\custom_transition_page.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-14.8.1\\lib\\src\\parser.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-14.8.1\\lib\\src\\route.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-14.8.1\\lib\\src\\route_data.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-14.8.1\\lib\\src\\router.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-14.8.1\\lib\\src\\state.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get_it-8.0.3\\lib\\get_it_impl.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\digest.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\hash.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\hmac.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\md5.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\sha1.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\sha256.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\sha512.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\rx.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\streams.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\subjects.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\transformers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\src\\shared_preferences_async.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\src\\shared_preferences_legacy.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\dsl\\columns.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\dsl\\database.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\dsl\\table.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\executor\\delayed_stream_queries.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\executor\\stream_queries.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\executor\\transactions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\devtools\\devtools.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\api\\batch.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\api\\connection.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\api\\connection_user.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\api\\dao_base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\api\\db_base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\api\\stream_updates.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\internal\\versioned_schema.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\utils\\async_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\utils\\single_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\utils\\async.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\expressions\\case_when.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\expressions\\internal.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\helpers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\components\\table_valued_function.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\expressions\\bitwise.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\on_table.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\components\\group_by.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\components\\join.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\components\\limit.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\components\\order_by.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\components\\subquery.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\components\\where.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\expressions\\aggregate.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\expressions\\algebra.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\expressions\\bools.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\expressions\\comparable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\expressions\\custom.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\expressions\\datetimes.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\expressions\\exists.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\expressions\\expression.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\expressions\\in.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\expressions\\null_check.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\expressions\\text.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\expressions\\variables.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\expressions\\window.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\generation_context.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\migration.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\schema\\column_impl.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\schema\\entities.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\schema\\table_info.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\schema\\view_info.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\statements\\delete.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\statements\\insert.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\statements\\query.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\statements\\select\\custom_select.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\statements\\select\\select.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\statements\\select\\select_with_join.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\query_builder\\statements\\update.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.2\\lib\\convert.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\manager\\composer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\manager\\filter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\manager\\join_builder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\manager\\ordering.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\manager\\references.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\manager\\computed_fields.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\async_cache.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\async_memoizer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\byte_collector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\cancelable_operation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\chunked_stream_reader.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\delegate\\event_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\delegate\\future.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\delegate\\sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\delegate\\stream.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\delegate\\stream_consumer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\delegate\\stream_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\delegate\\stream_subscription.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\future_group.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\lazy_stream.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\null_stream_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\restartable_timer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\result\\error.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\result\\future.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\result\\result.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\result\\value.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\single_subscription_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\sink_base.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\stream_closer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\stream_completer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\stream_extensions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\stream_group.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\stream_queue.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\stream_sink_completer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\stream_sink_extensions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\stream_sink_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\stream_splitter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\stream_subscription_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\stream_zip.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\subscription_stream.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\typed_stream_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\lib\\stream_channel.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\remote.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\isolate.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\remote\\protocol.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\constants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\database.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\functions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\in_memory_vfs.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\jsonb.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\result_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\sqlite3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\statement.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\vfs.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\ffi\\api.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\executor\\helpers\\delegates.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\executor\\helpers\\engines.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\executor\\helpers\\results.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\sqlite3\\native_functions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\characters.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\internal_style.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\parsed_path.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style\\posix.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style\\url.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style\\windows.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\desktop_environment\\lib\\spatial_os_shell.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\desktop_environment\\lib\\window_manager.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\desktop_environment\\lib\\floating_window.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\desktop_environment\\lib\\app_dock.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\desktop_environment\\lib\\performance_monitor_panel.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\desktop_environment\\lib\\dev_panel.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\desktop_environment\\lib\\types\\window_types.dart", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\packages\\desktop_environment\\lib\\utils\\desktop_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\lib\\src\\date_format_internal.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\lib\\src\\intl\\constants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\lib\\src\\intl\\date_builder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\lib\\src\\intl\\date_computation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\lib\\src\\intl\\regexp.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\lib\\src\\intl\\string_stack.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\lib\\src\\intl\\date_format_field.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\lib\\number_symbols.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\lib\\number_symbols_data.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\lib\\src\\intl\\number_format_parser.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\lib\\src\\intl\\number_parser.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\lib\\src\\intl\\compact_number_format.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\lib\\platform.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\get_application_id_real.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_directory.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_file.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_file_system.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_file_system_entity.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_link.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_random_access_file.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\directory.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\error_codes.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\file.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\file_system.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\file_system_entity.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\link.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\io.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_file_system.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.4\\lib\\src\\allocation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.4\\lib\\src\\arena.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.4\\lib\\src\\utf16.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.4\\lib\\src\\utf8.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\semantics\\binding.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\semantics\\debug.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics_event.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics_service.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_iterator.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\empty_unmodifiable_set.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\src\\contrast_curve.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\src\\tone_delta_pair.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\src\\hct_solver.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\src\\point_provider_lab.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\src\\point_provider.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\painting\\_network_image_io.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\characters_impl.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\physics\\clamped_simulation.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\physics\\friction_simulation.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\physics\\gravity_simulation.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\physics\\simulation.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\physics\\spring_simulation.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\physics\\tolerance.dart", "D:\\Coding\\Flutter\\flutter\\packages\\flutter\\lib\\src\\physics\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-14.8.1\\lib\\src\\logging.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-14.8.1\\lib\\src\\misc\\error_screen.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-14.8.1\\lib\\src\\pages\\cupertino.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-14.8.1\\lib\\src\\pages\\material.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-14.8.1\\lib\\src\\path_utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\logging-1.3.0\\lib\\logging.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\digest_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\hash_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\sha512_fastsinks.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\error_and_stacktrace.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\combine_latest.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\concat.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\concat_eager.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\connectable_stream.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\defer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\fork_join.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\from_callable.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\merge.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\never.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\race.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\range.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\repeat.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\replay_stream.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\retry.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\retry_when.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\sequence_equal.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\switch_latest.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\timer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\using.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\value_stream.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\zip.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\subjects\\behavior_subject.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\subjects\\publish_subject.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\subjects\\replay_subject.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\subjects\\subject.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\buffer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\debounce.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\pairwise.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\sample.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\throttle.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\window.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\default_if_empty.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\delay.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\delay_when.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\dematerialize.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\distinct_unique.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\do.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\end_with.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\end_with_many.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\exhaust_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\flat_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\group_by.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\ignore_elements.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\interval.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\map_not_null.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\map_to.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\materialize.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\max.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\min.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\on_error_resume.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\scan.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\skip_last.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\skip_until.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\start_with.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\start_with_many.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\switch_if_empty.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\switch_map.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\take_last.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\take_until.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\take_while_inclusive.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\time_interval.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\timestamp.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\where_not_null.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\where_type.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\with_latest_from.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\composite_subscription.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\notification.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\src\\shared_preferences_devtools_extension_data.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\devtools\\service_extension.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\runtime\\devtools\\shared.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.2\\lib\\src\\accumulator_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.2\\lib\\src\\byte_accumulator_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.2\\lib\\src\\codepage.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.2\\lib\\src\\fixed_datetime_formatter.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.2\\lib\\src\\hex.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.2\\lib\\src\\identity_codec.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.2\\lib\\src\\percent.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.2\\lib\\src\\string_accumulator_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\typed\\stream_subscription.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\result\\capture_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\result\\capture_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\result\\release_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\result\\release_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\stream_sink_transformer\\reject_errors.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\stream_sink_transformer\\handler_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\stream_sink_transformer\\stream_transformer_wrapper.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\lib\\src\\stream_sink_transformer\\typed.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\lib\\src\\close_guarantee_channel.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\lib\\src\\guarantee_channel.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\lib\\src\\stream_channel_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\lib\\src\\delegating_stream_channel.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\lib\\src\\disconnector.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\lib\\src\\json_document_transformer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\lib\\src\\multi_channel.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\lib\\src\\stream_channel_completer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\lib\\src\\stream_channel_controller.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\remote\\client_impl.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\remote\\communication.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\remote\\server_impl.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\lib\\typed_buffers.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\open.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\ffi\\implementation.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\drift-2.27.0\\lib\\src\\utils\\synchronized.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\lib\\clock.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\interface\\local_platform.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\interface\\platform.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\testing\\fake_platform.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\error_codes_dart_io.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_directory.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_file.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_link.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\grapheme_clusters\\breaks.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\grapheme_clusters\\constants.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\grapheme_clusters\\table.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\logging-1.3.0\\lib\\src\\level.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\logging-1.3.0\\lib\\src\\log_record.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\logging-1.3.0\\lib\\src\\logger.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\lib\\typed_data.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\collection_extensions.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\subscription.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\start_with_error.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\empty.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\backpressure.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\forwarding_sink.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\forwarding_stream.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\future.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\min_max.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.2\\lib\\src\\hex\\decoder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.2\\lib\\src\\hex\\encoder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.2\\lib\\src\\percent\\decoder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.2\\lib\\src\\percent\\encoder.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\lib\\stack_trace.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\lib\\src\\typed_buffer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\ffi\\load_library.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\implementation\\bindings.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\implementation\\database.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\implementation\\exception.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\implementation\\sqlite3.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\implementation\\statement.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\ffi\\generated\\shared.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\ffi\\bindings.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\ffi\\memory.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\lib\\src\\default.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\lib\\src\\clock.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\common.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_file_system_entity.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\lib\\src\\typed_queue.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.2\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.2\\lib\\src\\charcodes.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\lib\\src\\chain.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\lib\\src\\frame.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\lib\\src\\trace.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\lib\\src\\unparsed_frame.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\implementation\\finalizer.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\implementation\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\ffi\\generated\\dynamic_library.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\ffi\\generated\\native_library.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\lib\\src\\stopwatch.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\lib\\src\\lazy_chain.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\lib\\src\\stack_zone_specification.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\lib\\src\\utils.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\lib\\src\\lazy_trace.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\lib\\src\\vm_trace.dart", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sqlite3-2.7.6\\lib\\src\\ffi\\generated\\native.dart"], "outputs": ["D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\apps\\platform_app\\.dart_tool\\flutter_build\\2ab3122f7b0c361e8f6b6867dc5d9310\\app.dill", "D:\\狗py\\pythonProject\\CRUD\\Tasks_organizing\\pet_app\\apps\\platform_app\\.dart_tool\\flutter_build\\2ab3122f7b0c361e8f6b6867dc5d9310\\app.dill"]}