# This file configures the static analysis results for your project (errors,
# warnings, and lints).
#
# This enables the 'recommended' set of lints from `package:very_good_analysis`.
# This set provides extensive linting rules for high-quality Dart code.

include: package:very_good_analysis/analysis_options.yaml

# Uncomment the following section to specify additional rules.

# linter:
#   rules:
#     - camel_case_types

analyzer:
  exclude:
    - templates/**
    - test_init/**

# For more information about the core and recommended set of lints, see
# https://dart.dev/go/core-lints

# For additional information about configuring this file, see
# https://dart.dev/guides/language/analysis-options
