import 'package:test/test.dart';
import 'package:creative_workshop/src/services/local_ming_cli_service.dart';

void main() {
  group('LocalMingCliService 简单测试', () {
    test('MingCliResult 数据模型测试', () {
      const result = MingCliResult(
        success: true,
        command: 'ming create test_plugin',
        output: '插件创建成功',
        executionTime: Duration(seconds: 2),
        generatedFiles: ['lib/main.dart', 'pubspec.yaml'],
        projectPath: './test_plugin',
      );

      expect(result.success, isTrue);
      expect(result.command, equals('ming create test_plugin'));
      expect(result.output, equals('插件创建成功'));
      expect(result.executionTime, equals(const Duration(seconds: 2)));
      expect(result.generatedFiles, hasLength(2));
      expect(result.generatedFiles, contains('lib/main.dart'));
      expect(result.generatedFiles, contains('pubspec.yaml'));
      expect(result.projectPath, equals('./test_plugin'));
      expect(result.exitCode, equals(0));
      expect(result.error, isNull);
    });

    test('MingCliResult 失败结果测试', () {
      const result = MingCliResult(
        success: false,
        command: 'ming test',
        error: '测试失败',
        exitCode: 1,
      );

      expect(result.success, isFalse);
      expect(result.command, equals('ming test'));
      expect(result.error, equals('测试失败'));
      expect(result.exitCode, equals(1));
      expect(result.output, isNull);
      expect(result.projectPath, isNull);
      expect(result.generatedFiles, isEmpty);
    });

    test('PluginGenerationNotification 数据模型测试', () {
      final now = DateTime.now();
      final notification = PluginGenerationNotification(
        pluginId: 'test_plugin',
        pluginName: '测试插件',
        projectPath: './test_plugin',
        templateType: 'tool',
        generatedAt: now,
        version: '1.0.0',
        description: '测试插件描述',
        author: '测试作者',
        generatedFiles: ['lib/main.dart', 'pubspec.yaml', 'README.md'],
      );

      expect(notification.pluginId, equals('test_plugin'));
      expect(notification.pluginName, equals('测试插件'));
      expect(notification.projectPath, equals('./test_plugin'));
      expect(notification.templateType, equals('tool'));
      expect(notification.generatedAt, equals(now));
      expect(notification.version, equals('1.0.0'));
      expect(notification.description, equals('测试插件描述'));
      expect(notification.author, equals('测试作者'));
      expect(notification.generatedFiles, hasLength(3));
      expect(notification.generatedFiles, contains('lib/main.dart'));
      expect(notification.generatedFiles, contains('pubspec.yaml'));
      expect(notification.generatedFiles, contains('README.md'));
    });

    test('PluginGenerationNotification 默认值测试', () {
      final now = DateTime.now();
      final notification = PluginGenerationNotification(
        pluginId: 'simple_plugin',
        pluginName: '简单插件',
        projectPath: './simple_plugin',
        templateType: 'basic',
        generatedAt: now,
      );

      expect(notification.pluginId, equals('simple_plugin'));
      expect(notification.pluginName, equals('简单插件'));
      expect(notification.projectPath, equals('./simple_plugin'));
      expect(notification.templateType, equals('basic'));
      expect(notification.version, equals('1.0.0')); // 默认版本
      expect(notification.description, isNull);
      expect(notification.author, isNull);
      expect(notification.generatedFiles, isEmpty); // 默认空列表
    });

    test('LocalMingCliService 单例模式测试', () {
      final service1 = LocalMingCliService.instance;
      final service2 = LocalMingCliService.instance;

      expect(service1, same(service2));
    });

    test('命令历史数据结构测试', () {
      final service = LocalMingCliService.instance;

      // 测试初始状态
      expect(service.commandHistory, isEmpty);
      expect(service.isCliAvailable, isFalse); // 未初始化时应该是false
      expect(service.cliVersion, isNull);
    });

    test('插件生成通知流测试', () {
      final service = LocalMingCliService.instance;

      // 测试通知流存在
      expect(service.pluginGenerationNotifications, isNotNull);
      expect(service.pluginGenerationNotifications,
          isA<Stream<PluginGenerationNotification>>());
    });

    test('MingCliResult 工厂方法测试', () {
      // 测试成功结果的各种组合
      const result1 = MingCliResult(
        success: true,
        command: 'test command',
      );
      expect(result1.success, isTrue);
      expect(result1.exitCode, equals(0));
      expect(result1.generatedFiles, isEmpty);

      const result2 = MingCliResult(
        success: false,
        command: 'failed command',
        exitCode: 1,
      );
      expect(result2.success, isFalse);
      expect(result2.exitCode, equals(1));
    });

    test('数据模型不变性测试', () {
      const result = MingCliResult(
        success: true,
        command: 'test',
        generatedFiles: ['file1.dart', 'file2.dart'],
      );

      // 测试列表是不可变的
      expect(() => result.generatedFiles.add('file3.dart'),
          throwsUnsupportedError);
    });

    test('PluginGenerationNotification 时间处理测试', () {
      final now = DateTime.now();
      final notification = PluginGenerationNotification(
        pluginId: 'time_test',
        pluginName: '时间测试',
        projectPath: './time_test',
        templateType: 'test',
        generatedAt: now,
      );

      expect(notification.generatedAt, equals(now));
      expect(
          notification.generatedAt
              .isBefore(DateTime.now().add(const Duration(seconds: 1))),
          isTrue);
    });
  });
}
