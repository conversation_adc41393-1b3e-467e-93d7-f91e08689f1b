/*
---------------------------------------------------------------
File name:          core_services_v1_provider_test.dart
Author:             Ming Status CLI
Date created:       2025-07-14
Last modified:      2025-07-14
Dart Version:       3.2+
Description:        core_services_v1 Provider 单元测试
---------------------------------------------------------------
Change History:
    2025-07-14: Initial creation - core_services_v1 Provider 单元测试;
---------------------------------------------------------------
*/

import 'package:test/test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:core_services_v1/lib/src/providers/core_services_v1_provider.dart';

void main() {
  group('CoreServicesV1Provider 单元测试', () {
    late CoreServicesV1Provider provider;

    setUp(() {
      provider = CoreServicesV1Provider();
    });

    tearDown(() async {
    });

    test('初始状态应该正确', () {
      expect(provider.isInitialized, false);
      expect(provider.isLoading, false);
      expect(provider.error, null);
    });

    test('初始化应该成功', () async {
      await provider.initialize();
      expect(provider.isInitialized, true);
    });

    test('加载数据应该成功', () async {
      await provider.initialize();
      await provider.loadData();
      expect(provider.data, isNotEmpty);
    });

    test('选择项目应该更新状态', () async {
      await provider.initialize();
      await provider.loadData();
      final firstItem = provider.data.first;
      provider.selectItem(firstItem);
      expect(provider.selectedItem, firstItem);
    });

  });
}
