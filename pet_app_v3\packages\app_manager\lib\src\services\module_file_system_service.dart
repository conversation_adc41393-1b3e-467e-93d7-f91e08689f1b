/*
---------------------------------------------------------------
File name:          module_file_system_service.dart
Author:             lgnorant-lu
Date created:       2025-07-24
Last modified:      2025-07-24
Dart Version:       3.2+
Description:        模块文件系统服务
---------------------------------------------------------------
Change History:
    2025-07-24: Phase ******* - 实现模块文件系统服务;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:developer' as developer;
import '../models/module_file.dart';
import '../models/file_operation_result.dart';

/// 模块文件系统服务
/// 
/// 负责模块文件的浏览、管理、搜索等操作
class ModuleFileSystemService {
  static ModuleFileSystemService? _instance;
  static ModuleFileSystemService get instance => _instance ??= ModuleFileSystemService._();
  
  ModuleFileSystemService._();

  final Map<String, List<ModuleFile>> _moduleFiles = {};
  final StreamController<FileSystemEvent> _eventController = StreamController.broadcast();

  /// 文件系统事件流
  Stream<FileSystemEvent> get fileSystemEvents => _eventController.stream;

  /// 列出模块文件
  Future<List<ModuleFile>> listFiles(String moduleId, {String? path}) async {
    try {
      _log('info', '列出模块文件: $moduleId, path: $path');
      
      // 获取模块文件列表
      final moduleFiles = _moduleFiles[moduleId] ?? [];
      
      // 如果指定了路径，过滤文件
      if (path != null && path.isNotEmpty) {
        final filteredFiles = moduleFiles.where((file) => 
          file.path.startsWith(path)).toList();
        return filteredFiles;
      }
      
      return moduleFiles;
      
    } catch (e, stackTrace) {
      _log('severe', '列出模块文件失败: $moduleId', e, stackTrace);
      return [];
    }
  }

  /// 创建文件
  Future<FileOperationResult> createFile(
    String moduleId,
    String path,
    String content, {
    FileType type = FileType.text,
  }) async {
    try {
      _log('info', '创建文件: $moduleId/$path');
      
      // 检查文件是否已存在
      final existingFile = await getFile(moduleId, path);
      if (existingFile != null) {
        return FileOperationResult.failure(
          moduleId,
          path,
          '文件已存在',
          FileOperationError.fileExists,
        );
      }

      // 创建文件对象
      final file = ModuleFile(
        moduleId: moduleId,
        path: path,
        name: _extractFileName(path),
        type: type,
        size: content.length,
        content: content,
        createdAt: DateTime.now(),
        modifiedAt: DateTime.now(),
      );

      // 添加到文件列表
      _moduleFiles.putIfAbsent(moduleId, () => []).add(file);
      
      // 发送事件
      _eventController.add(FileSystemEvent.fileCreated(moduleId, path));
      
      _log('info', '文件创建成功: $moduleId/$path');
      return FileOperationResult.success(moduleId, path, '文件创建成功');
      
    } catch (e, stackTrace) {
      _log('severe', '文件创建失败: $moduleId/$path', e, stackTrace);
      return FileOperationResult.failure(
        moduleId,
        path,
        '创建过程中发生错误: $e',
        FileOperationError.operationFailed,
      );
    }
  }

  /// 读取文件
  Future<ModuleFile?> getFile(String moduleId, String path) async {
    try {
      final moduleFiles = _moduleFiles[moduleId] ?? [];
      return moduleFiles.firstWhere(
        (file) => file.path == path,
        orElse: () => throw StateError('File not found'),
      );
    } catch (e) {
      return null;
    }
  }

  /// 更新文件内容
  Future<FileOperationResult> updateFile(
    String moduleId,
    String path,
    String content,
  ) async {
    try {
      _log('info', '更新文件: $moduleId/$path');
      
      final moduleFiles = _moduleFiles[moduleId] ?? [];
      final fileIndex = moduleFiles.indexWhere((file) => file.path == path);
      
      if (fileIndex == -1) {
        return FileOperationResult.failure(
          moduleId,
          path,
          '文件不存在',
          FileOperationError.fileNotFound,
        );
      }

      // 更新文件
      final oldFile = moduleFiles[fileIndex];
      final updatedFile = oldFile.copyWith(
        content: content,
        size: content.length,
        modifiedAt: DateTime.now(),
      );
      
      moduleFiles[fileIndex] = updatedFile;
      
      // 发送事件
      _eventController.add(FileSystemEvent.fileUpdated(moduleId, path));
      
      _log('info', '文件更新成功: $moduleId/$path');
      return FileOperationResult.success(moduleId, path, '文件更新成功');
      
    } catch (e, stackTrace) {
      _log('severe', '文件更新失败: $moduleId/$path', e, stackTrace);
      return FileOperationResult.failure(
        moduleId,
        path,
        '更新过程中发生错误: $e',
        FileOperationError.operationFailed,
      );
    }
  }

  /// 删除文件
  Future<FileOperationResult> deleteFile(String moduleId, String path) async {
    try {
      _log('info', '删除文件: $moduleId/$path');
      
      final moduleFiles = _moduleFiles[moduleId] ?? [];
      final fileIndex = moduleFiles.indexWhere((file) => file.path == path);
      
      if (fileIndex == -1) {
        return FileOperationResult.failure(
          moduleId,
          path,
          '文件不存在',
          FileOperationError.fileNotFound,
        );
      }

      // 删除文件
      moduleFiles.removeAt(fileIndex);
      
      // 发送事件
      _eventController.add(FileSystemEvent.fileDeleted(moduleId, path));
      
      _log('info', '文件删除成功: $moduleId/$path');
      return FileOperationResult.success(moduleId, path, '文件删除成功');
      
    } catch (e, stackTrace) {
      _log('severe', '文件删除失败: $moduleId/$path', e, stackTrace);
      return FileOperationResult.failure(
        moduleId,
        path,
        '删除过程中发生错误: $e',
        FileOperationError.operationFailed,
      );
    }
  }

  /// 搜索文件
  Future<List<ModuleFile>> searchFiles(
    String moduleId,
    String query, {
    FileSearchOptions? options,
  }) async {
    try {
      _log('info', '搜索文件: $moduleId, query: $query');
      
      final moduleFiles = _moduleFiles[moduleId] ?? [];
      final searchOptions = options ?? const FileSearchOptions();
      
      List<ModuleFile> results = moduleFiles;
      
      // 按名称搜索
      if (query.isNotEmpty) {
        results = results.where((file) {
          final searchTarget = searchOptions.caseSensitive 
              ? file.name 
              : file.name.toLowerCase();
          final searchQuery = searchOptions.caseSensitive 
              ? query 
              : query.toLowerCase();
          
          if (searchOptions.exactMatch) {
            return searchTarget == searchQuery;
          } else {
            return searchTarget.contains(searchQuery);
          }
        }).toList();
      }
      
      // 按文件类型过滤
      if (searchOptions.fileTypes.isNotEmpty) {
        results = results.where((file) => 
          searchOptions.fileTypes.contains(file.type)).toList();
      }
      
      // 按大小过滤
      if (searchOptions.minSize != null) {
        results = results.where((file) => 
          file.size >= searchOptions.minSize!).toList();
      }
      
      if (searchOptions.maxSize != null) {
        results = results.where((file) => 
          file.size <= searchOptions.maxSize!).toList();
      }
      
      // 按修改时间过滤
      if (searchOptions.modifiedAfter != null) {
        results = results.where((file) => 
          file.modifiedAt.isAfter(searchOptions.modifiedAfter!)).toList();
      }
      
      if (searchOptions.modifiedBefore != null) {
        results = results.where((file) => 
          file.modifiedAt.isBefore(searchOptions.modifiedBefore!)).toList();
      }
      
      // 排序
      switch (searchOptions.sortBy) {
        case FileSortBy.name:
          results.sort((a, b) => a.name.compareTo(b.name));
          break;
        case FileSortBy.size:
          results.sort((a, b) => a.size.compareTo(b.size));
          break;
        case FileSortBy.modifiedDate:
          results.sort((a, b) => a.modifiedAt.compareTo(b.modifiedAt));
          break;
        case FileSortBy.type:
          results.sort((a, b) => a.type.name.compareTo(b.type.name));
          break;
      }
      
      if (searchOptions.sortDescending) {
        results = results.reversed.toList();
      }
      
      return results;
      
    } catch (e, stackTrace) {
      _log('severe', '文件搜索失败: $moduleId', e, stackTrace);
      return [];
    }
  }

  /// 获取模块文件统计信息
  ModuleFileStats getModuleFileStats(String moduleId) {
    final moduleFiles = _moduleFiles[moduleId] ?? [];
    
    if (moduleFiles.isEmpty) {
      return ModuleFileStats.empty(moduleId);
    }
    
    final totalSize = moduleFiles.fold<int>(0, (sum, file) => sum + file.size);
    final fileTypeCount = <FileType, int>{};
    
    for (final file in moduleFiles) {
      fileTypeCount[file.type] = (fileTypeCount[file.type] ?? 0) + 1;
    }
    
    final lastModified = moduleFiles
        .map((file) => file.modifiedAt)
        .reduce((a, b) => a.isAfter(b) ? a : b);
    
    return ModuleFileStats(
      moduleId: moduleId,
      totalFiles: moduleFiles.length,
      totalSize: totalSize,
      fileTypeCount: fileTypeCount,
      lastModified: lastModified,
    );
  }

  /// 初始化模块文件系统
  Future<void> initializeModuleFileSystem(String moduleId) async {
    _log('info', '初始化模块文件系统: $moduleId');
    
    // 创建默认文件结构
    _moduleFiles[moduleId] = [
      ModuleFile(
        moduleId: moduleId,
        path: 'README.md',
        name: 'README.md',
        type: FileType.markdown,
        size: 0,
        content: '# $moduleId 模块\n\n模块说明文档',
        createdAt: DateTime.now(),
        modifiedAt: DateTime.now(),
      ),
    ];
    
    _eventController.add(FileSystemEvent.moduleInitialized(moduleId));
  }

  /// 清理模块文件系统
  Future<void> cleanupModuleFileSystem(String moduleId) async {
    _log('info', '清理模块文件系统: $moduleId');
    
    _moduleFiles.remove(moduleId);
    _eventController.add(FileSystemEvent.moduleCleanedUp(moduleId));
  }

  /// 提取文件名
  String _extractFileName(String path) {
    return path.split('/').last;
  }

  /// 日志记录
  void _log(String level, String message, [Object? error, StackTrace? stackTrace]) {
    developer.log(
      message,
      name: 'ModuleFileSystemService',
      level: _getLogLevel(level),
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// 获取日志级别
  int _getLogLevel(String level) {
    switch (level) {
      case 'info':
        return 800;
      case 'warning':
        return 900;
      case 'severe':
        return 1000;
      default:
        return 700;
    }
  }

  /// 清理资源
  void dispose() {
    _eventController.close();
  }
}
