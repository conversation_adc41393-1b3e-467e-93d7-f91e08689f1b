name: app_manager
version: 1.0.0
description: 应用管理核心模块
author: Pet App Team
type: service
platform: crossPlatform
framework: agnostic
complexity: complex
maturity: development
tags: []
created_at: 2025-07-18T18:39:05.352690

# 模板依赖
dependencies:
  []

# 模板配置
configuration:
  include_tests: true
  include_documentation: true
  include_examples: true
  enable_git_init: true

# 生成信息
generation:
  tool: Ming Status CLI
  tool_version: 1.0.0
  generated_at: 2025-07-18T18:39:05.352690
