import 'package:test/test.dart';
import 'package:creative_workshop/src/services/local_ming_cli_service.dart';

void main() {
  group('真实Ming CLI集成测试', () {
    late LocalMingCliService service;

    setUp(() {
      service = LocalMingCliService.instance;
    });

    test('应该能够初始化服务并检查CLI可用性', () async {
      await service.initialize();
      
      // 验证服务已初始化
      expect(service.isCliAvailable, isA<bool>());
      
      // 如果CLI可用，应该有版本信息
      if (service.isCliAvailable) {
        expect(service.cliVersion, isNotNull);
        expect(service.cliVersion, isNotEmpty);
      }
    });

    test('应该能够查找Ming CLI路径', () async {
      await service.initialize();
      
      // 通过反射或其他方式测试路径查找
      // 这里我们通过执行一个简单命令来验证
      final result = await service.executeCommand('--version');
      
      expect(result, isNotNull);
      expect(result.command, contains('--version'));
      expect(result.success, isA<bool>());
    });

    test('应该能够执行简单的CLI命令', () async {
      await service.initialize();
      
      final result = await service.executeCommand('--help');
      
      expect(result, isNotNull);
      expect(result.command, contains('--help'));
      expect(result.success, isA<bool>());
      expect(result.executionTime, isNotNull);
      expect(result.executionTime!.inMilliseconds, greaterThan(0));
    });

    test('应该能够处理无效命令', () async {
      await service.initialize();
      
      final result = await service.executeCommand('invalid_command_xyz');
      
      expect(result, isNotNull);
      expect(result.command, contains('invalid_command_xyz'));
      expect(result.success, isA<bool>());
      
      // 无效命令应该失败或返回错误信息
      if (!result.success) {
        expect(result.error, isNotNull);
      }
    });

    test('应该能够提取版本号', () async {
      await service.initialize();
      
      // 测试版本号提取功能
      final testOutputs = [
        'Ming Status CLI 1.0.0',
        'v2.1.3',
        'version 1.5.0-beta',
        'CLI Tool v3.0.0',
      ];
      
      for (final output in testOutputs) {
        // 这里我们无法直接测试私有方法，但可以通过执行--version命令来间接测试
        final result = await service.executeCommand('--version');
        if (result.success && result.output != null) {
          expect(service.cliVersion, isNotNull);
          expect(service.cliVersion, matches(RegExp(r'\d+\.\d+\.\d+')));
        }
      }
    });

    test('应该能够维护命令历史', () async {
      await service.initialize();
      
      // 执行几个命令
      await service.executeCommand('--version');
      await service.executeCommand('--help');
      await service.executeCommand('doctor');
      
      final history = service.commandHistory;
      expect(history, isNotEmpty);
      expect(history.length, lessThanOrEqualTo(3));
      
      // 最新的命令应该在前面
      expect(history.first.command, contains('doctor'));
    });

    test('应该能够检测插件生成命令', () async {
      await service.initialize();
      
      // 测试插件生成命令
      final result = await service.executeCommand('create my_test_plugin');
      
      expect(result, isNotNull);
      expect(result.command, contains('create my_test_plugin'));
      
      // 如果是插件生成命令且成功，应该有项目路径
      if (result.success && result.command.contains('create')) {
        // 可能有项目路径信息
        expect(result.projectPath, isA<String?>());
      }
    });

    test('应该能够处理工作目录参数', () async {
      await service.initialize();
      
      final result = await service.executeCommand(
        '--version',
        workingDirectory: '.',
      );
      
      expect(result, isNotNull);
      expect(result.success, isA<bool>());
    });

    test('应该能够处理环境变量', () async {
      await service.initialize();
      
      final result = await service.executeCommand(
        '--version',
        environment: {'TEST_VAR': 'test_value'},
      );
      
      expect(result, isNotNull);
      expect(result.success, isA<bool>());
    });

    test('命令历史应该限制在100条以内', () async {
      await service.initialize();
      
      // 执行超过100个命令（这里只执行几个来测试逻辑）
      for (int i = 0; i < 5; i++) {
        await service.executeCommand('--version');
      }
      
      final history = service.commandHistory;
      expect(history.length, lessThanOrEqualTo(100));
      expect(history.length, lessThanOrEqualTo(5));
    });

    test('应该能够处理CLI不可用的情况', () async {
      // 这个测试验证当CLI不可用时的行为
      // 在开发环境中，应该启用模拟模式
      await service.initialize();
      
      final result = await service.executeCommand('test_command');
      
      expect(result, isNotNull);
      expect(result.success, isA<bool>());
      
      // 无论CLI是否可用，都应该返回有效的结果
      expect(result.command, contains('test_command'));
    });

    test('应该能够正确处理执行时间', () async {
      await service.initialize();
      
      final result = await service.executeCommand('--version');
      
      expect(result, isNotNull);
      expect(result.executionTime, isNotNull);
      expect(result.executionTime!.inMilliseconds, greaterThan(0));
      expect(result.executionTime!.inSeconds, lessThan(30)); // 不应该超过30秒
    });
  });
}
