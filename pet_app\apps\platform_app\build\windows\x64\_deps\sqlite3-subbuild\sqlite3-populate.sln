﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "CMakePredefinedTargets", "CMakePredefinedTargets", "{88196DC7-9C7C-3BC4-B20F-3A54CDAC1510}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ExternalProjectTargets", "ExternalProjectTargets", "{AB5F946D-0FB5-3A89-8527-D1821C02F057}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "sqlite3-populate", "ExternalProjectTargets\sqlite3-populate", "{7F60D967-E0F7-331E-8ADB-F62067735F1B}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{CA54F365-135B-3A33-B5E7-D434CF8176B3}"
	ProjectSection(ProjectDependencies) = postProject
		{3E02E05B-4219-38CF-8F17-DB3971914130} = {3E02E05B-4219-38CF-8F17-DB3971914130}
		{20659925-E8C0-3246-8DE2-43D4A14CB525} = {20659925-E8C0-3246-8DE2-43D4A14CB525}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{3E02E05B-4219-38CF-8F17-DB3971914130}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "sqlite3-populate", "sqlite3-populate.vcxproj", "{20659925-E8C0-3246-8DE2-43D4A14CB525}"
	ProjectSection(ProjectDependencies) = postProject
		{3E02E05B-4219-38CF-8F17-DB3971914130} = {3E02E05B-4219-38CF-8F17-DB3971914130}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{CA54F365-135B-3A33-B5E7-D434CF8176B3}.Debug|x64.ActiveCfg = Debug|x64
		{3E02E05B-4219-38CF-8F17-DB3971914130}.Debug|x64.ActiveCfg = Debug|x64
		{3E02E05B-4219-38CF-8F17-DB3971914130}.Debug|x64.Build.0 = Debug|x64
		{20659925-E8C0-3246-8DE2-43D4A14CB525}.Debug|x64.ActiveCfg = Debug|x64
		{20659925-E8C0-3246-8DE2-43D4A14CB525}.Debug|x64.Build.0 = Debug|x64
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{CA54F365-135B-3A33-B5E7-D434CF8176B3} = {88196DC7-9C7C-3BC4-B20F-3A54CDAC1510}
		{3E02E05B-4219-38CF-8F17-DB3971914130} = {88196DC7-9C7C-3BC4-B20F-3A54CDAC1510}
		{7F60D967-E0F7-331E-8ADB-F62067735F1B} = {AB5F946D-0FB5-3A89-8527-D1821C02F057}
		{20659925-E8C0-3246-8DE2-43D4A14CB525} = {7F60D967-E0F7-331E-8ADB-F62067735F1B}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {AF55C972-1CE2-3A4B-A21F-DD35DBD333D1}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
