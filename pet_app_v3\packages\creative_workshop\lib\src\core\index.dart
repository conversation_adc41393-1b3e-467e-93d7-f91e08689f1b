/*
---------------------------------------------------------------
File name:          index.dart
Author:             Pet App Team
Date created:       2025-07-18
Last modified:      2025-07-18
Dart Version:       3.2+
Description:        核心功能模块导出文件
---------------------------------------------------------------
*/

// 工坊管理导出
export 'workshop_manager.dart';

// 插件管理导出（将逐步迁移到Plugin System模块）
export 'plugins/plugin_manager.dart';
export 'plugins/plugin_registry.dart';
// TODO(职责清晰化): 插件系统集成适配器应该由Plugin System模块提供
// export 'plugins/plugin_system_integration_adapter.dart';

// 核心功能导出
export 'providers/index.dart';
export 'router/index.dart';
export 'theme/index.dart';
