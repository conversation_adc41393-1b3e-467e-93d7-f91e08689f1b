import 'dart:io';
import 'package:test/test.dart';
import 'package:path/path.dart' as path;

void main() {
  group('最终真实集成验证测试', () {
    late String testPluginPath;

    setUpAll(() async {
      // 设置测试插件路径
      final currentDir = Directory.current.path;
      testPluginPath = path.join(currentDir, '..', '..', 'plugins', 'testing');
    });

    group('插件模板修正验证', () {
      test('插件模板应该包含Plugin System依赖', () {
        final templatePubspec = File(path.join(
          Directory.current.path,
          'tools',
          'ming_status_cli',
          'templates',
          'plugin',
          '__brick__',
          'pubspec.yaml',
        ));
        
        expect(templatePubspec.existsSync(), isTrue, reason: '插件模板pubspec.yaml应该存在');
        
        final content = templatePubspec.readAsStringSync();
        expect(content.contains('plugin_system:'), isTrue, 
               reason: '插件模板应该包含plugin_system依赖');
        expect(content.contains('path: ../../packages/plugin_system'), isTrue,
               reason: '插件模板应该正确引用plugin_system路径');
      });

      test('插件模板应该使用真实的Plugin System导入', () {
        final templateCore = File(path.join(
          Directory.current.path,
          'tools',
          'ming_status_cli',
          'templates',
          'plugin',
          '__brick__',
          'lib',
          'src',
          'plugin_core.dart',
        ));
        
        expect(templateCore.existsSync(), isTrue, reason: '插件模板核心文件应该存在');
        
        final content = templateCore.readAsStringSync();
        expect(content.contains('import \'package:plugin_system/plugin_system.dart\' as plugin_sys;'), 
               isTrue, reason: '插件模板应该导入真实的Plugin System');
        expect(content.contains('extends plugin_sys.Plugin'), 
               isTrue, reason: '插件模板应该继承真实的Plugin基类');
        
        // 验证没有模拟实现
        expect(content.contains('abstract class Plugin'), isFalse, 
               reason: '插件模板不应该包含模拟的Plugin基类定义');
      });
    });

    group('生成插件验证', () {
      test('生成的插件应该包含Plugin System依赖', () {
        final pubspecFile = File(path.join(testPluginPath, 'pubspec.yaml'));
        expect(pubspecFile.existsSync(), isTrue, reason: '生成的插件pubspec.yaml应该存在');

        final content = pubspecFile.readAsStringSync();
        expect(content.contains('plugin_system:'), isTrue, 
               reason: '生成的插件应该包含plugin_system依赖');
        expect(content.contains('path: ../../packages/plugin_system'), isTrue,
               reason: '生成的插件应该正确引用plugin_system路径');
      });

      test('生成的插件应该能够获取依赖', () async {
        // 验证dart pub get是否成功
        final result = await Process.run(
          'dart',
          ['pub', 'get'],
          workingDirectory: testPluginPath,
        );

        expect(result.exitCode, equals(0), 
               reason: '生成的插件应该能够成功获取依赖');
      });

      test('生成的插件应该能够通过静态分析', () async {
        // 运行dart analyze，只检查错误，忽略警告
        final result = await Process.run(
          'dart',
          ['analyze', '--no-fatal-warnings'],
          workingDirectory: testPluginPath,
        );

        // 检查是否有编译错误（不是风格警告）
        final output = result.stderr.toString() + result.stdout.toString();
        
        // 不应该有error级别的问题
        expect(output.contains('error •'), isFalse, 
               reason: '生成的插件不应该有编译错误');
      });

      test('生成的插件应该包含正确的Plugin System集成', () {
        final coreFile = File(path.join(testPluginPath, 'lib', 'src', 'plugin_core.dart'));
        expect(coreFile.existsSync(), isTrue, reason: '插件核心文件应该存在');

        final content = coreFile.readAsStringSync();
        
        // 验证正确的导入语句
        expect(content.contains('import \'package:plugin_system/plugin_system.dart\' as plugin_sys;'), 
               isTrue, reason: '应该正确导入Plugin System');
        
        // 验证正确的基类继承
        expect(content.contains('extends plugin_sys.Plugin'), 
               isTrue, reason: '应该继承Plugin System基类');
        
        // 验证使用Plugin System类型
        expect(content.contains('plugin_sys.PluginState'), 
               isTrue, reason: '应该使用Plugin System状态类型');
      });
    });

    group('Creative Workshop集成验证', () {
      test('WorkshopManager应该能够扫描插件目录', () async {
        // 这个测试验证WorkshopManager是否能够发现生成的插件
        // 由于WorkshopManager是单例，我们需要小心测试
        
        // 验证插件目录存在
        final pluginsDir = Directory(path.join(Directory.current.path, '..', '..', 'plugins'));
        expect(pluginsDir.existsSync(), isTrue, reason: '插件目录应该存在');
        
        // 验证测试插件存在
        final testPlugin = Directory(path.join(pluginsDir.path, 'testing'));
        expect(testPlugin.existsSync(), isTrue, reason: '测试插件目录应该存在');
        
        // 验证插件清单存在
        final manifestFile = File(path.join(testPlugin.path, 'plugin.yaml'));
        expect(manifestFile.existsSync(), isTrue, reason: '插件清单应该存在');
      });

      test('插件清单应该包含Pet App V3兼容性配置', () {
        final manifestFile = File(path.join(testPluginPath, 'plugin.yaml'));
        expect(manifestFile.existsSync(), isTrue, reason: '插件清单应该存在');

        final content = manifestFile.readAsStringSync();
        
        // 验证基本信息
        expect(content.contains('id: test_plugin_fixed'), isTrue);
        expect(content.contains('name: Test Plugin Fixed'), isTrue);
        expect(content.contains('version: 1.0.0'), isTrue);
        
        // 验证Pet App V3兼容性
        expect(content.contains('min_pet_app_version: "3.0.0"'), isTrue);
        expect(content.contains('plugin_system_version: "^1.0.0"'), isTrue);
        
        // 验证多平台支持
        final platforms = ['android', 'ios', 'web', 'windows', 'macos', 'linux'];
        for (final platform in platforms) {
          expect(content.contains('- $platform'), isTrue, 
                 reason: '应该支持平台: $platform');
        }
        
        // 验证生命周期钩子
        expect(content.contains('lifecycle:'), isTrue);
        expect(content.contains('on_initialize: "initialize"'), isTrue);
        expect(content.contains('on_start: "start"'), isTrue);
        expect(content.contains('on_stop: "stop"'), isTrue);
        expect(content.contains('on_dispose: "dispose"'), isTrue);
      });
    });

    group('端到端集成验证', () {
      test('完整流程：模板修正 → 插件生成 → 依赖解析 → 编译验证', () async {
        // 1. 验证模板已修正
        final templatePubspec = File(path.join(
          Directory.current.path,
          'tools',
          'ming_status_cli',
          'templates',
          'plugin',
          '__brick__',
          'pubspec.yaml',
        ));
        final templateContent = templatePubspec.readAsStringSync();
        expect(templateContent.contains('plugin_system:'), isTrue, 
               reason: '步骤1失败：模板未包含Plugin System依赖');

        // 2. 验证插件已生成
        final generatedPubspec = File(path.join(testPluginPath, 'pubspec.yaml'));
        expect(generatedPubspec.existsSync(), isTrue, 
               reason: '步骤2失败：插件未生成');

        // 3. 验证依赖解析成功
        final pubGetResult = await Process.run(
          'dart',
          ['pub', 'get'],
          workingDirectory: testPluginPath,
        );
        expect(pubGetResult.exitCode, equals(0), 
               reason: '步骤3失败：依赖解析失败');

        // 4. 验证编译成功
        final analyzeResult = await Process.run(
          'dart',
          ['analyze', '--no-fatal-warnings'],
          workingDirectory: testPluginPath,
        );
        final analyzeOutput = analyzeResult.stderr.toString() + analyzeResult.stdout.toString();
        expect(analyzeOutput.contains('error •'), isFalse, 
               reason: '步骤4失败：编译有错误');

        // 5. 验证Plugin System集成
        final coreFile = File(path.join(testPluginPath, 'lib', 'src', 'plugin_core.dart'));
        final coreContent = coreFile.readAsStringSync();
        expect(coreContent.contains('extends plugin_sys.Plugin'), isTrue, 
               reason: '步骤5失败：Plugin System集成不正确');
      });

      test('验证Creative Workshop能够识别生成的插件', () {
        // 验证Creative Workshop的插件发现机制能够找到生成的插件
        
        // 1. 验证插件目录结构
        final pluginDir = Directory(testPluginPath);
        expect(pluginDir.existsSync(), isTrue, reason: '插件目录应该存在');
        
        // 2. 验证必需文件存在
        final requiredFiles = [
          'pubspec.yaml',
          'plugin.yaml',
          'lib/test_plugin_fixed.dart',
          'lib/src/plugin_core.dart',
        ];
        
        for (final filePath in requiredFiles) {
          final file = File(path.join(testPluginPath, filePath));
          expect(file.existsSync(), isTrue, 
                 reason: '必需文件应该存在: $filePath');
        }
        
        // 3. 验证插件清单格式正确
        final manifestFile = File(path.join(testPluginPath, 'plugin.yaml'));
        final manifestContent = manifestFile.readAsStringSync();
        
        // 基本验证：确保是有效的YAML且包含必需字段
        expect(manifestContent.contains('plugin:'), isTrue);
        expect(manifestContent.contains('id:'), isTrue);
        expect(manifestContent.contains('name:'), isTrue);
        expect(manifestContent.contains('version:'), isTrue);
      });
    });

    group('质量保证验证', () {
      test('验证没有模拟实现残留', () {
        final coreFile = File(path.join(testPluginPath, 'lib', 'src', 'plugin_core.dart'));
        final content = coreFile.readAsStringSync();
        
        // 验证没有模拟实现的痕迹
        final mockIndicators = [
          'abstract class Plugin',
          '// TODO: 实现',
          'throw UnimplementedError',
          '// 模拟实现',
          '// Mock implementation',
        ];
        
        for (final indicator in mockIndicators) {
          expect(content.contains(indicator), isFalse, 
                 reason: '不应该包含模拟实现标记: $indicator');
        }
      });

      test('验证使用真实的Plugin System组件', () {
        final coreFile = File(path.join(testPluginPath, 'lib', 'src', 'plugin_core.dart'));
        final content = coreFile.readAsStringSync();
        
        // 验证使用真实的Plugin System组件
        final realComponents = [
          'package:plugin_system/plugin_system.dart',
          'plugin_sys.Plugin',
          'plugin_sys.PluginState',
          'plugin_sys.PluginType',
        ];
        
        for (final component in realComponents) {
          expect(content.contains(component), isTrue, 
                 reason: '应该使用真实的Plugin System组件: $component');
        }
      });

      test('验证代码质量符合标准', () async {
        // 运行完整的静态分析
        final result = await Process.run(
          'dart',
          ['analyze'],
          workingDirectory: testPluginPath,
        );

        final output = result.stderr.toString() + result.stdout.toString();
        
        // 不应该有严重的代码质量问题
        expect(output.contains('error •'), isFalse, 
               reason: '不应该有错误级别的问题');
        
        // 可以有一些警告，但不应该有太多
        final warningCount = 'warning •'.allMatches(output).length;
        expect(warningCount, lessThan(10), 
               reason: '警告数量应该控制在合理范围内');
      });
    });
  });
}
