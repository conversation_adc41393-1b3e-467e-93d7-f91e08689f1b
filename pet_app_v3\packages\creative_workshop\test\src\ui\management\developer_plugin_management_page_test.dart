/*
---------------------------------------------------------------
File name:          developer_plugin_management_page_test.dart
Author:             lgnorant-lu
Date created:       2025-07-31
Last modified:      2025-07-31
Dart Version:       3.2+
Description:        开发者插件管理页面测试
---------------------------------------------------------------
Change History:
    2025-07-31: 创建测试文件，验证Plugin System集成;
---------------------------------------------------------------
*/

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:creative_workshop/src/ui/management/developer_plugin_management_page.dart';

void main() {
  group('DeveloperPluginManagementPage 测试', () {
    testWidgets('页面能正常渲染', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: DeveloperPluginManagementPage(),
        ),
      );
      await tester.pumpAndSettle();

      // 验证页面标题
      expect(find.text('开发者插件管理'), findsOneWidget);
      
      // 验证刷新按钮
      expect(find.byIcon(Icons.refresh), findsOneWidget);
      
      // 验证统计面板
      expect(find.text('已注册'), findsOneWidget);
      expect(find.text('已加载'), findsOneWidget);
      expect(find.text('运行中'), findsOneWidget);
    });

    testWidgets('统计面板显示正确的图标', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: DeveloperPluginManagementPage(),
        ),
      );
      await tester.pumpAndSettle();

      // 验证统计图标
      expect(find.byIcon(Icons.app_registration), findsOneWidget);
      expect(find.byIcon(Icons.check_circle), findsOneWidget);
      expect(find.byIcon(Icons.play_circle), findsOneWidget);
    });

    testWidgets('空状态显示正确', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: DeveloperPluginManagementPage(),
        ),
      );
      await tester.pumpAndSettle();

      // 验证空状态消息
      expect(find.text('暂无已注册的插件'), findsOneWidget);
      expect(find.text('插件将在开发时自动注册'), findsOneWidget);
      expect(find.byIcon(Icons.extension_off), findsOneWidget);
    });

    testWidgets('刷新按钮可以点击', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: DeveloperPluginManagementPage(),
        ),
      );
      await tester.pumpAndSettle();

      // 点击刷新按钮
      await tester.tap(find.byIcon(Icons.refresh));
      await tester.pumpAndSettle();

      // 验证页面仍然正常显示
      expect(find.text('开发者插件管理'), findsOneWidget);
    });

    testWidgets('页面布局结构正确', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: DeveloperPluginManagementPage(),
        ),
      );
      await tester.pumpAndSettle();

      // 验证页面结构
      expect(find.byType(Scaffold), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.byType(Column), findsWidgets);
      
      // 验证统计面板容器
      expect(find.byType(Container), findsWidgets);
      expect(find.byType(Row), findsWidgets);
    });

    group('职责边界测试', () {
      testWidgets('专注于开发者视角的插件管理', (WidgetTester tester) async {
        await tester.pumpWidget(
          const MaterialApp(
            home: DeveloperPluginManagementPage(),
          ),
        );
        await tester.pumpAndSettle();

        // 验证这是开发者专用的插件管理页面
        expect(find.text('开发者插件管理'), findsOneWidget);
        
        // 不应该包含用户插件管理的功能
        expect(find.text('应用商店'), findsNothing);
        expect(find.text('安装插件'), findsNothing);
        expect(find.text('购买'), findsNothing);
      });

      testWidgets('调用Plugin System接口而非重复实现', (WidgetTester tester) async {
        await tester.pumpWidget(
          const MaterialApp(
            home: DeveloperPluginManagementPage(),
          ),
        );
        await tester.pumpAndSettle();

        // 验证页面能正常工作，说明正确调用了Plugin System接口
        expect(find.byType(DeveloperPluginManagementPage), findsOneWidget);
        
        // 页面应该能正常渲染而不出错
        expect(tester.takeException(), isNull);
      });
    });

    group('UI组件测试', () {
      testWidgets('统计面板样式正确', (WidgetTester tester) async {
        await tester.pumpWidget(
          const MaterialApp(
            home: DeveloperPluginManagementPage(),
          ),
        );
        await tester.pumpAndSettle();

        // 验证统计面板的样式
        final containerFinder = find.byType(Container).first;
        final container = tester.widget<Container>(containerFinder);
        
        expect(container.margin, const EdgeInsets.all(16));
        expect(container.padding, const EdgeInsets.all(16));
        expect(container.decoration, isA<BoxDecoration>());
      });

      testWidgets('响应式布局正确', (WidgetTester tester) async {
        await tester.pumpWidget(
          const MaterialApp(
            home: DeveloperPluginManagementPage(),
          ),
        );
        await tester.pumpAndSettle();

        // 验证布局结构
        expect(find.byType(Column), findsWidgets);
        expect(find.byType(Expanded), findsOneWidget);
        
        // 验证统计面板使用Row布局
        expect(find.byType(Row), findsWidgets);
      });
    });

    group('错误处理测试', () {
      testWidgets('页面初始化不会抛出异常', (WidgetTester tester) async {
        expect(() async {
          await tester.pumpWidget(
            const MaterialApp(
              home: DeveloperPluginManagementPage(),
            ),
          );
          await tester.pumpAndSettle();
        }, returnsNormally);
      });

      testWidgets('刷新操作不会抛出异常', (WidgetTester tester) async {
        await tester.pumpWidget(
          const MaterialApp(
            home: DeveloperPluginManagementPage(),
          ),
        );
        await tester.pumpAndSettle();

        expect(() async {
          await tester.tap(find.byIcon(Icons.refresh));
          await tester.pumpAndSettle();
        }, returnsNormally);
      });
    });
  });
}
