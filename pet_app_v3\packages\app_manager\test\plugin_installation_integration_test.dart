import 'package:test/test.dart';
import 'package:app_manager/src/services/plugin_installation_service.dart';
import 'package:app_manager/src/services/app_store_service.dart';

void main() {
  group('插件安装真实集成测试', () {
    late PluginInstallationService installationService;
    late AppStoreService appStoreService;

    setUp(() {
      installationService = PluginInstallationService.instance;
      appStoreService = AppStoreService.instance;
    });

    test('PluginInstallationService应该能够初始化', () async {
      // 测试服务初始化
      await installationService.initialize();
      
      // 验证初始化成功
      expect(installationService, isNotNull);
    });

    test('AppStoreService应该能够初始化', () async {
      // 测试服务初始化
      await appStoreService.initialize();
      
      // 验证初始化成功
      expect(appStoreService, isNotNull);
    });

    test('PluginInstallationService应该能够处理安装请求', () async {
      // 初始化服务
      await installationService.initialize();
      
      // 尝试安装一个测试插件
      final result = await installationService.installPlugin(
        pluginId: 'test_plugin_for_integration',
        version: '1.0.0',
      );
      
      // 验证结果结构正确（可能成功或失败，但结构应该正确）
      expect(result, isNotNull);
      expect(result.pluginId, equals('test_plugin_for_integration'));
      expect(result.success, isA<bool>());
    });

    test('AppStoreService应该能够搜索插件', () async {
      // 初始化服务
      await appStoreService.initialize();
      
      // 搜索插件
      final result = await appStoreService.searchItems(
        type: AppStoreItemType.plugin,
        query: 'test',
        page: 1,
        pageSize: 10,
      );
      
      // 验证搜索结果结构正确
      expect(result, isNotNull);
      expect(result.items, isA<List<AppStoreItem>>());
      expect(result.totalCount, isA<int>());
      expect(result.page, equals(1));
      expect(result.pageSize, equals(10));
    });

    test('AppStoreService应该能够获取热门项目', () async {
      // 初始化服务
      await appStoreService.initialize();
      
      // 获取热门插件
      final result = await appStoreService.getPopularItems(
        type: AppStoreItemType.plugin,
        limit: 5,
      );
      
      // 验证结果结构正确
      expect(result, isA<List<AppStoreItem>>());
    });

    test('PluginInstallationService应该能够处理卸载请求', () async {
      // 初始化服务
      await installationService.initialize();
      
      // 尝试卸载一个不存在的插件
      final result = await installationService.uninstallPlugin('non_existent_plugin');
      
      // 验证结果结构正确
      expect(result, isNotNull);
      expect(result.pluginId, equals('non_existent_plugin'));
      expect(result.success, isFalse); // 应该失败，因为插件不存在
      expect(result.error, equals(PluginInstallationError.pluginNotFound));
    });

    test('AppStoreService安装集成应该正确调用PluginInstallationService', () async {
      // 初始化服务
      await appStoreService.initialize();
      
      // 创建测试应用商店项目
      const testItem = AppStoreItem(
        id: 'test_integration_plugin',
        name: '测试集成插件',
        description: '用于测试集成的插件',
        type: AppStoreItemType.plugin,
        version: '1.0.0',
        author: '测试作者',
      );
      
      // 尝试安装
      final result = await appStoreService.installItem(testItem);
      
      // 验证结果结构正确
      expect(result, isNotNull);
      expect(result.pluginId, equals('test_integration_plugin'));
      expect(result.success, isA<bool>());
    });

    test('PluginInstallationService应该能够提供安装进度', () async {
      // 初始化服务
      await installationService.initialize();
      
      // 获取不存在插件的进度流（应该返回null）
      final progressStream = installationService.getInstallationProgress('non_existent');
      
      // 验证返回null（因为没有正在进行的安装）
      expect(progressStream, isNull);
    });
  });
}
