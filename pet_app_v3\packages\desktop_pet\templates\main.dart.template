/*
---------------------------------------------------------------
File name:          main.dart
Author:             Pet App V3 Team
Date created:       2025-07-21
Last modified:      2025-07-21
Dart Version:       3.2+
Description:        desktop_pet应用程序主入口文件
---------------------------------------------------------------
Change History:
    2025-07-21: Initial creation - desktop_pet应用程序主入口文件;
---------------------------------------------------------------
*/


import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'src/app.dart';
import 'src/core/providers/app_providers.dart';
import 'src/core/services/app_initializer.dart';
import 'src/core/utils/error_handler.dart';

/// 应用程序主入口函数
///
/// 初始化应用程序并启动主界面
Future<void> main() async {
  // 确保Flutter绑定初始化
  WidgetsFlutterBinding.ensureInitialized();

  // 设置全局错误处理
  FlutterError.onError = ErrorHandler.handleFlutterError;
  PlatformDispatcher.instance.onError = ErrorHandler.handlePlatformError;

  // 运行在错误保护区域中
  await runZonedGuarded<Future<void>>(
    () async {
      // 设置系统UI样式
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);

      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
        ),
      );

      // 初始化企业级服务
      await AppInitializer.initializeEnterprise();
    },
    ErrorHandler.handleZoneError,
  );

  // 启动应用程序
  runApp(
    const ProviderScope(
      child: DesktopPetApp(),
    ),
  );
}

/// 应用程序错误处理
///
/// 处理应用程序运行时错误
void _handleError(Object error, StackTrace stackTrace) {
  // 记录错误
  debugPrint('应用程序错误: $error');
  debugPrint('堆栈跟踪: $stackTrace');

  // 发送错误报告到崩溃分析服务
  // FirebaseCrashlytics.instance.recordError(error, stackTrace);
}
