/*
---------------------------------------------------------------
File name:          core_services_v1_service_test.dart
Author:             Ming Status CLI
Date created:       2025-07-14
Last modified:      2025-07-14
Dart Version:       3.2+
Description:        core_services_v1 Service 单元测试
---------------------------------------------------------------
Change History:
    2025-07-14: Initial creation - core_services_v1 Service 单元测试;
---------------------------------------------------------------
*/

import 'package:test/test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:core_services_v1/lib/src/services/core_services_v1_service.dart';

void main() {
  group('CoreServicesV1Service 单元测试', () {
    late CoreServicesV1Service service;

    setUp(() {
      service = CoreServicesV1Service();
    });

    tearDown(() async {
      await service.dispose();
    });

    test('初始化应该成功', () async {
      await service.initialize();
      expect(service.isInitialized, true);
    });

    test('获取数据应该返回列表', () async {
      await service.initialize();
      final data = await service.fetchData();
      expect(data, isA<List<Map<String, dynamic>>>());
      expect(data, isNotEmpty);
    });

    test('根据ID获取数据应该返回正确项目', () async {
      await service.initialize();
      final item = await service.fetchById(1);
      expect(item, isNotNull);
      expect(item!['id'], 1);
    });

    test('创建数据应该成功', () async {
      await service.initialize();
      final newData = {'name': '测试项目', 'description': '测试描述'};
      final created = await service.createData(newData);
      expect(created, isNotNull);
      expect(created['name'], '测试项目');
    });

  });
}
