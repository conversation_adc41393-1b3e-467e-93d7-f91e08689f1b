import 'package:test/test.dart';
import 'package:creative_workshop/creative_workshop.dart';

void main() {
  group('WorkshopManager清理后测试', () {
    late WorkshopManager workshopManager;

    setUp(() {
      workshopManager = WorkshopManager.instance;
    });

    tearDown(() async {
      // 清理测试状态
      try {
        await workshopManager.dispose();
      } catch (e) {
        // 忽略清理错误
      }
    });

    group('基本功能测试', () {
      test('应该能够获取WorkshopManager实例', () {
        expect(workshopManager, isNotNull);
        expect(workshopManager, equals(WorkshopManager.instance));
      });

      test('应该有正确的初始状态', () {
        expect(workshopManager.state, equals(WorkshopState.uninitialized));
      });

      test('应该能够访问项目管理器', () {
        final projectManager = workshopManager.projectManager;
        expect(projectManager, isNotNull);
      });

      test('应该能够获取项目数量', () {
        final projectCount = workshopManager.projectCount;
        expect(projectCount, isA<int>());
        expect(projectCount, greaterThanOrEqualTo(0));
      });
    });

    group('生命周期管理测试', () {
      test('应该能够完成初始化', () async {
        final result = await workshopManager.initialize();
        expect(result, isTrue);
        expect(workshopManager.state, equals(WorkshopState.ready));
      });

      test('应该能够处理重复初始化', () async {
        // 第一次初始化
        final firstResult = await workshopManager.initialize();
        expect(firstResult, isTrue);

        // 第二次初始化应该跳过
        final secondResult = await workshopManager.initialize();
        expect(secondResult, isTrue);
        expect(workshopManager.state, equals(WorkshopState.ready));
      });

      test('应该能够启动工坊', () async {
        // 先初始化
        await workshopManager.initialize();
        
        // 然后启动
        final result = await workshopManager.start();
        expect(result, isTrue);
        expect(workshopManager.state, equals(WorkshopState.running));
      });

      test('应该能够暂停和恢复工坊', () async {
        // 初始化并启动
        await workshopManager.initialize();
        await workshopManager.start();
        
        // 暂停
        final pauseResult = await workshopManager.pause();
        expect(pauseResult, isTrue);
        expect(workshopManager.state, equals(WorkshopState.paused));
        
        // 恢复
        final resumeResult = await workshopManager.resume();
        expect(resumeResult, isTrue);
        expect(workshopManager.state, equals(WorkshopState.running));
      });

      test('应该能够停止工坊', () async {
        // 初始化并启动
        await workshopManager.initialize();
        await workshopManager.start();
        
        // 停止
        final result = await workshopManager.stop();
        expect(result, isTrue);
        expect(workshopManager.state, equals(WorkshopState.stopped));
      });
    });

    group('状态管理测试', () {
      test('应该能够监听状态变化', () async {
        final stateChanges = <WorkshopState>[];
        final subscription = workshopManager.stateChanges.listen((state) {
          stateChanges.add(state);
        });

        // 触发状态变化
        await workshopManager.initialize();
        await workshopManager.start();

        // 等待状态变化传播
        await Future<void>.delayed(const Duration(milliseconds: 100));

        subscription.cancel();

        // 验证状态变化序列
        expect(stateChanges.length, greaterThanOrEqualTo(2));
        expect(stateChanges.contains(WorkshopState.initializing), isTrue);
        expect(stateChanges.contains(WorkshopState.ready), isTrue);
        expect(stateChanges.contains(WorkshopState.running), isTrue);
      });

      test('应该能够处理无效的状态转换', () async {
        // 尝试在未初始化时启动
        final result = await workshopManager.start();
        expect(result, isFalse);
        expect(workshopManager.state, equals(WorkshopState.uninitialized));
      });
    });

    group('错误处理测试', () {
      test('应该能够处理初始化错误', () async {
        // 这个测试验证错误处理机制
        // 由于我们的实现比较简单，这里主要验证异常不会导致系统崩溃
        
        expect(() async => await workshopManager.initialize(), returnsNormally);
      });

      test('应该能够处理状态转换错误', () async {
        // 验证无效状态转换的处理
        final pauseResult = await workshopManager.pause();
        expect(pauseResult, isFalse);
        
        final resumeResult = await workshopManager.resume();
        expect(resumeResult, isFalse);
      });
    });

    group('职责边界验证', () {
      test('不应该包含插件生命周期管理功能', () {
        // 验证WorkshopManager不再包含插件相关方法
        expect(() => workshopManager.runtimeType.toString().contains('Plugin'), isFalse);
      });

      test('应该专注于开发工作台功能', () {
        // 验证包含项目管理功能
        expect(workshopManager.projectManager, isNotNull);
        expect(workshopManager.projectCount, isA<int>());
      });

      test('应该有清晰的状态管理', () {
        // 验证状态管理功能
        expect(workshopManager.state, isA<WorkshopState>());
        expect(workshopManager.stateChanges, isA<Stream<WorkshopState>>());
      });
    });

    group('完整流程测试', () {
      test('验证完整的工坊生命周期', () async {
        // 1. 初始状态
        expect(workshopManager.state, equals(WorkshopState.uninitialized));
        
        // 2. 初始化
        final initResult = await workshopManager.initialize();
        expect(initResult, isTrue);
        expect(workshopManager.state, equals(WorkshopState.ready));
        
        // 3. 启动
        final startResult = await workshopManager.start();
        expect(startResult, isTrue);
        expect(workshopManager.state, equals(WorkshopState.running));
        
        // 4. 暂停
        final pauseResult = await workshopManager.pause();
        expect(pauseResult, isTrue);
        expect(workshopManager.state, equals(WorkshopState.paused));
        
        // 5. 恢复
        final resumeResult = await workshopManager.resume();
        expect(resumeResult, isTrue);
        expect(workshopManager.state, equals(WorkshopState.running));
        
        // 6. 停止
        final stopResult = await workshopManager.stop();
        expect(stopResult, isTrue);
        expect(workshopManager.state, equals(WorkshopState.stopped));
      });

      test('验证项目管理集成', () async {
        await workshopManager.initialize();
        
        // 验证项目管理器可用
        final projectManager = workshopManager.projectManager;
        expect(projectManager, isNotNull);
        
        // 验证项目数量统计
        final projectCount = workshopManager.projectCount;
        expect(projectCount, isA<int>());
      });
    });
  });
}
