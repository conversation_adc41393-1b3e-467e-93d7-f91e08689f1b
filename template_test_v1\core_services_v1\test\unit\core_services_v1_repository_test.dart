/*
---------------------------------------------------------------
File name:          core_services_v1_repository_test.dart
Author:             Ming Status CLI
Date created:       2025-07-14
Last modified:      2025-07-14
Dart Version:       3.2+
Description:        core_services_v1 Repository 单元测试
---------------------------------------------------------------
Change History:
    2025-07-14: Initial creation - core_services_v1 Repository 单元测试;
---------------------------------------------------------------
*/

import 'package:test/test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:core_services_v1/lib/src/repositories/core_services_v1_repository.dart';

void main() {
  group('CoreServicesV1Repository 单元测试', () {
    late CoreServicesV1Repository repository;

    setUp(() {
      repository = CoreServicesV1Repository();
    });

    tearDown(() async {
      await repository.dispose();
    });

    test('初始化应该成功', () async {
      await repository.initialize();
      expect(repository.isInitialized, true);
    });

    test('创建记录应该成功', () async {
      await repository.initialize();
      final data = {'name': '测试记录', 'description': '测试描述'};
      final created = await repository.create(data);
      
      expect(created, isNotNull);
      expect(created['name'], '测试记录');
      expect(created['id'], isNotNull);
    });

    test('查找记录应该成功', () async {
      await repository.initialize();
      final found = await repository.findById(1);
      expect(found, isNotNull);
      expect(found!['id'], 1);
    });

    test('查找所有记录应该返回列表', () async {
      await repository.initialize();
      final all = await repository.findAll();
      expect(all, isA<List<Map<String, dynamic>>>());
    });

  });
}
