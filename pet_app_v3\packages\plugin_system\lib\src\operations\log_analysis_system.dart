/*
---------------------------------------------------------------
File name:          log_analysis_system.dart
Author:             lgnorant-lu
Date created:       2025-07-26
Last modified:      2025-07-26
Dart Version:       3.2+
Description:        日志分析系统 - Phase 5.3 运维工具
---------------------------------------------------------------
Change History:
    2025-07-26: Phase 5.3 - 日志分析系统实现;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:collection';

import 'dart:math' as math;

/// 日志级别
enum LogLevel {
  /// 调试
  debug,

  /// 信息
  info,

  /// 警告
  warning,

  /// 错误
  error,

  /// 严重错误
  fatal,
}

/// 日志条目
class LogEntry {
  const LogEntry({
    required this.timestamp,
    required this.level,
    required this.message,
    required this.source,
    this.category,
    this.userId,
    this.sessionId,
    this.traceId,
    this.spanId,
    this.tags = const <String, String>{},
    this.fields = const <String, dynamic>{},
    this.stackTrace,
  });

  /// 时间戳
  final DateTime timestamp;

  /// 日志级别
  final LogLevel level;

  /// 消息
  final String message;

  /// 来源
  final String source;

  /// 类别
  final String? category;

  /// 用户ID
  final String? userId;

  /// 会话ID
  final String? sessionId;

  /// 跟踪ID
  final String? traceId;

  /// 跨度ID
  final String? spanId;

  /// 标签
  final Map<String, String> tags;

  /// 字段
  final Map<String, dynamic> fields;

  /// 堆栈跟踪
  final String? stackTrace;

  /// 转换为JSON
  Map<String, dynamic> toJson() => <String, dynamic>{
        'timestamp': timestamp.toIso8601String(),
        'level': level.name,
        'message': message,
        'source': source,
        'category': category,
        'userId': userId,
        'sessionId': sessionId,
        'traceId': traceId,
        'spanId': spanId,
        'tags': tags,
        'fields': fields,
        'stackTrace': stackTrace,
      };

  /// 从JSON创建
  static LogEntry fromJson(Map<String, dynamic> json) => LogEntry(
        timestamp: DateTime.parse(json['timestamp'] as String),
        level:
            LogLevel.values.firstWhere((LogLevel l) => l.name == json['level']),
        message: json['message'] as String,
        source: json['source'] as String,
        category: json['category'] as String?,
        userId: json['userId'] as String?,
        sessionId: json['sessionId'] as String?,
        traceId: json['traceId'] as String?,
        spanId: json['spanId'] as String?,
        tags: Map<String, String>.from(
            json['tags'] as Map? ?? <dynamic, dynamic>{}),
        fields: Map<String, dynamic>.from(
            json['fields'] as Map? ?? <dynamic, dynamic>{}),
        stackTrace: json['stackTrace'] as String?,
      );
}

/// 日志查询条件
class LogQuery {
  const LogQuery({
    this.startTime,
    this.endTime,
    this.levels = const <LogLevel>[],
    this.sources = const <String>[],
    this.categories = const <String>[],
    this.searchText,
    this.tags = const <String, String>{},
    this.fields = const <String, dynamic>{},
    this.limit = 1000,
    this.offset = 0,
    this.sortBy = LogSortField.timestamp,
    this.sortOrder = SortOrder.descending,
  });

  /// 开始时间
  final DateTime? startTime;

  /// 结束时间
  final DateTime? endTime;

  /// 日志级别
  final List<LogLevel> levels;

  /// 来源
  final List<String> sources;

  /// 类别
  final List<String> categories;

  /// 搜索文本
  final String? searchText;

  /// 标签过滤
  final Map<String, String> tags;

  /// 字段过滤
  final Map<String, dynamic> fields;

  /// 限制数量
  final int limit;

  /// 偏移量
  final int offset;

  /// 排序字段
  final LogSortField sortBy;

  /// 排序顺序
  final SortOrder sortOrder;
}

/// 日志排序字段
enum LogSortField {
  /// 时间戳
  timestamp,

  /// 级别
  level,

  /// 来源
  source,

  /// 类别
  category,
}

/// 排序顺序
enum SortOrder {
  /// 升序
  ascending,

  /// 降序
  descending,
}

/// 日志查询结果
class LogQueryResult {
  const LogQueryResult({
    required this.entries,
    required this.totalCount,
    required this.query,
    required this.executionTime,
  });

  /// 日志条目
  final List<LogEntry> entries;

  /// 总数
  final int totalCount;

  /// 查询条件
  final LogQuery query;

  /// 执行时间（毫秒）
  final int executionTime;
}

/// 日志统计
class LogStatistics {
  const LogStatistics({
    required this.totalCount,
    required this.countByLevel,
    required this.countBySource,
    required this.countByCategory,
    required this.timeRange,
    required this.errorRate,
    required this.topErrors,
  });

  /// 总数
  final int totalCount;

  /// 按级别统计
  final Map<LogLevel, int> countByLevel;

  /// 按来源统计
  final Map<String, int> countBySource;

  /// 按类别统计
  final Map<String, int> countByCategory;

  /// 时间范围
  final Duration timeRange;

  /// 错误率
  final double errorRate;

  /// 热门错误
  final List<LogPattern> topErrors;
}

/// 日志模式
class LogPattern {
  const LogPattern({
    required this.pattern,
    required this.count,
    required this.firstSeen,
    required this.lastSeen,
    required this.examples,
  });

  /// 模式
  final String pattern;

  /// 出现次数
  final int count;

  /// 首次出现
  final DateTime firstSeen;

  /// 最后出现
  final DateTime lastSeen;

  /// 示例
  final List<LogEntry> examples;
}

/// 日志告警规则
class LogAlertRule {
  const LogAlertRule({
    required this.id,
    required this.name,
    required this.query,
    required this.threshold,
    required this.timeWindow,
    this.enabled = true,
    this.description,
  });

  /// 规则ID
  final String id;

  /// 规则名称
  final String name;

  /// 查询条件
  final LogQuery query;

  /// 阈值
  final int threshold;

  /// 时间窗口
  final Duration timeWindow;

  /// 是否启用
  final bool enabled;

  /// 描述
  final String? description;
}

/// 日志分析系统
class LogAnalysisSystem {
  LogAnalysisSystem._();
  static final LogAnalysisSystem _instance = LogAnalysisSystem._();
  static LogAnalysisSystem get instance => _instance;

  /// 日志存储
  final Queue<LogEntry> _logEntries = Queue<LogEntry>();

  /// 索引
  final Map<String, Set<int>> _sourceIndex = <String, Set<int>>{};
  final Map<LogLevel, Set<int>> _levelIndex = <LogLevel, Set<int>>{};
  final Map<String, Set<int>> _categoryIndex = <String, Set<int>>{};
  final Map<String, Set<int>> _textIndex = <String, Set<int>>{};

  /// 日志流控制器
  final StreamController<LogEntry> _logController =
      StreamController<LogEntry>.broadcast();

  /// 告警规则
  final Map<String, LogAlertRule> _alertRules = <String, LogAlertRule>{};

  /// 模式检测器
  final Map<String, LogPatternDetector> _patternDetectors =
      <String, LogPatternDetector>{};

  /// 最大存储条目数
  final int _maxEntries = 100000;

  /// 获取日志流
  Stream<LogEntry> get logStream => _logController.stream;

  /// 添加日志条目
  void addLogEntry(LogEntry entry) {
    // 添加到存储
    _logEntries.add(entry);
    final index = _logEntries.length - 1;

    // 更新索引
    _updateIndexes(entry, index);

    // 清理旧条目
    _cleanupOldEntries();

    // 发送到流
    _logController.add(entry);

    // 检查告警
    _checkLogAlerts(entry);

    // 模式检测
    _detectPatterns(entry);
  }

  /// 查询日志
  LogQueryResult queryLogs(LogQuery query) {
    final startTime = DateTime.now().millisecondsSinceEpoch;

    // 获取候选索引
    final candidateIndexes = _getCandidateIndexes(query);

    // 过滤日志条目
    final filteredEntries = <LogEntry>[];
    for (final index in candidateIndexes) {
      if (index >= _logEntries.length) continue;

      final entry = _logEntries.elementAt(index);
      if (_matchesQuery(entry, query)) {
        filteredEntries.add(entry);
      }
    }

    // 排序
    _sortEntries(filteredEntries, query.sortBy, query.sortOrder);

    // 分页
    final totalCount = filteredEntries.length;
    final startIndex = query.offset;
    final int endIndex =
        math.min(startIndex + query.limit, filteredEntries.length);
    final pagedEntries = filteredEntries.sublist(startIndex, endIndex);

    final executionTime = DateTime.now().millisecondsSinceEpoch - startTime;

    return LogQueryResult(
      entries: pagedEntries,
      totalCount: totalCount,
      query: query,
      executionTime: executionTime,
    );
  }

  /// 获取日志统计
  LogStatistics getLogStatistics({
    DateTime? startTime,
    DateTime? endTime,
  }) {
    final start =
        startTime ?? DateTime.now().subtract(const Duration(hours: 24));
    final end = endTime ?? DateTime.now();

    final filteredEntries = _logEntries
        .where((LogEntry entry) =>
            entry.timestamp.isAfter(start) && entry.timestamp.isBefore(end))
        .toList();

    // 按级别统计
    final countByLevel = <LogLevel, int>{};
    for (final level in LogLevel.values) {
      countByLevel[level] = 0;
    }

    // 按来源统计
    final countBySource = <String, int>{};

    // 按类别统计
    final countByCategory = <String, int>{};

    for (final entry in filteredEntries) {
      countByLevel[entry.level] = (countByLevel[entry.level] ?? 0) + 1;
      countBySource[entry.source] = (countBySource[entry.source] ?? 0) + 1;
      if (entry.category != null) {
        countByCategory[entry.category!] =
            (countByCategory[entry.category!] ?? 0) + 1;
      }
    }

    // 计算错误率
    final errorCount = (countByLevel[LogLevel.error] ?? 0) +
        (countByLevel[LogLevel.fatal] ?? 0);
    final errorRate =
        filteredEntries.isNotEmpty ? errorCount / filteredEntries.length : 0.0;

    // 获取热门错误模式
    final topErrors = _getTopErrorPatterns(filteredEntries);

    return LogStatistics(
      totalCount: filteredEntries.length,
      countByLevel: countByLevel,
      countBySource: countBySource,
      countByCategory: countByCategory,
      timeRange: end.difference(start),
      errorRate: errorRate,
      topErrors: topErrors,
    );
  }

  /// 添加告警规则
  void addLogAlertRule(LogAlertRule rule) {
    _alertRules[rule.id] = rule;
  }

  /// 移除告警规则
  void removeLogAlertRule(String ruleId) {
    _alertRules.remove(ruleId);
  }

  /// 搜索日志
  List<LogEntry> searchLogs({
    required String searchText,
    int limit = 100,
    DateTime? startTime,
    DateTime? endTime,
  }) {
    final query = LogQuery(
      searchText: searchText,
      startTime: startTime,
      endTime: endTime,
      limit: limit,
    );

    return queryLogs(query).entries;
  }

  /// 获取错误日志
  List<LogEntry> getErrorLogs({
    int limit = 100,
    DateTime? startTime,
    DateTime? endTime,
  }) {
    final query = LogQuery(
      levels: <LogLevel>[LogLevel.error, LogLevel.fatal],
      startTime: startTime,
      endTime: endTime,
      limit: limit,
    );

    return queryLogs(query).entries;
  }

  /// 分析日志趋势
  Map<String, dynamic> analyzeLogTrends({
    Duration timeWindow = const Duration(hours: 24),
    Duration bucketSize = const Duration(hours: 1),
  }) {
    final endTime = DateTime.now();
    final startTime = endTime.subtract(timeWindow);

    final buckets = <DateTime, Map<LogLevel, int>>{};
    final bucketCount = timeWindow.inMilliseconds ~/ bucketSize.inMilliseconds;

    // 初始化桶
    for (int i = 0; i < bucketCount; i++) {
      final bucketTime =
          startTime.add(Duration(milliseconds: i * bucketSize.inMilliseconds));
      buckets[bucketTime] = <LogLevel, int>{
        for (final LogLevel level in LogLevel.values) level: 0
      };
    }

    // 填充数据
    for (final entry in _logEntries) {
      if (entry.timestamp.isBefore(startTime) ||
          entry.timestamp.isAfter(endTime)) {
        continue;
      }

      final bucketIndex =
          entry.timestamp.difference(startTime).inMilliseconds ~/
              bucketSize.inMilliseconds;
      final bucketTime = startTime
          .add(Duration(milliseconds: bucketIndex * bucketSize.inMilliseconds));

      if (buckets.containsKey(bucketTime)) {
        buckets[bucketTime]![entry.level] =
            (buckets[bucketTime]![entry.level] ?? 0) + 1;
      }
    }

    return <String, dynamic>{
      'timeWindow': timeWindow.inMilliseconds,
      'bucketSize': bucketSize.inMilliseconds,
      'buckets': buckets.map(
        (DateTime time, Map<LogLevel, int> counts) => MapEntry(
          time.toIso8601String(),
          counts
              .map((LogLevel level, int count) => MapEntry(level.name, count)),
        ),
      ),
    };
  }

  /// 更新索引
  void _updateIndexes(LogEntry entry, int index) {
    // 来源索引
    _sourceIndex[entry.source] ??= <int>{};
    _sourceIndex[entry.source]!.add(index);

    // 级别索引
    _levelIndex[entry.level] ??= <int>{};
    _levelIndex[entry.level]!.add(index);

    // 类别索引
    if (entry.category != null) {
      _categoryIndex[entry.category!] ??= <int>{};
      _categoryIndex[entry.category!]!.add(index);
    }

    // 文本索引（简化实现）
    final words = entry.message.toLowerCase().split(RegExp(r'\W+'));
    for (final word in words) {
      if (word.length > 2) {
        _textIndex[word] ??= <int>{};
        _textIndex[word]!.add(index);
      }
    }
  }

  /// 获取候选索引
  Set<int> _getCandidateIndexes(LogQuery query) {
    Set<int>? candidates;

    // 按来源过滤
    if (query.sources.isNotEmpty) {
      final sourceIndexes = <int>{};
      for (final source in query.sources) {
        sourceIndexes.addAll(_sourceIndex[source] ?? <int>{});
      }
      candidates = _intersectSets(candidates, sourceIndexes);
    }

    // 按级别过滤
    if (query.levels.isNotEmpty) {
      final levelIndexes = <int>{};
      for (final level in query.levels) {
        levelIndexes.addAll(_levelIndex[level] ?? <int>{});
      }
      candidates = _intersectSets(candidates, levelIndexes);
    }

    // 按类别过滤
    if (query.categories.isNotEmpty) {
      final categoryIndexes = <int>{};
      for (final category in query.categories) {
        categoryIndexes.addAll(_categoryIndex[category] ?? <int>{});
      }
      candidates = _intersectSets(candidates, categoryIndexes);
    }

    // 按搜索文本过滤
    if (query.searchText != null && query.searchText!.isNotEmpty) {
      final words = query.searchText!.toLowerCase().split(RegExp(r'\W+'));
      final textIndexes = <int>{};
      for (final word in words) {
        textIndexes.addAll(_textIndex[word] ?? <int>{});
      }
      candidates = _intersectSets(candidates, textIndexes);
    }

    // 如果没有过滤条件，返回所有索引
    return candidates ??
        Set<int>.from(List.generate(_logEntries.length, (int i) => i));
  }

  /// 集合交集
  Set<int>? _intersectSets(Set<int>? set1, Set<int> set2) {
    if (set1 == null) return set2;
    return set1.intersection(set2);
  }

  /// 检查是否匹配查询
  bool _matchesQuery(LogEntry entry, LogQuery query) {
    // 时间范围检查
    if (query.startTime != null && entry.timestamp.isBefore(query.startTime!)) {
      return false;
    }
    if (query.endTime != null && entry.timestamp.isAfter(query.endTime!)) {
      return false;
    }

    // 标签检查
    for (final tag in query.tags.entries) {
      if (entry.tags[tag.key] != tag.value) {
        return false;
      }
    }

    // 字段检查
    for (final field in query.fields.entries) {
      if (entry.fields[field.key] != field.value) {
        return false;
      }
    }

    return true;
  }

  /// 排序条目
  void _sortEntries(
      List<LogEntry> entries, LogSortField sortBy, SortOrder sortOrder) {
    entries.sort((LogEntry a, LogEntry b) {
      int comparison;

      switch (sortBy) {
        case LogSortField.timestamp:
          comparison = a.timestamp.compareTo(b.timestamp);
        case LogSortField.level:
          comparison = a.level.index.compareTo(b.level.index);
        case LogSortField.source:
          comparison = a.source.compareTo(b.source);
        case LogSortField.category:
          comparison = (a.category ?? '').compareTo(b.category ?? '');
      }

      return sortOrder == SortOrder.ascending ? comparison : -comparison;
    });
  }

  /// 清理旧条目
  void _cleanupOldEntries() {
    while (_logEntries.length > _maxEntries) {
      _logEntries.removeFirst();

      // 重建索引（简化实现）
      if (_logEntries.length % 1000 == 0) {
        _rebuildIndexes();
      }
    }
  }

  /// 重建索引
  void _rebuildIndexes() {
    _sourceIndex.clear();
    _levelIndex.clear();
    _categoryIndex.clear();
    _textIndex.clear();

    for (int i = 0; i < _logEntries.length; i++) {
      _updateIndexes(_logEntries.elementAt(i), i);
    }
  }

  /// 检查日志告警
  void _checkLogAlerts(LogEntry entry) {
    for (final rule in _alertRules.values) {
      if (!rule.enabled) continue;

      // 简化的告警检查逻辑
      if (_matchesQuery(entry, rule.query)) {
        // 检查时间窗口内的条目数量
        final windowStart = DateTime.now().subtract(rule.timeWindow);
        final recentEntries = _logEntries
            .where(
              (LogEntry e) =>
                  e.timestamp.isAfter(windowStart) &&
                  _matchesQuery(e, rule.query),
            )
            .length;

        if (recentEntries >= rule.threshold) {
          // 触发告警（这里可以发送通知）
          print('Log alert triggered: ${rule.name}');
        }
      }
    }
  }

  /// 检测模式
  void _detectPatterns(LogEntry entry) {
    for (final detector in _patternDetectors.values) {
      detector.analyze(entry);
    }
  }

  /// 获取热门错误模式
  List<LogPattern> _getTopErrorPatterns(List<LogEntry> entries) {
    final errorEntries = entries
        .where(
          (LogEntry e) =>
              e.level == LogLevel.error || e.level == LogLevel.fatal,
        )
        .toList();

    final patterns = <String, List<LogEntry>>{};

    for (final entry in errorEntries) {
      // 简化的模式提取（基于消息的前50个字符）
      final pattern = entry.message.length > 50
          ? entry.message.substring(0, 50)
          : entry.message;

      patterns[pattern] ??= <LogEntry>[];
      patterns[pattern]!.add(entry);
    }

    final logPatterns =
        patterns.entries.map((MapEntry<String, List<LogEntry>> entry) {
      final patternEntries = entry.value;
      patternEntries
          .sort((LogEntry a, LogEntry b) => a.timestamp.compareTo(b.timestamp));

      return LogPattern(
        pattern: entry.key,
        count: patternEntries.length,
        firstSeen: patternEntries.first.timestamp,
        lastSeen: patternEntries.last.timestamp,
        examples: patternEntries.take(3).toList(),
      );
    }).toList();

    logPatterns
        .sort((LogPattern a, LogPattern b) => b.count.compareTo(a.count));
    return logPatterns.take(10).toList();
  }

  /// 清理资源
  Future<void> dispose() async {
    await _logController.close();
    _logEntries.clear();
    _sourceIndex.clear();
    _levelIndex.clear();
    _categoryIndex.clear();
    _textIndex.clear();
    _alertRules.clear();
    _patternDetectors.clear();
  }
}

/// 日志模式检测器接口
abstract class LogPatternDetector {
  String get name;
  void analyze(LogEntry entry);
  List<LogPattern> getDetectedPatterns();
}
