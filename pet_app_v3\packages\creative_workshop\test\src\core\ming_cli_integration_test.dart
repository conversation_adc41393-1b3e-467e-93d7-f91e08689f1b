import 'dart:io';
import 'package:test/test.dart';
import 'package:path/path.dart' as path;
import 'package:creative_workshop/creative_workshop.dart';

void main() {
  group('Ming CLI集成测试', () {
    late LocalMingCliService mingCliService;
    late Directory tempDir;

    setUpAll(() async {
      mingCliService = LocalMingCliService.instance;
      
      // 创建临时测试目录
      tempDir = await Directory.systemTemp.createTemp('ming_cli_test_');
    });

    tearDownAll(() async {
      // 清理临时目录
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
      }
    });

    group('服务初始化测试', () {
      test('应该能够获取Ming CLI服务实例', () {
        expect(mingCliService, isNotNull);
        expect(mingCliService, equals(LocalMingCliService.instance));
      });

      test('应该能够检测Ming CLI状态', () async {
        await mingCliService.detectMingCli();
        
        final status = mingCliService.status;
        expect(status, isNotNull);
        expect(status.isNotEmpty, isTrue);
        
        final mode = mingCliService.currentMode;
        expect(mode, isA<MingCliMode>());
      });

      test('应该能够获取版本信息', () async {
        await mingCliService.detectMingCli();
        
        final version = mingCliService.version;
        expect(version, isNotNull);
        expect(version.isNotEmpty, isTrue);
      });
    });

    group('命令执行测试', () {
      test('应该能够执行version命令', () async {
        final result = await mingCliService.executeCommand(['--version']);
        
        expect(result, isNotNull);
        expect(result.success, isTrue);
        expect(result.output.isNotEmpty, isTrue);
        expect(result.output.contains('ming_status_cli'), isTrue);
      });

      test('应该能够执行doctor命令', () async {
        final result = await mingCliService.executeCommand(['doctor']);
        
        expect(result, isNotNull);
        expect(result.success, isTrue);
        expect(result.output.isNotEmpty, isTrue);
        expect(result.output.contains('环境检查'), isTrue);
      });

      test('应该能够执行template list命令', () async {
        final result = await mingCliService.executeCommand(['template', 'list']);
        
        expect(result, isNotNull);
        expect(result.success, isTrue);
        expect(result.output.isNotEmpty, isTrue);
        expect(result.output.contains('模板列表'), isTrue);
      });

      test('应该能够处理无效命令', () async {
        final result = await mingCliService.executeCommand(['invalid_command']);
        
        expect(result, isNotNull);
        expect(result.success, isFalse);
        expect(result.output.contains('不支持'), isTrue);
      });
    });

    group('项目创建测试', () {
      test('应该能够创建基础项目', () async {
        final projectName = 'test_project_${DateTime.now().millisecondsSinceEpoch}';
        final projectPath = path.join(tempDir.path, projectName);
        
        // 切换到临时目录
        final originalDir = Directory.current;
        Directory.current = tempDir;
        
        try {
          final result = await mingCliService.executeCommand(['create', projectName]);
          
          expect(result, isNotNull);
          expect(result.success, isTrue);
          expect(result.output.contains('项目创建成功'), isTrue);
          
          // 验证项目文件是否创建
          final projectDir = Directory(projectPath);
          expect(await projectDir.exists(), isTrue);
          
          final pubspecFile = File(path.join(projectPath, 'pubspec.yaml'));
          expect(await pubspecFile.exists(), isTrue);
          
          final readmeFile = File(path.join(projectPath, 'README.md'));
          expect(await readmeFile.exists(), isTrue);
          
          final mainFile = File(path.join(projectPath, 'lib', 'main.dart'));
          expect(await mainFile.exists(), isTrue);
          
          final testFile = File(path.join(projectPath, 'test', 'widget_test.dart'));
          expect(await testFile.exists(), isTrue);
        } finally {
          Directory.current = originalDir;
        }
      });

      test('应该能够处理项目名称冲突', () async {
        final projectName = 'conflict_project';
        final projectPath = path.join(tempDir.path, projectName);
        
        // 先创建一个同名目录
        await Directory(projectPath).create();
        
        final originalDir = Directory.current;
        Directory.current = tempDir;
        
        try {
          final result = await mingCliService.executeCommand(['create', projectName]);
          
          expect(result, isNotNull);
          expect(result.success, isFalse);
          expect(result.output.contains('已存在'), isTrue);
        } finally {
          Directory.current = originalDir;
        }
      });

      test('应该能够处理无效的create命令参数', () async {
        final result = await mingCliService.executeCommand(['create']);
        
        expect(result, isNotNull);
        expect(result.success, isFalse);
        expect(result.output.contains('用法错误'), isTrue);
      });
    });

    group('模板功能测试', () {
      test('应该能够列出可用模板', () async {
        final result = await mingCliService.executeCommand(['template', 'list']);
        
        expect(result, isNotNull);
        expect(result.success, isTrue);
        expect(result.output.contains('basic'), isTrue);
        expect(result.output.contains('plugin'), isTrue);
        expect(result.output.contains('widget'), isTrue);
      });

      test('应该能够处理模板创建请求', () async {
        final result = await mingCliService.executeCommand(['template', 'create']);
        
        expect(result, isNotNull);
        expect(result.success, isTrue);
        expect(result.output.contains('需要完整的Ming CLI'), isTrue);
      });

      test('应该能够处理模板验证请求', () async {
        final result = await mingCliService.executeCommand(['template', 'validate']);
        
        expect(result, isNotNull);
        expect(result.success, isTrue);
        expect(result.output.contains('需要完整的Ming CLI'), isTrue);
      });
    });

    group('验证功能测试', () {
      test('应该能够执行项目验证', () async {
        final result = await mingCliService.executeCommand(['validate']);
        
        expect(result, isNotNull);
        expect(result.success, isTrue);
        expect(result.output.contains('项目验证'), isTrue);
        expect(result.output.contains('降级模式'), isTrue);
      });
    });

    group('错误处理测试', () {
      test('应该能够处理空命令', () async {
        final result = await mingCliService.executeCommand([]);
        
        expect(result, isNotNull);
        expect(result.success, isFalse);
      });

      test('应该能够记录执行时间', () async {
        final result = await mingCliService.executeCommand(['--version']);
        
        expect(result, isNotNull);
        expect(result.executionTime, isA<Duration>());
        expect(result.executionTime.inMilliseconds, greaterThan(0));
      });

      test('应该能够正确设置退出码', () async {
        final successResult = await mingCliService.executeCommand(['--version']);
        expect(successResult.exitCode, equals(0));
        
        final failResult = await mingCliService.executeCommand(['invalid']);
        expect(failResult.exitCode, equals(1));
      });
    });

    group('模式检测测试', () {
      test('应该能够检测当前运行模式', () async {
        await mingCliService.detectMingCli();
        
        final mode = mingCliService.currentMode;
        expect(mode, isA<MingCliMode>());
        
        // 在测试环境中，通常会是fallback模式
        expect([
          MingCliMode.fallback,
          MingCliMode.dartRun,
          MingCliMode.localBuilt,
          MingCliMode.systemInstalled,
        ].contains(mode), isTrue);
      });

      test('应该能够提供模式描述', () async {
        await mingCliService.detectMingCli();
        
        final status = mingCliService.status;
        expect(status, isNotNull);
        expect(status.isNotEmpty, isTrue);
        
        // 状态描述应该包含版本或模式信息
        expect(status.contains('v') || status.contains('模式'), isTrue);
      });
    });
  });
}
